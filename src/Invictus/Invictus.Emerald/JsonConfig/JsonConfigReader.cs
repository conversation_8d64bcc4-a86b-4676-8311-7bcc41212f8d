using Invictus.Nomenklatura.Misc;

namespace Invictus.Emerald.JsonConfig;

public class JsonConfigReader
{
    private readonly InvJsonSerializer _serializer = new ();
    
    public List<string> JsonToCommandList(string jsonInput)
    {
        var data = _serializer.DeserializeForInternals<JsonRoot>(jsonInput);

        var output = new List<string>();

        foreach (JsonTriggerDefinition trigger in data.TriggerDefinitions)
        {
            output.Add($"trigdef new {trigger.Name}");
        }

        foreach (JsonFeature feature in data.Features)
        {
            output.Add($"fea new {feature.Name}");
            
            foreach (JsonOperation operation in feature.Operations)
            {
                output.Add($"op new {feature.Name} {operation.Name}");
                
                foreach (string trigger in operation.Triggers)
                {
                    output.Add($"trig new {feature.Name} {operation.Name} {trigger}");
                }
            }
            
            foreach (JsonResource resource in feature.Resources)
            {
                output.Add($"res new {feature.Name} {resource.Name}");
            }
        }

        foreach (JsonFeature feature in data.Features)
        {
            foreach (JsonOperation operation in feature.Operations)
            {
                foreach (string resourceName in operation.In)
                {
                    output.Add($"op res {feature.Name} {operation.Name} in {resourceName}");
                }
                
                foreach (string resourceName in operation.Out)
                {
                    output.Add($"op res {feature.Name} {operation.Name} out {resourceName}");
                }
            }
        }

        foreach (string run in data.Run)
        {
            output.Add($"op run {run}");
        }

        foreach (JsonFeature feature in data.Features)
        {
            foreach (JsonOperation operation in feature.Operations)
            {
                output.Add($"op routine {feature.Name} {operation.Name} 7");
            }
        }

        return output;
    }
}

