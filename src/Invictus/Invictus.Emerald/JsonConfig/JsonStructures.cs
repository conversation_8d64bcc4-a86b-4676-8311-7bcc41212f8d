using System.Text.Json.Serialization;

namespace Invictus.Emerald.JsonConfig;

class JsonRoot
{
    [JsonPropertyName("TriggerDefinitions")]
    public List<JsonTriggerDefinition> TriggerDefinitions { get; set; } = new();
    [JsonPropertyName("Features")]
    public List<JsonFeature> Features { get; set; } = new();
    [JsonPropertyName("Run")]
    public List<string> Run { get; set; } = new();
}

class JsonTriggerDefinition
{
    [JsonPropertyName("Name")]
    public string Name { get; set; }
}

class JsonFeature
{
    [JsonPropertyName("Name")]
    public string Name { get; set; }

    [JsonPropertyName("Operations")]
    public List<JsonOperation> Operations { get; set; } = new();
    [JsonPropertyName("Resources")]
    public List<JsonResource> Resources { get; set; } = new();
}

class JsonOperation
{
    [JsonPropertyName("Name")]
    public string Name { get; set; }
    [JsonPropertyName("Triggers")]
    public List<string> Triggers { get; set; } = new();

    [JsonPropertyName("In")]
    public List<string> In { get; set; } = new();
    
    [JsonPropertyName("Out")]
    public List<string> Out { get; set; } = new();
}

class JsonResource
{
    [JsonPropertyName("Name")]
    public string Name { get; set; }
}