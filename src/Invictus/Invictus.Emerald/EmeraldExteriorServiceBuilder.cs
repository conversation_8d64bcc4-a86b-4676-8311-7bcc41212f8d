using System.CommandLine;

using Invictus.Emerald.Misc;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.ExteriorServ;

using Microsoft.Extensions.DependencyInjection;

namespace Invictus.Emerald;

public class EmeraldExteriorServiceBuilder : ExteriorServiceBuilderBase<EmeraldExteriorService>
{
    protected override void AddConcealedServices(ServiceCollection concealedServiceCollection)
    {
        concealedServiceCollection.AddWorkerSingleton<EmeraldCoreWorker>();
        concealedServiceCollection.AddWorkerSingleton<EmeraldExecWorker>();
        
        concealedServiceCollection.AddSingleton<EmeraldConsole>();
        concealedServiceCollection.AddSingleton<EmeraldServiceLocator>();
        concealedServiceCollection.AddSingleton<EmeraldAssemblyObjectsLocator>();
        concealedServiceCollection.AddSingleton<EmeraldShared>();
        concealedServiceCollection.AddSingleton<IConsole, SerilogConsole>();
        concealedServiceCollection.AddSingleton<EmeraldService>();
        
        base.AddConcealedServices(concealedServiceCollection);
    }

    protected override void ExposeConcealedServices()
    {
        this.ExposeSingleton<EmeraldConsole>();
        this.ExposeSingleton<EmeraldService>();
        
        base.ExposeConcealedServices();
    }
}