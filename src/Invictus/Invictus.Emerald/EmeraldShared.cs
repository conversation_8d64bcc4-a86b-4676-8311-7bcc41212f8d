using Invictus.Emerald.Shared;
using Invictus.Nomenklatura.Misc;

namespace Invictus.Emerald;

public class EmeraldShared
{
    public const string NAME_SPLITTER = "::";
    public const int CANCEL_DELAY_MS = 300;

    public static bool IsProperFullName(string name)
    {
        return name.ContainsCount(NAME_SPLITTER, StringComparison.Ordinal) == 1;
    }

    public static (string featureName, string name) SplitFullName(string fullName)
    {
        string[] spl = fullName.Split(NAME_SPLITTER);

        if (spl.Length != 2)
            throw new EmeraldException($"{fullName} is not a proper full name.");

        return (spl[0], spl[1]);
    }
    
    private readonly ILazyResolve<IEmeraldCoreWorkerInvoke> _emeraldCoreInvokeResolve;
    private readonly ILazyResolve<EmeraldConsole> _consoleResolve;
    private readonly ILazyResolve<IEmeraldExecWorker> _execWorkerResolve;
    private readonly ILazyResolve<IEmeraldCoreWorker> _coreWorkerResolve;

    public IEmeraldCoreWorkerInvoke Invoke => _emeraldCoreInvokeResolve.Resolve();

    public IEmeraldExecWorker ExecWorker => _execWorkerResolve.Resolve();

    public EmeraldConsole Console => _consoleResolve.Resolve();

    public IEmeraldCoreWorker CoreWorker => _coreWorkerResolve.Resolve();
    
    public EmeraldAssemblyObjectsLocator AssemblyObjectsLocator { get; }
    public EmeraldServiceLocator ServiceLocator { get; }

    public EmeraldShared(
        EmeraldAssemblyObjectsLocator assemblyObjectsLocator,
        EmeraldServiceLocator serviceLocator,
        ILazyResolve<IEmeraldCoreWorkerInvoke> emeraldCoreInvokeResolve,
        ILazyResolve<EmeraldConsole> consoleResolve,
        ILazyResolve<IEmeraldExecWorker> execWorkerResolve,
        ILazyResolve<IEmeraldCoreWorker> coreWorkerResolve
    )
    {
        _emeraldCoreInvokeResolve = emeraldCoreInvokeResolve;
        _consoleResolve = consoleResolve;
        _execWorkerResolve = execWorkerResolve;
        _coreWorkerResolve = coreWorkerResolve;
        
        AssemblyObjectsLocator = assemblyObjectsLocator;
        ServiceLocator = serviceLocator;
    }
}