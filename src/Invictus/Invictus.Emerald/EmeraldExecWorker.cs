using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Workers;

namespace Invictus.Emerald;

public interface IEmeraldExecWorker : IWorker<IEmeraldExecWorker>
{
    Task TaskRunMethod(EmeraldOperation operation, List<object> resourceArgs);
}

class EmeraldExecWorker :
    IWorkerImpl<IEmeraldExecWorker>, IEmeraldExecWorker
{
    public ILogger Log => _logger;
    private readonly ILogger _logger = InvLog.Logger<EmeraldExecWorker>();

    public WorkerCore Core { get; set; }

    IEmeraldExecWorker IWorkerImpl<IEmeraldExecWorker>.PublicInterface { get; set; }

    public WorkerConfiguration WorkerConfiguration { get; } = new(
        "EMRLD1",
        new WorkerConfiguration.Thread("EmeraldExec", ThreadPriority.Normal, IsBackground: false),
        LogEventLevel.Verbose,
        AllowDirectCall: true
    );

    public Task TaskRunMethod(EmeraldOperation operation, List<object> resourceArgs)
    {
        return operation.TaskRunMethodOnExecThread(resourceArgs);
    }
}