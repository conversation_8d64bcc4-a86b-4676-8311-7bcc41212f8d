using Invictus.Emerald.Shared;
using Invictus.Nomenklatura.Exceptions;

namespace Invictus.Emerald;

public class EmeraldTriggerDefinition
{
    private readonly EmeraldShared _shared;
    
    public string Name { get; }

    private readonly IEmTriggerDefinition _triggerDefinition;
    
    public EmeraldTriggerDefinition(EmeraldShared shared, string name, Type triggerType)
    {
        _shared = shared;
        
        Name = name;

        _triggerDefinition = (IEmTriggerDefinition)shared.ServiceLocator.GetService(triggerType);
    }

    public IEmTrigger CreateTrigger(IEmeraldCoreWorkerInvoke coreInvoke, EmeraldOperation operation, OperationInvocationFlags flags, string arg)
    {
        var triggerProxy = new EmeraldTriggerProxy(coreInvoke, operation, flags);
        
        return _triggerDefinition.CreateTrigger(triggerProxy, arg);
    }
}

public class EmeraldTriggerProxy : IEmTriggerTarget
{
    private readonly IEmeraldCoreWorkerInvoke _coreInvoke;
    private readonly EmeraldOperation _operation;
    private readonly OperationInvocationFlags _flags;
    private TaskCompletionSource _waitingForOperationCompletion;
    
    public EmeraldTriggerProxy(IEmeraldCoreWorkerInvoke coreInvoke, EmeraldOperation operation, OperationInvocationFlags flags)
    {
        _coreInvoke = coreInvoke;
        _operation = operation;
        _flags = flags;
        
        if ((flags & ~OperationInvocationFlags.RunCancel) != OperationInvocationFlags.None)
            throw new EmeraldException($"Trigger flags {flags} are not supported.");
    }
    
    public Task TriggerOperationFromTrigger()
    {
        if (_waitingForOperationCompletion != null)
            return _waitingForOperationCompletion.Task;
        
        _coreInvoke.Invoke(this.TriggerOperationInvokation);

        if (_waitingForOperationCompletion == null)
            throw new JustNoWayException();

        return _waitingForOperationCompletion.Task;
    }

    private void OperationOnOperationCompleted()
    {
        _operation.OperationCompleted -= this.OperationOnOperationCompleted;
        _waitingForOperationCompletion.SetResult();
        _waitingForOperationCompletion = null;
    }

    private void TriggerOperationInvokation()
    {
        _waitingForOperationCompletion = new TaskCompletionSource();
        _operation.OperationCompleted += this.OperationOnOperationCompleted;
        
        _operation.TriggerOperation(_flags);
    }
}