using Invictus.Nomenklatura.ExteriorServ;

namespace Invictus.Emerald;

public class EmeraldExteriorService : IExteriorService
{
    public EmeraldExteriorService(
        IEmeraldCoreWorker emeraldCoreWorker,
        IEmeraldCoreWorkerInvoke emeraldCoreWorkerInvoke,
        EmeraldConsole emeraldConsole,
        EmeraldServiceLocator serviceLocator,
        EmeraldAssemblyObjectsLocator assemblyObjectsLocator,
        EmeraldShared emeraldShared
    )
    {
        emeraldCoreWorker.Run(w => w.Initialize());
    }

    public Task Run()
    {
        return Task.CompletedTask;
    }
}