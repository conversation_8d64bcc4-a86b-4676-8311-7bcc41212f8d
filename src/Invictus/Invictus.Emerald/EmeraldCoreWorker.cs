using System.Reflection;

using Invictus.Emerald.JsonConfig;
using Invictus.Emerald.Shared;
using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;
using Invictus.Nomenklatura.Workers;

using OneOf;

namespace Invictus.Emerald;

public interface IEmeraldCoreWorker : IWorker<IEmeraldCoreWorker>
{
    void Initialize();
    
    void LoadAssembly(string assemblyFileName);
    
    void FinalizeLoadAssembly(string assemblyFileName);

    void RunLoaded();
    
    void AddFeature(string featureName);
    void AddOperation(string featureName, string operationName);

    void AddResource(string featureName, string resourceName);
    
    void AddTriggerDefinition(string triggerName);

    void AddTrigger(string featureName, string operationName, string triggerName, string triggerArgument);
    
    void SetRoutineFlags(string featureName,  string operationName, RoutineFlags routineFlags);

    void RunOperation(string featureName, string operationName, OperationInvocationFlags operationFlags);

    void AddOperationInput(string featureName, string operationName, string resourceName);
    
    void AddOperationOutput(string featureName, string operationName, string resourceName);

    void UnhaltOperation(string featureName, string operationName);

    Task<object> ResolveResource(ILogger caller, string featureName, string resourceName);
    
    object FindFeatureClass(string featureName);
}

public interface IEmeraldCoreWorkerInvoke : IWorker<IEmeraldCoreWorkerInvoke>
{
    [DoNotLog]
    void Invoke(Action action);
}

public class EmeraldCoreWorker : 
    IWorkerImpl<IEmeraldCoreWorker>, IEmeraldCoreWorker, 
    IWorkerImpl<IEmeraldCoreWorkerInvoke>, IEmeraldCoreWorkerInvoke
{
    public ILogger Log { get; } = InvLog.Logger<EmeraldCoreWorker>();
    private readonly ILogger _logger;

    public WorkerCore Core { get; set; }

    IEmeraldCoreWorker IWorkerImpl<IEmeraldCoreWorker>.PublicInterface { get; set; }
    IEmeraldCoreWorkerInvoke IWorkerImpl<IEmeraldCoreWorkerInvoke>.PublicInterface { get; set; }
    
    public WorkerConfiguration WorkerConfiguration { get; } = new(
        "EMRLD0",
        new WorkerConfiguration.Thread("EmeraldCore", ThreadPriority.Normal, IsBackground: false),
        LogEventLevel.Verbose,
        AllowDirectCall: true
    );

    private readonly InvEnv _environment;
    private readonly InvTasks _threadedTasks;
    private readonly EmeraldShared _shared;
    
    private readonly List<EmeraldFeature> _features = new();
    private readonly List<EmeraldTriggerDefinition> _triggerDefinitions = new();

    public EmeraldCoreWorker(InvEnv environment, InvTasks threadedTasks, EmeraldShared shared)
    {
        _environment = environment;
        _threadedTasks = threadedTasks;
        _shared = shared;
        _logger = Log;
    }
    
    public void Initialize()
    {
    }

    public void Invoke(Action action)
    {
        action();
    }

    private readonly List<string> _loadedRunCommands = new();

    public void LoadAssembly(string assemblyFileName)
    {
        _logger.Information($"Loading {assemblyFileName}");
        
        string filePath = Path.Combine(AppContext.BaseDirectory, assemblyFileName + ".dll");

        Assembly assembly = TypeResolver.LoadedAssemblies.FirstOrDefault(a => a.GetName().Name == assemblyFileName);

        if (assembly == null)
        {
            assembly = Assembly.LoadFrom(filePath);
            
            TypeResolver.LoadedAssemblies.Add(assembly);
        }

        _shared.AssemblyObjectsLocator.AddAssembly(assembly);

        string[] assemblyManifestNames = assembly.GetManifestResourceNames();
        
        string emeraldJsonResource = assemblyManifestNames.FirstOrDefault(r => r.Contains("Emerald.json", StringComparison.InvariantCultureIgnoreCase));
        
        StreamReader streamReader;

        IEnumerable<string> commands;

        if (emeraldJsonResource != null)
        {
            streamReader = new StreamReader(assembly.GetManifestResourceStream(emeraldJsonResource));
            
            var r = new JsonConfigReader();
            
            commands = r.JsonToCommandList(streamReader.ReadToEnd());
            
            this.ExecuteCommandsOnLoad(assemblyFileName, commands);
            
            return;
        }
        
        string emScriptResource = assemblyManifestNames.FirstOrDefault(r => r.Contains("em.script", StringComparison.InvariantCultureIgnoreCase));

        if (emScriptResource == null)
        {
            _shared.Console.CommandProgram("fin_load " + assemblyFileName);

            return;
        }

        streamReader = new StreamReader(assembly.GetManifestResourceStream(emScriptResource));
        
        commands = streamReader.ReadToEnd().Replace("\r", "").Split('\n');

        this.ExecuteCommandsOnLoad(assemblyFileName, commands);
    }

    private void ExecuteCommandsOnLoad(string assemblyFileName, IEnumerable<string> lines)
    {
        List<string> toExecuteNow = new();
        
        foreach (string command in lines)
        {
            if (command.StartsWith("op routine") || command.StartsWith("op run"))
                _loadedRunCommands.Add(command);
            else
                toExecuteNow.Add(command);
        }
        
        this.DoExecuteCommands(toExecuteNow.ToArray());

        _shared.Console.CommandProgram("fin_load " + assemblyFileName);
    }
    
    public void FinalizeLoadAssembly(string assemblyFileName)
    {
        _shared.Console.CommandProgram("fin_load_loopback " + assemblyFileName);
    }

    public void RunLoaded()
    {
        string[] commands = _loadedRunCommands
            .OrderByDescending(x => x.StartsWith("op routine", StringComparison.OrdinalIgnoreCase))
            .ThenBy(x => x)
            .ToArray();
        
        _loadedRunCommands.Clear();
        
        this.DoExecuteCommands(commands);
    }

    private void DoExecuteCommands(string[] lines)
    {
        foreach (string line in lines)
        {
            _shared.Console.CommandProgram(line);
        }
    }
    
    public async Task<object> ResolveResource(ILogger caller, string featureName, string resourceName)
    {
        EmeraldResource resource = this.FindFeatureRequired(featureName).FindResourceRequired(resourceName);

        // Fast path
        if (resource.State == ResourceState.Ready)
            return Task.FromResult(resource.ResourceInstance);

        EmeraldOperation responsibleOperation = null;

        foreach (EmeraldOperation operation in _features.SelectMany(f => f.Operations))
        {
            if (!operation.OutputResources.Contains(resource))
                continue;

            if (responsibleOperation != null)
                throw new EmeraldException($"Multiple operations can produce {featureName}::{resourceName}");

            responsibleOperation = operation;
        }

        if (responsibleOperation == null)
            throw new EmeraldException($"No one is responsible for {featureName}::{resourceName}!");

        return await new WaitForResource(caller, responsibleOperation, resource).Wait();
    }

    public void RunOperation(string featureName, string operationName, OperationInvocationFlags operationFlags)
    {
        EmeraldOperation operation = this.FindOperationRequired(featureName, operationName);
        
        operation.TriggerOperation(operationFlags);
    }

    public void UnhaltOperation(string featureName, string operationName)
    {
        EmeraldOperation operation = this.FindOperationRequired(featureName, operationName);
        
        operation.Unhalt();
    }

    public void SetRoutineFlags(string featureName, string operationName, RoutineFlags routineFlags)
    {
        EmeraldOperation operation = this.FindOperationRequired(featureName, operationName);
        
        operation.Routine(routineFlags);
    }

    public void AddFeature(string featureName)
    {
        if (!featureName.All(IntStringUtil.IsBasicLetterColonOrDigit))
            throw new EmeraldException("Feature name contains invalid characters.");

        Type featureType = _shared.AssemblyObjectsLocator.GetFeatureType(featureName);
        
        object featureInstance = _shared.ServiceLocator.GetService(featureType);
        
        _features.Add(new EmeraldFeature(_shared, featureName, featureInstance));
    }
    
    public void AddTrigger(string featureName, string operationName, string triggerName, string triggerArgument)
    {
        EmeraldOperation operation = this.FindTriggerDefinition(featureName, operationName, triggerName, out EmeraldTriggerDefinition triggerDefinition);

        operation.AddTrigger(triggerDefinition, triggerArgument);
    }

    private EmeraldOperation FindTriggerDefinition(string featureName, string operationName, string triggerName, out EmeraldTriggerDefinition triggerDefinition)
    {
        EmeraldOperation operation = this.FindOperationRequired(featureName, operationName);
        triggerDefinition = this.FindTriggerDefinitionRequired(triggerName);
        
        return operation;
    }
    
    public void AddTriggerDefinition(string triggerName)
    {
        if (!triggerName.All(IntStringUtil.IsBasicLetterColonOrDigit))
            throw new EmeraldException($"Trigger {triggerName} contains invalid characters.");

        Type type = _shared.AssemblyObjectsLocator.GetTriggerDefinitionType(triggerName);

        if (!type.IsAssignableTo(typeof(IEmTriggerDefinition)))
            throw new EmeraldException($"Trigger {triggerName} should implement {nameof(IEmTriggerDefinition)}");
        
        _triggerDefinitions.Add(new EmeraldTriggerDefinition(_shared, triggerName, type));
    }

    public void AddResource(string featureName, string resourceName)
    {
        if (!resourceName.All(IntStringUtil.IsBasicLetterColonOrDigit))
            throw new EmeraldException("Resource name contains invalid characters.");
        
        EmeraldFeature feature = this.FindFeatureRequired(featureName);
        
        feature.AddResource(resourceName);
    }

    public void AddOperation(string featureName, string operationName)
    {
        if (!operationName.All(IntStringUtil.IsBasicLetterColonOrDigit))
            throw new EmeraldException("Operation name contains invalid characters.");
        
        EmeraldFeature feature = this.FindFeatureRequired(featureName);
        
        Type workerType = feature.FeatureInstance.GetType();
        
        OneOf<MethodInfo, Type> methodInfoOrType = _shared.AssemblyObjectsLocator.GetOperation(featureName, operationName);
        
        if (methodInfoOrType.IsT0 && !methodInfoOrType.AsT0.DeclaringType.IsAssignableFrom(workerType))
            throw new EmeraldException($"Method {methodInfoOrType.AsT0.DeclaringType} {methodInfoOrType.AsT0.Name} should correspond to the feature worker type.");
        
        feature.AddOperation(operationName, methodInfoOrType);
    }

    public void AddOperationInput(string featureName, string operationName, string resourceName)
    {
        EmeraldOperation operation = this.FindOperationRequired(featureName, operationName);
        EmeraldResource resource = this.FindResourceRequired(resourceName, featureName);
        
        operation.AddInputResource(resource);
    }

    public void AddOperationOutput(string featureName, string operationName, string resourceName)
    {
        EmeraldOperation operation = this.FindOperationRequired(featureName, operationName);
        EmeraldResource resource = this.FindResourceRequired(resourceName, featureName);
        
        operation.AddOutputResource(resource);
    }

    private EmeraldResource FindResourceRequired(string resourceName, string contextFeatureName = null)
    {
        bool fullName = EmeraldShared.IsProperFullName(resourceName);

        if (!fullName && contextFeatureName == null)
            throw new EmeraldException("Should supply either full resource name of context feature name");

        string featureName;
        
        if (fullName)
        {
            (featureName, resourceName) = EmeraldShared.SplitFullName(resourceName);
        } else
        {
            featureName = contextFeatureName;
        }
        
        EmeraldResource res = this.FindFeatureRequired(featureName).FindResourceRequired(resourceName);
        return res;
    }
    
    private EmeraldFeature FindFeatureRequired(string featureName)
    {
        return _features.Where(f => f.Name == featureName).SingleWith($"Feature {featureName}");
    }
    
    private EmeraldFeature FindFeature(string featureName)
    {
        return _features.FirstOrDefault(f => f.Name == featureName);
    }

    private EmeraldOperation FindOperationRequired(string featureName, string operationName)
    {
        EmeraldOperation res = this.FindOperation(featureName, operationName);

        if (res == null)
            throw new EmeraldException($"Operation {featureName}::{operationName} was not found.");

        return res;
    }
    
    private EmeraldOperation FindOperation(string featureName, string operationName)
    {
        EmeraldFeature feature = _features.FirstOrDefault(f => f.Name == featureName);
        if (feature == null)
            return null;
        return feature.FindOperation(operationName);
    }

    private EmeraldTriggerDefinition FindTriggerDefinitionRequired(string triggerName)
    {
        EmeraldTriggerDefinition res = _triggerDefinitions.FirstOrDefault(f => f.Name == triggerName);
        
        if (res == null)
            throw new EmeraldException($"Trigger '{triggerName}' was not found.");

        return res;
    }

    public object FindFeatureClass(string featureName)
    {
        return this.FindFeature(featureName).FeatureInstance;
    }
}