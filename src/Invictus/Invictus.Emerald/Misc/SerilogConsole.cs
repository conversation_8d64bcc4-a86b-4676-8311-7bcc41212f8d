using System.CommandLine;
using System.CommandLine.IO;

using Invictus.Nomenklatura.Logg;

namespace Invictus.Emerald.Misc;

public class SerilogConsole : IConsole
{
    private readonly ILogger _logger = InvLog.Logger<SerilogConsole>();
    private readonly TextWriter _out;
    private readonly TextWriter _error;

    public SerilogConsole()
    {
        _out = new SerilogTextWriter(_logger, LogEventLevel.Information);
        _error = new SerilogTextWriter(_logger, LogEventLevel.Error);
    }

    public IStandardStreamWriter Out => new StandardStreamWriter(_out);
    public IStandardStreamWriter Error => new StandardStreamWriter(_error);
    public bool IsOutputRedirected => false;
    public bool IsErrorRedirected => false;
    public bool IsInputRedirected => true;

    private class StandardStreamWriter : IStandardStreamWriter
    {
        private readonly TextWriter _writer;

        public StandardStreamWriter(TextWriter writer)
        {
            _writer = writer;
        }

        public void Write(string value)
        {
            _writer.Write(value);
        }
    }

    private class SerilogTextWriter : TextWriter
    {
        private readonly ILogger _logger;
        private readonly LogEventLevel _level;
        private string _currentLine = string.Empty;

        public SerilogTextWriter(ILogger logger, LogEventLevel level)
        {
            _logger = logger;
            _level = level;
        }

        public override void Write(char value)
        {
            if (value == '\n')
            {
                this.WriteLine(_currentLine);
                _currentLine = string.Empty;
            }
            else if (value != '\r')
            {
                _currentLine += value;
            }
        }

        public override void Write(string value)
        {
            if (string.IsNullOrEmpty(value)) return;

            string[] lines = value.Split(new[] { Environment.NewLine }, StringSplitOptions.None);
            for (int i = 0; i < lines.Length; i++)
            {
                _currentLine += lines[i];
                if (i < lines.Length - 1)
                {
                    this.WriteLine(_currentLine);
                    _currentLine = string.Empty;
                }
            }
        }

        public override void WriteLine(string value)
        {
            string lineToWrite = string.IsNullOrEmpty(_currentLine) 
                ? value 
                : _currentLine + value;

            if (!string.IsNullOrEmpty(lineToWrite))
            {
                switch (_level)
                {
                    case LogEventLevel.Error:
                        _logger.Error(lineToWrite);
                        break;
                    case LogEventLevel.Information:
                        _logger.Information(lineToWrite);
                        break;
                    default:
                        _logger.Write(_level, lineToWrite);
                        break;
                }
            }

            _currentLine = string.Empty;
        }

        public override Encoding Encoding => Encoding.UTF8;
    }
}