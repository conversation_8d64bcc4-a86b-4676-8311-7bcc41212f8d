using JetBrains.Annotations;

namespace Invictus.Emerald.Shared;

[AttributeUsage(AttributeTargets.Class, Inherited = false)]
[MeansImplicitUse]
public sealed class EmeraldResourceAttribute : Attribute
{
    public string Name { get; }
    
    public EmeraldResourceAttribute(string name)
    {
        Name = name;
    }
}

[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class, Inherited = false)]
[MeansImplicitUse]
public sealed class EmeraldOperationAttribute : Attribute
{
    public string Name { get; }

    public EmeraldOperationAttribute()
    {
    }

    public EmeraldOperationAttribute(string name)
    {
        Name = name;
    }
}

[AttributeUsage(AttributeTargets.Class | AttributeTargets.Interface, Inherited = false)]
[MeansImplicitUse]
public sealed class EmeraldFeatureAttribute : Attribute
{
    public string Name { get; }
    
    public EmeraldFeatureAttribute(string name)
    {
        Name = name;
    }
}

[AttributeUsage(AttributeTargets.Class, Inherited = false)]
[MeansImplicitUse]
public sealed class EmeraldTriggerDefinitionAttribute : Attribute
{
    public string Name { get; }
    
    public EmeraldTriggerDefinitionAttribute(string name)
    {
        Name = name;
    }
}