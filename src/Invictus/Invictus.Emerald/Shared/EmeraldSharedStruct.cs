namespace Invictus.Emerald.Shared;

[Flags]
public enum RoutineFlags
{
    None = 0,
    OperationTriggers = 1,
    ResourceTriggers = 2,
    Timers = 4,

    AllPublic = OperationTriggers | ResourceTriggers | Timers
}

[Flags]
public enum OperationInvocationFlags
{
    None = 0,
    Run = 1,
    <PERSON><PERSON> = 2,
    <PERSON><PERSON> = 4,
    <PERSON><PERSON>ancel = 3,
    <PERSON>R<PERSON><PERSON> = 5,
    Can<PERSON>Rerun = 6,
    All = 7
}

public enum ResourceState
{
    NotReady = 0,
    Ready
}

public interface IEmOperation
{
    void Execute();
}

public interface IEmTrigger
{
    RoutineFlags TriggerRoutineType { get; }
    bool TryRoutine();
    bool TryRoutineStop();
}

public interface IEmTriggerTarget
{
    Task TriggerOperationFromTrigger();
}

public interface IEmTriggerDefinition
{
    IEmTrigger CreateTrigger(IEmTriggerTarget triggerTarget, string arg);
}

public abstract class EmeraldTriggerBase
{
    private IEmTriggerTarget TriggerTarget { get; }

    protected EmeraldTriggerBase(IEmTriggerTarget triggerTarget)
    {
        TriggerTarget = triggerTarget;
    }

    protected Task Invoke()
    {
        return TriggerTarget.TriggerOperationFromTrigger();
    }
}