using System.CommandLine;
using System.CommandLine.Builder;
using System.CommandLine.Parsing;

using Invictus.Emerald.Shared;
using Invictus.Nomenklatura.Logg;

using Microsoft.Extensions.Hosting;

namespace Invictus.Emerald;

public class EmeraldConsole
{
    private readonly ILogger _logger = InvLog.Logger<EmeraldConsole>();

    private readonly IServiceProvider _serviceProvider;
    private readonly IEmeraldCoreWorker _core;
    private readonly EmeraldServiceLocator _serviceLocator;
    private readonly IConsole _console;
    private readonly IHostApplicationLifetime _hostApplicationLifetime;

    private readonly RootCommand _rootCommand;
    private readonly Parser _commandLineParser;
    
    private TaskCompletionSource _loadedTcs = new();
    private List<string> _assembliesToWait = new();

    public EmeraldConsole(IServiceProvider serviceProvider, IConsole console, IHostApplicationLifetime hostApplicationLifetime, IEmeraldCoreWorker core, EmeraldShared emeraldShared)
    {
        _serviceProvider = serviceProvider;
        _core = core;
        _serviceLocator = emeraldShared.ServiceLocator;
        _console = console;
        _hostApplicationLifetime = hostApplicationLifetime;

        // Load
        // load <assembly>
        var assemblyNameArgument = new Argument<string>("Assembly", "");
        var loadAssemblyCommand = new Command("load", "Load assembly and execute em.script or Emerald.json")
        {
            assemblyNameArgument
        };
        loadAssemblyCommand.SetHandler(this.LoadAssembly, assemblyNameArgument);

        // Features
        // fea new <name> <worker>
        var featureNameArgument = new Argument<string>("FeatureName", "Name of the feature to create");

        var newFeatureCommand = new Command("new", "Create a new feature");
        newFeatureCommand.AddArgument(featureNameArgument);
        newFeatureCommand.SetHandler(this.NewFeature, featureNameArgument);

        var featureCommand = new Command("fea", "New feature")
        {
            newFeatureCommand
        };

        // Commands
        // op new <fea> <name> <methodName>
        var operationNameArgument = new Argument<string>("OperationName", "");
        var newOperationCommand = new Command("new", "New operation");
        newOperationCommand.AddArgument(featureNameArgument);
        newOperationCommand.AddArgument(operationNameArgument);
        newOperationCommand.SetHandler(this.NewOperation, featureNameArgument, operationNameArgument);
        
        // op routine <fea> <name> <routine flags>
        var routineOperationCommand = new Command("routine", "Begin a routine");
        var routineFlagsArguments = new Argument<string>("RoutineFlags", "");
        routineOperationCommand.AddArgument(featureNameArgument);
        routineOperationCommand.AddArgument(operationNameArgument);
        routineOperationCommand.AddArgument(routineFlagsArguments);
        routineOperationCommand.SetHandler(this.OperationRoutine, featureNameArgument, operationNameArgument, routineFlagsArguments);
        
        // op run <fea> <name> <run flags>
        var runOperationCommand = new Command("run", "Start operation");
        var runOperationFlagsArgument = new Argument<string>("RunOperationFlags", () => "Run", "");
        runOperationCommand.AddArgument(featureNameArgument);
        runOperationCommand.AddArgument(operationNameArgument);
        runOperationCommand.AddArgument(runOperationFlagsArgument);
        runOperationCommand.SetHandler(this.OperationStart, featureNameArgument, operationNameArgument, runOperationFlagsArgument);
        
        // op unhalt <fea> <name>
        var unhaltOperationCommand = new Command("unhalt", "Start operation");
        unhaltOperationCommand.AddArgument(featureNameArgument);
        unhaltOperationCommand.AddArgument(operationNameArgument);
        unhaltOperationCommand.SetHandler(this.OperationUnhalt, featureNameArgument, operationNameArgument);
        
        // op res <fea> <name> in <resname>
        // op res <fea> <name> out <resname>
        var setOperationCommand = new Command("res", "");
        var setOperationSpecifierArgument = new Argument<string>("ResSpecifier", "");
        var resourceNameArgument = new Argument<string>("ResourceName", "");
        setOperationCommand.AddArgument(featureNameArgument);
        setOperationCommand.AddArgument(operationNameArgument);
        setOperationCommand.AddArgument(setOperationSpecifierArgument);
        setOperationCommand.AddArgument(resourceNameArgument);
        setOperationCommand.SetHandler(this.OperationSetRes, featureNameArgument, operationNameArgument, setOperationSpecifierArgument, resourceNameArgument);
        
        var operationCommand = new Command("op", "Manage features")
        {
            newOperationCommand,
            routineOperationCommand,
            runOperationCommand,
            unhaltOperationCommand,
            setOperationCommand
        };
        
        // Resources
        // res new <fea> <name> <classname>
        
        var newResourceCommand = new Command("new", "New resource");
        newResourceCommand.AddArgument(featureNameArgument);
        newResourceCommand.AddArgument(resourceNameArgument);
        newResourceCommand.SetHandler(this.NewResource, featureNameArgument, resourceNameArgument);
        
        var resourceCommand = new Command("res", "Manage features")
        {
            newResourceCommand
        };
        
        // Trigger definitions
        // trigdef new <name> <classname>
        var triggerDefinitionNameArgument = new Argument<string>("TriggerDefinitionName", "");
        var newTriggerDefinitionCommand = new Command("new", "New triggerDefinition");
        newTriggerDefinitionCommand.AddArgument(triggerDefinitionNameArgument);
        newTriggerDefinitionCommand.SetHandler(this.NewTriggerDefinition, triggerDefinitionNameArgument);
        
        var triggerDefinitionCommand = new Command("trigdef", "Manage features")
        {
            newTriggerDefinitionCommand
        };
        
        // Triggers
        // trig add <featurename> <opname> <trigname>
        var addTriggerCommand = new Command("new", "Add a new trigger");
        var triggerArgument = new Argument<string>("TriggerArgument", "");
        addTriggerCommand.AddArgument(featureNameArgument);
        addTriggerCommand.AddArgument(operationNameArgument);
        addTriggerCommand.AddArgument(triggerDefinitionNameArgument);
        addTriggerCommand.AddArgument(triggerArgument);
        addTriggerCommand.SetHandler(this.AddTrigger, featureNameArgument, operationNameArgument, triggerDefinitionNameArgument, triggerArgument);

        var triggerCommand = new Command("trig", "Manage features")
        {
            addTriggerCommand
        };
        
        var runLoadedCommand = new Command("run_loaded", "");
        runLoadedCommand.SetHandler(this.RunLoaded);

        var finalizeLoadCommand = new Command("fin_load", "")
        {
            assemblyNameArgument
        };
        finalizeLoadCommand.SetHandler(this.FinalizeLoad, assemblyNameArgument);
        
        var finalizeLoadLoopbackCommand = new Command("fin_load_loopback", "")
        {
            assemblyNameArgument
        };
        finalizeLoadLoopbackCommand.SetHandler(this.FinalizeLoadLoopback, assemblyNameArgument);
        
        _rootCommand = new RootCommand("em")
        {
            loadAssemblyCommand,
            runLoadedCommand,
            featureCommand,
            operationCommand,
            resourceCommand,
            triggerDefinitionCommand,
            triggerCommand,
            finalizeLoadCommand,
            finalizeLoadLoopbackCommand
        };

        _commandLineParser = new CommandLineBuilder(_rootCommand)
            .UseVersionOption()
            .UseHelp()
            .UseEnvironmentVariableDirective()
            .UseParseDirective()
            .UseSuggestDirective()
            // .RegisterWithDotnetSuggest()
            // .UseTypoCorrections()
            .UseParseErrorReporting()
            // .UseExceptionHandler()
            .CancelOnProcessTermination()
            .Build();
    }
    
    private void FinalizeLoad(string assemblyName)
    {
        _core.Run(w => w.FinalizeLoadAssembly(assemblyName));
    }

    private void FinalizeLoadLoopback(string assemblyName)
    {
        _assembliesToWait.Remove(assemblyName);
        
        if (_assembliesToWait.Count == 0)
            _loadedTcs.SetResult();
    }
    
    public void JoinLoad()
    {
        _loadedTcs.Task.Wait(_hostApplicationLifetime.ApplicationStopping);
        
        _loadedTcs = null;
        _assembliesToWait = null;
    }
    
    private void RunLoaded()
    {
        _core.Run(w => w.RunLoaded());
    }

    public void CommandProgram(string input)
    {
        this.Input(input, false);
    }

    public void CommandUser(string input)
    {
        try
        {
            this.Input(input, true);
        }
        catch (Exception exc)
        {
            _logger.Error(exc, "Command exception");
        }
    }

    private void Input(string input, bool user)
    {
        if (string.IsNullOrWhiteSpace(input))
            return;
        if (input.TrimStart().StartsWith("//"))
            return;

        if (user)
        {
            _logger.Information("USER: " + input);
        } else
        {
            _logger.Information("PROGRAM: " + input);
        }

        ParseResult parseResult = _commandLineParser.Parse(input);

        if (!user && parseResult.Errors.Any())
        {
            throw new Exception($"{input} is wrong! \r\n {parseResult.Errors[0].Message}");
        }

        parseResult.Invoke(_console);
    }

    public void Loopback(string text)
    {
        _console.Write(text);
    }

    private void LoadAssembly(string assemblyFileName)
    {
        _assembliesToWait.Add(assemblyFileName);
        
        _core.Run(w => w.LoadAssembly(assemblyFileName));
    }
    
    private void OperationSetRes(string featureName, string operationName, string specifier, string resourceName)
    {
        switch (specifier)
        {
            case "out":
                _core.Run(w => w.AddOperationOutput(featureName, operationName, resourceName));
                break;
            case "in":
                _core.Run(w => w.AddOperationInput(featureName, operationName, resourceName));
                break;
            default:
                throw new EmeraldException($"Unknown specifier {specifier}");
        }
    }
    
    private void OperationUnhalt(string featureName, string operationName)
    {
        _core.Run(w => w.UnhaltOperation(featureName, operationName));
    }
    
    private void OperationStart(string featureName, string operationName, string operationRunFlags)
    {
        var flags = this.ParseFlags<OperationInvocationFlags>(operationRunFlags);
        _core.Run(w => w.RunOperation(featureName, operationName, flags));
    }
    
    private void OperationRoutine(string featureName, string operationName, string newRoutineTypeFlags)
    {
        var flags = this.ParseFlags<RoutineFlags>(newRoutineTypeFlags);
        _core.Run(w => w.SetRoutineFlags(featureName, operationName, flags));
    }

    private void AddTrigger(string featureName, string operationName, string triggerName, string triggerArgument)
    {
        _core.Run(w => w.AddTrigger(featureName, operationName, triggerName, triggerArgument));
    }

    private void NewTriggerDefinition(string triggerName)
    {
        _core.Run(w => w.AddTriggerDefinition(triggerName));
    }

    private void NewResource(string featureName, string resourceName)
    {
        _core.Run(w => w.AddResource(featureName, resourceName));
    }

    private void NewOperation(string featureName, string operationName)
    {
        _core.Run(w => w.AddOperation(featureName, operationName));
    }

    private void NewFeature(string featureName)
    {
        _core.Run(w => w.AddFeature(featureName));
    }
    
    private T ParseFlags<T>(string text)
    {
        if (int.TryParse(text, out int number))
            return (T)Enum.ToObject(typeof(T), number);
        
        int flags = 0;

        foreach (Enum e in text.Split("&").Select(u => Enum.Parse(typeof(T), u, true)))
        {
            flags |= (int)(object)e;
        }
        
        return (T)(object)flags;
    }

}

