using Invictus.Nomenklatura.Exceptions;

namespace Invictus.Emerald;

public readonly record struct ExceptionArbiterVerdict(ExceptionArbiterVerdictEnum Enum, bool Log);

public enum ExceptionArbiterVerdictEnum
{
    None,
    Retry,
    BusinessAsUsual,
    HaltOperation
}

public class ExceptionArbiter
{
    private readonly bool _operationIsRepeatable;
    private short _failuresInARow;
    private bool _justWasException;

    public ExceptionArbiter(bool operationIsRepeatable)
    {
        if (!operationIsRepeatable)
            throw new NotImplementedException();
        _operationIsRepeatable = operationIsRepeatable;
    }

    public void Finally()
    {
        if (_justWasException)
        {
            _justWasException = false;
            return;
        }
        
        _failuresInARow = 0;
    }
    
    public ExceptionArbiterVerdict Exception(Exception exc)
    {
        _justWasException = true;
        
        bool log = !ExceptionUtil.IsPureOrNestedCancellation(exc);
        
        if (++_failuresInARow > 2)
        {
            return new ExceptionArbiterVerdict(ExceptionArbiterVerdictEnum.HaltOperation, log);
        }
        
        return new ExceptionArbiterVerdict(ExceptionArbiterVerdictEnum.BusinessAsUsual, log);
    }
}