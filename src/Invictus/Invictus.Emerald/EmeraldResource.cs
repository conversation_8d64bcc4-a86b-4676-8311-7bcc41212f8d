using Invictus.Emerald.Shared;

namespace Invictus.Emerald;

public class EmeraldResource
{
    public EmeraldFeature Feature { get; }
    public string Name { get; }
    
    public ResourceState State { get; private set; }
    
    public object ResourceInstance { get; }

    public event Action OnReady;

    public EmeraldResource(EmeraldFeature feature, string name, object instance)
    {
        Feature = feature;
        Name = name;
        ResourceInstance = instance;
    }

    public void SubscribeToReady(Action action)
    {
        if (State == ResourceState.Ready)
            throw new EmeraldException($"Resource {Name} is already ready.");

        this.OnReady += action;
    }

    public void UnsubscribeFromReady(Action action)
    {
        this.OnReady -= action;
    }

    public void SetReadyState()
    {
        State = ResourceState.Ready;
        
        this.OnReady?.Invoke();
    }

    public void SetNotReadyState()
    {
        State = ResourceState.NotReady;
    }
}

class WaitForResource
{
    private readonly ILogger _logger;
    private readonly EmeraldOperation _responsibleOperation;
    private readonly EmeraldResource _resource;
    private readonly TaskCompletionSource<object> _tcs = new ();

    public WaitForResource(ILogger caller, EmeraldOperation responsibleOperation, EmeraldResource resource)
    {
        _logger = caller;
        _responsibleOperation = responsibleOperation;
        _resource = resource;
    }

    public async Task<object> Wait()
    {
        try
        {
            _resource.SubscribeToReady(this.ResourceOnReady);
            
            switch (_responsibleOperation.CurrentState)
            {
                case EmeraldOperationState.Halted:
                    throw new EmeraldException($"Operation {_responsibleOperation.Name} which is responsible for {_resource.Feature.Name}::{_resource.Name} is in Halted state.");
                case EmeraldOperationState.Executing:
                case EmeraldOperationState.Preparation:
                    _logger.Debug($"No need to trigger {_responsibleOperation.Name} to resolve {_resource.Feature.Name}::{_resource.Name}.");
                    break;
                case EmeraldOperationState.Idle:
                    _logger.Debug($"Triggering {_responsibleOperation.Name} to resolve {_resource.Feature.Name}::{_resource.Name}.");
                
                    _responsibleOperation.OperationCompleted += this.ResponsibleOperationOnOperationCompleted;
                    
                    bool triggered = _responsibleOperation.TriggerOperation(OperationInvocationFlags.Run);

                    if (!triggered)
                        throw new EmeraldException($"Failed to trigger {_responsibleOperation.Name} to resolve {_resource.Feature.Name}::{_resource.Name}.");
                    
                    break;
            }

            await _tcs.Task;

            return _tcs.Task.Result;
        }
        finally
        {
            _responsibleOperation.OperationCompleted -= this.ResponsibleOperationOnOperationCompleted;
        }
    }

    private void ResourceOnReady()
    {
        _resource.UnsubscribeFromReady(this.ResourceOnReady);
        
        _tcs.SetResult(_resource.ResourceInstance);
    }

    private void ResponsibleOperationOnOperationCompleted()
    {
        if (!_tcs.Task.IsCompleted)
            throw new EmeraldException($"Waiting for resource {_resource.Feature.Name}::{_resource.Name} as operation responsible did not update said resource.");
    }
}


