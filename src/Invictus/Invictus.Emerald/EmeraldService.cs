using Invictus.Emerald.Shared;

namespace Invictus.Emerald;

public class EmeraldService
{
    private readonly IEmeraldCoreWorker _coreWorker;

    public EmeraldService(IEmeraldCoreWorker coreWorker)
    {
        _coreWorker = coreWorker;
    }

    public void RunOperation(string commandName, OperationInvocationFlags flags)
    {
        (string featureName, commandName) = EmeraldShared.SplitFullName(commandName);
        
        _coreWorker.RunOperation(featureName, commandName, flags);
    }
}