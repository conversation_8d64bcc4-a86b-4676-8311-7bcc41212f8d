using System.Reflection;

using Invictus.Emerald.Shared;
using Invictus.Nomenklatura.Misc;

using Microsoft.Extensions.DependencyInjection;

using OneOf;

namespace Invictus.Emerald;

public class EmeraldServiceLocator
{
    private readonly IServiceProvider _serviceProvider;
    private readonly Dictionary<Type, object> _ownServiceCollection = new ();
    
    public EmeraldServiceLocator(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }
    
    public T GetService<T>()
    {
        return (T)this.GetService(typeof(T));
    }

    public object GetService(Type type)
    {
        object res = _serviceProvider.GetService(type);

        if (res == null)
            _ownServiceCollection.TryGetValue(type, out res);
        
        if (res == null)
        {
            res = ActivatorUtilities.CreateInstance(_serviceProvider, type);
            
            _ownServiceCollection[type] = res;
        }

        return res;
    }
}

public class EmeraldAssemblyObjectsLocator
{
    private readonly Dictionary<string, Type> _featuresClasses = new();
    private readonly Dictionary<string, Type> _resourceClasses = new();
    private readonly Dictionary<string, Type> _triggerDefinitionClasses = new();
    private readonly Dictionary<(string, string), OneOf<MethodInfo, Type>> _operations = new();
    
    public void AddAssembly(Assembly assembly)
    {
        Type[] assemblyTypes = assembly.GetTypes();
        
        foreach (Type type in assemblyTypes)
        {
            var feature = type.GetCustomAttribute<EmeraldFeatureAttribute>();
            var triggerDefinition = type.GetCustomAttribute<EmeraldTriggerDefinitionAttribute>();
            var operation = type.GetCustomAttribute<EmeraldOperationAttribute>();

            if (operation != null)
            {
                if (operation.Name == null)
                    throw new EmeraldException("Operation as class should have Feature::Name");
                
                if (!EmeraldShared.IsProperFullName(operation.Name))
                    throw new EmeraldException("Operation as class should have Feature::Name");
                
                _operations.Add(EmeraldShared.SplitFullName(operation.Name), type);
            }
            
            if (feature != null)
            {
                _featuresClasses.Add(feature.Name, type);

                MethodInfo[] methodInfos = type
                    .GetMethods(BindingFlags.Public | BindingFlags.Instance)
                    .Concat(type.GetMethods(BindingFlags.NonPublic | BindingFlags.Instance))
                    .ToArray();

                foreach (MethodInfo methodInfo in methodInfos)
                {
                    operation = methodInfo.GetCustomAttribute<EmeraldOperationAttribute>();
                    
                    if (operation == null)
                        continue;

                    string name = operation.Name ?? methodInfo.Name;
                    
                    _operations.Add((feature.Name, name), methodInfo);
                }
            }
            
            if (triggerDefinition != null)
            {
                _triggerDefinitionClasses.Add(triggerDefinition.Name, type);
            }
            
        }

        foreach (Type type in assemblyTypes)
        {
            var resource = type.GetCustomAttribute<EmeraldResourceAttribute>();
            
            if (resource != null)
            {
                string name = resource.Name;
                
                if (!EmeraldShared.IsProperFullName(resource.Name) && type.IsNested)
                {
                    Type featureType = Enumerable2.FromHierarchy(type, t => t.DeclaringType)
                        .Where(t => _featuresClasses.ContainsValue(t))
                        .SingleOrDefault2<Type, EmeraldException>($"Multiple Nested features for resource {resource.Name}");

                    if (featureType != null)
                    {
                        string featureName = _featuresClasses.Single(kv => kv.Value == featureType).Key;

                        name = featureName + EmeraldShared.NAME_SPLITTER + name;
                    }
                }
                
                _resourceClasses.Add(name, type);
            }
        }
    }

    public Type GetFeatureType(string name)
    {
        return _featuresClasses[name];
    }
    
    public Type GetResourceType(string name)
    {
        return _resourceClasses[name];
    }
    
    public Type GetTriggerDefinitionType(string name)
    {
        return _triggerDefinitionClasses[name];
    }

    public OneOf<MethodInfo, Type> GetOperation(string featureName, string name)
    {
        return _operations[(featureName, name)];
    }
}