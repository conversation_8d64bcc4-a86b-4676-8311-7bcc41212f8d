using System.Linq.Expressions;
using System.Reflection;

using Invictus.Emerald.Shared;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;
using Invictus.Nomenklatura.Workers;

using OneOf;

using Serilog.Context;

namespace Invictus.Emerald;

public enum EmeraldOperationState
{
    Idle,
    Preparation,
    Executing,
    Halted
}

public class EmeraldOperation
{
    private readonly ILogger _logger;
    
    private readonly EmeraldShared _shared;
    private readonly ExceptionArbiter _exceptionArbiter = new(true);
    
    public EmeraldFeature Feature { get; }
    public string Name { get; }

    private readonly string _logName;
    
    public RoutineFlags CurrentRoutineFlags { get; private set; }

    public event Action OperationCompleted;

    // Executing
    private Task _currentExecutingTask;
    private CancellationTokenSource _runningCts;

    private EmeraldOperationState _currentState;

    public EmeraldOperationState CurrentState => _currentState;
    
    private readonly List<IEmTrigger> _triggers = new();
    private readonly List<EmeraldResource> _inputResources = new();
    private readonly List<EmeraldResource> _outputResources = new();

    public IReadOnlyCollection<EmeraldResource> InputResources => _inputResources.AsReadOnly();
    public IReadOnlyCollection<EmeraldResource> OutputResources => _outputResources.AsReadOnly();
    
    private readonly IOperationCaller _operationCaller;
    
    public bool ReRunWasScheduled { get; private set; }
    
    public EmeraldOperation(EmeraldShared shared, EmeraldFeature feature, string name, OneOf<MethodInfo, Type> methodInfoOrType)
    {
        _shared = shared;
        
        Feature = feature;
        Name = name;

        _logName = feature.Name + "::" + name + "$";

        _operationCaller = methodInfoOrType.Match(
            IOperationCaller (methodInfo) => {
                if (methodInfo.ReturnType != typeof(void) && methodInfo.ReturnType != typeof(Task))
                    throw new EmeraldException($"Method {methodInfo.Name}: only Task and void return types are supported");
                
                var methodCallFactory = shared.ServiceLocator.GetService<IServiceFactory<IMethodCallFactory>>();

                if (feature.IsWorkerClass)
                    return new WorkerMethodOperationCaller(
                        this,
                        methodCallFactory,
                        methodInfo,
                        Feature.WorkerType
                    );

                object instance = shared.ServiceLocator.GetService(methodInfo.DeclaringType);

                return new DefaultOperationCaller(
                    this,
                    methodCallFactory,
                    methodInfo,
                    instance
                );
            },
            IOperationCaller (operationType) => {
                if (!feature.IsWorkerClass)
                    throw new EmeraldException($"Operation {feature.Name}::{name} - operation cannot be a class while feature is not {nameof(IWorker)}");

                var constructorCallFactory = shared.ServiceLocator.GetService<IServiceFactory<IConstructorCallFactory>>();
                
                return new WorkerOperationClassCaller(this, constructorCallFactory, Feature.WorkerType, operationType);
            }
        );
        
        _logger = InvLog.Logger<EmeraldOperation>().ForContext(EmeraldSignal.OPERATION_LOG_PROPERTY, _logName);
    }

    public void AddOutputResource(EmeraldResource resource)
    {
        _outputResources.Add(resource);
    }
    
    public void AddInputResource(EmeraldResource resource)
    {
        _inputResources.Add(resource);
    }

    public void Unhalt()
    {
        if (_currentState == EmeraldOperationState.Halted)
        {
            _logger.Information("Halt lifted");
            _currentState = EmeraldOperationState.Idle;
            return;
        }
        
        _logger.Debug("Not Halted currently");
    }

    public void AddTrigger(EmeraldTriggerDefinition triggerDefinition, string triggerArgument)
    {
        IEmTrigger trigger = triggerDefinition.CreateTrigger(_shared.Invoke, this, OperationInvocationFlags.Run, triggerArgument);
        
        _triggers.Add(trigger);
    }
    
    public bool TriggerOperation(OperationInvocationFlags flags)
    {
        _logger.Verbose($"Attempt to trigger {flags}");
        
        if ((flags & OperationInvocationFlags.Cancel) == OperationInvocationFlags.Cancel)
        {
            this.Cancel();
        }

        bool run = (flags & OperationInvocationFlags.Run) == OperationInvocationFlags.Run;
        bool rerun = (flags & OperationInvocationFlags.Rerun) == OperationInvocationFlags.Rerun;
        
        if (rerun && !run)
        {
            throw new EmeraldException("Why rerun and not run");
        }

        if (run)
        {
            bool res = this.Run();
            
            if (!res && rerun)
                this.ScheduleRerun();

            return res;
        }

        return false;
    }

    private bool Run()
    {
        switch (_currentState)
        {
            case EmeraldOperationState.Preparation:
                _logger.Debug("Already preparing to run.");
                return false;
            
            case EmeraldOperationState.Executing:
                _logger.Debug("Already running.");
                return false;
            
            case EmeraldOperationState.Halted:
                _logger.Debug("Cannot run task due to Halted state");
                
                throw new Exception("Operation is Halted and cannot run until intervention.");
        }
        
        _logger.Information("Starting");

        _runningCts = new CancellationTokenSource();
        
        var resourceArgs = new List<object>
        {
            _runningCts.Token
        };

        _currentState = EmeraldOperationState.Preparation;
        
        Task task = _shared.ExecWorker.Run(w => w.TaskRunMethod(this, resourceArgs));
        
        _currentExecutingTask = task;
        
        task.ContinueWithShortThread(this.OnExecCompletedForeignThreadContinuation);
        
        return true;
    }
    
    public async Task TaskRunMethodOnExecThread(List<object> resourceArgs)
    {
        using (LogContext.PushProperty(EmeraldSignal.OPERATION_LOG_PROPERTY, _logName))
        {
            try
            {
                await await _operationCaller.Exec(this, resourceArgs);
            }
            catch (Exception exc)
            {
                ExceptionArbiterVerdict verdict = _exceptionArbiter.Exception(exc);

                if (verdict.Log)
                    _logger.Error(exc, "Operation exception");

                switch (verdict.Enum)
                {
                    case ExceptionArbiterVerdictEnum.Retry:
                    case ExceptionArbiterVerdictEnum.BusinessAsUsual: 
                        throw;
                    case ExceptionArbiterVerdictEnum.HaltOperation:
                        _logger.Error("Operation Halted");
                        _currentState = EmeraldOperationState.Halted;
                        throw;
                }
            }
            finally
            {
                _exceptionArbiter.Finally();
            }
        }
    }

    private void OnExecCompletedForeignThreadContinuation(Task task)
    {
        _shared.Invoke.Invoke(() => this.OnCompleted(task));
    }

    private void OnCompleted(Task task)
    {
        switch (_currentState)
        {
            case EmeraldOperationState.Idle:
                throw new JustNoWayException();
            case EmeraldOperationState.Halted:
                this.OperationCompleted?.Invoke();
                return;
            case EmeraldOperationState.Preparation:
                throw new JustNoWayException();
            case EmeraldOperationState.Executing:
                _currentState = EmeraldOperationState.Idle;
                break;
        }
        
        foreach (EmeraldResource resource in _outputResources)
        {
            resource.SetReadyState();
        }

        _runningCts = null;
        _currentExecutingTask = null;
        
        this.OperationCompleted?.Invoke();

        if (ReRunWasScheduled)
        {
            _logger.Debug("Completed. Rerun was scheduled.");
            
            ReRunWasScheduled = false;

            this.Run();
        } else
        {
            _logger.Debug("Completed");
        }
    }

    public void Routine(RoutineFlags routineFlags)
    {
        _logger.Debug($"Routine set {routineFlags}");
        
        RoutineFlags routineOn = routineFlags ^ CurrentRoutineFlags;
        RoutineFlags routineOff = ~(CurrentRoutineFlags ^ routineFlags);

        IEmTrigger[] triggersOn = _triggers
            .Where(t => t.TriggerRoutineType != RoutineFlags.None && (routineOn & t.TriggerRoutineType) == t.TriggerRoutineType)
            .ToArray();

        IEmTrigger[] triggersOff = _triggers
            .Where(t => t.TriggerRoutineType != RoutineFlags.None && (routineOff & t.TriggerRoutineType) == t.TriggerRoutineType)
            .ToArray();

        if (triggersOn.Intersect(triggersOff).Count() > 1)
            throw new EmeraldException("triggersOn.Intersect(triggersOff).Count() > 1");

        foreach (IEmTrigger coreTrigger in triggersOn)
        {
            if (!coreTrigger.TryRoutine())
                throw new EmeraldException($"ICoreTrigger {coreTrigger} !TryRoutine()");
        }

        foreach (IEmTrigger coreTrigger in triggersOff)
        {
            if (!coreTrigger.TryRoutineStop())
                throw new EmeraldException($"ICoreTrigger {coreTrigger} !TryRoutine()");
        }

        CurrentRoutineFlags = routineFlags;
    }
    
    public bool Cancel()
    {
        if (_runningCts == null || _runningCts.IsCancellationRequested)
        {
            _logger.Debug("Already canceling");

            return false;
        }
        
        _logger.Debug("Canceled");
        _runningCts.CancelAfter(EmeraldShared.CANCEL_DELAY_MS);
        return true;
    }
        
    public Task ScheduleRerun()
    {
        _logger.Debug("Rerun scheduled");
        
        ReRunWasScheduled = true;

        return Task.CompletedTask;
    }
    
    private interface IOperationCaller
    {
        Task<Task> Exec(EmeraldOperation operation, List<object> resourceArgs);
    }
    
    private abstract class OperationCallerBase
    {
        private readonly EmeraldOperation _operation;

        protected OperationCallerBase(EmeraldOperation operation)
        {
            _operation = operation;
        }

        protected async Task GatherResources(List<object> resourceArgs)
        {
            List<Task<object>> waitForResolvation = new();
            
            foreach (EmeraldResource emeraldResource in _operation._inputResources)
            {
                if (emeraldResource.State == ResourceState.Ready)
                {
                    // Fast path
                    resourceArgs.Add(emeraldResource.ResourceInstance);

                    continue;
                }

                waitForResolvation.Add(
                    _operation.Feature.ResolveResource(
                        _operation._logger,
                        emeraldResource.Feature.Name + EmeraldShared.NAME_SPLITTER + emeraldResource.Name
                    )
                );
            }

            foreach (Task<object> task in waitForResolvation)
            {
                await task;
                
                resourceArgs.Add(task.Result);
            }

            _operation._currentState = EmeraldOperationState.Executing;
        }
    }
    
    private class DefaultOperationCaller : OperationCallerBase, IOperationCaller
    {
        private readonly object _targetTypeInstance;
        private readonly IMethodCallFactory _methodCallFactory;
        
        public DefaultOperationCaller(EmeraldOperation operation, IServiceFactory<IMethodCallFactory> methodCallFactory, MethodInfo methodInfo, object targetTypeInstance)
            : base(operation)
        {
            _targetTypeInstance = targetTypeInstance;
            _methodCallFactory = methodCallFactory.Create(Array.Empty<object>(), methodInfo);
        }

        public async Task<Task> Exec(EmeraldOperation operation, List<object> resourceArgs)
        {
            await this.GatherResources(resourceArgs);
            
            return (Task)_methodCallFactory.CallMethod(_targetTypeInstance, resourceArgs.ToArray());
        }
    }
    
    private class WorkerMethodOperationCaller : OperationCallerBase, IOperationCaller
    {
        private readonly Func<object[], Expression> _createWorkerExpression;
        private readonly IMethodCallFactory _methodCallFactory;
        private readonly MethodBase _methodInfo;
        
        public WorkerMethodOperationCaller(EmeraldOperation operation, IServiceFactory<IMethodCallFactory> methodCallFactory, MethodInfo methodInfo, Type workerType)
            : base(operation)
        {
            _methodInfo = methodInfo;
            _methodCallFactory = methodCallFactory.Create(Array.Empty<object>(), methodInfo);
            _createWorkerExpression = CreateGenericWorkerExpression(workerType, (MethodInfo)_methodInfo, _methodCallFactory.GetArgumentTypes());
        }

        public async Task<Task> Exec(EmeraldOperation operation, List<object> resourceArgs)
        {
            await this.GatherResources(resourceArgs);
            
            object[] arguments = _methodCallFactory.GetArgumentsForMethodCall(resourceArgs.ToArray());
            
            Expression expression = _createWorkerExpression(arguments);

            if (((MethodInfo)_methodInfo).ReturnType == typeof(Task))
                return (Task)operation.Feature.WorkerRunMethodCallTaskTask.CallWithReturn(expression, operation.Name);

            return (Task)operation.Feature.WorkerRunMethodCallTask.CallWithReturn(expression, operation.Name);
        }
        
        private static Func<object[], Expression> CreateGenericWorkerExpression(Type workerType, MethodInfo methodInfo, Type[] parameterTypes)
        {
            // Parameter for the outer lambda (arguments)
            ParameterExpression argumentsParam = Expression.Parameter(typeof(object[]), "args");
    
            // Parameter for the inner lambda (IWorker<T>)
            ParameterExpression workerParam = Expression.Parameter(workerType, "w");
    
            // Convert arguments to array index expressions
            Expression[] argumentExpressions = parameterTypes.Select(Expression (type, index) => 
                Expression.Convert(
                    Expression.ArrayIndex(
                        argumentsParam,
                        Expression.Constant(index)
                    ),
                    type
                )).ToArray();

            // Create method call expression
            MethodCallExpression methodCall = Expression.Call(
                workerParam,
                methodInfo,
                argumentExpressions
            );

            // Determine if the method returns void or has a return type
            bool isVoid = methodInfo.ReturnType == typeof(void);
    
            // Create delegate type based on return type
            Type delegateType;
            if (isVoid)
            {
                delegateType = typeof(Action<>).MakeGenericType(workerType);
            }
            else
            {
                delegateType = typeof(Func<,>).MakeGenericType(workerType, methodInfo.ReturnType);
            }

            // Create the inner lambda as an Expression (not wrapped in another lambda)
            UnaryExpression innerLambda = Expression.Quote(
                Expression.Lambda(delegateType, methodCall, workerParam)
            );
        
            // Create the outer lambda that returns the Expression
            Expression<Func<object[], Expression>> outerLambda = Expression.Lambda<Func<object[], Expression>>(
                innerLambda,
                argumentsParam
            );

            return outerLambda.Compile();
        }
    }
    
    private class WorkerOperationClassCaller : OperationCallerBase, IOperationCaller
    {
        private readonly Type _operationType;
        private readonly Func<object[], Expression> _createWorkerExpression;
        private readonly IConstructorCallFactory _constructorCallFactory;

        public WorkerOperationClassCaller(EmeraldOperation operation, IServiceFactory<IConstructorCallFactory> constructorCallFactory, Type workerType, Type operationType)
            : base(operation)
        {
            _operationType = operationType;
            _createWorkerExpression = CreateGenericWorkerExpression(workerType, typeof(Action));

            ConstructorInfo constructor = operationType.GetConstructors().SingleWith($"{operationType.FullName} constructors");
            
            _constructorCallFactory = constructorCallFactory.Create(Array.Empty<object>(), constructor);
        }

        public async Task<Task> Exec(EmeraldOperation operation, List<object> resourceArgs)
        {
            await this.GatherResources(resourceArgs);
            
            var operationInstance = (IEmOperation)_constructorCallFactory.CreateInstance(_operationType, resourceArgs.ToArray());
            
            Expression expression = _createWorkerExpression(new object[] { (Action)operationInstance.Execute });
            
            //if (((MethodInfo)_methodInfo).ReturnType == typeof(Task))
            //    return (Task)operation.Feature.WorkerRunMethodCallTaskTask.CallWithReturn(expression, operation.Name);
            //else
            
            return (Task)operation.Feature.WorkerRunMethodCallTask.CallWithReturn(expression, operation.Name);
        }
        
        private static Func<object[], Expression> CreateGenericWorkerExpression(Type workerType, Type innerDelegateType)
        {
            // Parameter for the outer lambda (arguments)
            ParameterExpression argumentsParam = Expression.Parameter(typeof(object[]), "args");

            // Parameter for the inner lambda (IWorker<T>)
            ParameterExpression workerParam = Expression.Parameter(workerType, "w");

            // Get the delegate from the arguments array
            UnaryExpression delegateExpression = Expression.Convert(
                Expression.ArrayIndex(
                    argumentsParam,
                    Expression.Constant(0)
                ),
                innerDelegateType
            );

            // Create delegate invocation expression
            Expression invokeExpression = Expression.Invoke(
                delegateExpression
            );

            // Create delegate type - we'll determine void/Task at runtime
            Type delegateType = typeof(Action<>).MakeGenericType(workerType);

            // Create the inner lambda as an Expression (not wrapped in another lambda)
            UnaryExpression innerLambda = Expression.Quote(
                Expression.Lambda(delegateType, invokeExpression, workerParam)
            );
    
            // Create the outer lambda that returns the Expression
            Expression<Func<object[], Expression>> outerLambda = Expression.Lambda<Func<object[], Expression>>(
                innerLambda,
                argumentsParam
            );

            return outerLambda.Compile();
        }
    }
}