using System.Linq.Expressions;
using System.Reflection;

using Invictus.Emerald.Shared;
using Invictus.Nomenklatura.Misc;
using Invictus.Nomenklatura.Workers;

using OneOf;

namespace Invictus.Emerald;

public class EmeraldFeature
{
    private readonly EmeraldShared _shared;

    public object FeatureInstance { get; }
    public string Name { get; }
    
    private readonly List<EmeraldOperation> _operations = new();
    private readonly List<EmeraldResource> _resources = new();

    public IReadOnlyCollection<EmeraldOperation> Operations => _operations.AsReadOnly();
    public IReadOnlyCollection<EmeraldResource> Resources => _resources.AsReadOnly();

    public bool IsWorkerClass { get; }
    
    public Type WorkerGenericType { get; }
    public Type WorkerType { get; }
    
    public CompiledDelegateWithInstance WorkerRunMethodCallTask { get; }
    public CompiledDelegateWithInstance WorkerRunMethodCallTaskTask { get; }

    public EmeraldFeature(EmeraldShared shared, string featureName, object featureInstance)
    {
        _shared = shared;
        FeatureInstance = featureInstance;
        Name = featureName;

        Type type = featureInstance.GetType();
        Type workerGenericType = null;
        
        foreach (Type @interface in  type.GetAllInterfaces())
        {
            if (!@interface.IsGenericType)
                continue;
            if (@interface.GetGenericTypeDefinition() != typeof(IWorker<>))
                continue;

            workerGenericType = @interface;

            IsWorkerClass = true;
        }

        if (IsWorkerClass)
        {
            WorkerGenericType = workerGenericType;
            
            WorkerType = workerGenericType.GetGenericArguments()[0];
            
            Type actionType = typeof(Expression<>)
                .MakeGenericType(typeof(Action<>).MakeGenericType(WorkerType));

            MethodInfo workerRunMethod = workerGenericType.GetMethod("Run",
                BindingFlags.Instance | BindingFlags.Public,
                new[]
                {
                    actionType, typeof(string)
                }
            );

            WorkerRunMethodCallTask = CompiledDelegateWithInstance.Build(workerRunMethod, featureInstance);
            
            actionType = typeof(Expression<>)
                .MakeGenericType(typeof(Func<,>).MakeGenericType(WorkerType, typeof(Task)));
            
            workerRunMethod = workerGenericType.GetMethod("Run",
                BindingFlags.Instance | BindingFlags.Public,
                new[]
                {
                    actionType, typeof(string)
                }
            );
            
            WorkerRunMethodCallTaskTask = CompiledDelegateWithInstance.Build(workerRunMethod, featureInstance);
        }
    }

    public void AddOperation(string name, OneOf<MethodInfo, Type> methodInfoOrType)
    {
        _operations.Add(new EmeraldOperation(_shared, this, name, methodInfoOrType));
    }
    
    public void AddResource(string name)
    {
        Type resourceType = _shared.AssemblyObjectsLocator.GetResourceType(Name + EmeraldShared.NAME_SPLITTER + name);
        
        object resourceInstance = _shared.ServiceLocator.GetService(resourceType);

        var emeraldResource = new EmeraldResource(this, name, resourceInstance);
        
        _resources.Add(emeraldResource);
    }

    public async Task<object> ResolveResource(ILogger caller, string name)
    {
        (string featureName, string resourceName) = EmeraldShared.SplitFullName(name);
        
        return await _shared.CoreWorker.Run(w => w.ResolveResource(caller, featureName, resourceName));
    }
    
    public EmeraldOperation FindOperation(string name) => _operations.FirstOrDefault(o => o.Name == name);
    
    public EmeraldResource FindResourceRequired(string name)
    {
        EmeraldResource res = _resources.FirstOrDefault(o => o.Name == name);
        if (res == null)
            throw new EmeraldException($"Resource {Name}::{name} was not found.");

        return res;
    }
}
