<Project Sdk="Microsoft.NET.Sdk.Razor">

    <PropertyGroup>
        <AddRazorSupportForMvc>true</AddRazorSupportForMvc>
        <!-- NuGet properties-->
        <Title>OrchardCore Invictus Jupiter module</Title>
        <Description>$(OCCMSDescription)

            Invictus Jupiter module.</Description>
        <PackageTags>$(PackageTags) OrchardCoreCMS</PackageTags>
        <TargetFramework>net8.0</TargetFramework>
    </PropertyGroup>

    <ItemGroup>
        <FrameworkReference Include="Microsoft.AspNetCore.App" />
    </ItemGroup>

    <ItemGroup>
        <Compile Include="..\..\void\GlobalUsings.cs">
            <Link>GlobalUsings.cs</Link>
        </Compile>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\Invictus\Invictus.Nomenklatura.Shared\Invictus.Nomenklatura.Shared.csproj" />
        <ProjectReference Include="..\..\..\..\OrchardCore\OrchardCore.Admin.Abstractions\OrchardCore.Admin.Abstractions.csproj" />
        <ProjectReference Include="..\..\..\..\OrchardCore\OrchardCore.Module.Targets\OrchardCore.Module.Targets.csproj" />
        <ProjectReference Include="..\..\..\..\OrchardCore\OrchardCore.Navigation.Core\OrchardCore.Navigation.Core.csproj" />
        <ProjectReference Include="..\..\..\..\OrchardCore\OrchardCore.ContentManagement.Abstractions\OrchardCore.ContentManagement.Abstractions.csproj" />
        <ProjectReference Include="..\..\..\..\OrchardCore\OrchardCore.ContentTypes.Abstractions\OrchardCore.ContentTypes.Abstractions.csproj" />
        <ProjectReference Include="..\..\..\..\OrchardCore\OrchardCore.DisplayManagement\OrchardCore.DisplayManagement.csproj" />
        <ProjectReference Include="..\..\..\..\OrchardCore\OrchardCore.ResourceManagement\OrchardCore.ResourceManagement.csproj" />
        
        <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="6.0.0" />
        
        <ProjectReference Include="..\..\..\OrchardCore\OrchardCore.Users.Abstractions\OrchardCore.Users.Abstractions.csproj" />
    </ItemGroup>

    <ItemGroup>
      <EmbeddedResource Update="wwwroot\Scripts\signalr.min.js">
        <CopyToPublishDirectory>Never</CopyToPublishDirectory>
      </EmbeddedResource>
      <EmbeddedResource Update="wwwroot\Scripts\terminal.js">
        <CopyToPublishDirectory>Never</CopyToPublishDirectory>
      </EmbeddedResource>
    </ItemGroup>

    <ItemGroup>
      <None Remove="Assets\js\signalr.min.js" />
      <None Remove="Assets\js\terminal.js" />
      <None Remove="Assets.json" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Models\" />
      <Folder Include="Services\" />
      <Folder Include="ViewModels\" />
    </ItemGroup>
</Project>
