using Invictus.Nomenklatura.Misc;

namespace Invictus.Nomenklatura.Exceptions;

public static class ExceptionUtil
{
    public static IEnumerable<Exception> GetAllInnerExceptionsAndThisOne(Exception exception)
    {
        yield return exception;

        if (exception is AggregateException aggregateException)
        {
            foreach (Exception innerException in aggregateException.InnerExceptions)
            {
                foreach (Exception exc2 in GetAllInnerExceptionsAndThisOne(innerException))
                    yield return exc2;
            }
        }

        if (exception.InnerException != null)
        {
            foreach (Exception exc2 in GetAllInnerExceptionsAndThisOne(exception.InnerException))
                yield return exc2;
        }
    }

    public static bool IsPureCancellation(Exception exception)
    {
        IEnumerable<Exception> allInnerExceptions = GetAllInnerExceptionsAndThisOne(exception).ToArray();

        if (allInnerExceptions.Any(e => e is TimeoutException))
            return false;

        if (exception is TaskCanceledException && (exception.InnerException == null || IsPureCancellation(exception.InnerException)))
            return true;

        if (exception is OperationCanceledException)
            return true;

        return allInnerExceptions.All(e =>
            (e is TaskCanceledException && (e.InnerException == null || IsPureCancellation(e.InnerException))) ||
            e is OperationCanceledException or AggregateException
        );
    }

    public static bool IsPureOrNestedCancellation(Exception exception)
    {
        if (exception is TaskCanceledException or OperationCanceledException)
            return true;

        if (exception.InnerException is TaskCanceledException or OperationCanceledException)
            return true;

        IEnumerable<Exception> allInnerExceptions = GetAllInnerExceptionsAndThisOne(exception);

        return allInnerExceptions.All(e => e is TaskCanceledException or OperationCanceledException or AggregateException);
    }
    
    public static bool IsRandomNetworkIOException(Exception exception)
    {
        if (exception is AggregateException aggregateException)
            return aggregateException.InnerExceptions.Any(IsRandomNetworkIOException);
            
        return exception is IOException ioException 
               &&
               ioException.Message.Contains(
                   "Unable to read data from the transport connection: The I/O operation has been aborted because of either a thread exit or an application request"
               );
    }

    public static bool HasTaskOrOperationCancelledException(Exception exception)
    {
        return exception.Any(e => e is TaskCanceledException or OperationCanceledException);
    }

    public static bool HasTimeoutExceptionOrIOException(Exception exception)
    {
        return exception.Any(e => e is IOException or TimeoutException);
    }

    public static IEnumerable<Exception> GetInnerExceptionsOfType(Exception exception, string typeLastOrFullName)
    {
        IEnumerable<Exception> allInnerExceptions = GetAllInnerExceptionsAndThisOne(exception);

        foreach (Exception exc in allInnerExceptions)
        {
            if (exc.GetType().FullName.EndsWith(typeLastOrFullName))
                yield return exc;
        }
    }

    public static string ExceptionToMessage(Exception exc)
    {
        var bdr = new StringBuilder();

        WriteException(bdr, exc, exc.StackTrace);

        int i = 0;

        while (exc.InnerException != null)
        {
            exc = exc.InnerException;

            WriteException(bdr, exc, null);
            bdr.AppendLine("-- [InnerException(" + (++i) + ") End]");
        }

        return bdr.ToString();
    }

    private static void WriteException(StringBuilder bdr, Exception exc, string exceptionStackTrace)
    {
        bdr.AppendLine("(Exception) ");

        if (exc == null)
        {
            bdr.AppendLine("(null)");

            return;
        }

        string name = exc.GetType().FullName;
        string message = exc.Message;
        string source = exc.Source;
        string sourceStr = source ?? "";

        bdr.AppendLine(name + " Message: " + message + " from " + sourceStr);
        bdr.AppendLine("Top Stack Trace: ");
        bdr.AppendLine(string.IsNullOrWhiteSpace(exceptionStackTrace) ? exc.StackTrace : exceptionStackTrace);

        if (exc is TaskCanceledException taskCancelledExc)
        {
            if (taskCancelledExc.Task == null)
            {
                bdr.AppendLine("TaskId: (null)");
            } else
            {
                bdr.AppendLine("TaskId: " + taskCancelledExc.Task.Id);
            }

            if (taskCancelledExc.InnerException != null)
            {
                bdr.AppendLine("--- TaskCanceledException Contents: ");
                WriteException(bdr, taskCancelledExc.InnerException, null);
            }
        }

        if (exc is AggregateException aggregateExc)
        {
            bdr.AppendLine("--- AggregateException Contents: ");

            foreach (Exception innerException in aggregateExc.Flatten().InnerExceptions)
            {
                WriteException(bdr, innerException, null);
            }
        }
    }
}