namespace Invictus.Nomenklatura.Exceptions;

public class TypeAbominationException : Exception
{
    public static TypeAbominationException Enum(Type enumType, object val)
    {
        return new TypeAbominationException(enumType, val);
    }

    public static TypeAbominationException String(string fieldName, string val)
    {
        return new TypeAbominationException(fieldName, val);
    }

    public static TypeAbominationException Variant(string fieldName, object val)
    {
        return new TypeAbominationException(fieldName, val.ToString());
    }
    
    private TypeAbominationException(Type enumType, object val)
        : base($"Invalid Enum value, EnumType={enumType.FullName}, EnumValue={val}")
    {
    }

    private TypeAbominationException(string fieldName, string val)
        : base($"Unexpected type of {fieldName}, got " + (val == null ? "(null)" : val.GetType().FullName))
    {
    }
}

public class JustNoWayException : Exception
{
    public JustNoWayException()
    {
    }

    public JustNoWayException(string message)
        : base(message)
    {
    }

    public JustNoWayException(string message, Exception inner)
        : base(message, inner)
    {
    }
}