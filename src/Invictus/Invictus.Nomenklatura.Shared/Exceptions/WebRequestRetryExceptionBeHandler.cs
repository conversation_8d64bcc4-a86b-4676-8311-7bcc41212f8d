using System.Net;
using System.Net.Sockets;

using Invictus.Nomenklatura.Web;

namespace Invictus.Nomenklatura.Exceptions;

public static class WebRequestRetryExceptionBeHandler
{
    public static RetryAdvice GetWebRequestRetryAdvice(ILogger logger, Exception aggregateOrNotException, string proxyUrlAndPortUsedByRequest = null)
    {
        if (ExceptionUtil.IsPureOrNestedCancellation(aggregateOrNotException))
            return RetryAdvice.ThrowFurther;

        HttpRequestException[] httpRequestExceptions =
            ExceptionUtil.GetInnerExceptionsOfType(aggregateOrNotException, typeof(HttpRequestException).FullName).Cast<HttpRequestException>().ToArray();

        Exception[] socksExceptions =
            ExceptionUtil.GetInnerExceptionsOfType(aggregateOrNotException, "SocksException").ToArray();

        SocketException[] socketExceptions =
            ExceptionUtil.GetInnerExceptionsOfType(aggregateOrNotException, typeof(SocketException).FullName).Cast<SocketException>().ToArray();

        TimeoutException[] timeoutExceptions =
            ExceptionUtil.GetInnerExceptionsOfType(aggregateOrNotException, typeof(TimeoutException).FullName).Cast<TimeoutException>().ToArray();

        IOException[] ioExceptions =
            ExceptionUtil.GetInnerExceptionsOfType(aggregateOrNotException, typeof(IOException).FullName).Cast<IOException>().ToArray();

        if (httpRequestExceptions.Length == 0 && socksExceptions.Length == 0 && socketExceptions.Length == 0 && timeoutExceptions.Length == 0 && ioExceptions.Length == 0)
        {
            logger.Error("Not found exceptions to handle.");

            return RetryAdvice.ThrowFurther;
        }

        if (socksExceptions.Length > 1)
        {
            logger.Warning("More than one SocksException in AggregateException. Handling only first one. All of these exceptions:");

            foreach (Exception exc in socksExceptions)
                logger.Warning(exc, "");
        }

        if (timeoutExceptions.Length > 1)
        {
            logger.Error("More that one TimeoutException in AggregateException. Handling only first one. All of these exceptions:");

            foreach (TimeoutException exc in timeoutExceptions)
                logger.Warning(exc, "");
        }

        if (ioExceptions.Length > 1)
        {
            logger.Error("More that one IOException in AggregateException. Handling only first one. All of these exceptions:");

            foreach (IOException exc in ioExceptions)
                logger.Warning(exc, "");
        }

        return HandleHttpRequestException(
            logger,
            aggregateOrNotException,
            httpRequestExceptions,
            socksExceptions.FirstOrDefault(),
            socketExceptions.FirstOrDefault(),
            timeoutExceptions.FirstOrDefault(),
            ioExceptions.FirstOrDefault(),
            proxyUrlAndPortUsedByRequest,
            true
        );
    }

    private static RetryAdvice HandleHttpRequestException(
        ILogger logger,
        Exception motherException,
        HttpRequestException[] httpExceptions,
        Exception socksException,
        SocketException socketException,
        TimeoutException timeoutException,
        IOException ioException,
        string proxyUrlAndPortUsedByRequest,
        bool warnWhenShitHappens
    )
    {
        Action<string> log = warnWhenShitHappens ? logger.Warning : logger.Debug;

        if (ioException != null)
        {
            const string H_MSG1 = "Unable to read data from the transport connection: An existing connection was forcibly closed by the remote host.";
            const string H_MSG2 = "Received an unexpected EOF or 0 bytes from the transport stream.";

            if (ioException.Message.Contains(H_MSG1))
            {
                log($"Exception contains \"{H_MSG1}\".");

                return RetryAdvice.Wait;
            }

            if (ioException.Message.Contains(H_MSG2))
            {
                log($"Exception contains \"{H_MSG2}\".");

                return RetryAdvice.Wait;
            }
        }

        if (socksException != null)
        {
            const string H_MSG1 = "Failed to authenticate with the SOCKS server.";

            // Proxy server temporarily bugged out.
            if (socksException.Message.Contains(H_MSG1))
            {
                log($"Exception contains \"{H_MSG1}\".");

                return RetryAdvice.Wait;
            }
        }

        if (httpExceptions != null)
        {
            foreach (HttpRequestException httpException in httpExceptions)
            {
                // IDK why this happens.
                const string H_MSG1 = "The response ended prematurely.";
                const string H_MSG2 = "The requested name is valid, but no data of the requested type was found.";
                const string H_MSG3 = "The request was aborted.";

                if (httpException.Message.Contains(H_MSG1) || (httpException.InnerException != null && httpException.InnerException.Message.Contains(H_MSG1)))
                {
                    log($"Exception contains \"{H_MSG1}\".");

                    return RetryAdvice.Wait;
                }

                if (httpException.Message.Contains(H_MSG2) || (httpException.InnerException != null && httpException.InnerException.Message.Contains(H_MSG2)))
                {
                    log($"Exception contains \"{H_MSG2}\".");

                    return RetryAdvice.WaitALot;
                }

                if (httpException.Message.Contains(H_MSG3) || (httpException.InnerException != null && httpException.InnerException.Message.Contains(H_MSG3)))
                {
                    log($"Exception contains \"{H_MSG3}\".");

                    return RetryAdvice.Wait;
                }

                if (httpException.StatusCode == HttpStatusCode.InternalServerError)
                    return RetryAdvice.WaitOnce;
            }
        }

        if (socketException != null)
        {
            const string H_MSG2 =
                "A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond";

            // IDK why this happens.
            if (socketException.Message.Contains(H_MSG2))
            {
                log("Timeout! " + ExceptionUtil.ExceptionToMessage(socketException));

                return RetryAdvice.WaitALot;
            }
        }

        if (timeoutException != null)
        {
            // if (timeoutException)
            {
                logger.Error(motherException, "Timeout Exception. Please take a look and handle more properly.");

                return RetryAdvice.WaitALot;
            }
        }

        log("HandleHttpRequestException is returning false");

        return RetryAdvice.ThrowFurther;
    }
}