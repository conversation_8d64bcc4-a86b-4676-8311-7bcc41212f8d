using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Misc;

namespace Invictus.Nomenklatura.Web;

public class WebRequestWithRetry
{
    public static async Task<object> WebCallWithRetry<TArg>(ILogger logger, TArg arg, Func<TArg, Task<WebRequestWithRetryResult>> action, CancellationToken ct)
    {
        DateTimeOffset startTimeUtc = ServerClock.GetCurrentUtcTime();

        const int INITIAL_RETRY_INTERVAL_MS = 2 * 1000;      // 2 sec.
        const int LONG_RETRY_WAIT_MS = 10 * 1000;            // 10 sec.
        const int LOG_WARN_AFTER_TOTAL_TRIES_MS = 4 * 1000;  // 4 sec.
        const int TOTAL_ATTEMPTS_COUNT = 4;
        
        int nextRetryIntervalMs = INITIAL_RETRY_INTERVAL_MS;

        int retryCount = 0;

        bool waitingOnceForAllOrForLong = false;

    _retry:

        async Task regRetryAttemptAndWait(bool forLong)
        {
            if (nextRetryIntervalMs >= LOG_WARN_AFTER_TOTAL_TRIES_MS)
            {
                logger.Warning($"Cannot get a proper response from remote resource after {(ServerClock.GetCurrentUtcTime() - startTimeUtc).TotalMinutes} minutes.");
            }

            if (++retryCount > TOTAL_ATTEMPTS_COUNT)
            {
                throw new Exception("Out of retry attempts. See log above for more information.");
            }

            if (forLong)
            {
                nextRetryIntervalMs = LONG_RETRY_WAIT_MS;
            }

            logger.Warning($"Retry in {(nextRetryIntervalMs / 1000)} sec.");

            await Task.Delay(nextRetryIntervalMs, ct);

            if (forLong)
                nextRetryIntervalMs = LONG_RETRY_WAIT_MS;
            else
                nextRetryIntervalMs = (int)(INITIAL_RETRY_INTERVAL_MS * 1.8 * retryCount);
        }

        WebRequestWithRetryResult r = await action(arg);
        
        if (r.Advice != RetryAdvice.Proceed && waitingOnceForAllOrForLong)
            throw new AggregateException(r.Exception);

        switch (r.Advice)
        {
            case RetryAdvice.Proceed: return r.Res;

            case RetryAdvice.ThrowFurther:
                if (r.Exception == null)
                    throw new Exception($"{nameof(WebCallWithRetry)} caller should have supplied an exception.");

                throw new AggregateException(r.Exception);

            case RetryAdvice.None: 
                throw TypeAbominationException.Enum(typeof(RetryAdvice), RetryAdvice.None);

            case RetryAdvice.WaitOnce: 
                waitingOnceForAllOrForLong = true;
                goto case RetryAdvice.Wait;
                
            case RetryAdvice.Wait:
                await regRetryAttemptAndWait(false);

                goto _retry;

            case RetryAdvice.WaitALot:
                waitingOnceForAllOrForLong = true;
                await regRetryAttemptAndWait(true);

                goto _retry;
            case RetryAdvice.RESERVED_FIRST:
            case RetryAdvice.RESERVED_LAST:
            default: throw TypeAbominationException.Enum(typeof(RetryAdvice), r.Advice);
        }
    }
}

public readonly struct WebRequestWithRetryResult
{
    public Exception Exception { get; private init; }
    public RetryAdvice Advice { get; private init; }
    public object Res { get; private init;}

    public static WebRequestWithRetryResult Success(object result)
    {
        return new WebRequestWithRetryResult
        {
            Advice = RetryAdvice.Proceed,
            Res = result
        };
    }

    public static WebRequestWithRetryResult MightRetry(Exception exception)
    {
        return new WebRequestWithRetryResult
        {
            Advice = RetryAdvice.Wait,
            Exception = exception
        };
    }

    public static WebRequestWithRetryResult ThrowFurther(Exception exception)
    {
        return new WebRequestWithRetryResult
        {
            Advice = RetryAdvice.ThrowFurther,
            Exception = exception
        };
    }

    public static WebRequestWithRetryResult FromExceptionAdvice(Exception exception, RetryAdvice advice)
    {
        return new WebRequestWithRetryResult
        {
            Advice = advice,
            Exception = exception
        };
    }
}

public enum RetryAdvice
{
    None = 0,
    ThrowFurther,

    /// <summary>
    /// The issue might fix itself soon.
    /// </summary>
    Wait,

    /// <summary>
    /// The issue might fix itself soon, but it might be a different issue.
    /// </summary>
    WaitOnce,

    /// <summary>
    /// If the server is still a shared hosting, some works are going on probs.
    /// </summary>
    WaitALot,

    // ReSharper disable once InconsistentNaming
    RESERVED_FIRST = 10000,
    // ReSharper disable once InconsistentNaming
    RESERVED_LAST = 12000,

    /// <summary>
    /// In case of exception absence.
    /// </summary>
    Proceed = short.MaxValue
}
