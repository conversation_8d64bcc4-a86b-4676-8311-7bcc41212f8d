using System.Collections.Specialized;
using System.Web;

namespace Invictus.Nomenklatura.Web;

public class WebQueryUtil
{
    public static string GetControllerPathWithParameters(string baseAddress, string controllerName, string controllerMethodName, params (string key, string value)[] kvs)
    {
        string fullPath = GetControllerPath(baseAddress, controllerName, controllerMethodName);

        return GetControllerPathWithParametersInner(kvs, fullPath);
    }

    private static string GetControllerPathWithParametersInner((string key, string value)[] kvs, string fullPath)
    {
        var uriBuilder = new UriBuilder(fullPath);
        NameValueCollection query = HttpUtility.ParseQueryString(uriBuilder.Query);

        foreach ((string key, string value) kv in kvs)
            query[kv.key] = kv.value;

        uriBuilder.Query = query.ToString();

        return uriBuilder.ToString();
    }

    public static string GetControllerPathWithParameters(string baseAddress, Type controllerType, string controllerMethodName, params (string key, string value)[] kvs)
    {
        string fullPath = GetControllerPath(baseAddress, controllerType, controllerMethodName);

        var uriBuilder = new UriBuilder(fullPath);
        NameValueCollection query = HttpUtility.ParseQueryString(uriBuilder.Query);

        foreach ((string key, string value) kv in kvs)
            query[kv.key] = kv.value;

        uriBuilder.Query = query.ToString();

        return uriBuilder.ToString();
    }

    public static string GetControllerPath(string baseAddress, string controllerPath, string controllerMethodName)
    {
        if (!baseAddress.EndsWith("/"))
            baseAddress += "/";

        return baseAddress + controllerPath + "/" + controllerMethodName;
    }

    public static string GetControllerPath(string baseAddress, Type controllerType, string controllerMethodName)
    {
        if (!controllerType.Name.EndsWith("Controller"))
            throw new Exception("Expected controller type. Must end with 'Controller'.");

        string controllerPath = controllerType.Name.Remove(controllerType.Name.Length - "Controller".Length);

        return GetControllerPath(baseAddress, controllerPath, controllerMethodName);
    }
}