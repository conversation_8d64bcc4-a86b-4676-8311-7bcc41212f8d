<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <RootNamespace>Invictus.Nomenklatura</RootNamespace>
        <Configurations>RemoteRelease;RemoteDebug;LocalDebug</Configurations>
        <Platforms>AnyCPU</Platforms>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'RemoteRelease' ">
      <Optimize Condition=" '$(Optimize)' == '' ">true</Optimize>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'LocalDebug' ">
      <DebugSymbols Condition=" '$(DebugSymbols)' == '' ">true</DebugSymbols>
      <Optimize Condition=" '$(Optimize)' == '' ">false</Optimize>
    </PropertyGroup>

    <ItemGroup>
        <Compile Include="..\..\void\GlobalUsings.cs">
            <Link>GlobalUsings.cs</Link>
        </Compile>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="OneOf" Version="3.0.271" />
        <PackageReference Include="Serilog" Version="4.2.0" />
        <PackageReference Include="Castle.Core" Version="5.1.1" />
        <PackageReference Include="ClosedXML" Version="0.104.2" />
        <PackageReference Include="HtmlAgilityPack.NetCore" Version="1.5.0.1" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.12" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Jupiter\" />
    </ItemGroup>

</Project>
