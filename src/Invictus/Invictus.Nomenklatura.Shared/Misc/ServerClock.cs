using System.Diagnostics;

namespace Invictus.Nomenklatura.Misc;

public static class ServerClock
{
    // TODO: real global time once in a while
    public static DateTimeOffset StartTime { get; } = TimeProvider.System.GetUtcNow();
        
    private static readonly Stopwatch _Stopwatch = Stopwatch.StartNew();

    public static DateTimeOffset GetCurrentUtcTime()
    {
        return StartTime + _Stopwatch.Elapsed;
    }
}