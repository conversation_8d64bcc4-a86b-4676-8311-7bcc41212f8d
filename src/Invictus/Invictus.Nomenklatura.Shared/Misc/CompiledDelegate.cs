using System.Diagnostics;
using System.Linq.Expressions;
using System.Reflection;

using Enumerable = System.Linq.Enumerable;

namespace Invictus.Nomenklatura.Misc;

public class CompiledDelegateWithInstance
{
    private readonly object _instance;
    private readonly CompiledDelegate _compiledDelegate;

    public Type ReturnType => _compiledDelegate.ReturnType;

    public static CompiledDelegateWithInstance Build(MethodInfo methodInfo, object instance)
    {
        return new CompiledDelegateWithInstance(methodInfo, instance);
    }

    private CompiledDelegateWithInstance(MethodInfo methodInfo, object instance)
    {
        _instance = instance;
        _compiledDelegate = CompiledDelegate.Build(methodInfo);
    }

    public object CallWithReturn(params object[] parameters)
    {
        return _compiledDelegate.CallWithReturn(_instance, parameters);
    }

    public void Call(params object[] parameters)
    {
        _compiledDelegate.Call(_instance, parameters);
    }
}

/// <summary>
/// Dynamic parameter delegate.
/// You can call method with known count of parameters but unknown parameter types without invoking it dynamically.
/// Supports up to 14 parameters.
/// Useful when you want to mane frequent calls with cheap method call time.
/// </summary>
public class CompiledDelegate
{
    private static readonly object _GlobalLockObject = new();

    public static MethodInfo GetGenericMethod(Type type, BindingFlags bindingFlags, string name, Type[] parameterTypes)
    {
        MethodInfo[] methods = type.GetMethods(bindingFlags);

        foreach (MethodInfo method in methods.Where(m => m.Name == name))
        {
            Type[] methodParameterTypes = method.GetParameters().Select(p => p.ParameterType).ToArray();

            if (methodParameterTypes.SequenceEqual(parameterTypes, SimpleTypeComparer.Instance))
            {
                return method;
            }
        }

        return null;
    }

    // THIS CLASS CAN BE REPLACED BY SINGLE REFLECTION CALL
    // This just makes slower initialization, but faster execution
    private const int APP_CUR_MAX_PARAM_COUNT = 12;

    private delegate Delegate CreateDpDelegate(MethodBase method, Type[] parameterTypes);

    static CompiledDelegate()
    {
        _Kreator = CreateKreator();
    }

    //readonly static ConcurrentDictionary<Delegate, Delegate> CachedMethods = new ConcurrentDictionary<Delegate, Delegate>();
    private static readonly CreateDpDelegate _Kreator;

    private static CreateDpDelegate CreateKreator()
    {
        MethodInfo methodLambda = GetGenericMethod(typeof(Expression),
            BindingFlags.Instance | BindingFlags.Static | BindingFlags.Public,
            "Lambda",
            new[] { typeof(Expression), typeof(ParameterExpression[]) }
        );

        var stExpressions = new ParameterExpression[APP_CUR_MAX_PARAM_COUNT];

        for (int i = 0; i < APP_CUR_MAX_PARAM_COUNT; i++)
        {
            stExpressions[i] = Expression.Parameter(typeof(object), "arg" + (i + 1));
        }

        var stLambdaCallsAction = new Func<Expression, ParameterExpression[], object>[APP_CUR_MAX_PARAM_COUNT];
        var stLambdaCallsFunc = new Func<Expression, ParameterExpression[], object>[APP_CUR_MAX_PARAM_COUNT];

        var stMethodsCompileAction = new MethodInfo[APP_CUR_MAX_PARAM_COUNT];
        var stMethodsCompileFunc = new MethodInfo[APP_CUR_MAX_PARAM_COUNT];

        for (int i = 0; i < APP_CUR_MAX_PARAM_COUNT; i++)
        {
            MethodInfo currentLambdaMethod = methodLambda.MakeGenericMethod(GetActionTypeWithPointer(i));

            Func<Expression, ParameterExpression[], object> currentLambdaCall = CreateTypeImplicitDelegate_ForKreator(currentLambdaMethod,
                new[] { typeof(Expression), typeof(ParameterExpression[]) }
            );

            stLambdaCallsAction[i] = currentLambdaCall;

            stMethodsCompileAction[i] = GetGenericMethod(typeof(Expression<>).MakeGenericType(GetActionTypeWithPointer(i)),
                BindingFlags.Instance | BindingFlags.Static | BindingFlags.Public,
                "Compile",
                new Type[] { }
            );
        }

        for (int i = 0; i < APP_CUR_MAX_PARAM_COUNT; i++)
        {
            MethodInfo currentLambdaMethod = methodLambda.MakeGenericMethod(GetFuncTypeWithPointer(i));

            Func<Expression, ParameterExpression[], object> currentLambdaCall = CreateTypeImplicitDelegate_ForKreator(currentLambdaMethod,
                new[] { typeof(Expression), typeof(ParameterExpression[]) }
            );

            stLambdaCallsFunc[i] = currentLambdaCall;

            stMethodsCompileFunc[i] = GetGenericMethod(typeof(Expression<>).MakeGenericType(GetFuncTypeWithPointer(i)),
                BindingFlags.Instance | BindingFlags.Static | BindingFlags.Public,
                "Compile",
                []
            );
        }

        ParameterExpression[] stSingleArgument = new[]
        {
            Expression.Parameter(typeof(object), "obj")
        };

        return (givenMethodInfo, givenParametersType) =>
            DelegateKreator(givenParametersType,
                givenMethodInfo,
                stExpressions,
                stSingleArgument,
                stLambdaCallsFunc,
                stMethodsCompileFunc,
                stLambdaCallsAction,
                stMethodsCompileAction
            );
    }

    private static Delegate DelegateKreator(
        Type[] givenParametersType,
        MethodBase givenMethodInfo,
        ParameterExpression[] stExpressions,
        ParameterExpression[] stSingleArgument,
        Func<Expression, ParameterExpression[], object>[] stLambdaCallsFunc,
        MethodInfo[] stMethodsCompileFunc,
        Func<Expression, ParameterExpression[], object>[] stLambdaCallsAction,
        MethodInfo[] stMethodsCompileAction
    )
    {
        lock (_GlobalLockObject)
        {
            int paramCount = givenParametersType.Length;

            var passingParameters = new Expression[paramCount];
                
            for (int i = 0; i < paramCount; i++)
            {
                passingParameters[i] = Expression.Convert(stExpressions[i + 1], givenParametersType[i]);
            }

            Expression methodCalledExpression;
            ParameterExpression[] currentExpressions;

            var mi = givenMethodInfo as MethodInfo;
            var ci = givenMethodInfo as ConstructorInfo;

            if (paramCount == 0)
            {
                if (givenMethodInfo.IsStatic)
                {
                    if (mi == null)
                        throw new NotSupportedException();
                    methodCalledExpression = Expression.Call(null, mi);
                } else
                {
                    if (ci != null)
                    {
                        methodCalledExpression = Expression.New(ci);
                    } else
                    {
                        methodCalledExpression = Expression.Call(Expression.Convert(stSingleArgument[0], givenMethodInfo.DeclaringType), mi);
                    }
                }

                currentExpressions = stSingleArgument;
            } else
            {
                if (givenMethodInfo.IsStatic)
                {
                    if (mi == null)
                        throw new NotSupportedException();
                    methodCalledExpression = Expression.Call(null, mi, passingParameters);
                } else
                {
                    if (ci != null)
                    {
                        methodCalledExpression = Expression.New(ci, passingParameters);
                    } else
                    {
                        methodCalledExpression = Expression.Call(Expression.Convert(stExpressions[0], givenMethodInfo.DeclaringType), mi, passingParameters);
                    }
                }

                currentExpressions = new ParameterExpression[paramCount + 1];

                Array.Copy(stExpressions, currentExpressions, paramCount + 1);
            }

            Type returnType = mi != null ? mi.ReturnType : givenMethodInfo.DeclaringType;

            if (returnType != typeof(void))
            {
                methodCalledExpression = Expression.Convert(methodCalledExpression, typeof(object));
            }

            Func<Expression, ParameterExpression[], object> stfLambdaCall;
            MethodInfo stfMethodCompiler;

            if (returnType != typeof(void))
            {
                stfLambdaCall = stLambdaCallsFunc[paramCount];
                stfMethodCompiler = stMethodsCompileFunc[paramCount];
            } else
            {
                stfLambdaCall = stLambdaCallsAction[paramCount];
                stfMethodCompiler = stMethodsCompileAction[paramCount];
            }

            object bareExpression = stfLambdaCall(methodCalledExpression, currentExpressions);

            return (Delegate)stfMethodCompiler.Invoke(bareExpression,
                new object[]
                {
                }
            );
        }
    }

    // We are actually generating something like this. Calling this.
    private static Func<object, object, object> CreateTypeImplicitDelegate_ForKreator(MethodInfo methodInfo, Type[] types)
    {
        ParameterExpression parameter1 = Expression.Parameter(typeof(object), "arg1");
        ParameterExpression parameter2 = Expression.Parameter(typeof(object), "arg2");

        return Expression.Lambda<Func<object, object, object>>(
            Expression.Call(null, methodInfo, Expression.Convert(parameter1, types[0]), Expression.Convert(parameter2, types[1])),
            parameter1,
            parameter2
        ).Compile();
    }

    public static CompiledDelegate Build(MethodBase methodInfo)
    {
        Type[] parameterTypes = methodInfo.GetParameters().Select(p => p.ParameterType).ToArray();

        if (parameterTypes.Length >= APP_CUR_MAX_PARAM_COUNT)
            throw new ArgumentException("parameters.Length >= " + APP_CUR_MAX_PARAM_COUNT);

        if (methodInfo.IsGenericMethodDefinition)
            throw new ArgumentException("MethodInfo sohuld be concrete generic method, not generic definition");

        return new CompiledDelegate(methodInfo, parameterTypes);
    }

    private readonly MethodBase _methodInfo;
    private readonly Type[] _parameterTypes;
    private readonly int _parametersCount;

    public Type ReturnType { get; }

    public Delegate StoredDelegate { get; }

    private CompiledDelegate(MethodBase methodInfo, Type[] parameterTypes)
    {
        if (parameterTypes == null)
        {
            _parameterTypes = Array.Empty<Type>();
            _parametersCount = 0;
        } else
        {
            _parameterTypes = parameterTypes;
            _parametersCount = parameterTypes.Length;
        }

        _methodInfo = methodInfo;

        ReturnType = _methodInfo is MethodInfo mi ? mi.ReturnType : methodInfo.DeclaringType;

        StoredDelegate = this.Create();
    }

    private Delegate Create()
    {
        return _Kreator(_methodInfo, _parameterTypes);
    }

    [DebuggerStepThrough]
    public object CallWithReturn(object instance, params object[] parameters)
    {
        if (_parametersCount != 0 && parameters == null)
            throw new ArgumentNullException(nameof(parameters));

        if (parameters.Length != _parametersCount)
            throw new ArgumentException("Wrong length of parameters array", nameof(parameters));

        //We need to call target quickly, so just hardcode...

        switch (_parametersCount)
        {
            case 0: return ((Func<object, object>)StoredDelegate)(instance);
            case 1: return ((Func<object, object, object>)StoredDelegate)(instance, parameters[0]);
            case 2: return ((Func<object, object, object, object>)StoredDelegate)(instance, parameters[0], parameters[1]);
            case 3: return ((Func<object, object, object, object, object>)StoredDelegate)(instance, parameters[0], parameters[1], parameters[2]);
            case 4: return ((Func<object, object, object, object, object, object>)StoredDelegate)(instance, parameters[0], parameters[1], parameters[2], parameters[3]);
            case 5:
                return ((Func<object, object, object, object, object, object, object>)StoredDelegate)(instance,
                    parameters[0],
                    parameters[1],
                    parameters[2],
                    parameters[3],
                    parameters[4]
                );
            case 6:
                return ((Func<object, object, object, object, object, object, object, object>)StoredDelegate)(instance,
                    parameters[0],
                    parameters[1],
                    parameters[2],
                    parameters[3],
                    parameters[4],
                    parameters[5]
                );
            case 7:
                return ((Func<object, object, object, object, object, object, object, object, object>)StoredDelegate)(instance,
                    parameters[0],
                    parameters[1],
                    parameters[2],
                    parameters[3],
                    parameters[4],
                    parameters[5],
                    parameters[6]
                );
            case 8:
                return ((Func<object, object, object, object, object, object, object, object, object, object>)StoredDelegate)(instance,
                    parameters[0],
                    parameters[1],
                    parameters[2],
                    parameters[3],
                    parameters[4],
                    parameters[5],
                    parameters[6],
                    parameters[7]
                );
            case 9:
                return ((Func<object, object, object, object, object, object, object, object, object, object, object>)StoredDelegate)(instance,
                    parameters[0],
                    parameters[1],
                    parameters[2],
                    parameters[3],
                    parameters[4],
                    parameters[5],
                    parameters[6],
                    parameters[7],
                    parameters[8]
                );
            case 10:
                return ((Func<object, object, object, object, object, object, object, object, object, object, object, object>)StoredDelegate)(instance,
                    parameters[0],
                    parameters[1],
                    parameters[2],
                    parameters[3],
                    parameters[4],
                    parameters[5],
                    parameters[6],
                    parameters[7],
                    parameters[8],
                    parameters[9]
                );
            case 11:
                return ((Func<object, object, object, object, object, object, object, object, object, object, object, object, object>)StoredDelegate)(instance,
                    parameters[0],
                    parameters[1],
                    parameters[2],
                    parameters[3],
                    parameters[4],
                    parameters[5],
                    parameters[6],
                    parameters[7],
                    parameters[8],
                    parameters[9],
                    parameters[10]
                );
            case 12:
                return ((Func<object, object, object, object, object, object, object, object, object, object, object, object, object, object>)StoredDelegate)(instance,
                    parameters[0],
                    parameters[1],
                    parameters[2],
                    parameters[3],
                    parameters[4],
                    parameters[5],
                    parameters[6],
                    parameters[7],
                    parameters[8],
                    parameters[9],
                    parameters[10],
                    parameters[11]
                );
            case 13:
                return ((Func<object, object, object, object, object, object, object, object, object, object, object, object, object, object, object>)StoredDelegate)(instance,
                    parameters[0],
                    parameters[1],
                    parameters[2],
                    parameters[3],
                    parameters[4],
                    parameters[5],
                    parameters[6],
                    parameters[7],
                    parameters[8],
                    parameters[9],
                    parameters[10],
                    parameters[11],
                    parameters[12]
                );
            case 14:
                return ((Func<object, object, object, object, object, object, object, object, object, object, object, object, object, object, object, object>)StoredDelegate)(
                    instance,
                    parameters[0],
                    parameters[1],
                    parameters[2],
                    parameters[3],
                    parameters[4],
                    parameters[5],
                    parameters[6],
                    parameters[7],
                    parameters[8],
                    parameters[9],
                    parameters[10],
                    parameters[11],
                    parameters[12],
                    parameters[13]
                );
            default: throw new ArgumentException();
        }
    }

    [DebuggerStepThrough]
    public void Call(object instance, params object[] parameters)
    {
        if (_parametersCount != 0 && parameters == null)
            throw new ArgumentNullException(nameof(parameters));

        if (parameters.Length != _parametersCount)
            throw new ArgumentException("Wrong length of parameters array", nameof(parameters));

        //We need to call target quickly, so just hardcode...

        switch (_parametersCount)
        {
            case 0:
                ((Action<object>)StoredDelegate)(instance);

                break;
            case 1:
                ((Action<object, object>)StoredDelegate)(instance, parameters[0]);

                break;
            case 2:
                ((Action<object, object, object>)StoredDelegate)(instance, parameters[0], parameters[1]);

                break;
            case 3:
                ((Action<object, object, object, object>)StoredDelegate)(instance, parameters[0], parameters[1], parameters[2]);

                break;
            case 4:
                ((Action<object, object, object, object, object>)StoredDelegate)(instance, parameters[0], parameters[1], parameters[2], parameters[3]);

                break;
            case 5:
                ((Action<object, object, object, object, object, object>)StoredDelegate)(instance, parameters[0], parameters[1], parameters[2], parameters[3], parameters[4]);

                break;
            case 6:
                ((Action<object, object, object, object, object, object, object>)StoredDelegate)(instance,
                    parameters[0],
                    parameters[1],
                    parameters[2],
                    parameters[3],
                    parameters[4],
                    parameters[5]
                );

                break;
            case 7:
                ((Action<object, object, object, object, object, object, object, object>)StoredDelegate)(instance,
                    parameters[0],
                    parameters[1],
                    parameters[2],
                    parameters[3],
                    parameters[4],
                    parameters[5],
                    parameters[6]
                );

                break;
            case 8:
                ((Action<object, object, object, object, object, object, object, object, object>)StoredDelegate)(instance,
                    parameters[0],
                    parameters[1],
                    parameters[2],
                    parameters[3],
                    parameters[4],
                    parameters[5],
                    parameters[6],
                    parameters[7]
                );

                break;
            case 9:
                ((Action<object, object, object, object, object, object, object, object, object, object>)StoredDelegate)(instance,
                    parameters[0],
                    parameters[1],
                    parameters[2],
                    parameters[3],
                    parameters[4],
                    parameters[5],
                    parameters[6],
                    parameters[7],
                    parameters[8]
                );

                break;
            case 10:
                ((Action<object, object, object, object, object, object, object, object, object, object, object>)StoredDelegate)(instance,
                    parameters[0],
                    parameters[1],
                    parameters[2],
                    parameters[3],
                    parameters[4],
                    parameters[5],
                    parameters[6],
                    parameters[7],
                    parameters[8],
                    parameters[9]
                );

                break;
            case 11:
                ((Action<object, object, object, object, object, object, object, object, object, object, object, object>)StoredDelegate)(instance,
                    parameters[0],
                    parameters[1],
                    parameters[2],
                    parameters[3],
                    parameters[4],
                    parameters[5],
                    parameters[6],
                    parameters[7],
                    parameters[8],
                    parameters[9],
                    parameters[10]
                );

                break;
            case 12:
                ((Action<object, object, object, object, object, object, object, object, object, object, object, object, object>)StoredDelegate)(instance,
                    parameters[0],
                    parameters[1],
                    parameters[2],
                    parameters[3],
                    parameters[4],
                    parameters[5],
                    parameters[6],
                    parameters[7],
                    parameters[8],
                    parameters[9],
                    parameters[10],
                    parameters[11]
                );

                break;
            case 13:
                ((Action<object, object, object, object, object, object, object, object, object, object, object, object, object, object>)StoredDelegate)(instance,
                    parameters[0],
                    parameters[1],
                    parameters[2],
                    parameters[3],
                    parameters[4],
                    parameters[5],
                    parameters[6],
                    parameters[7],
                    parameters[8],
                    parameters[9],
                    parameters[10],
                    parameters[11],
                    parameters[12]
                );

                break;
            case 14:
                ((Action<object, object, object, object, object, object, object, object, object, object, object, object, object, object, object>)StoredDelegate)(instance,
                    parameters[0],
                    parameters[1],
                    parameters[2],
                    parameters[3],
                    parameters[4],
                    parameters[5],
                    parameters[6],
                    parameters[7],
                    parameters[8],
                    parameters[9],
                    parameters[10],
                    parameters[11],
                    parameters[12],
                    parameters[13]
                );

                break;
            default: throw new ArgumentException();
        }
    }

    private static Type GetActionTypeWithPointer(int argCount) //Pointer means pointer to type instance of method.
    {
        if (argCount > APP_CUR_MAX_PARAM_COUNT)
            throw new ArgumentException("argCount");

        Type[] types = Enumerable.Repeat(typeof(object), argCount + 1).ToArray();

        return Expression.GetActionType(types);
    }

    private static Type GetFuncTypeWithPointer(int argCount) //Pointer means pointer to type instance of method.
    {
        if (argCount > APP_CUR_MAX_PARAM_COUNT)
            throw new ArgumentException("argCount");

        Type[] types = Enumerable.Repeat(typeof(object), argCount + 2).ToArray();

        return Expression.GetFuncType(types);
    }
}