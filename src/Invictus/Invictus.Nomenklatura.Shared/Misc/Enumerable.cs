using System.Collections;

using Invictus.Nomenklatura.Exceptions;

namespace Invictus.Nomenklatura.Misc;

public class SelectSingleException : Exception
{
    public SelectSingleException()
    {
    }

    public SelectSingleException(string message)
        : base(message)
    {
    }

    public SelectSingleException(string message, Exception inner)
        : base(message, inner)
    {
    }
}

public static class Enumerable2
{
    public static int ContainsCount(this string text, string value, StringComparison comparison)
    {
        if (text == null)
            throw new ArgumentNullException(nameof(text));

        if (string.IsNullOrEmpty(value))
            throw new ArgumentException("Value cannot be empty", nameof(value));

        int index = 0;
        int count = 0;

        while ((index = text.IndexOf(value, index, text.Length - index, comparison)) != -1)
        {
            index++;
            count++;
        }

        return count;
    }

    public static TSource SingleWith<TSource>(this IEnumerable<TSource> source, string elementName)
    {
        return source.Single2<TSource, SelectSingleException>($"Element '{elementName}' was not found.", $"More than one '{elementName}' found.");
    }
    
    public static TSource SingleWith<TSource, TExceptionType>(this IEnumerable<TSource> source, string elementName)
        where TExceptionType : Exception
    {
        return source.Single2<TSource, TExceptionType>($"Element '{elementName}' was not found.", $"More than one '{elementName}' found.");
    }
    
    public static TSource Single2<TSource, TExceptionType>(
        this IEnumerable<TSource> source,
        string noElementsMessage,
        string moreThanOneMatchMessage
    )
        where TExceptionType : Exception
    {
        if (source == null)
            throw new ArgumentNullException(nameof(source));

        if (source is IList<TSource> list)
        {
            switch (list.Count)
            {
                case 0:  throw (TExceptionType) Activator.CreateInstance(typeof(TExceptionType), noElementsMessage);
                case 1:  return list[0];
                default: throw (TExceptionType) Activator.CreateInstance(typeof(TExceptionType), moreThanOneMatchMessage);
            }
        }

        using IEnumerator<TSource> enumerator = source.GetEnumerator();

        if (!enumerator.MoveNext())
            throw (TExceptionType) Activator.CreateInstance(typeof(TExceptionType), noElementsMessage);

        TSource current = enumerator.Current;

        if (!enumerator.MoveNext())
            return current;

        throw (TExceptionType) Activator.CreateInstance(typeof(TExceptionType), moreThanOneMatchMessage);
    }
    
    public static TSource SingleOrDefaultWith<TSource>(this IEnumerable<TSource> source, string elementName)
    {
        return source.SingleOrDefault2<TSource, SelectSingleException>($"More than one '{elementName}' found.");
    }
    
    public static TSource SingleOrDefaultWith<TSource, TExceptionType>(this IEnumerable<TSource> source, string elementName)
        where TExceptionType : Exception
    {
        return source.SingleOrDefault2<TSource, TExceptionType>($"More than one '{elementName}' found.");
    }

    public static TSource SingleOrDefault2<TSource, TExceptionType>(
        this IEnumerable<TSource> source,
        string moreThanOneMatchMessage
    )
        where TExceptionType : Exception
    {
        if (source == null)
            throw new ArgumentNullException(nameof(source));

        if (source is IList<TSource> list)
        {
            switch (list.Count)
            {
                case 0:  return default(TSource);
                case 1:  return list[0];
                default: throw (TExceptionType) Activator.CreateInstance(typeof(TExceptionType), moreThanOneMatchMessage);
            }
        }

        using IEnumerator<TSource> enumerator = source.GetEnumerator();

        if (!enumerator.MoveNext())
            return default(TSource);

        TSource current = enumerator.Current;

        if (!enumerator.MoveNext())
            return current;

        throw (TExceptionType) Activator.CreateInstance(typeof(TExceptionType), moreThanOneMatchMessage);
    }

    public static bool Any(this Exception e, Func<Exception, bool> predicate)
    {
        return ExceptionUtil.GetAllInnerExceptionsAndThisOne(e).Any(predicate);
    }

    public static int IndexOf<TSource>(this IEnumerable<TSource> source, Func<TSource, bool> predicate)
    {
        int i = 0;

        foreach (TSource element in source)
        {
            if (predicate(element))
                return i;

            i++;
        }

        return -1;
    }

    public static IEnumerable<TElement> DistinctBy<TElement>(this IEnumerable<TElement> source, IEqualityComparer<TElement> comparer = null)
    {
        return DistinctByIterator(source, comparer);
    }

    public static IEnumerable<TElement> DistinctByIterator<TElement>(IEnumerable<TElement> source, IEqualityComparer<TElement> comparer = null)
    {
        comparer ??= EqualityComparer<TElement>.Default;

        using IEnumerator<TElement> enumerator = source.GetEnumerator();

        if (enumerator.MoveNext())
        {
            var set = new HashSet<TElement>(7, comparer);

            do
            {
                TElement element = enumerator.Current;

                if (set.Add(element))
                {
                    yield return element;
                }
            } while (enumerator.MoveNext());
        }
    }

    public static IEnumerable<TElement> OverrideBy<TKey, TElement>(
        this IEnumerable<TElement> first,
        IEnumerable<TElement> second,
        Func<TElement, TKey> keySelector,
        IEqualityComparer<TKey> comparer = null
    )
    {
        return OverrideByIterator(first, second, keySelector, comparer);
    }

    private static IEnumerable<TElement> OverrideByIterator<TKey, TElement>(
        IEnumerable<TElement> first,
        IEnumerable<TElement> second,
        Func<TElement, TKey> keySelector,
        IEqualityComparer<TKey> comparer = null
    )
    {
        comparer ??= EqualityComparer<TKey>.Default;

        ILookup<TKey, TElement> bLookup = second.ToLookup(keySelector, comparer);

        var keysProcessed = new List<TKey>();

        foreach (TElement elementInFirst in first)
        {
            TKey elementKey = keySelector(elementInFirst);

            keysProcessed.Add(elementKey);

            bool overridden = false;

            foreach (TElement elementOverride in bLookup[elementKey])
            {
                overridden = true;

                yield return elementOverride;
            }

            if (!overridden)
            {
                yield return elementInFirst;
            }
        }

        foreach (IGrouping<TKey, TElement> overrideWithGrouping in bLookup)
        {
            if (keysProcessed.Contains(overrideWithGrouping.Key))
                continue;

            foreach (TElement elementOverride in overrideWithGrouping)
            {
                yield return elementOverride;
            }
        }
    }

    public static IEnumerable<IGrouping<TKey, TElement>> GroupBySequence<TKey, TElement>(
        this IEnumerable<TElement> source,
        Func<TElement, TKey> keySelector,
        IEqualityComparer<TKey> comparer = null
    )
    {
        return GroupBySequenceIterator(source, keySelector, e => e);
    }

    public static IEnumerable<IGrouping<TKey, TElement>> GroupBySequence<TSource, TKey, TElement>(
        this IEnumerable<TSource> source,
        Func<TSource, TKey> keySelector,
        Func<TSource, TElement> elementSelector,
        IEqualityComparer<TKey> comparer = null
    )
    {
        return GroupBySequenceIterator(source, keySelector, elementSelector);
    }

    private static IEnumerable<IGrouping<TKey, TElement>> GroupBySequenceIterator<TSource, TKey, TElement>(
        IEnumerable<TSource> source,
        Func<TSource, TKey> keySelector,
        Func<TSource, TElement> elementSelector,
        IEqualityComparer<TKey> comparer = null
    )
    {
        comparer ??= EqualityComparer<TKey>.Default;

        using IEnumerator<TSource> enumerator = source.GetEnumerator();

        if (!enumerator.MoveNext())
        {
            yield break;
        }

        TKey currentKey = keySelector(enumerator.Current);
        var foundItems = new List<TElement>();

        do
        {
            TKey key = keySelector(enumerator.Current);

            if (!comparer.Equals(currentKey, key))
            {
                yield return new Grouping<TKey, TElement>(currentKey, foundItems);
                currentKey = key;
                foundItems = new List<TElement>();
            }

            foundItems.Add(elementSelector(enumerator.Current));
        } while (enumerator.MoveNext());

        if (foundItems.Count > 0)
        {
            yield return new Grouping<TKey, TElement>(currentKey, foundItems);
        }
    }

    private class Grouping<TKey, TElement> : IGrouping<TKey, TElement>
    {
        private readonly IEnumerable<TElement> _elements;

        public TKey Key { get; }

        public Grouping(TKey key, IEnumerable<TElement> elements)
        {
            _elements = elements;

            Key = key;
        }

        public IEnumerator<TElement> GetEnumerator()
        {
            return _elements.GetEnumerator();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return _elements.GetEnumerator();
        }
    }

    public static IEnumerable<TSource> FromHierarchy<TSource>(TSource source, Func<TSource, TSource> nextItem, Func<TSource, bool> canContinue)
    {
        for (TSource current = source; canContinue(current); current = nextItem(current))
        {
            yield return current;
        }
    }

    public static IEnumerable<TSource> FromHierarchy<TSource>(TSource source, Func<TSource, TSource> nextItem)
        where TSource : class
    {
        return FromHierarchy(source, nextItem, s => s != null);
    }

    public static IEnumerable<TResult> FullOuterGroupJoin<TA, TB, TKey, TResult>(
        this IEnumerable<TA> a,
        IEnumerable<TB> b,
        Func<TA, TKey> selectKeyA,
        Func<TB, TKey> selectKeyB,
        Func<IEnumerable<TA>, IEnumerable<TB>, TKey, TResult> projection,
        IEqualityComparer<TKey> cmp = null
    )
    {
        cmp ??= EqualityComparer<TKey>.Default;
        ILookup<TKey, TA> aLookup = a.ToLookup(selectKeyA, cmp);
        ILookup<TKey, TB> bLookup = b.ToLookup(selectKeyB, cmp);

        var keys = new HashSet<TKey>(aLookup.Select(p => p.Key), cmp);
        keys.UnionWith(bLookup.Select(p => p.Key));

        IEnumerable<TResult> join = from key in keys
            let xa = aLookup[key]
            let xb = bLookup[key]
            select projection(xa, xb, key);

        return join;
    }

    // ReSharper disable once UnusedMember.Global
    public static IEnumerable<TResult> FullOuterJoin<TA, TB, TKey, TResult>(
        this IEnumerable<TA> a,
        IEnumerable<TB> b,
        Func<TA, TKey> selectKeyA,
        Func<TB, TKey> selectKeyB,
        Func<TA, TB, TKey, TResult> projection,
        TA defaultA = default,
        TB defaultB = default,
        IEqualityComparer<TKey> cmp = null
    )
    {
        cmp ??= EqualityComparer<TKey>.Default;
        ILookup<TKey, TA> aLookup = a.ToLookup(selectKeyA, cmp);
        ILookup<TKey, TB> bLookup = b.ToLookup(selectKeyB, cmp);

        var keys = new HashSet<TKey>(aLookup.Select(p => p.Key), cmp);
        keys.UnionWith(bLookup.Select(p => p.Key));

        IEnumerable<TResult> join = from key in keys
            from xa in aLookup[key].DefaultIfEmpty(defaultA)
            from xb in bLookup[key].DefaultIfEmpty(defaultB)
            select projection(xa, xb, key);

        return join;
    }

    public static IEnumerable<TResult> FullOuterJoinSingle<TA, TB, TKey, TResult>(
        this IEnumerable<TA> a,
        IEnumerable<TB> b,
        Func<TA, TKey> selectKeyA,
        Func<TB, TKey> selectKeyB,
        Func<TA, TB, TKey, TResult> projection,
        TA defaultA = default,
        TB defaultB = default,
        IEqualityComparer<TKey> cmp = null
    )
    {
        return FullOuterJoinSingleWithCheats(a, b, selectKeyA, selectKeyB, projection, null, defaultA, defaultB, cmp);
    }

    public static IEnumerable<EnumerableMatch.Result<TKey, TLeft, TRight>> Match<TKey, TLeft, TRight>(
        this IEnumerable<TLeft> left,
        IEnumerable<TRight> right,
        Func<TLeft, TKey> selectKeyLeft,
        Func<TRight, TKey> selectKeyRight,
        IEqualityComparer<TKey> cmp = null
    )
        where TLeft : class
        where TRight : class
    {
        return left.FullOuterGroupJoin(right,
            selectKeyLeft,
            selectKeyRight,
            MatchSelector,
            cmp
        );
    }

    private static EnumerableMatch.Result<TKey, TLeft, TRight> MatchSelector<TKey, TLeft, TRight>(IEnumerable<TLeft> leftEnumerable, IEnumerable<TRight> rightEnumerable, TKey key)
        where TLeft : class
        where TRight : class
    {
        EnumerableMatch.Result<TKey, TLeft, TRight> result(EnumerableMatch.ResultType resultType)
        {
            return new EnumerableMatch.Result<TKey, TLeft, TRight>(
                resultType,
                key,

                // ReSharper disable once PossibleMultipleEnumeration
                leftEnumerable,

                // ReSharper disable once PossibleMultipleEnumeration
                rightEnumerable
            );
        }

        // ReSharper disable once PossibleMultipleEnumeration
        using IEnumerator<TLeft> leftEnumerator = leftEnumerable.GetEnumerator();

        // ReSharper disable once PossibleMultipleEnumeration
        using IEnumerator<TRight> rightEnumerator = rightEnumerable.GetEnumerator();

        bool hasLeft = leftEnumerator.MoveNext();
        bool hasRight = rightEnumerator.MoveNext();

        bool multipleRight;

        if (!hasLeft)
        {
            if (!hasRight)
                throw new Exception($"Given any key it should not be possible to have zero elements on both sides, key: {key}");

            multipleRight = rightEnumerator.MoveNext();

            return result(multipleRight ? EnumerableMatch.ResultType.OnlyRightMultiple : EnumerableMatch.ResultType.OnlyRightSingle);
        }

        bool multipleLeft = leftEnumerator.MoveNext();

        if (!hasRight)
        {
            return result(multipleLeft ? EnumerableMatch.ResultType.OnlyLeftMultiple : EnumerableMatch.ResultType.OnlyLeftSingle);
        }

        multipleRight = rightEnumerator.MoveNext();

        if (multipleLeft)
        {
            return result(multipleRight ? EnumerableMatch.ResultType.ManyToMany : EnumerableMatch.ResultType.LeftMultipleOneRight);
        }

        return result(multipleRight ? EnumerableMatch.ResultType.LeftOneRightMultiple : EnumerableMatch.ResultType.OneToOne);
    }

    private static IEnumerable<TResult> FullOuterJoinSingleWithCheats<TA, TB, TKey, TResult>(
        IEnumerable<TA> a,
        IEnumerable<TB> b,
        Func<TA, TKey> selectKeyA,
        Func<TB, TKey> selectKeyB,
        Func<TA, TB, TKey, TResult> projection,
        Func<TA[], TB[], TKey, TResult> projectionForMultiples = null,
        TA defaultA = default,
        TB defaultB = default,
        IEqualityComparer<TKey> cmp = null
    )
    {
        TResult projectionWithCheck(IEnumerable<TA> a, IEnumerable<TB> b, TKey key)
        {
            // ReSharper disable once PossibleMultipleEnumeration
            using IEnumerator<TA> aEnumerator = a.GetEnumerator();

            // ReSharper disable once PossibleMultipleEnumeration
            using IEnumerator<TB> bEnumerator = b.GetEnumerator();

            bool hasA = aEnumerator.MoveNext();
            bool hasB = bEnumerator.MoveNext();

            TA aVal = hasA ? aEnumerator.Current : defaultA;
            TB bVal = hasB ? bEnumerator.Current : defaultB;

            if (hasA && aEnumerator.MoveNext())
            {
                if (projectionForMultiples == null)
                    throw new InvalidOperationException($"Multiple left side values for key '{key}'.");

                return projectionForMultiples(

                    // ReSharper disable once PossibleMultipleEnumeration
                    a.ToArray(),

                    // ReSharper disable once PossibleMultipleEnumeration
                    b.ToArray(),
                    key
                );
            }

            if (hasB && bEnumerator.MoveNext())
            {
                if (projectionForMultiples == null)
                    throw new InvalidOperationException($"Multiple right side values for key '{key}'.");

                return projectionForMultiples(

                    // ReSharper disable once PossibleMultipleEnumeration
                    a.ToArray(),

                    // ReSharper disable once PossibleMultipleEnumeration
                    b.ToArray(),
                    key
                );
            }

            return projection(aVal, bVal, key);
        }

        return a.FullOuterGroupJoin(b, selectKeyA, selectKeyB, projectionWithCheck, cmp);
    }
}

public class EnumerableMatch
{
    public enum ResultType
    {
        OnlyRightSingle,
        OnlyRightMultiple,
        OneToOne,
        OnlyLeftSingle,
        OnlyLeftMultiple,
        LeftOneRightMultiple,
        LeftMultipleOneRight,
        ManyToMany
    }

    public record struct Result<TKey, TLeft, TRight>(ResultType Type, TKey Key, IEnumerable<TLeft> LeftEnumerable, IEnumerable<TRight> RightEnumerable)
        where TLeft : class
        where TRight : class
    {
        public TLeft LeftSingle => LeftEnumerable.Single();
        public TRight RightSingle => RightEnumerable.Single();

        private TLeft[] _leftArray = null;
        public TLeft[] LeftArray => _leftArray ??= LeftEnumerable.ToArray();

        private TRight[] _rightArray = null;
        public TRight[] RightArray => _rightArray ??= RightEnumerable.ToArray();
    }
}