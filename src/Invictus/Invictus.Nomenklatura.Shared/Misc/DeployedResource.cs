using System.IO.Compression;
using System.Security.Cryptography;

using Invictus.Nomenklatura.Misc;

[AIGenerated]
public class DeployedResource
{
    private readonly ILogger _logger;
    private readonly bool _isZipMode; // Flag indicating if we are processing a zip file

    // --- Paths ---
    // Path to the ZIP archive, ONLY valid if _isZipMode is true.
    private readonly string _zipFilePath;
    // Path to the final content file (either the file provided directly, or the file extracted from zip).
    private readonly string _contentFilePath;

    // --- State ---
    // In ZIP mode: True if verified/extracted successfully at least once AND file believed to exist.
    // In Direct mode: Always true after constructor success.
    private bool _isVerifiedAndReady;

    // --- Calculated paths (only relevant in ZIP mode) ---
    private string ChecksumFilePath => _isZipMode ? _zipFilePath + ".checksum.tmp" : null;


    /// <summary>
    /// Initializes a new instance of the DeployedResource class.
    /// Determines if it should operate in ZIP mode (using filePath + ".zip")
    /// or Direct mode (using filePath directly).
    /// </summary>
    /// <param name="filePath">The base path to the resource. The class will look for filePath + ".zip" first.</param>
    /// <param name="logger">The Serilog logger instance.</param>
    /// <exception cref="ArgumentNullException">Thrown if filePath or logger is null or whitespace.</exception>
    /// <exception cref="FileNotFoundException">Thrown if neither filePath + ".zip" nor filePath exists.</exception>
    public DeployedResource(string filePath, ILogger logger)
    {
        // 1. Null/Empty Checks
        if (string.IsNullOrWhiteSpace(filePath))
        {
            logger?.Error("DeployedResource Init: filePath provided is null or empty."); // Log before throw
            throw new ArgumentNullException(nameof(filePath), "Base file path cannot be null or empty.");
        }
        _logger = logger ?? throw new ArgumentNullException(nameof(logger), "Logger cannot be null.");

        if (filePath.EndsWith(".zip"))
            filePath = Path.Combine(Path.GetDirectoryName(filePath), Path.GetFileNameWithoutExtension(filePath));
        
        // 2. Determine Mode based on file existence
        string potentialZipPath = filePath + ".zip";
        bool zipExists = File.Exists(potentialZipPath);
        bool contentFileExists = File.Exists(filePath);

        if (zipExists)
        {
            // --- ZIP Mode ---
            _isZipMode = true;
            _zipFilePath = potentialZipPath;
            _contentFilePath = filePath; // Target for extraction is the original filePath
            _isVerifiedAndReady = false; // Needs verification/extraction via GetFile()
            // No logging needed on success path
        }
        else if (contentFileExists)
        {
            // --- Direct Mode ---
            _isZipMode = false;
            _zipFilePath = null; // Not applicable
            _contentFilePath = filePath; // Use the file directly
            _isVerifiedAndReady = true; // File exists, ready immediately
            // No logging needed on success path
        }
        else
        {
            // --- Error: Neither exists ---
            _logger.Error("DeployedResource Init: Neither ZIP file {ZipPath} nor direct file {FilePath} found.", potentialZipPath, filePath);
            throw new FileNotFoundException($"Neither the ZIP resource ('{potentialZipPath}') nor the direct file ('{filePath}') could be found.");
        }
    }

    /// <summary>
    /// If in ZIP mode, ensures the content file is extracted and up-to-date.
    /// If in Direct mode, ensures the file still exists.
    /// Returns a new Stream to the content file.
    /// </summary>
    /// <returns>A new FileStream opened for reading the content file.</returns>
    /// <exception cref="InvalidDataException">Thrown if the zip archive is invalid or empty during extraction (ZIP mode only).</exception>
    /// <exception cref="IOException">Thrown on errors reading/writing files or checksum.</exception>
    /// <exception cref="UnauthorizedAccessException">Thrown if permissions are insufficient.</exception>
    /// <exception cref="FileNotFoundException">Thrown if the content file is unexpectedly missing.</exception>
    /// <exception cref="Exception">Other potential exceptions during file operations or extraction.</exception>
    public FileStream GetFile()
    {
        if (_isZipMode)
        {
            return this.GetFileFromZip();
        }

        return this.GetFileDirect();
    }

    /// <summary>
    /// Handles getting the file stream when in Direct Mode.
    /// </summary>
    private FileStream GetFileDirect()
    {
        // In Direct mode, _isVerifiedAndReady was set true in constructor if file existed.
        // We just need to ensure it *still* exists.
        if (!_isVerifiedAndReady)
        {
             // This state shouldn't be possible if constructor succeeded in Direct Mode, indicates internal logic error.
             _logger.Error("DeployedResource Direct Mode: Invalid state - _isVerifiedAndReady is false. File: {FilePath}", _contentFilePath);
             throw new InvalidOperationException("Internal state error in Direct Mode.");
        }

        if (File.Exists(_contentFilePath))
        {
            // Success: File exists. Return stream.
             return new FileStream(_contentFilePath, FileMode.Open, FileAccess.Read, FileShare.Read);
        }

        // File disappeared after constructor check!
        _isVerifiedAndReady = false; // Update state
        _logger.Error("DeployedResource Direct Mode: Content file {ContentPath} missing unexpectedly.", _contentFilePath);
        throw new FileNotFoundException("Direct content file disappeared after initialization.", _contentFilePath);
    }

    /// <summary>
    /// Handles checksum verification, extraction (if needed), and getting the file stream when in ZIP Mode.
    /// </summary>
    private FileStream GetFileFromZip()
    {
        // --- Fast Path (ZIP Mode): Already verified? ---
        if (_isVerifiedAndReady)
        {
            if (File.Exists(_contentFilePath))
            {
                // Success: Already verified and extracted file exists. Return stream.
                return new FileStream(_contentFilePath, FileMode.Open, FileAccess.Read, FileShare.Read);
            }

            // Extracted file missing despite previous verification! Reset state and proceed.
            _logger.Warning("DeployedResource ZIP Mode: Content file {ContentPath} missing despite previous verification for {ZipPath}. Re-verifying.", _contentFilePath, _zipFilePath);
            _isVerifiedAndReady = false; // Force re-verification
            // Fall through to the verification logic below...
        }

        // --- Verification/Extraction Path (ZIP Mode) ---
        string currentZipChecksum = null;
        string storedChecksum = null;
        bool needsExtraction = false;
        string checksumFilePath = ChecksumFilePath; // Use property

        try
        {
            // 1. Calculate Current ZIP Checksum
            currentZipChecksum = this.CalculateMd5(_zipFilePath);

            // 2. Read Stored Checksum
            if (File.Exists(checksumFilePath))
            {
                try
                {
                    storedChecksum = File.ReadAllText(checksumFilePath)?.Trim();
                }
                catch (IOException ex)
                {
                    _logger.Warning(ex, "DeployedResource ZIP Mode: Could not read checksum file {ChecksumPath}. Will force extraction.", checksumFilePath);
                    storedChecksum = null; // Treat as non-existent
                }
            }

            // 3. Determine if extraction is needed
            bool fileExists = File.Exists(_contentFilePath);
            needsExtraction = !fileExists || !string.Equals(currentZipChecksum, storedChecksum, StringComparison.OrdinalIgnoreCase);

            // 4. Perform Extraction (if needed)
            if (needsExtraction)
            {
                try
                {
                    using (ZipArchive archive = ZipFile.OpenRead(_zipFilePath))
                    {
                        ZipArchiveEntry entryToExtract = archive.Entries.FirstOrDefault(e => !string.IsNullOrEmpty(e.Name));
                        if (entryToExtract == null)
                        {
                            _logger.Error("DeployedResource ZIP Mode: ZIP file {ZipPath} is empty or contains no file entries to extract.", _zipFilePath);
                            throw new InvalidDataException($"ZIP file '{_zipFilePath}' is empty or contains no actual file entries.");
                        }
                        entryToExtract.ExtractToFile(_contentFilePath, true); // true = overwrite
                    }

                    // Write new checksum *after* successful extraction
                    try
                    {
                        File.WriteAllText(checksumFilePath, currentZipChecksum);
                        _isVerifiedAndReady = true; // Set flag only on full success
                    }
                    catch (IOException ex)
                    {
                       _logger.Error(ex, "DeployedResource ZIP Mode: Error writing checksum file {ChecksumPath} after extraction.", checksumFilePath);
                       _isVerifiedAndReady = true; // Mark ready anyway, content extracted
                    }
                }
                catch (Exception ex) when (ex is IOException || ex is InvalidDataException || ex is UnauthorizedAccessException)
                {
                    _logger.Error(ex, "DeployedResource ZIP Mode: Failed to extract content from {ZipPath} to {ContentPath}.", _zipFilePath, _contentFilePath);
                     _isVerifiedAndReady = false; // Ensure flag is false on failure
                    throw; // Re-throw extraction error
                }
                catch(Exception ex) // Catch unexpected errors during extraction
                {
                     _logger.Error(ex, "DeployedResource ZIP Mode: Unexpected error during extraction processing for {ZipPath}.", _zipFilePath);
                     _isVerifiedAndReady = false;
                     throw;
                }
            }
            else
            {
                // Checksums match and file exists. Mark as ready.
                _isVerifiedAndReady = true;
            }

            // 5. Return Stream to the content file
            return new FileStream(_contentFilePath, FileMode.Open, FileAccess.Read, FileShare.Read);

        }
        catch (Exception ex) // Catch-all for logging unexpected issues during ZIP verification
        {
             _isVerifiedAndReady = false; // Ensure flag is false on any exception path
             // Avoid logging again if it was already logged and rethrown
             if (!(ex is FileNotFoundException || ex is InvalidDataException || ex is IOException || ex is UnauthorizedAccessException))
             {
                _logger.Error(ex, "DeployedResource ZIP Mode: Unexpected error processing ZIP file {ZipPath}", _zipFilePath);
             }
             throw; // Re-throw the exception
        }
    }


    /// <summary>
    /// Calculates the MD5 checksum of a file. Only call when _isZipMode is true.
    /// </summary>
    private string CalculateMd5(string filePath) // filePath will be _zipFilePath here
    {
        // Basic check, though should only be called internally when needed
        if (string.IsNullOrEmpty(filePath)) return null;

        try
        {
            using (var md5 = MD5.Create())
            using (FileStream stream = File.Open(filePath, FileMode.Open, FileAccess.Read, FileShare.Read))
            {
                byte[] hashBytes = md5.ComputeHash(stream);
                var sb = new StringBuilder(hashBytes.Length * 2);
                foreach (byte b in hashBytes) { sb.Append(b.ToString("x2")); }
                return sb.ToString();
            }
        }
        catch (Exception ex) when (ex is IOException || ex is UnauthorizedAccessException)
        {
            _logger.Error(ex, "DeployedResource: Failed to calculate MD5 checksum for {FilePath}", filePath);
            throw; // Re-throw known exceptions after logging
        }
        catch (Exception ex) // Catch unexpected errors
        {
            _logger.Error(ex, "DeployedResource: Unexpected error calculating MD5 for {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// Indicates whether the resource is currently considered ready for streaming.
    /// In ZIP mode, requires verification/extraction. In Direct mode, relies on initial check.
    /// Also verifies the physical existence of the content file.
    /// </summary>
    public bool IsFileReady =>
        // _isVerifiedAndReady reflects the state based on mode (set true in constructor for Direct mode)
        // _contentFilePath is always set to the final target path
        _isVerifiedAndReady && File.Exists(_contentFilePath);
}