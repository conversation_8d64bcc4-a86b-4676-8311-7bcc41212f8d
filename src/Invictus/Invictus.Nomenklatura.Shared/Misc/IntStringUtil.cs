using System.Diagnostics.Contracts;

using HtmlAgilityPack;

namespace Invictus.Nomenklatura.Misc;

public static class IntStringUtil
{
    public static TimeSpan ParseTimeOfDay(string text)
    {
        DateTime d;

        if (!DateTime.TryParseExact(text, "HH:mm", CultureInfo.InvariantCulture, DateTimeStyles.None, out d))
            if (!DateTime.TryParseExact(text, "HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out d))
                throw new Exception($"Unrecognized time of day {text}");

        return d.TimeOfDay;
    }
    
    public static bool IsBasicLetterColonOrDigit(char c) {
        return (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || c == ':' || char.IsDigit(c);
    }
    
    public static string SubstituteAll(string input, Dictionary<string, string> substitutions)
    {
        if (string.IsNullOrEmpty(input) || substitutions == null || substitutions.Count == 0)
        {
            return input;
        }

        // Sort substitutions by length in descending order to handle overlapping patterns
        List<KeyValuePair<string, string>> orderedSubstitutions = substitutions
            .OrderByDescending(x => x.Key.Length)
            .ToList();

        string result = input;
        foreach (KeyValuePair<string, string> substitution in orderedSubstitutions)
        {
            result = result.Replace(substitution.Key, substitution.Value);
        }

        return result;
    }
        
    public static string FirstCharManipulation(this string str, Func<char, char> func)
    {
        if ( !string.IsNullOrEmpty(str) && char.IsUpper(str[0]))
            return str.Length == 1 ? func(str[0]).ToString() : func(str[0]) + str[1..];

        return str;
    }
        
    public static string ConvertSomeHtmlToText(string html)
    {
        var doc = new HtmlDocument();
        doc.LoadHtml(html);

        var sw = new StringWriter();
        ConvertTo(doc.DocumentNode, sw);
        sw.Flush();
        string res = sw.ToString();
        return res;
    }

    private static void ConvertTo(HtmlNode node, TextWriter outText)
    {
        switch (node.NodeType)
        {
            case HtmlNodeType.Comment:
                // don't output comments
                break;

            case HtmlNodeType.Document:
                ConvertContentTo(node, outText);
                break;

            case HtmlNodeType.Text:
                // script and style must not be output
                string parentName = node.ParentNode.Name;
                if ((parentName == "script") || (parentName == "style"))
                    break;

                // get text
                string html = ((HtmlTextNode)node).Text;

                // is it in fact a special closing node output as text?
                if (HtmlNode.IsOverlappedClosingElement(html))
                    break;

                // check the text is meaningful and not a bunch of whitespaces
                if (html.Trim().Length > 0)
                {
                    outText.Write(HtmlEntity.DeEntitize(html));
                }
                break;

            case HtmlNodeType.Element:
                switch (node.Name)
                {
                    case "p":
                    case "br":
                        outText.Write("\n");
                        break;
                }

                if (node.HasChildNodes)
                {
                    ConvertContentTo(node, outText);
                }
                break;
        }
    }

    private static void ConvertContentTo(HtmlNode node, TextWriter outText)
    {
        foreach (HtmlNode subNode in node.ChildNodes)
        {
            ConvertTo(subNode, outText);
        }
    }

    public static string ConvertNewlinesToHtmlBr(string text)
    {
        return text.Replace("\r", "").Replace("\n", "<br/>");
    }

    public static string[] SplitCommand(string command)
    {
        return command.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
    }

    public static string CapitalizeEachNewLine(string input)
    {
        if (string.IsNullOrEmpty(input))
        {
            return input;
        }

        var result = new StringBuilder(input.Length);
        bool capitalizeNext = true;

        foreach (char c in input)
        {
            if (capitalizeNext && !char.IsWhiteSpace(c))
            {
                result.Append(char.ToUpper(c));
                capitalizeNext = false;
            }
            else
            {
                result.Append(c);
            }

            if (c == '\n')
            {
                capitalizeNext = true;
            }
        }

        return result.ToString();
    }

    [Pure]
    public static long Val(this long? me)
    {
        if (!me.HasValue)
            throw new ArgumentNullException(nameof(me));

        return me.Value;
    }
    
    
    [Pure]
    public static int Val(this int? me)
    {
        if (!me.HasValue)
            throw new ArgumentNullException(nameof(me));

        return me.Value;
    }

    // ReSharper disable once UnusedMember.Global
    public static string MultilineString(IEnumerable<string> str)
    {
        var sb = new StringBuilder();
        string[] arr = str.ToArray();

        for (int i = 0; i < arr.Length; i++)
        {
            if (i == arr.Length - 1)
                sb.Append(arr[i]);
            else
                sb.AppendLine(arr[i]);
        }

        return sb.ToString();
    }

    public static List<string> MultilineStrings(IEnumerable<string> str, int maxSize)
    {
        var res = new List<string>();

        var sb = new StringBuilder();
        string[] arr = str.ToArray();

        for (int i = 0; i < arr.Length; i++)
        {
            if (sb.Length + arr[i].Length > maxSize)
            {
                res.Add(sb.ToString());
                sb.Clear();
            }

            if (i == arr.Length - 1)
                sb.Append(arr[i]);
            else
                sb.AppendLine(arr[i]);
        }

        res.Add(sb.ToString());

        return res;
    }



    public static string RemoveWhitespace(string input)
    {
        return new string(
            input.ToCharArray()
                .Where(c => !char.IsWhiteSpace(c))
                .ToArray()
        );
    }

    public static int GetStableHashCode(string str)
    {
        unchecked
        {
            int hash1 = 5381;
            int hash2 = hash1;

            for (int i = 0; i < str.Length && str[i] != '\0'; i += 2)
            {
                hash1 = ((hash1 << 5) + hash1) ^ str[i];

                if (i == str.Length - 1 || str[i + 1] == '\0')
                    break;

                hash2 = ((hash2 << 5) + hash2) ^ str[i + 1];
            }

            return hash1 + (hash2 * 1566083941);
        }
    }

    public static bool StartsWithEither(this string str, StringComparison stringComparison, params string[] startsWith)
    {
        return startsWith.Any(sw => str.StartsWith(sw, stringComparison));
    }

    public static int GetIso8601WeekOfYear(DateTime time)
    {
        // Seriously cheat.  If its Monday, Tuesday or Wednesday, then it'll 
        // be the same week# as whatever Thursday, Friday or Saturday are,
        // and we always get those right
        DayOfWeek day = CultureInfo.InvariantCulture.Calendar.GetDayOfWeek(time);
        if (day >= DayOfWeek.Monday && day <= DayOfWeek.Wednesday)
        {
            time = time.AddDays(3);
        }

        // Return the week of our adjusted day
        return CultureInfo.InvariantCulture.Calendar.GetWeekOfYear(time, CalendarWeekRule.FirstFourDayWeek, DayOfWeek.Monday);
    }

    private static readonly Dictionary<byte, string> _FancyDigits = new Dictionary<byte, string>
    {
        { 0, "⓿" },
        { 1, "❶" },
        { 2, "❷" },
        { 3, "❸" },
        { 4, "❹" },
        { 5, "❺" },
        { 6, "❻" },
        { 7, "❼" },
        { 8, "❽" },
        { 9, "❾" },
    };

    public static string ReplaceIntWithUnicodeFancyChars(string str)
    {
        if (!long.TryParse(str, out long n))
            return str;

        return ReplaceIntWithUnicodeFancyChars(n);
    }

    public static string ReplaceIntWithUnicodeFancyChars(long n)
    {
        IEnumerable<byte> digits = n.ToString()
            .Select(i => byte.Parse(new string(new char[1] { i })))
            .ToArray();

        string[] strs = digits
            .Select(digit => _FancyDigits[digit])
            .ToArray();

        return string.Join("", strs);
    }
}