using System.IO.Compression;
using System.Runtime.Serialization;

namespace Invictus.Nomenklatura.Misc;

public class SerializationContext<T>
{
    private readonly List<Type> _addKnownTypes = new();
    private readonly Lazy<DataContractSerializer> _serializer;

    public SerializationContext()
    {
        _serializer = new Lazy<DataContractSerializer>(() => new DataContractSerializer(
            typeof(T),
            new DataContractSerializerSettings
            {
                KnownTypes = _addKnownTypes
            }
        ));
    }

    public void AddKnownType(Type type)
    {
        // ReSharper disable once InconsistentlySynchronizedField
        if (_serializer.IsValueCreated)
            throw new Exception("Too late to add known type");

        if (!_addKnownTypes.Contains(type))
            _addKnownTypes.Add(type);
    }

    public byte[] SerializeToBinaryCompressed(T obj)
    {
        using var ms = new MemoryStream();
        var gz = new GZipStream(ms, CompressionLevel.Fastest);

        lock (_serializer)
        {
            _serializer.Value.WriteObject(gz, obj);
        }

        ms.Position = 0;

        using var sr = new BinaryReader(ms);

        byte[] res = sr.ReadBytes((int)ms.Length);

        return res;
    }

    public T DeserializeFromBinaryCompressed(byte[] bytes)
    {
        int lastByte = 0;

        // sql binary...
        for (int i = bytes.Length - 1; i >= 0; i--)
        {
            if (bytes[i] != 0) // ???
            {
                lastByte = i;
                break;
            }
        }

        using var ms = new MemoryStream();
        // ???
        var bw = new BinaryWriter(ms);
        bw.Write(bytes, 0, lastByte);
        bw.Flush();
        ms.Position = 0;

        using var gz = new GZipStream(ms, CompressionMode.Decompress, true);
        using var sr = new StreamReader(gz);

        string str = sr.ReadToEnd(); // ???

        ms.Position = 0;
        var sw = new StreamWriter(ms);
        sw.Write(str);
        sw.Flush();
        ms.Position = 0;
        // ???

        lock (_serializer)
        {
            return (T)_serializer.Value.ReadObject(ms);
        }
    }
}