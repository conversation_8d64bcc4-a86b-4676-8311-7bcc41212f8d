namespace Invictus.Nomenklatura.Misc;

public interface IMethodCallFactory
{
    object CallMethod(object instance, params object[] addMoreArguments);
    object CallMethod(Type instanceType, params object[] addMoreArguments);
    object[] GetArgumentsForMethodCall(object[] addMoreArguments);
    Type[] GetArgumentTypes();
}

public interface IConstructorCallFactory
{
    object CreateInstance(Type instanceType, params object[] addMoreArguments);
}