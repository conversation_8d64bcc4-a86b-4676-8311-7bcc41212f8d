using System.Collections;
using System.Reflection;
using System.Runtime.CompilerServices;

namespace Invictus.Nomenklatura.Misc;

public class DebugUtil
{
    public static string DumpObject(object obj,  bool includeFields = false, bool includePrivate = false)
    {
        return new ObjectDumper(5, includeFields, includePrivate).Dump(obj);
    }
}

public class ObjectDumper
{
    // ... (Keep all fields and constructor from the previous "most aggressive" version) ...
    private readonly int _maxDepth;
    private StringBuilder _stringBuilder;
    private readonly HashSet<object> _visitedObjects;
    private int _currentDepth;
    private string _indentation;

    private readonly bool _includeFields;
    private readonly bool _includePrivate;
    private readonly int _maxEnumerableItems;
    private readonly int _maxStringLengthPerProperty;
    private readonly int _maxSingleAppendSize;
    private readonly int _maxOutputSize;
    private bool _outputTruncated;
    private bool _individualValueTruncated;

    private const string TotalOutputTruncationMessage = "\n{0}[TOTAL OUTPUT TRUNCATED: Max size ~{1:F1}KB. Further details omitted.]";
    private const string ValueTooLargeTruncationMessage = "[VALUE TRUNCATED: Exceeded single value size limit]";
    private const string StringPropertyTooLargeMessage = "... [String property truncated]";
    private const string ReflectionTypeSkippedMessage = "[Reflection Type Skipped]"; // New message

    public ObjectDumper(
        int maxDepth = 7,
        bool includeFields = false,
        bool includePrivate = false,
        int maxEnumerableItems = 20,
        int maxStringLengthPerProperty = 256,
        int maxSingleAppendSize = 512,
        int maxOutputSize = 128 * 1024
    )
    {
        _maxDepth = maxDepth < 0 ? 7 : maxDepth;
        _includeFields = includeFields;
        _includePrivate = includePrivate;
        _maxEnumerableItems = maxEnumerableItems < 0 ? 20 : maxEnumerableItems;
        _maxStringLengthPerProperty = maxStringLengthPerProperty < 0 ? 256 : maxStringLengthPerProperty;
        _maxSingleAppendSize = Math.Max(maxSingleAppendSize < 0 ? 512 : maxSingleAppendSize, 64);
        _maxOutputSize = Math.Max(maxOutputSize, 2048);
        _visitedObjects = new HashSet<object>(ReferenceEqualityComparer.Instance);
    }

    private void SafeAppend(string value)
    {
        if (_outputTruncated || string.IsNullOrEmpty(value)) return;
        int currentLength = _stringBuilder.Length;
        int availableSpace = _maxOutputSize - currentLength;
        if (availableSpace <= 0) { _outputTruncated = true; return; }

        string valueToAppend = value;
        if (value.Length > _maxSingleAppendSize)
        {
            valueToAppend = value.Substring(0, _maxSingleAppendSize) + ValueTooLargeTruncationMessage;
            _individualValueTruncated = true;
        }

        if (valueToAppend.Length > availableSpace)
        {
            _stringBuilder.Append(valueToAppend, 0, availableSpace);
            _outputTruncated = true;
        }
        else
        {
            _stringBuilder.Append(valueToAppend);
        }
    }

    private void Write(string value)
    {
        if (_outputTruncated) return;
        this.SafeAppend(value);
    }

    private void WriteLine(string value = "")
    {
        if (_outputTruncated) return;
        this.SafeAppend(value);
        if (_outputTruncated) return;
        if (_stringBuilder.Length + Environment.NewLine.Length <= _maxOutputSize)
            this.SafeAppend(Environment.NewLine);
        else _outputTruncated = true;
    }

    public string Dump(object obj)
    {
        // ... (Dump method largely the same as the previous "most aggressive" one, including StringBuilder init and final truncation message logic)
        if (obj == null) return "null";

        try
        {
            _stringBuilder = new StringBuilder(_maxOutputSize);
        }
        catch (OutOfMemoryException oomEx)
        {
            return $"[OOM during StringBuilder allocation: Requested capacity {_maxOutputSize / 1024.0:F1}KB. Reduce maxOutputSize. Details: {oomEx.Message}]";
        }
        catch (ArgumentOutOfRangeException argEx)
        {
            return $"[Error initializing StringBuilder: {argEx.Message} (maxOutputSize: {_maxOutputSize})]";
        }

        _visitedObjects.Clear();
        _currentDepth = 0;
        _indentation = "";
        _outputTruncated = false;
        _individualValueTruncated = false;

        try
        {
            this.WriteObjectRecursive(obj);
        }
        catch (OutOfMemoryException oomRecursionEx)
        {
            _stringBuilder.Clear();
            this.SafeAppend($"[CRITICAL OOM during dump recursion: {oomRecursionEx.Message}]");
            _outputTruncated = true;
        }
        catch (Exception ex)
        {
            if (!_outputTruncated)
            {
                try
                {
                    this.WriteLine();
                    this.Write($"{_indentation}[UNEXPECTED ERROR DURING DUMP: {ex.GetType().Name} - {ex.Message}]");
                }
                catch { /* Best effort */ }
                _outputTruncated = true; 
            }
        }

        if (_outputTruncated || _individualValueTruncated)
        {
            if (_outputTruncated) {
                string message = string.Format(TotalOutputTruncationMessage,
                                            _indentation,
                                            _maxOutputSize / 1024.0); 

                int currentLen = _stringBuilder.Length;
                int messageLen = message.Length;
                int spaceForMessage = _maxOutputSize - currentLen;

                if (messageLen <= spaceForMessage) { 
                    _stringBuilder.Append(message); 
                } else {
                    int targetContentLength = _maxOutputSize - messageLen;
                    if (targetContentLength < 0) { 
                        _stringBuilder.Length = 0; 
                        _stringBuilder.Append(message, 0, Math.Min(messageLen, _maxOutputSize));
                    } else {
                        if (currentLen > targetContentLength) {
                            _stringBuilder.Length = targetContentLength;
                        }
                        _stringBuilder.Append(message); 
                    }
                }
                if (_stringBuilder.Length > _maxOutputSize) _stringBuilder.Length = _maxOutputSize;
            }
        }
        return _stringBuilder.ToString();
    }

    private bool ShouldSkipType(Type type)
    {
        if (type.FullName.StartsWith("Serilog") || type.FullName.StartsWith("System.Threading"))
            return true;

        if (typeof(Delegate).IsAssignableFrom(type))
            return true;
        
        return typeof(Type).IsAssignableFrom(type) ||
               typeof(MemberInfo).IsAssignableFrom(type) ||
               // Add other specific reflection types if needed, e.g., Assembly, Module
               // Be careful not to filter out types you might want to see, like enums if they happen to be Type instances.
               // The IsPrimitive check already handles enums correctly.
               type.FullName != null && (type.FullName.StartsWith("System.Reflection.") || type.FullName.StartsWith("System.Runtime"));
    }


    private void WriteObjectRecursive(object obj)
    {
        if (_outputTruncated) return;

        if (obj == null)
        {
            this.Write(_indentation);
            this.Write("null");
            return;
        }

        Type type = obj.GetType();

        // *** NEW: Skip dumping the *object itself* if it's a reflection type, unless it's a primitive/string ***
        // (Enums are handled by IsPrimitive)
        if (!this.IsPrimitive(type) && this.ShouldSkipType(type))
        {
            this.Write(_indentation);
            this.Write($"{ReflectionTypeSkippedMessage} ({type.Name})");
            return;
        }

        if (this.IsPrimitive(type)) // IsPrimitive check is now after the ShouldSkipReflectionType for the object itself
        {
            this.Write(_indentation);
            this.WritePrimitiveValue(obj);
            return;
        }

        if (_visitedObjects.Contains(obj))
        {
            this.Write(_indentation);
            this.Write($"[Circular Reference: {type.Name}]");
            return;
        }

        if (_currentDepth >= _maxDepth)
        {
            this.Write(_indentation);
            this.Write($"[Max Depth Reached ({_maxDepth}): {type.Name}]");
            return;
        }

        _visitedObjects.Add(obj);
        _currentDepth++;
        string parentIndentation = _indentation;

        this.Write(_indentation);

        _indentation += "    ";
        if (_outputTruncated) { _indentation = parentIndentation; _currentDepth--; _visitedObjects.Remove(obj); return; }

        if (obj is IEnumerable enumerable && !(obj is string))
        {
            this.Write($"{type.Name} [");
            this.WriteLine();
            this.WriteEnumerableItems(enumerable);
            if (!_outputTruncated) {
                this.Write(parentIndentation);
                this.WriteLine("]"); }
        }
        else
        {
            this.Write($"{type.Name} {{");
            this.WriteLine();
            this.WriteComplexObjectMembers(obj); // This method will now filter properties
            if (!_outputTruncated) {
                this.Write(parentIndentation);
                this.WriteLine("}}"); }
        }

        _indentation = parentIndentation;
        _currentDepth--;
        _visitedObjects.Remove(obj);
    }

    private void WritePrimitiveValue(object obj)
    {
        // ... (same as previous aggressive version)
        if (_outputTruncated) return;

        if (obj is string str)
        {
            if (str.Length > _maxStringLengthPerProperty)
            {
                this.Write($"\"{str.Substring(0, _maxStringLengthPerProperty)}{StringPropertyTooLargeMessage}\"");
            }
            else
            {
                this.Write($"\"{str}\"");
            }
        }
        else if (obj is DateTime dt)
        {
            this.Write(dt.ToString("o"));
        }
        else if (obj is bool b)
        {
            this.Write(b.ToString().ToLower());
        }
        else if (obj != null)
        {
            string valStr;
            try {
                valStr = obj.ToString();
            } catch (Exception ex) {
                valStr = $"[Error in ToString(): {ex.Message}]";
                 _individualValueTruncated = true;
            }

            if (valStr.Length > _maxStringLengthPerProperty)
            {
                this.Write($"{valStr.Substring(0, _maxStringLengthPerProperty)}[Value Truncated: Max len {_maxStringLengthPerProperty}]");
                 _individualValueTruncated = true;
            }
            else
            {
                this.Write(valStr);
            }
        }
         else
        {
            this.Write("null");
        }
    }

    private void WriteEnumerableItems(IEnumerable enumerable)
    {
        // ... (same as previous aggressive version)
        if (_outputTruncated) return;
        int itemCount = 0;
        long? totalCount = null;
        if (enumerable is ICollection coll) totalCount = coll.Count;

        foreach (object item in enumerable)
        {
            if (_outputTruncated) return;
            if (itemCount >= _maxEnumerableItems)
            {
                this.Write(_indentation);
                string msg = totalCount.HasValue
                    ? $"... [{totalCount.Value - _maxEnumerableItems} more items from {totalCount.Value} total (max: {_maxEnumerableItems})]"
                    : $"... [more items truncated after {_maxEnumerableItems}]";

                this.WriteLine(msg);
                return;
            }

            this.WriteObjectRecursive(item);
            if (!_outputTruncated) {
                this.WriteLine(","); }
            itemCount++;
        }
    }

    private void WriteComplexObjectMembers(object obj)
    {
        if (_outputTruncated) return;
        Type type = obj.GetType();
        BindingFlags bindingFlags = BindingFlags.Instance | BindingFlags.Public;
        if (_includePrivate) bindingFlags |= BindingFlags.NonPublic;

        var membersToProcess = new List<MemberInfo>();
        try
        {
            PropertyInfo[] properties = type.GetProperties(bindingFlags);
            foreach (PropertyInfo prop in properties)
            {
                // *** NEW: Filter out properties of reflection types ***
                if (prop.CanRead && prop.GetIndexParameters().Length == 0 &&
                    !this.ShouldSkipType(prop.PropertyType)) // <<<--- ADDED THIS CHECK
                {
                    membersToProcess.Add(prop);
                }
            }

            if (_includeFields)
            {
                FieldInfo[] fields = type.GetFields(bindingFlags);
                foreach (FieldInfo field in fields)
                {
                    // *** NEW: Filter out fields of reflection types ***
                    if (!this.ShouldSkipType(field.FieldType)) // <<<--- ADDED THIS CHECK
                    {
                        bool isBackingField = Attribute.IsDefined(field, typeof(CompilerGeneratedAttribute)) &&
                                              field.Name.StartsWith("<") && field.Name.EndsWith(">k__BackingField");
                        if (isBackingField)
                        {
                            int endIndex = field.Name.IndexOf('>');
                            if (endIndex > 1 && membersToProcess.OfType<PropertyInfo>().Any(p => p.Name == field.Name.Substring(1, endIndex - 1)))
                                continue;
                        }
                        membersToProcess.Add(field);
                    }
                }
            }
        }
        catch (Exception refEx)
        {
            if (!_outputTruncated)
                this.WriteLine($"{_indentation}[Reflection Error: {refEx.Message}]");
            _individualValueTruncated = true;
            return;
        }

        IOrderedEnumerable<MemberInfo> sortedMembers = membersToProcess.OrderBy(m => m.Name);
        foreach (MemberInfo member in sortedMembers)
        {
            if (_outputTruncated) return;
            this.Write(_indentation);
            this.Write(member.Name);
            this.WriteLine(":");
            if (_outputTruncated) return;

            try
            {
                object value = member switch
                {
                    PropertyInfo pi => pi.GetValue(obj),
                    FieldInfo fi => fi.GetValue(obj),
                    _ => null
                };

                this.WriteObjectRecursive(value); // This will now also skip if 'value' itself is a ReflectionType
            }
            catch (Exception ex)
            {
                string errorMsg = $"[Error reading member '{member.Name}': {ex.GetType().Name}]";
                if (ex.InnerException != null) errorMsg += $" (Inner: {ex.InnerException.GetType().Name})";
                this.WriteObjectRecursive(errorMsg);
                _individualValueTruncated = true;
            }
            if (!_outputTruncated) {
                this.WriteLine(","); }
        }
    }

    private bool IsPrimitive(Type type)
    {
        // ... (same as previous aggressive version)
        return type.IsPrimitive || type == typeof(string) || type == typeof(decimal) ||
               type == typeof(DateTime) || type == typeof(DateTimeOffset) || type == typeof(TimeSpan) ||
               type == typeof(Guid) || type.IsEnum;
    }
}