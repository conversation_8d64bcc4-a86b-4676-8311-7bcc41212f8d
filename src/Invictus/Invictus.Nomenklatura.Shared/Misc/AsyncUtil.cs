namespace Invictus.Nomenklatura.Misc;

public sealed class WhenAllCancellationTokenRegistration : IDisposable
{
    private readonly CancellationTokenRegistration[] _registrations;

    private readonly CancellationTokenSource _target;
    private readonly CancellationToken[] _sources;

    public WhenAllCancellationTokenRegistration(CancellationTokenSource target, params CancellationToken[] sources)
    {
        _target = target;
        _sources = sources;

        _registrations = new CancellationTokenRegistration[sources.Length];

        for (int i = 0; i < _registrations.Length; i++)
        {
            _registrations[i] = sources[i].UnsafeRegister(this.WhenOneCancelled, this);
        }
    }

    private void WhenOneCancelled(object obj)
    {
        if (_sources.All(ct => ct.IsCancellationRequested))
            _target.Cancel();
    }

    public void Dispose()
    {
        GC.SuppressFinalize(this);

        foreach (CancellationTokenRegistration cancellationTokenRegistration in _registrations)
        {
            cancellationTokenRegistration.Dispose();
        }
    }

    ~WhenAllCancellationTokenRegistration()
    {
        throw new Exception("This should have been disposed.");
    }
}

public class AsyncUtil
{
    public static void RunSync(Func<Task> task)
    {
        SynchronizationContext oldContext = SynchronizationContext.Current;
        var syncContext = new ExclusiveSynchronizationContext();
        SynchronizationContext.SetSynchronizationContext(syncContext);

        // ReSharper disable once AsyncVoidLambda
        syncContext.Post(async _ =>
        {
            try
            {
                await task();
            }
            catch (Exception e)
            {
                syncContext.InnerException = e;
                throw;
            }
            finally
            {
                syncContext.EndMessageLoop();
            }
        }, null);

        syncContext.BeginMessageLoop();

        SynchronizationContext.SetSynchronizationContext(oldContext);
    }

    public static T RunSync<T>(Func<Task<T>> task)
    {
        SynchronizationContext oldContext = SynchronizationContext.Current;
        var syncContext = new ExclusiveSynchronizationContext();
        SynchronizationContext.SetSynchronizationContext(syncContext);
        T result = default;

        // ReSharper disable once AsyncVoidLambda
        syncContext.Post(async _ =>
        {
            try
            {
                result = await task();
            }
            catch (Exception e)
            {
                syncContext.InnerException = e;
                throw;
            }
            finally
            {
                syncContext.EndMessageLoop();
            }
        }, null);

        syncContext.BeginMessageLoop();

        SynchronizationContext.SetSynchronizationContext(oldContext);

        return result;
    }

    private class ExclusiveSynchronizationContext : SynchronizationContext
    {
        private readonly AutoResetEvent _workItemsWaiting = new AutoResetEvent(false);
        private readonly Queue<Tuple<SendOrPostCallback, object>> _items = new ();

        private bool _isDone;

        public Exception InnerException { get; set; }

        public override void Send(SendOrPostCallback d, object state)
        {
            throw new NotSupportedException("We cannot send to our same thread");
        }

        public override void Post(SendOrPostCallback d, object state)
        {
            lock (_items)
            {
                _items.Enqueue(Tuple.Create(d, state));
            }

            _workItemsWaiting.Set();
        }

        public void EndMessageLoop()
        {
            this.Post(_ => _isDone = true, null);
        }

        public void BeginMessageLoop()
        {
            while (!_isDone)
            {
                Tuple<SendOrPostCallback, object> task = null;
                lock (_items)
                {
                    if (_items.Count > 0)
                    {
                        task = _items.Dequeue();
                    }
                }

                if (task != null)
                {
                    task.Item1(task.Item2);

                    if (InnerException != null)
                    {
                        throw new AggregateException("AsyncHelpers.Run method threw an exception.", InnerException);
                    }
                }
                else
                {
                    _workItemsWaiting.WaitOne();
                }
            }
        }

        public override SynchronizationContext CreateCopy()
        {
            return this;
        }
    }
}