namespace Invictus.Nomenklatura.Misc;

public interface IGetTimeToWait
{
    /// <summary>
    /// In case minus delta between calls is less than the time that has passed between the calls - it is not guaranteed to be processed in time. <br/>
    /// See also <seealso cref="DynamicPriorityTimedQueue{T}.RECHECK_PRIORITIES_MS"/>
    /// </summary>
    TimeSpan GetTimeToWait();
}

/// <summary>
/// This collection is thread safe for multiple producers but one consumer.
/// </summary>
public class DynamicPriorityTimedQueue<T>
    where T : class, IGetTimeToWait
{
    private const int RECHECK_PRIORITIES_MS = 4000;

    private record Item(T Value, long Priority)
    {
        public DateTimeOffset EnqueueTimeUtc { get; } = ServerClock.GetCurrentUtcTime();
    }

    private readonly record struct ItemWithCurTimeToWait(Item Item, TimeSpan TimeToWait);

    private readonly List<Item> _items = new ();
    private readonly object _syncRoot = new();
    private readonly ManualResetEventSlim _enqueueEvent = new (true);
        
    public int Count
    {
        get
        {
            lock (_syncRoot)
            {
                return _items.Count(i => i != null);
            }
        }
    }

    public void Enqueue(T value, long priority)
    {
        ArgumentNullException.ThrowIfNull(value);

        var item = new Item(value, priority);

        lock (_syncRoot)
        {
            _enqueueEvent.Set();

            for (int i = 0; i < _items.Count; i++)
            {
                if (_items[i] == null)
                {
                    _items[i] = item;
                    return;
                }
            }

            _items.Add(item);
        }
    }

    private Item GetNextItem(out TimeSpan toWaitForThisItem)
    {
        toWaitForThisItem = TimeSpan.Zero;

        lock (_syncRoot) // PERF
        {
            if (_items.Count(i => i != null) == 0)
                return null;

            // PERF
            ItemWithCurTimeToWait[] itemsWithWaitTimes = _items.Where(i => i != null)
                .OrderByDescending(i => i.Priority) // GetTimeToWait() will return less as we iterate over this so keep prioritized for the last calls.
                .Select(i => new ItemWithCurTimeToWait(i, i.Value.GetTimeToWait()))
                .ToArray();

            TimeSpan minTimeToWait = itemsWithWaitTimes.Min(i => i.TimeToWait);

            ItemWithCurTimeToWait[] withMinTimeToWait = itemsWithWaitTimes
                .Where(i => i.TimeToWait == minTimeToWait)
                .ToArray();

            long minPriority = withMinTimeToWait.Min(i => i.Item.Priority);

            ItemWithCurTimeToWait[] withMinPriority = itemsWithWaitTimes
                .Where(i => i.Item.Priority == minPriority)
                .ToArray();

            DateTimeOffset minQueueTimeUtc = withMinPriority.Min(i => i.Item.EnqueueTimeUtc);

            ItemWithCurTimeToWait item = withMinPriority.Last(i => i.Item.EnqueueTimeUtc == minQueueTimeUtc);

            toWaitForThisItem = minTimeToWait;

            return item.Item;
        }
    }

    private void DequeueItem(Item item)
    {
        lock (_syncRoot)
        {
            for (int i = 0; i < _items.Count; i++)
            {
                if (_items[i] == item)
                {
                    _items[i] = null;
                    return;
                }
            }
        }

        throw new Exception("Item not found.");
    }

    /// <summary>
    /// Returns null in case of cancellation.
    /// </summary>
    public T Take(CancellationToken ct)
    {
    _again:
        if (ct.IsCancellationRequested)
            return null;

        Item nextItem = this.GetNextItem(out TimeSpan toWaitForThisItem);

        if (nextItem == null)
        {
            WaitHandle.WaitAny(new[] { ct.WaitHandle, _enqueueEvent.WaitHandle }, RECHECK_PRIORITIES_MS);

            _enqueueEvent.Reset();

            goto _again;
        }

        if (toWaitForThisItem > TimeSpan.Zero)
        {
            WaitHandle.WaitAny(new[] { ct.WaitHandle, _enqueueEvent.WaitHandle }, Math.Min((int)toWaitForThisItem.TotalMilliseconds, RECHECK_PRIORITIES_MS));

            _enqueueEvent.Reset();

            goto _again;
        }

        this.DequeueItem(nextItem);

        _enqueueEvent.Reset();

        return nextItem.Value;
    }

    public T[] ToArray()
    {
        lock (_syncRoot)
        {
            return _items.Where(i => i != null).Select(i => i.Value).ToArray();
        }
    }
}