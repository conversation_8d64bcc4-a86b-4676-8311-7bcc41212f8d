using Microsoft.Extensions.DependencyInjection;

namespace Invictus.Nomenklatura.Misc;

public interface IProvider<T>
{
    T Create();
}

public interface IServiceFactory<out TDecorated>
{
    TDecorated Create();
    TDecorated Create(params object[] addOrOverrideMoreArguments);
}

// ReSharper disable once TypeParameterCanBeVariant
public interface ILazyResolve<TService>
{
    TService Resolve();
}

public class LazyResolveOnce<TService> : ILazyResolve<TService>
{
    private IServiceProvider _serviceProvider;
    private TService _service;

    public LazyResolveOnce(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public TService Resolve()
    {
        if (_service != null)
            return _service;

        _service = _serviceProvider.GetRequiredService<TService>();
        _serviceProvider = null;

        return _service;
    }
}

public interface IServiceScopesInternal
{
    object GetScopedObjectOfType(Type t);
}

public interface IFactoryScope : IDisposable
{
    void AddService<T>(T val);
}

public interface IServiceScopes
{
    IFactoryScope EnterFactoryScope();
}

public class ServiceScopes : IServiceScopes, IServiceScopesInternal
{
    protected readonly ThreadLocal<Scope> _currentScope = new();

    public IFactoryScope EnterFactoryScope()
    {
        if (_currentScope.Value != null)
            throw new Exception("Already in scope.");

        _currentScope.Value = new Scope(this);

        return _currentScope.Value;
    }

    public object GetScopedObjectOfType(Type t)
    {
        return _currentScope.Value?.GetScopedObjectOfType(t);
    }

    protected class Scope : IFactoryScope
    {
        private readonly List<object> _scopedObjects = new();
        private readonly ServiceScopes _scopes;

        public object GetScopedObjectOfType(Type t)
        {
            return _scopedObjects.SingleOrDefault(obj => obj.GetType() == t);
        }

        public Scope(ServiceScopes scopes)
        {
            _scopes = scopes;
        }

        public void Dispose()
        {
            _scopes._currentScope.Value = null;
        }

        public void AddService<T>(T val)
        {
            _scopedObjects.Add(val);
        }
    }
}