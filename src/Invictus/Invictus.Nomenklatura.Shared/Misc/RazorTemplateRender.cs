using System.Xml;
using System.Xml.Linq;

using Microsoft.AspNetCore.Html;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.Razor;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.ViewEngines;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.DependencyInjection;

namespace Invictus.Nomenklatura.Misc;

public class RazorTemplateRender
{
    private readonly IRazorViewEngine _viewEngine;
    private readonly ITempDataProvider _tempDataProvider;
    private readonly IServiceScopeFactory _serviceScopeFactory;

    public RazorTemplateRender(
        IRazorViewEngine viewEngine,
        ITempDataProvider tempDataProvider,
        IServiceScopeFactory serviceScopeFactory)
    {
        _viewEngine = viewEngine;
        _tempDataProvider = tempDataProvider;
        _serviceScopeFactory = serviceScopeFactory;
    }

    public string Render<TModel>(string name, TModel model, StringBuilder reusableStringBuilder)
    {
        this.RenderRaw(name, model, reusableStringBuilder);
        this.RazorOutputToHumanText(reusableStringBuilder);

        return reusableStringBuilder.ToString();
    }

    public static Task<IHtmlContent> RenderPartial<TModel, TPartialModel>(IHtmlHelper<TModel> currentHtml, string name, TPartialModel model)
    {
        return currentHtml.PartialAsync(name, model);
    }

    public static ViewDataDictionary<TModel> GetViewDataDictionary<TModel>(TModel model)
    {
        return new ViewDataDictionary<TModel>(
            metadataProvider: new EmptyModelMetadataProvider(),
            modelState: new ModelStateDictionary()
        )
        {
            Model = model
        };
    }

    private void RenderRaw<TModel>(string name, TModel model, StringBuilder reusableStringBuilder)
    {
        using (IServiceScope scope = _serviceScopeFactory.CreateScope())
        {
            ActionContext actionContext = this.GetActionContext(scope.ServiceProvider);
                
            ViewEngineResult viewEngineResult = _viewEngine.FindView(actionContext, name, false);

            if (!viewEngineResult.Success)
            {
                throw new Exception($"Couldn't find view '{name}'");
            }

            IView view = viewEngineResult.View;

            reusableStringBuilder.Clear();
            reusableStringBuilder.AppendLine("<root>");

            using (var output = new StringWriter(reusableStringBuilder))
            {
                var viewContext = new ViewContext(
                    actionContext,
                    view,
                    GetViewDataDictionary(model),
                    new TempDataDictionary(
                        actionContext.HttpContext,
                        _tempDataProvider
                    ),
                    output,
                    new HtmlHelperOptions()
                );

                view.RenderAsync(viewContext).GetAwaiter().GetResult();
            }

            reusableStringBuilder.AppendLine("</root>");
        }
    }

    private ActionContext GetActionContext(IServiceProvider serviceProvider)
    {
        var httpContext = new DefaultHttpContext();
        httpContext.RequestServices = serviceProvider;
        return new ActionContext(httpContext, new RouteData(), new ActionDescriptor());
    }

    /*
    private sealed class NoHttpContext : HttpContext
    {
        public override void Abort()
        {
            throw new NotImplementedException();
        }

        public override IFeatureCollection Features { get; }
        public override HttpRequest Request { get; }
        public override HttpResponse Response { get; }
        public override ConnectionInfo Connection { get; }
        public override WebSocketManager WebSockets { get; }
        public override ClaimsPrincipal User { get; set; }
        public override IDictionary<object, object> Items { get; set; }
        public override IServiceProvider RequestServices { get; set; }
        public override CancellationToken RequestAborted { get; set; }
        public override string TraceIdentifier { get; set; }
        public override ISession Session { get; set; }
    }
    */

    private void RazorOutputToHumanText(StringBuilder reusableStringBuilder)
    {
        XDocument xDocument = XDocument.Load(new StringReader(reusableStringBuilder.ToString()));

        reusableStringBuilder.Clear();

        this.BuildHumanTextRecursive(xDocument.Root, reusableStringBuilder);
    }

    private void BuildHumanTextRecursive(XContainer xParentContainer, StringBuilder reusableStringBuilder)
    {
        XNode[] parentContainerNodes = xParentContainer.Nodes().ToArray();

        bool parentContainerHasElements = parentContainerNodes.Any(n => n.NodeType == XmlNodeType.Element);

        foreach (XNode xNode in parentContainerNodes)
        {
            switch (xNode.NodeType)
            {
                case XmlNodeType.Element:
                    var xElement = (XElement)xNode;
                    switch (xElement.Name.LocalName.ToLowerInvariant())
                    {
                        case "p":
                            this.BuildHumanTextRecursive(xElement, reusableStringBuilder);
                            reusableStringBuilder.AppendLine();
                            break;
                        case "s":
                            this.BuildHumanTextRecursive(xElement, reusableStringBuilder);
                            break;
                        case "br":
                            reusableStringBuilder.AppendLine();
                            break;
                        default: throw new Exception("Invalid element: " + xElement.Name.LocalName);
                    }
                    break;
                case XmlNodeType.Text:
                    var xText = (XText)xNode;

                    bool trim = parentContainerHasElements; // xNode.Parent != null && !(xNode.Parent.Name.LocalName == "p" || xNode.Parent.Name.LocalName == "s");

                    reusableStringBuilder.Append(trim ? xText.Value.Trim('\r', '\n', ' ', '\t') : xText.Value);
                    break;
                case XmlNodeType.EndElement:
                    break;
                case XmlNodeType.Comment:
                    break;
                default:
                    throw new NotImplementedException("Node type: " + xNode.NodeType);
            }
        }
    }
}