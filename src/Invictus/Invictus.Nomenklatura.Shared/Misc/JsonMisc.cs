using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Invictus.Nomenklatura.Misc;

public class InvJsonSerializer
{
    public string SerializeForApis(object obj, Type objectType)
    {
        JsonSerializerOptions settings = this.GetSettingsForApis();

        string json = JsonSerializer.Serialize(obj, objectType, settings);

        return json;
    }

    public string SerializeForInternals(object obj, Type objectType)
    {
        JsonSerializerOptions settings = this.GetSettingsForInternals();

        string json = JsonSerializer.Serialize(obj, objectType, settings);

        return json;
    }
    
    public T DeserializeForApis<T>(string text)
    {
        JsonSerializerOptions settings = this.GetSettingsForApis();

        return JsonSerializer.Deserialize<T>(text, settings);
    }

    public T DeserializeForInternals<T>(string text)
    {
        JsonSerializerOptions settings = this.GetSettingsForInternals();
        
        return JsonSerializer.Deserialize<T>(text, settings);
    }
    
    public T DeserializeForInternals<T>(Stream stream)
    {
        JsonSerializerOptions settings = this.JsonSerializerSettings();
        settings.ReferenceHandler = ReferenceHandler.Preserve;
        settings.WriteIndented = true;
        settings.Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
        
        return JsonSerializer.Deserialize<T>(stream, settings);
    }

    public JsonSerializerOptions GetSettingsForApis()
    {
        JsonSerializerOptions settings = this.JsonSerializerSettings();
        settings.ReferenceHandler = ReferenceHandler.IgnoreCycles;
        settings.WriteIndented = false;

        return settings;
    }
    
    public JsonSerializerOptions GetSettingsForApisNonStrict()
    {
        JsonSerializerOptions settings = this.JsonSerializerSettings();
        settings.ReferenceHandler = ReferenceHandler.IgnoreCycles;
        settings.WriteIndented = false;
        settings.UnmappedMemberHandling = JsonUnmappedMemberHandling.Skip;

        return settings;
    }

    public JsonSerializerOptions GetSettingsForInternals()
    {
        JsonSerializerOptions settings = this.JsonSerializerSettings();
        settings.ReferenceHandler = ReferenceHandler.Preserve;
        settings.WriteIndented = true;
        settings.Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping;

        return settings;
    }

    
    protected virtual JsonSerializerOptions JsonSerializerSettings()
    {
        var options = new JsonSerializerOptions
        {
            AllowTrailingCommas = false,
            DefaultBufferSize = 4096,
            DefaultIgnoreCondition = JsonIgnoreCondition.Never,
            MaxDepth = 32,
            PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower,
            DictionaryKeyPolicy = JsonNamingPolicy.SnakeCaseLower,
            IgnoreReadOnlyFields = true,
            IgnoreReadOnlyProperties = true,
            NumberHandling = JsonNumberHandling.Strict,
            ReadCommentHandling = JsonCommentHandling.Skip,
            IncludeFields = false,
            PropertyNameCaseInsensitive = false,
            UnmappedMemberHandling = JsonUnmappedMemberHandling.Disallow
        };

        JsonConverter[] customJsonConverters = Array.Empty<JsonConverter>();

        List<JsonConverter> converters = this.GetJsonConverters(customJsonConverters);

        foreach (JsonConverter jsonConverter in converters)
        {
            options.Converters.Add(jsonConverter);
        }

        return options;
    }

    protected virtual List<JsonConverter> GetJsonConverters(IReadOnlyList<JsonConverter> customConverters)
    {
        var converters = new List<JsonConverter>();

        converters.AddRange(customConverters);

        return converters;
    }
    
    public static string TryPrettifyJson(string json)
    {
        try
        {
            if (string.IsNullOrEmpty(json)) 
                return json;
   
            if (!json.StartsWith("{") && !json.StartsWith("["))
                return json;
            
            return PrettifyJson(json);
        }
        catch (JsonException)
        {
            return json;
        }
    }
    
    public static string PrettifyJson(string jsonString)
    {
        // Parse the JSON string
        using JsonDocument document = JsonDocument.Parse(jsonString);

        // Create options for formatting
        var options = new JsonSerializerOptions
        {
            WriteIndented = true,                                  // Enable pretty printing
            Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping, // Don't escape Unicode characters
            PropertyNamingPolicy = null                            // Preserve property names as-is
        };

        // Serialize back to string with formatting
        return JsonSerializer.Serialize(document, options);
    }
}

public class StringEnumConverter<T> : JsonConverter<T> 
    where T : struct, Enum
{
    public override T Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        // Read the string value and convert to enum
        string enumString = reader.GetString();
        return (T)Enum.Parse(typeof(T), enumString, true);
    }

    public override void Write(Utf8JsonWriter writer, T value, JsonSerializerOptions options)
    {
        // Write the enum value as a string
        writer.WriteStringValue(value.ToString());
    }
}

public class StringEnumNullConverter<T> : JsonConverter<T?> 
    where T : struct, Enum
{
    public override T? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        // Read the string value and convert to enum
        string enumString = reader.GetString();
        if (enumString == null)
            return null;
        return (T)Enum.Parse(typeof(T), enumString, true);
    }

    public override void Write(Utf8JsonWriter writer, T? value, JsonSerializerOptions options)
    {
        // Write the enum value as a string
        if (value == null)
            writer.WriteNullValue();
        else
            writer.WriteStringValue(value.ToString());
    }
}

[AttributeUsage(AttributeTargets.Property)]
public class JsonGreatestEverDateTimeConverterAttribute : JsonConverterAttribute
{
    private readonly string _format;

    public JsonGreatestEverDateTimeConverterAttribute(string format) => _format = format;

    public override JsonConverter CreateConverter(Type typeToConvert)
    {
        if (typeToConvert != typeof(DateTime) && typeToConvert != typeof(DateTime?))
            throw new ArgumentException("This converter only works with DateTime.");

        return new GreatestEverDateTimeConverter(_format);
    }
}

public class GreatestEverDateTimeConverter : JsonConverter<DateTime>
{
    private readonly string _format;

    public GreatestEverDateTimeConverter(string format)
    {
        _format = format;
    }

    public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        // Parse the string to DateTime using the custom format
        string dateString = reader.GetString();
        return DateTime.ParseExact(dateString, _format, CultureInfo.InvariantCulture);
    }

    public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
    {
        // Write the DateTime as a string using the custom format
        writer.WriteStringValue(value.ToString(_format, CultureInfo.InvariantCulture));
    }
}

public class RawJsonConverter : JsonConverter<string>
{
    public override string Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        // Read the JSON as a raw string
        using JsonDocument document = JsonDocument.ParseValue(ref reader);

        return document.RootElement.GetRawText();
    }

    public override void Write(Utf8JsonWriter writer, string value, JsonSerializerOptions options)
    {
        // Write the raw JSON value from the string
        using JsonDocument document = JsonDocument.Parse(value);

        document.WriteTo(writer);
    }
}

public class DictionaryAsArrayConverter<TKey, TValue> : JsonConverter<Dictionary<TKey, TValue>>
{
    public override Dictionary<TKey, TValue> Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        var dictionary = (Dictionary<TKey, TValue>)Activator.CreateInstance(typeToConvert);

        if (reader.TokenType != JsonTokenType.StartArray)
        {
            throw new JsonException("Expected StartArray token");
        }

        while (reader.Read())
        {
            if (reader.TokenType == JsonTokenType.EndArray)
            {
                break;
            }

            if (reader.TokenType != JsonTokenType.StartObject)
            {
                throw new JsonException("Expected StartObject token");
            }

            TKey key = default;
            TValue value = default;

            while (reader.Read())
            {
                if (reader.TokenType == JsonTokenType.EndObject)
                {
                    break;
                }

                if (reader.TokenType == JsonTokenType.PropertyName)
                {
                    string propertyName = reader.GetString();
                    reader.Read();

                    if (propertyName == "Key")
                    {
                        key = JsonSerializer.Deserialize<TKey>(ref reader, options);
                    }
                    else if (propertyName == "Value")
                    {
                        value = JsonSerializer.Deserialize<TValue>(ref reader, options);
                    }
                }
            }

            if (key != null)
            {
                dictionary.Add(key, value);
            }
        }

        return dictionary;
    }

    public override void Write(Utf8JsonWriter writer, Dictionary<TKey, TValue> value, JsonSerializerOptions options)
    {
        writer.WriteStartArray();

        foreach (KeyValuePair<TKey, TValue> kvp in value)
        {
            writer.WriteStartObject();
            writer.WritePropertyName("Key");
            JsonSerializer.Serialize(writer, kvp.Key, options);
            writer.WritePropertyName("Value");
            JsonSerializer.Serialize(writer, kvp.Value, options);
            writer.WriteEndObject();
        }

        writer.WriteEndArray();
    }
}

public class DictionaryAsArrayWithComparerConverter<TKey, TValue, TComparer> : JsonConverter<Dictionary<TKey, TValue>>
    where TComparer : IEqualityComparer<TKey>, new()
{
    public override Dictionary<TKey, TValue> Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        var dictionary = (Dictionary<TKey, TValue>)Activator.CreateInstance(typeToConvert, new TComparer());

        if (reader.TokenType != JsonTokenType.StartArray)
        {
            throw new JsonException("Expected StartArray token");
        }

        while (reader.Read())
        {
            if (reader.TokenType == JsonTokenType.EndArray)
            {
                break;
            }

            if (reader.TokenType != JsonTokenType.StartObject)
            {
                throw new JsonException("Expected StartObject token");
            }

            TKey key = default;
            TValue value = default;

            while (reader.Read())
            {
                if (reader.TokenType == JsonTokenType.EndObject)
                {
                    break;
                }

                if (reader.TokenType == JsonTokenType.PropertyName)
                {
                    string propertyName = reader.GetString();
                    reader.Read();

                    if (propertyName == "Key")
                    {
                        key = JsonSerializer.Deserialize<TKey>(ref reader, options);
                    }
                    else if (propertyName == "Value")
                    {
                        value = JsonSerializer.Deserialize<TValue>(ref reader, options);
                    }
                }
            }

            if (key != null)
            {
                dictionary.Add(key, value);
            }
        }

        return dictionary;
    }

    public override void Write(Utf8JsonWriter writer, Dictionary<TKey, TValue> value, JsonSerializerOptions options)
    {
        writer.WriteStartArray();

        foreach (KeyValuePair<TKey, TValue> kvp in value)
        {
            writer.WriteStartObject();
            writer.WritePropertyName("Key");
            JsonSerializer.Serialize(writer, kvp.Key, options);
            writer.WritePropertyName("Value");
            JsonSerializer.Serialize(writer, kvp.Value, options);
            writer.WriteEndObject();
        }

        writer.WriteEndArray();
    }
}