namespace Invictus.Nomenklatura.Misc;

public class RateLimitCounter
{
    private readonly int _maxCalls;
    private readonly TimeSpan _timePeriod;

    private readonly Queue<DateTimeOffset> _actionTimesUtc = new();

    public RateLimitCounter(int maxCalls, TimeSpan timePeriod)
    {
        if (maxCalls < 1)
            throw new ArgumentException(nameof(maxCalls));

        if (timePeriod < TimeSpan.FromMilliseconds(100))
            throw new AggregateException(nameof(timePeriod));

        _maxCalls = maxCalls;
        _timePeriod = timePeriod;
    }

    public void RegisterActionIrregardless() // Regardless + not Regardless = Irregardless?
    {
        _actionTimesUtc.Enqueue(ServerClock.GetCurrentUtcTime());
    }

    public TimeSpan GetWaitTime()
    {
        DateTimeOffset nowUtc = ServerClock.GetCurrentUtcTime();
        DateTimeOffset cutoff = nowUtc - _timePeriod;

        DateTimeOffset firstActionTimeUtc;

        while (_actionTimesUtc.TryPeek(out firstActionTimeUtc))
        {
            if (firstActionTimeUtc < cutoff)
                _actionTimesUtc.Dequeue();
            else
                break;
        }

        if (_actionTimesUtc.Count < _maxCalls)
        {
            return TimeSpan.Zero;
        }

        DateTimeOffset waitTillUtc = firstActionTimeUtc + _timePeriod;

        TimeSpan toWait = waitTillUtc - nowUtc;

        if (toWait < TimeSpan.Zero)
        {
            return TimeSpan.Zero;
        }

        return toWait;
    }
}