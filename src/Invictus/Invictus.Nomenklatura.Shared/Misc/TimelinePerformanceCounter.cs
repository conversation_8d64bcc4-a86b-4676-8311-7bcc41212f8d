namespace Invictus.Nomenklatura.Misc;

public class TimelinePerformanceCounter
{
    private readonly Func<bool> _executionIsInternal;

    private class BlockContainer
    {
        private readonly Dictionary<string, TimeSpan> _allBlockTimes = new();

        private string _currentBlock;
        private DateTimeOffset _currentBlockStartUtc;

        public void EnterBlock(string blockName)
        {
            lock (this)
            {
                if (_currentBlock != null)
                {
                    if (_currentBlock == blockName)
                        return;
                    _allBlockTimes[_currentBlock] += ServerClock.GetCurrentUtcTime() - _currentBlockStartUtc;
                }

                _allBlockTimes.TryAdd(blockName, TimeSpan.Zero);
                _currentBlock = blockName;
                _currentBlockStartUtc = ServerClock.GetCurrentUtcTime();
            }
        }

        public IDisposable CaptureBlock()
        {
            string blockName = _currentBlock ?? "(null)";

            return new CapturedBlock(this, blockName);
        }

        public (string, float)[] GetReportAndFlush()
        {
            lock (this)
            {
                var res = new (string, float)[_allBlockTimes.Count];

                int i = 0;
                TimeSpan totalTime = TimeSpan.Zero;
                foreach (KeyValuePair<string, TimeSpan> kv in _allBlockTimes.ToArray())
                {
                    res[i++] = (kv.Key, (float)kv.Value.TotalSeconds);

                    totalTime += kv.Value;

                    _allBlockTimes[kv.Key] = TimeSpan.Zero;
                }

                for (int j = 0; j < res.Length; j++)
                {
                    res[j].Item2 /= (float)totalTime.TotalSeconds;
                }

                return res;
            }
        }
    }

    private class CapturedBlock : IDisposable
    {
        private readonly BlockContainer _blockContainer;
        private readonly string _blockName;

        public CapturedBlock(BlockContainer blockContainer, string blockName)
        {
            _blockContainer = blockContainer;
            _blockName = blockName;
        }

        public void Dispose()
        {
            _blockContainer.EnterBlock(_blockName);
        }
    }

    private readonly BlockContainer _internalContainer = new ();
    private readonly BlockContainer _externalContainer = new ();

    public TimelinePerformanceCounter(Func<bool> executionIsInternal)
    {
        _executionIsInternal = executionIsInternal;
    }

    public IDisposable CaptureBlock()
    {
        BlockContainer container = _executionIsInternal()
            ? _internalContainer
            : _externalContainer;

        return container.CaptureBlock();
    }

    public void EnterBlock(string blockName)
    {
        BlockContainer container = _executionIsInternal()
            ? _internalContainer
            : _externalContainer;

        container.EnterBlock(blockName);
    }

    public class Report
    {
        public (string, float)[] InternalExecution { get; init; }
        public (string, float)[] ExternalExecution { get; init; }
    }

    public Report GetReportAndFlush()
    {
        return new Report
        {
            InternalExecution = _internalContainer.GetReportAndFlush(),
            ExternalExecution = _externalContainer.GetReportAndFlush(),
        };
    }
}