using System.Reflection;

namespace Invictus.Nomenklatura.Misc;

public class SimpleTypeComparer : IEqualityComparer<Type>
{
    private static SimpleTypeComparer _Instance;

    public static SimpleTypeComparer Instance => _Instance ??= new SimpleTypeComparer();

    public bool Equals(Type x, Type y)
    {
        return x.Assembly.FullName == y.Assembly.FullName &&
               x.FullName == y.FullName;
    }

    public int GetHashCode(Type obj)
    {
        var hc = new HashCode();
        hc.Add(obj.Assembly.FullName);
        hc.Add(obj.FullName);
        return hc.ToHashCode();
    }
}

public static class TypeResolver
{
    private static List<Assembly> _LoadedAssemblies;

    public static List<Assembly> LoadedAssemblies => _LoadedAssemblies ??= AppDomain.CurrentDomain.GetAssemblies().ToList();
    
    public static Type GetType(string typeName)
    {
        var type = Type.GetType(typeName);

        if (type != null)
            return type;
        
        foreach (Assembly assembly in LoadedAssemblies)
        {
            Type assembliesFoundType = assembly.GetType(typeName);

            if (assembliesFoundType != null)
            {
                if (type != null)
                    throw new Exception($"There are multiple {typeName} types registered");
                type = assembliesFoundType;
            }
        }
        
        if (type == null)
            throw new Exception($"Could not resolve type {typeName}");

        return type;
    }

}

public static class ReflectionUtil
{
    public static IEnumerable<Type> GetTypesImplementingInterface(Type[] types, Type interfaceType)
    {
        foreach (Type type in types)
        {
            bool yes = type.GetInterface(interfaceType.FullName) != null;

            if (yes)
            {
                yield return type;
            }
        }
    }
        
    public static IEnumerable<(Type type, Attribute attr)> GetAllAssemblyTypeAttributes(Assembly assembly, Type[] attributeTypes, string filterByNamespace)
    {
        IEnumerable<(Type type, Attribute attr)> typesAndAttrs = GetAllAssemblyTypeAttributes(assembly, attributeTypes);

        return typesAndAttrs.Where(typeAndAttr => typeAndAttr.type.Namespace.StartsWith(filterByNamespace));
    }

    public static IEnumerable<(Type type, Attribute attr)> GetAllAssemblyTypeAttributes(Assembly assembly, Type[] attributeTypes)
    {
        return from type in assembly.GetTypes()
            from attributeType in attributeTypes
            let attr = type.GetCustomAttribute(attributeType)
            where attr != null
            select (type, attr);
    }

    public static IEnumerable<Type> GetAllInterfaces(this Type type)
    {
        if (type.IsInterface)
            yield return type;

        foreach (Type @interface in type.GetInterfaces())
        {
            foreach (Type interface2 in @interface.GetAllInterfaces())
            {
                yield return interface2;
            }
        }
    }
        
    public static Type[] GetInheritanceChain(this Type type)
    {
        var inheritanceChain = new List<Type>();

        Type current = type;
        while (current.BaseType != null)
        {
            inheritanceChain.Add(current.BaseType);
            current = current.BaseType;
        }

        return inheritanceChain.ToArray();
    }

    public static IEnumerable<Type> GetThisTypeAndInheritanceChain(this Type type)
    {
        return new[] { type }.Concat(type.GetInheritanceChain());
    }

    public static IEnumerable<Type> GetAllTypesBasedOn(Type[] allTypes, Type[] basedOnOneOf, string filterByNamespace)
    {
        IEnumerable<Type> types = GetAllTypesBasedOn(allTypes, basedOnOneOf);

        if (filterByNamespace == null)
            return types;

        return types.Where(type => type.Namespace.StartsWith(filterByNamespace));
    }

    public static IEnumerable<Type> GetAllTypesBasedOn(Type[] allTypes, Type[] basedOnOneOf)
    {
        foreach (Type type in allTypes)
        {
            bool yes = type.GetInheritanceChain().Any(basedOnOneOf.Contains);

            if (yes)
            {
                yield return type;
            }
        }
    }

    public static IEnumerable<Type> GetAllTypesImplementingInterface(Type[] allTypes, Type interfaceType, string filterByNamespace)
    {
        IEnumerable<Type> types = GetAllTypesImplementingInterface(allTypes, interfaceType);

        return types.Where(type => type.Namespace.StartsWith(filterByNamespace));
    }

    public static IEnumerable<Type> GetAllTypesImplementingInterface(Type[] allTypes, Type interfaceType)
    {
        foreach (Type type in allTypes)
        {
            bool yes = type.GetInterface(interfaceType.FullName) != null;

            if (yes)
            {
                yield return type;
            }
        }
    }

    public static MethodInfo GetGenericMethod(Type type, BindingFlags bindingFlags, string name, Type[] parameterTypes)
    {
        MethodInfo[] methods = type.GetMethods(bindingFlags);

        foreach (MethodInfo method in methods.Where(m => m.Name == name))
        {
            Type[] methodParameterTypes = method.GetParameters().Select(p => p.ParameterType).ToArray();

            if (methodParameterTypes.SequenceEqual(parameterTypes, SimpleTypeComparer.Instance))
            {
                return method;
            }
        }

        return null;
    }
}