using System.Collections.ObjectModel;

namespace Invictus.Nomenklatura.Misc;

public interface IFriendGetDto
{
    object Friend_GetDto();
}

public class VoidDisposable : IDisposable
{
    public static VoidDisposable Instance = new();

    private VoidDisposable()
    {
    }

    public void Dispose()
    {
    }
}

public class ActionDisposable : IDisposable
{
    private readonly Action<bool> _dispose;

    public ActionDisposable(Action<bool> dispose)
    {
        _dispose = dispose ?? throw new ArgumentNullException(nameof(dispose));
    }

    public void Dispose()
    {
        this.Dispose(false);

        GC.SuppressFinalize(this);
    }

    public void Dispose(bool finalizer)
    {
        _dispose?.Invoke(finalizer);
    }

    ~ActionDisposable()
    {
        this.Dispose(true);
    }
}

public class ObjRef<T>
{
    public T Value { get; set; }

    public T GetValue()
    {
        return Value;
    }

    public static implicit operator T(ObjRef<T> d)
    {
        return d.Value;
    }

    public static implicit operator ObjRef<T>(T b) => new ObjRef<T> { Value = b };
}

public static class Various
{
    public static readonly string UkraineTimeZoneId;
    public static readonly string UnicodeSentinelCharacter = "\uF5DC"; // Not database friendly
    public static readonly object ObjectSentinel = new ();

    static Various()
    {
        ReadOnlyCollection<TimeZoneInfo> tzInfos = TimeZoneInfo.GetSystemTimeZones();

        string ukraineTimeZoneId = null;

        foreach (TimeZoneInfo timeZoneInfo in tzInfos)
        {
            if (timeZoneInfo.DisplayName.Contains("Kyiv"))
            {
                ukraineTimeZoneId = timeZoneInfo.Id;
            }
        }

        UkraineTimeZoneId = ukraineTimeZoneId ?? throw new Exception("Timezone that contains 'Kyiv' was not found.");
    }

    public static void SetApplicationWideCultureInvariantCulture()
    {
        Thread.CurrentThread.CurrentCulture = CultureInfo.InvariantCulture;

        CultureInfo.DefaultThreadCurrentCulture = CultureInfo.InvariantCulture;
    }

    public static DateTime UtcToUkraineTime(this DateTime dbDateTime)
    {
        TimeZoneInfo timeInfo = TimeZoneInfo.FindSystemTimeZoneById(UkraineTimeZoneId);
        DateTime userTime = TimeZoneInfo.ConvertTimeFromUtc(dbDateTime, timeInfo);

        return userTime;
    }

    public static string ToStringUADateAndTimeWithoutYear(this DateTime localTime)
    {
        return localTime.ToString("dd MMMM HH:mm", CultureHandler.UkrainianCulture);
    }

    public static string ToStringUADateAndTimeShort(this DateTime localTime)
    {
        return localTime.ToString("dd.MM HH:mm", CultureHandler.UkrainianCulture);
    }

    public static string ToStringUADateAndTimeShortSort(this DateTime localTime)
    {
        return localTime.ToString("MM.dd-HH:mm", CultureHandler.UkrainianCulture);
    }

    public static string ToStringUADate(this DateTime localTime)
    {
        return localTime.ToString("dd MMMM yyyy", CultureHandler.UkrainianCulture);
    }

    public static DateTime UnixTimeStampToDateTime(double unixTimeStamp)
    {
        // Unix timestamp is seconds past epoch
        var dateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
        dateTime = dateTime.AddSeconds(unixTimeStamp).ToLocalTime();
        return dateTime;
    }
}