namespace Invictus.Nomenklatura.Misc;

public static class SerializationUtil<T>
{
    private static readonly SerializationContext<T> _SerializationContext = new();

    public static void AddKnownType(Type type)
    {
        _SerializationContext.AddKnownType(type);
    }

    public static byte[] SerializeToBinaryCompressed(T obj)
    {
        return _SerializationContext.SerializeToBinaryCompressed(obj);
    }

    public static T DeserializeFromBinaryCompressed(byte[] bytes)
    {
        return _SerializationContext.DeserializeFromBinaryCompressed(bytes);
    }
}