using System.Reflection;

using ClosedXML.Excel;

namespace Invictus.Nomenklatura.Misc;

public static class XlsxUtil
{
    public readonly record struct Cell(string Text, string Comment);

    public static MemoryStream CreateXlsxFromTemplate(Cell[,] entries)
    {
        var ms = new MemoryStream();

        string resourceName = Assembly.GetExecutingAssembly()
            .GetManifestResourceNames()
            .Single(r => r.StartsWith("irisdropwebservice.Resources.Epicentr.export-template.xlsx"));
        
        using Stream stream = Assembly.GetExecutingAssembly().GetManifestResourceStream(resourceName);
        {
            stream.CopyTo(ms);
        }

        ms.Position = 0;
        
        using (var workbook = new XLWorkbook(ms))
        {
            IXLWorksheet worksheet = workbook.Worksheets.First();
            
            string templateTellReference = $"{GetColumnLetter(1)}{1}";
            
            worksheet.Cell(templateTellReference);

            // Iterate through the 2D array
            for (int row = entries.GetLength(0) - 1; row >= 0; row--)
            {
                for (int col = entries.GetLength(1) - 1; col >= 0; col--)
                {
                    Cell cellEntry = entries[row, col];
                    
                    // Get Excel cell reference (A1, B2, etc.)
                    string cellReference = $"{GetColumnLetter(col + 1)}{row + 1}";

                    IXLCell cell = worksheet.Cell(cellReference);

                    if (row != 0 && col != 0)
                    {
                        worksheet.Cell(templateTellReference).CopyTo(cell);
                    }

                    // Add cell value if not empty
                    if (!string.IsNullOrEmpty(cellEntry.Text))
                    {
                        cell.Value = cellEntry.Text;
                    }

                    // Add comment if not empty
                    if (!string.IsNullOrEmpty(cellEntry.Comment))
                    {
                        cell.GetComment().AddText(cellEntry.Comment);
                    } else
                    {
                        cell.GetComment().Delete();
                    }
                }
            }

            // Save to memory stream
            workbook.Save();
        }

        // Reset stream position
        ms.Position = 0;
        return ms;
    }

    private static string GetColumnLetter(int columnNumber)
    {
        string columnName = string.Empty;
        while (columnNumber > 0)
        {
            int modulo = (columnNumber - 1) % 26;
            columnName = Convert.ToChar(65 + modulo) + columnName;
            columnNumber = (columnNumber - modulo) / 26;
        }
        return columnName;
    }
}