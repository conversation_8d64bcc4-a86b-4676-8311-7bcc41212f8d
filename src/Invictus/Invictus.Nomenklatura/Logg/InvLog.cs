using Serilog.Core;

namespace Invictus.Nomenklatura.Logg;

public class InvLog
{
    public const string WARNING_TAG_DISPLAY_NAME = "[WRN]";
    public const string ERROR_TAG_DISPLAY_NAME = "[ERR]";
    public const string FATAL_TAG_DISPLAY_NAME = "[FTL]";

    public static ILogger Logger<T>(bool tolerateSilentLogger = false)
    {
        ILogger logger = Log.ForContext<T>().ForContext("DefaultContext", "1");
        
        // TODO: check if app is shutting down
        if (!tolerateSilentLogger && logger.GetType().FullName.Contains("SilentLogger"))
        {
            throw new Exception("Log.ForContext<T> returned SilentLogger. This is unacceptable. This logger should not be used further down the road.");
        }

        return logger;
    }
    
    public static ILogger RequestLogger<T>()
    {
        ILogger logger = Log.ForContext<T>().ForContext("RequestContext", "1");
        
        // TODO: check if app is shutting down
        if (logger.GetType().FullName.Contains("SilentLogger"))
        {
            throw new Exception("Log.ForContext<T> returned SilentLogger. This is unacceptable. This logger should not be used further down the road.");
        }

        return logger;
    }
    
    public static class LevelSwitch
    {
        public static LoggingLevelSwitch TerminalSwitch { get; set; }
    }
}
