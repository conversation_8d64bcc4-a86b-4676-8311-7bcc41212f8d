using JetBrains.Annotations;

using Serilog.Configuration;
using Serilog.Sinks.SystemConsole.Themes;

namespace Invictus.Nomenklatura.Logg;

public static class LogExtensions
{
    [UsedImplicitly]
    public static SystemConsoleTheme InvictusConsoleTheme { get; } = new(
        new Dictionary<ConsoleThemeStyle, SystemConsoleThemeStyle>
        {
            [ConsoleThemeStyle.Text] = new() { Foreground = ConsoleColor.White },
            [ConsoleThemeStyle.SecondaryText] = new() { Foreground = ConsoleColor.Gray },
            [ConsoleThemeStyle.TertiaryText] = new() { Foreground = ConsoleColor.DarkCyan },
            [ConsoleThemeStyle.Invalid] = new() { Foreground = ConsoleColor.Yellow },
            [ConsoleThemeStyle.Null] = new() { Foreground = ConsoleColor.White },
            [ConsoleThemeStyle.Name] = new() { Foreground = ConsoleColor.Green },
            [ConsoleThemeStyle.String] = new() { Foreground = ConsoleColor.Green },
            [ConsoleThemeStyle.Number] = new() { Foreground = ConsoleColor.Green },
            [ConsoleThemeStyle.Boolean] = new() { Foreground = ConsoleColor.Green },
            [ConsoleThemeStyle.Scalar] = new() { Foreground = ConsoleColor.Green },
            [ConsoleThemeStyle.LevelVerbose] = new() { Foreground = ConsoleColor.Gray, Background = ConsoleColor.DarkGray },
            [ConsoleThemeStyle.LevelDebug] = new() { Foreground = ConsoleColor.White, Background = ConsoleColor.DarkGray },
            [ConsoleThemeStyle.LevelInformation] = new() { Foreground = ConsoleColor.White, Background = ConsoleColor.Blue },
            [ConsoleThemeStyle.LevelWarning] = new() { Foreground = ConsoleColor.DarkGray, Background = ConsoleColor.Yellow },
            [ConsoleThemeStyle.LevelError] = new() { Foreground = ConsoleColor.White, Background = ConsoleColor.Red },
            [ConsoleThemeStyle.LevelFatal] = new() { Foreground = ConsoleColor.White, Background = ConsoleColor.Red }
        }
    );
    
    [UsedImplicitly]
    public static LoggerConfiguration WithSourceContextPropertyAndAddPaddingEnricher(
        this LoggerEnrichmentConfiguration enrichmentConfiguration,
        int? padding,
        bool trimClassName
    )
    {
        ArgumentNullException.ThrowIfNull(enrichmentConfiguration);

        return enrichmentConfiguration.With(new SourceContextPropertyAndPaddingEnricher(padding, trimClassName));
    }

    [UsedImplicitly]
    public static LoggerConfiguration WithMyThreadId(this LoggerEnrichmentConfiguration enrichmentConfiguration)
    {
        return enrichmentConfiguration != null ? enrichmentConfiguration.With<MyThreadIdEnricher>() : throw new ArgumentNullException(nameof(enrichmentConfiguration));
    }
}