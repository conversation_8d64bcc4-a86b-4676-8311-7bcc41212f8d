using Invictus.Nomenklatura.Misc;
using Invictus.Nomenklatura.Workers;

using Serilog.Core;

namespace Invictus.Nomenklatura.Logg;

public class MyThreadIdEnricher : ILogEventEnricher
{
    public const string THREAD_ID_PROPERTY_NAME = "MyThreadId";

    public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
    {
        string myThreadId = WorkersGod.GetFormattedThreadIdOrShrtWorkerName();

        logEvent.AddPropertyIfAbsent(new LogEventProperty(THREAD_ID_PROPERTY_NAME, new ScalarValue(myThreadId)));
    }
}

public class ProductInfoEnricher : ILogEventEnricher
{
    private const string PRODUCT_INFO_PROPERTY = "ProductInfo";

    public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
    {
        if (logEvent.Properties.TryGetValue("SkuCode", out LogEventPropertyValue skuCode))
        {
            logEvent.AddPropertyIfAbsent(new LogEventProperty(PRODUCT_INFO_PROPERTY, new ScalarValue("Product: " + skuCode + " ")));
        }
    }
}

// TODO

/*
public class CustomerContextEnricher : ILogEventEnricher
{
    private const string PRODUCT_INFO_PROPERTY = "CustCtxInfo";
    
    public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
    {
        
        if (logEvent.Properties.TryGetValue("CustomerContext", out LogEventPropertyValue customerContext))
        {
            logEvent.AddPropertyIfAbsent(new LogEventProperty(PRODUCT_INFO_PROPERTY, new ScalarValue("Customer: " + customerContext + " ")));
        }
    }
    
    private string GetCustomerContext()
    {
        
    }
}
*/

public class SourceContextPropertyAndPaddingEnricher : ILogEventEnricher
{
    private readonly int? _allTagsPadding;
    private readonly bool _trimClassName;

    private static readonly Dictionary<string, string> _NamespaceReplaceList = new()
    {
        { "Service", "" },
        { "Services", "" },
        { "Nomenklatura", "Nom" },
        { "Emerald", "Em" },
        { "EmeraldOperation", "op" },
        { "Invictus", "Inv" },
        { "Inventory", "Inv" },
        { "irisdropwebservice", "IDWS" },
        { "Microsoft", "MS" },
        { "EntityFrameworkCore", "EF" },
        { "AspNetCore", "ASP" },
        { "Ins", "" },
        { "Internal", "" },
        { "Internals", "" },
    };

    public SourceContextPropertyAndPaddingEnricher(int? padding, bool trimClassName)
    {
        _allTagsPadding = padding;
        _trimClassName = trimClassName;
    }

    public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
    {
        KeyValuePair<string, LogEventPropertyValue> sourceContextProp = logEvent.Properties.FirstOrDefault(p => p.Key == "SourceContext");

        if (sourceContextProp.Key == null)
            return;

        string classNameWithNs = sourceContextProp.Value.ToString().Trim('\"');
        string className = "?";
        
        if (_trimClassName)
        {
            // Remove/replace some namespace parts
            string[] spl = classNameWithNs.Split('.');
            className = spl.Last();
            
            var splList = new List<string>(spl.Length);
            bool anyChanges = false;

            foreach (string nsPart in spl)
            {
                string nsPartReplace;

                if (!_NamespaceReplaceList.TryGetValue(nsPart, out nsPartReplace))
                {
                    splList.Add(nsPart);

                    continue;
                }

                if (nsPartReplace == "")
                {
                    splList.Add(Various.UnicodeSentinelCharacter);
                }
                else
                {
                    splList.Add(nsPartReplace);
                }

                anyChanges = true;
            }

            if (anyChanges)
            {
                classNameWithNs = string.Join(".", splList).Replace(Various.UnicodeSentinelCharacter, "");
            }

            // Remove some namespaces
            if (classNameWithNs.Count(c => c == '.') > 4)
            {
                // Microsoft.AspNetCore.Watch.BrowserRefresh.BrowserRefreshMiddleware
                // ->
                // Microsoft.AspNetCore..BrowserRefresh.BrowserRefreshMiddleware
                classNameWithNs = classNameWithNs.Substring(0, classNameWithNs.IndexOf(".", classNameWithNs.IndexOf(".", StringComparison.InvariantCulture) + 2, StringComparison.InvariantCulture))
                            + "."
                            + classNameWithNs.Remove(0,
                                classNameWithNs.LastIndexOf(".", classNameWithNs.LastIndexOf(".", StringComparison.InvariantCulture) - 0, StringComparison.InvariantCulture)
                            );
            }
        }

        logEvent.AddPropertyIfAbsent(new LogEventProperty("SourceClassShort", new ScalarValue(classNameWithNs)));
        logEvent.AddPropertyIfAbsent(new LogEventProperty("SourceClassName", new ScalarValue(className)));
        
        string renderedMessage = classNameWithNs;

        if (!_allTagsPadding.HasValue)
            return;

        int dif = _allTagsPadding.Value - renderedMessage.Length;

        if (logEvent.Properties.TryGetValue(MyThreadIdEnricher.THREAD_ID_PROPERTY_NAME, out LogEventPropertyValue threadIdValue))
            dif -= threadIdValue.ToString().Length;

        if (dif <= 0)
            return;

        logEvent.AddPropertyIfAbsent(new LogEventProperty("MyPadding", new ScalarValue(new string(' ', dif))));
    }
}