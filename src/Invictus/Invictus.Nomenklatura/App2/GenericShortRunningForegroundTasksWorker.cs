using System.Diagnostics;

using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Workers;

namespace Invictus.Nomenklatura.App2;

public interface IGenericShortRunningForegroundTasksWorker : IWorker<IGenericShortRunningForegroundTasksWorker>
{
    void ExecuteInThread(Action action);

    T ExecuteInThreadFunc<T>(Func<T> action);
}

public class GenericShortRunningForegroundTasksWorker : IWorkerImpl<IGenericShortRunningForegroundTasksWorker>, IGenericShortRunningForegroundTasksWorker
{
    public WorkerConfiguration WorkerConfiguration { get; } = new WorkerConfiguration(
        "SHORTF",
        new WorkerConfiguration.Thread("GenericShortRunningForegroundTasks", ThreadPriority.AboveNormal, IsBackground: true),
        (LogEventLevel)(-1),
        AllowDirectCall: false
    );

    public ILogger Log { get; } = InvLog.Logger<GenericShortRunningForegroundTasksWorker>();

    public WorkerCore Core { get; set; }

    IGenericShortRunningForegroundTasksWorker IWorkerImpl<IGenericShortRunningForegroundTasksWorker>.PublicInterface { get; set; }

    public void ExecuteInThread(Action action)
    {
        var sw = Stopwatch.StartNew();

        action();

        sw.Stop();

        if (sw.Elapsed.TotalSeconds > 1)
            Log.Warning("ShortRunning task took more than 1 second to run (see trace log).");
    }

    public T ExecuteInThreadFunc<T>(Func<T> action)
    {
        var sw = Stopwatch.StartNew();

        T res = action();

        sw.Stop();

        if (sw.Elapsed.TotalSeconds > 1)
            Log.Warning("ShortRunning task took more than 1 second to run (see trace log).");

        return res;
    }
}