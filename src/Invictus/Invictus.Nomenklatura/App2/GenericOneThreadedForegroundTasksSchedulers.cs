namespace Invictus.Nomenklatura.App2;

public class GenericShortRunningOneThreadedForegroundTasksScheduler : TaskScheduler
{
    public static GenericShortRunningOneThreadedForegroundTasksScheduler Instance { get; set; }

    private readonly InvTasks _invTasks;
    private readonly List<Task> _scheduledTasks = new();

    public GenericShortRunningOneThreadedForegroundTasksScheduler(InvTasks invTasks)
    {
        _invTasks = invTasks;
        Instance = this;
    }

    protected override IEnumerable<Task> GetScheduledTasks()
    {
        lock (_scheduledTasks)
        {
            return _scheduledTasks;
        }
    }

    protected override void QueueTask(Task task)
    {
        lock (_scheduledTasks)
        {
            _scheduledTasks.Add(task);
        }

        _invTasks.RunShort(() => this.ExecuteTask(task), "FromOneThreadedTaskScheduler");
    }

    private void ExecuteTask(Task task)
    {
        try
        {
            this.TryExecuteTask(task);
        }
        finally
        {
            lock (_scheduledTasks)
            {
                _scheduledTasks.Remove(task);
            }
        }
    }

    protected override bool TryDequeue(Task task)
    {
        return false;
    }

    protected override bool TryExecuteTaskInline(Task task, bool taskWasPreviouslyQueued)
    {
        return false;
    }
}