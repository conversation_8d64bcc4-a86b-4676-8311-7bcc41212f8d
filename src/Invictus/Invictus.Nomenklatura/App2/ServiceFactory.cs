using System.Reflection;

using Invictus.Nomenklatura.Misc;

using Microsoft.Extensions.DependencyInjection;

namespace Invictus.Nomenklatura.App2;

public abstract class MethodCallFactoryBase
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IServiceScopesInternal _serviceScopes;
    private readonly ObjectAndType[] _additionalArgs;
    protected readonly Type[] _methodParameterTypes;

    protected MethodCallFactoryBase(IServiceProvider serviceProvider, IServiceScopesInternal serviceScopes, object[] additionalArgs, MethodBase methodBase)
    {
        _serviceProvider = serviceProvider;
        _serviceScopes = serviceScopes;
        _additionalArgs = CreateObjAndType(additionalArgs);
        _methodParameterTypes = methodBase.GetParameters().Select(p => p.ParameterType).ToArray();
    }

    protected object[] GetArgumentsBesidesServiceProvider(IEnumerable<object> addOrOverrideMoreArguments)
    {
        ObjectAndType[] addOrOverrideObjTypes = CreateObjAndType(addOrOverrideMoreArguments);
        ObjectAndType[] argsNonScope = _additionalArgs.OverrideBy(addOrOverrideObjTypes, k => k.Type, SimpleTypeComparer.Instance).ToArray();

        var res = new List<object>();

        foreach (Type parameterType in _methodParameterTypes)
        {
            ObjectAndType suppliedArg = argsNonScope.SingleOrDefault(objType => parameterType.IsAssignableFrom(objType.Type));
            object scopedArg = _serviceScopes?.GetScopedObjectOfType(parameterType);

            if (suppliedArg.Type != null)
            {
                if (scopedArg != null)
                    throw new Exception($"Both scoped arg and supplied arg of are found for parameter type {parameterType.FullName}.");

                res.Add(suppliedArg.Object);
            }

            if (scopedArg != null)
            {
                res.Add(scopedArg);
            }
        }

        return res.ToArray();
    }
    
    public object[] GetArgumentsForMethodCall(object[] addMoreArguments)
    {
        object[] preArguments = this.GetArgumentsBesidesServiceProvider(addMoreArguments);
        object[] arguments = new object[_methodParameterTypes.Length];

        for (int i = 0; i < _methodParameterTypes.Length; i++)
        {
            Type methodParameterType = _methodParameterTypes[i];

            object arg = preArguments.SingleOrDefault(arg => arg.GetType() == methodParameterType, Various.ObjectSentinel);

            if (arg != Various.ObjectSentinel)
            {
                arguments[i] = arg;
                continue;
            }

            arguments[i] = _serviceProvider.GetRequiredService(methodParameterType);
        }

        return arguments;
    }

    public Type[] GetArgumentTypes()
    {
        return _methodParameterTypes;
    }

    private readonly record struct ObjectAndType(object Object, Type Type);

    private static ObjectAndType[] CreateObjAndType(IEnumerable<object> objects)
    {
        // ReSharper disable once SuspiciousParameterNameInArgumentNullException
        ObjectAndType[] res = objects.Select(obj => new ObjectAndType(obj ?? throw new ArgumentNullException(nameof(objects)), obj.GetType())).ToArray();

        if (res.DistinctBy(ObjectAndTypeComparer.Instance).Count() != res.Length)
            throw new Exception("Same types are spotted.");

        return res;
    }

    private class ObjectAndTypeComparer : IEqualityComparer<ObjectAndType>
    {
        public static readonly ObjectAndTypeComparer Instance = new ObjectAndTypeComparer();

        public bool Equals(ObjectAndType x, ObjectAndType y)
        {
            if (ReferenceEquals(x.Type, null))
            {
                if (ReferenceEquals(y.Type, null))
                    return true;

                return false;
            }

            if (ReferenceEquals(y.Type, null))
                return false;

            return SimpleTypeComparer.Instance.Equals(x.Type, y.Type);
        }

        public int GetHashCode(ObjectAndType obj)
        {
            if (obj.Type == null)
                return 0;
            return SimpleTypeComparer.Instance.GetHashCode(obj.Type);
        }
    }
}

public class MethodCallFactory : MethodCallFactoryBase, IMethodCallFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly MethodInfo _methodInfo;

    public MethodCallFactory(IServiceProvider serviceProvider, IServiceScopesInternal serviceScopes, object[] additionalArgs, MethodInfo methodInfo)
        : base(serviceProvider, serviceScopes, additionalArgs, methodInfo)
    {
        _serviceProvider = serviceProvider;
        _methodInfo = methodInfo;
    }

    public object CallMethod(Type instanceType, params object[] addMoreArguments)
    {
        object instance = _serviceProvider.GetRequiredService(instanceType);

        return this.CallMethod(instance, addMoreArguments);
    }

    public object CallMethod(object instance, params object[] addMoreArguments)
    {
        object[] arguments = this.GetArgumentsForMethodCall(addMoreArguments);

        return _methodInfo.Invoke(instance, arguments);
    }
}

public class ConstructorCallFactory : MethodCallFactoryBase, IConstructorCallFactory
{
    private readonly ConstructorInfo _constructorInfo;

    public ConstructorCallFactory(IServiceProvider serviceProvider, IServiceScopesInternal serviceScopes, object[] additionalArgs, ConstructorInfo constructorInfo)
        : base(serviceProvider, serviceScopes, additionalArgs, constructorInfo)
    {
        _constructorInfo = constructorInfo;
    }

    public object CreateInstance(Type instanceType, params object[] addMoreArguments)
    {
        object[] arguments = this.GetArgumentsForMethodCall(addMoreArguments);

        return _constructorInfo.Invoke(arguments);
    }
}

public class ServiceFactory<TDecorated> : MethodCallFactoryBase, IServiceFactory<TDecorated>
{
    private readonly IServiceProvider _serviceProvider;

    public ServiceFactory(IServiceProvider serviceProvider, IServiceScopesInternal serviceScopes, object[] additionalArgs, Type[] preferredConstructor)
        : base(serviceProvider, serviceScopes, additionalArgs, GetConstructor(preferredConstructor))
    {
        _serviceProvider = serviceProvider;
    }

    private static ConstructorInfo GetConstructor(Type[] preferredConstructor)
    {
        ConstructorInfo[] constructors = typeof(TDecorated).GetConstructors();

        if (preferredConstructor == null && constructors.Length != 1)
            throw new Exception("Multiple constructors are not supported.");

        ConstructorInfo constructor = preferredConstructor != null
            ? constructors.Single(ci => ci.GetParameters().Select(p => p.ParameterType).SequenceEqual(preferredConstructor))
            : constructors.Single();

        return constructor;
    }

    public TDecorated Create()
    {
        return ActivatorUtilities.CreateInstance<TDecorated>(_serviceProvider, this.GetArgumentsBesidesServiceProvider(Array.Empty<object>()));
    }

    public TDecorated Create(params object[] addMoreArguments)
    {
        return ActivatorUtilities.CreateInstance<TDecorated>(_serviceProvider, this.GetArgumentsBesidesServiceProvider(addMoreArguments));
    }
}