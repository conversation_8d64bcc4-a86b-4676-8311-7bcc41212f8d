using Invictus.Nomenklatura.ExteriorServ;
using Invictus.Nomenklatura.Front;
using Invictus.Nomenklatura.Misc;

using Microsoft.Extensions.DependencyInjection;

namespace Invictus.Nomenklatura.App2;

public class NomenklaturaExteriorService : IExteriorService
{
    private readonly IServiceProvider _serviceProvider;

    public NomenklaturaExteriorService(
        GenericShortRunningOneThreadedForegroundTasksScheduler genericShortRunningOneThreadedForegroundTasksScheduler,
        BackgroundTasksWorker backgroundTasksWorker,
        GenericShortRunningForegroundTasksWorker genericShortRunningForegroundTasksWorker,
        IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public Task Run()
    {
        return Task.CompletedTask;
    }

    public void SeedAuthData()
    {
        using IServiceScope scope = _serviceProvider.CreateScope();

        var authDataSeeder = ActivatorUtilities.CreateInstance<FrontAuthDataSeeder>(scope.ServiceProvider);
        
        AsyncUtil.RunSync(authDataSeeder.SeedRolesAsync);
        AsyncUtil.RunSync(authDataSeeder.SeedUsersAsync);
    }
}