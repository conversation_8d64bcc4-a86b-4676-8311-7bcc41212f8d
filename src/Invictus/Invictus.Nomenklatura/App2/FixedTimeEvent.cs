using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;

namespace Invictus.Nomenklatura.App2;

public class FixedTimeEvent
{
    private readonly Func<Task> _action;
    private readonly TimeSpan _spanBand;
    private readonly CancellationToken _cancellationToken;
    private readonly string _logName;
    private readonly TimeSpan[] _triggerTimesUtc;

    private readonly ILogger _logger = InvLog.Logger<FixedTimeEvent>();

    private bool _isStarted;

    public FixedTimeEvent(Func<Task> action, TimeSpan spanBand, CancellationToken ct, string logName, params TimeSpan[] triggerTimesUtc)
    {
        _action = action;
        _spanBand = spanBand;
        _cancellationToken = ct;
        _logName = logName;
        _triggerTimesUtc = triggerTimesUtc.OrderBy(t => t).ToArray();
        
        if (_triggerTimesUtc.Length == 0)
            throw new ArgumentException("No Trigger times supplied.");

        if (_triggerTimesUtc.Any(t => t >= spanBand))
            throw new ArgumentException("Trigger time cannot be ouside or equal to band.");
    }

    public void Start()
    {
        if (_isStarted)
            throw new Exception("Already started.");

        _isStarted = true;

        this.SetNextTimer();
    }

    private DateTimeOffset GetNextFiringTime(DateTimeOffset utcNow)
    {
        var breadthStart = new DateTime(utcNow.Ticks - utcNow.Ticks % _spanBand.Ticks);
        
        _logger.Information("breadthStart: " + breadthStart);
        _logger.Information("utcNow: " + utcNow);

        foreach (TimeSpan triggerTime in _triggerTimesUtc)
        {
            // _logger.Information("breadthStart + triggerTime: " + (breadthStart + triggerTime));
            
            if (breadthStart + triggerTime > utcNow)
                return breadthStart + triggerTime;
        }
        
        _logger.Information("breadthStart + _spanBand + _triggerTimesUtc[0]: " + (breadthStart + _spanBand + _triggerTimesUtc[0]));

        // Next span
        return breadthStart + _spanBand + _triggerTimesUtc[0];
    }

    private void SetNextTimer()
    {
        DateTimeOffset utcNow = ServerClock.GetCurrentUtcTime();
        DateTimeOffset nextFiringTime = this.GetNextFiringTime(utcNow);
        TimeSpan toWait = nextFiringTime - utcNow;

        bool t = false;

        if (toWait <= TimeSpan.Zero)
        {
            toWait = TimeSpan.FromMilliseconds(5);
            t = true;
        }

        if (toWait < TimeSpan.FromSeconds(2))
        {
            this.Log($"TIMER WARNING: toWait: {toWait} \n\n" + new ObjectDumper(10, true, true).Dump(this));

            if (utcNow.TimeOfDay < TimeSpan.FromHours(4)) // TODO fix
            {
                this.Log("Wait until 4 AM");
                toWait = TimeSpan.FromHours(4) - utcNow.TimeOfDay;
                t = false;
            }
        }

        if (t)
            throw new Exception("Bad timer");

        Task delay = Task.Delay(toWait, _cancellationToken);

        // ReSharper disable once MethodSupportsCancellation
        delay.ContinueWithShortThread(this.DelayContinuation, TaskContinuationOptions.NotOnCanceled);
    }

    private void DelayContinuation(Task t)
    {
        this.Log("Fire");
        
        Task actionResult = _action();

        // ReSharper disable once MethodSupportsCancellation
        actionResult.ContinueWithShortThread(this.OnActionCompleted);
    }

    private void OnActionCompleted(Task t)
    {
        this.SetNextTimer();
    }
    
    private void Log(string msg)
    {
        _logger.Warning(_logName + " " + msg);
    }
}