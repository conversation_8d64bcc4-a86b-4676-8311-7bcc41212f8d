using System.Reflection;

using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.AppConfig;
using Invictus.Nomenklatura.Misc;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

using Serilog.Extensions.Logging;

namespace Invictus.Nomenklatura.App2;


class DbGlobal
{
    private static readonly object _LockObject = new();

    public static void AcquireLock()
    {
        if (!Monitor.TryEnter(_LockObject, TimeSpan.FromSeconds(5)))
            throw new Exception("Deadlock");
    }

    public static void ReleaseLock()
    {
        Monitor.Exit(_LockObject);
    }
}

public interface IDbOptionsCompletion
{
    void CompleteDbOptions(DbContextOptionsBuilder options);

    bool GlobalAccessorsLock { get; }
}

public interface IDbAccessPrivate
{
    public DbContext GetDbContext();
}

public abstract class DbAccessBase : IDbAccessPrivate, IDisposable
{
    private readonly DbAccessOptions _dbAccessOptions;
    private readonly DbContext _dbContext;

    protected DbAccessBase(DbAccessOptions dbAccessOptions, DbContext dbContext)
    {
        _dbAccessOptions = dbAccessOptions;
        _dbContext = dbContext;

        if (_dbAccessOptions.GlobalAccessorsLock)
        {
            DbGlobal.AcquireLock();
        }
    }

    public virtual void SaveChanges()
    {
        if (_dbAccessOptions.DbContextOwnedExternally)
            throw new Exception("DbAccessBase::SaveChanges() is not allowed when _dbAccessOptions.DbContextOwnedExternally");
        
        _dbContext.SaveChanges();
    }

    public void Dispose()
    {
        if (_dbAccessOptions.DbContextOwnedExternally)
            throw new Exception("DbAccessBase::SaveChanges() is not allowed when _dbAccessOptions.DbContextOwnedExternally");
        
        _dbContext.Dispose();

        if (_dbAccessOptions.GlobalAccessorsLock)
        {
            DbGlobal.ReleaseLock();
        }

        GC.SuppressFinalize(this);
    }

    ~DbAccessBase()
    {
        if (_dbAccessOptions.DbContextOwnedExternally)
            return;
        
        throw new Exception("DbAccess should be disposed properly.");
    }

    DbContext IDbAccessPrivate.GetDbContext()
    {
        return _dbContext;
    }
}

public interface IDbAccessFactory<out T>
    where T : DbAccessBase
{
    T CreateAccess();
    
    T CreateAccessForExternalCode();
}

public class DefaultDbOptionsCompletion : IDbOptionsCompletion
{
    private readonly string _key;
    private readonly NomenklaturaDatabaseConfiguration _configuration;

    public bool GlobalAccessorsLock => false;

    public DefaultDbOptionsCompletion(InvAppConfig invAppConfig, string key)
    {
        _key = key;
        _configuration = invAppConfig.BasicConfiguration.Database;
        
        if (_configuration == null)
            throw new Exception("DB configuration is null");
    }

    public void CompleteDbOptions(DbContextOptionsBuilder options)
    {
        /*
        options.UseSqlServer(
            _key == "idws" ? _configuration.IdwsConnectionString : _configuration.ConnectionString,
            builder => builder.MigrationsAssembly(_configuration.MigrationsAssembly)
        );
        */

        // options.UseMySQL(_configuration.ConnectionString);
        options.UseMySql(_configuration.ConnectionString, new MariaDbServerVersion("10.4.7-MariaDB"));

        options.EnableSensitiveDataLogging();
        options.EnableDetailedErrors();
    }
}

public record DbAccessOptions
{
    public bool GlobalAccessorsLock { get; init; }
    
    public bool DbContextOwnedExternally { get; init; }
}

public abstract class DbAccessFactory
{
    public abstract DbContext AutotestCreateDbContext();

    protected static readonly MethodInfo AddDbContextMethodInfo = ReflectionUtil.GetGenericMethod(
        typeof(EntityFrameworkServiceCollectionExtensions),
        BindingFlags.Static | BindingFlags.Public,
        nameof(EntityFrameworkServiceCollectionExtensions.AddDbContext),
        new[] { typeof(IServiceCollection), typeof(Action<DbContextOptionsBuilder>), typeof(ServiceLifetime), typeof(ServiceLifetime) }
    );
}

public class DbAccessFactory<T> : DbAccessFactory, IDbAccessFactory<T>
    where T : DbAccessBase
{
    private static readonly Type _DbContextAccessParameterType;

    static DbAccessFactory()
    {
        ParameterInfo pi = typeof(T).GetConstructors()
            .Select(c => c.GetParameters())
            .Single(p => p.Length == 2 && p.Any(p1 => p1.ParameterType.IsAssignableTo(typeof(DbContext))))
            .Single(p => p.ParameterType.IsAssignableTo(typeof(DbContext)));

        _DbContextAccessParameterType = pi.ParameterType;
    }

    private readonly IServiceProvider _realServiceProvider;
    private readonly IServiceProvider _dbContextServiceProvider;

    public DbAccessFactory(IServiceProvider serviceProvider)
    {
        _realServiceProvider = serviceProvider;

        MethodInfo addDbContextMethodInfoGeneric = AddDbContextMethodInfo.MakeGenericMethod(_DbContextAccessParameterType);

        var privateServices = new ServiceCollection();

        string optionsKey = typeof(T).Namespace.StartsWith("irisdropwebservice") ? "idws" : "default";

        var dbOptionsCompletion = serviceProvider.GetRequiredKeyedService<IDbOptionsCompletion>(optionsKey);
        
        // TODO LOG PROPERLY
        privateServices.AddSerilog();
        privateServices.AddSingleton<ILoggerProvider>(new SerilogLoggerProvider());

        privateServices.AddTransient(
            sp => new DbAccessOptions { GlobalAccessorsLock = dbOptionsCompletion.GlobalAccessorsLock }
        );

        addDbContextMethodInfoGeneric.Invoke(null,
            new object[]
            {
                privateServices,
                (Action<DbContextOptionsBuilder>)(options => dbOptionsCompletion.CompleteDbOptions(options)),
                ServiceLifetime.Transient,
                ServiceLifetime.Singleton
            }
        );

        _dbContextServiceProvider = privateServices.BuildServiceProvider();
    }

    public override DbContext AutotestCreateDbContext()
    {
        return (DbContext)ActivatorUtilities.CreateInstance(_dbContextServiceProvider, _DbContextAccessParameterType);
    }

    public T CreateAccess()
    {
        return this.CreateAccess(o => o);
    }

    public T CreateAccessForExternalCode()
    {
        return this.CreateAccess(o => o with { DbContextOwnedExternally = true });
    }

    private T CreateAccess(Func<DbAccessOptions, DbAccessOptions> modifyOptions)
    {
        object dbContext = ActivatorUtilities.CreateInstance(_dbContextServiceProvider, _DbContextAccessParameterType);
        var accessOptions = _dbContextServiceProvider.GetService<DbAccessOptions>();

        accessOptions = modifyOptions(accessOptions);

        object res = ActivatorUtilities.CreateInstance(_realServiceProvider, typeof(T), dbContext, accessOptions);

        return (T)res;
    }
}