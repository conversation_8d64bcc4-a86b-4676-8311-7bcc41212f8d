using System.Linq.Expressions;
using System.Reflection;

using Castle.DynamicProxy;

using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;

using Microsoft.Extensions.DependencyInjection;

namespace Invictus.Nomenklatura.App2;

public interface IAutoDecorationRegistrator
{
    void RegisterDecoratedServiceSingleton(IServiceCollection serviceCollection, Type interfaceType);
}

[AttributeUsage(AttributeTargets.Interface)]
sealed class HasAutoDecoratorAttribute : Attribute
{
    public Type DecoratedType { get; set; }
    public Type InterceptorType { get; set; }

    public HasAutoDecoratorAttribute(Type interceptorType, Type decoratedType)
    {
        InterceptorType = interceptorType;
        DecoratedType = decoratedType;
    }
}

public class AutoDecorationRegistrator : IAutoDecorationRegistrator
{
    private readonly ILogger _logger = InvLog.Logger<AutoDecorationRegistrator>();

    private readonly (Type, HasAutoDecoratorAttribute)[] _interfacesWithAutoDecoAttribute;
    private readonly ProxyGenerator _proxyGenerator = new();

    public AutoDecorationRegistrator(IAssemblyReferenceService assemblyReference)
    {
        _interfacesWithAutoDecoAttribute = assemblyReference.Assembly.GetTypes()
            .Where(t => t.IsDefined(typeof(HasAutoDecoratorAttribute)))
            .Select(t => (t, t.GetCustomAttribute<HasAutoDecoratorAttribute>()))
            .ToArray();

        foreach ((Type iface, HasAutoDecoratorAttribute hasAutoDecoratorAttribute) in _interfacesWithAutoDecoAttribute)
        {
            if (!typeof(IInterceptor).IsAssignableFrom(hasAutoDecoratorAttribute.InterceptorType))
                throw new Exception(
                    $"Type {hasAutoDecoratorAttribute.InterceptorType.FullName} should implement {typeof(IInterceptor).FullName}");

            if (!hasAutoDecoratorAttribute.InterceptorType.IsPublic)
                throw new Exception($"Type {hasAutoDecoratorAttribute.InterceptorType.FullName} should be public.");

            if (!iface.IsAssignableFrom(hasAutoDecoratorAttribute.DecoratedType))
                throw new Exception(
                    $"Type {hasAutoDecoratorAttribute.DecoratedType.FullName} should implement {iface.FullName}");

            if (!hasAutoDecoratorAttribute.DecoratedType.IsPublic)
                throw new Exception($"Type {hasAutoDecoratorAttribute.DecoratedType.FullName} should be public.");
        }

        _logger.Verbose(
            $"AutoDecorationRegistrator was constructed for assembly {assemblyReference.Assembly.FullName}");
    }

    public void RegisterDecoratedServiceSingleton(IServiceCollection serviceCollection, Type interfaceType)
    {
        HasAutoDecoratorAttribute hasAutoDecoratorAttribute =
            _interfacesWithAutoDecoAttribute.Single(tuple => tuple.Item1 == interfaceType).Item2;

        serviceCollection.AddSingleton(interfaceType,
            serviceProvider =>
                this.ResolveWrapper(serviceProvider, interfaceType, hasAutoDecoratorAttribute.InterceptorType,
                    hasAutoDecoratorAttribute.DecoratedType)
        );

        serviceCollection.AddSingleton(hasAutoDecoratorAttribute.DecoratedType);
    }

    private object ResolveWrapper(IServiceProvider serviceProvider, Type interfaceType, Type interceptorType,
        Type decoratedType)
    {
        _logger.Verbose($"Resolving decorator for {interfaceType.FullName}.");

        var res = new ObjRef<object>();

        bool addRecursiveWrapperRefToInterceptor = interceptorType.GetConstructors().Single().GetParameters()
            .Any(p =>
                p.ParameterType.IsGenericType &&
                p.ParameterType.GetGenericTypeDefinition() == typeof(Func<>) &&
                p.ParameterType.GenericTypeArguments[0] == interfaceType
            );

        IInterceptor decorator;

        if (addRecursiveWrapperRefToInterceptor)
        {
            Delegate getWrapperFunc = Expression.Lambda(typeof(Func<>).MakeGenericType(interfaceType),
                Expression.Convert(
                    Expression.Call(
                        Expression.Constant(res),
                        typeof(ObjRef<object>).GetMethod(
                            nameof(ObjRef<object>.GetValue),
                            BindingFlags.Public | BindingFlags.Instance
                        )
                    ),
                    interfaceType
                )
            ).Compile();

            decorator = (IInterceptor) ActivatorUtilities.CreateInstance(serviceProvider, interceptorType,
                getWrapperFunc);
        }
        else
        {
            decorator = (IInterceptor) ActivatorUtilities.CreateInstance(serviceProvider, interceptorType);
        }

        object decorated;

        bool interceptorRefToDecorated = decoratedType.GetConstructors().First().GetParameters()
            .Any(p => p.ParameterType == interceptorType);

        if (interceptorRefToDecorated)
            decorated = ActivatorUtilities.CreateInstance(serviceProvider, decoratedType, decorator);
        else
            decorated = ActivatorUtilities.CreateInstance(serviceProvider, decoratedType);

        res.Value = _proxyGenerator.CreateInterfaceProxyWithTargetInterface(interfaceType, decorated, decorator);

        return res.Value;
    }
}