using Microsoft.Extensions.DependencyInjection;

namespace Invictus.Nomenklatura.App2;

public static partial class ServiceCollectionExtensions
{
    public static IServiceCollection AddDbAccess<TImpl>(this IServiceCollection services)
        where TImpl : DbAccessBase
    {
        return services.AddDbAccess(typeof(TImpl));
    }
    
    public static IServiceCollection AddDbAccess(this IServiceCollection services, Type t)
    {
        services.AddSingleton(
            typeof(IDbAccessFactory<>).MakeGenericType(t),
            typeof(DbAccessFactory<>).MakeGenericType(t)
        );

        return services;
    }
}