using Invictus.Nomenklatura.App;

namespace Invictus.Nomenklatura.App2;

public class InvTasks
{
    private readonly InvEnv _environment;
    private readonly IBackgroundTasksWorker _backgroundTasksWorker;
    private readonly IGenericShortRunningForegroundTasksWorker _genericShortRunningForegroundTasksWorker;

    public InvTasks(
        InvEnv environment,
        IBackgroundTasksWorker backgroundTasksWorker,
        IGenericShortRunningForegroundTasksWorker genericShortRunningForegroundTasksWorker
    )
    {
        _environment = environment;
        _backgroundTasksWorker = backgroundTasksWorker;
        _genericShortRunningForegroundTasksWorker = genericShortRunningForegroundTasksWorker;
    }

    public Task RunShort(Action action, string methodName)
    {
        return _genericShortRunningForegroundTasksWorker.Run(w => w.ExecuteInThread(action), methodName);
    }

    public Task<T> RunShort<T>(Func<T> action, string methodName)
    {
        return _genericShortRunningForegroundTasksWorker.Run(w => w.ExecuteInThreadFunc(action), methodName);
    }

    public Task RunShort(Action action)
    {
        return _genericShortRunningForegroundTasksWorker.Run(w => w.ExecuteInThread(action));
    }

    public Task<T> RunShort<T>(Func<T> action)
    {
        return _genericShortRunningForegroundTasksWorker.Run(w => w.ExecuteInThreadFunc(action));
    }

    public Task RunBackground(Action action, string logMethodName)
    {
        return _backgroundTasksWorker.Run(w => w.ExecuteInThread(action), logMethodName);
    }

    public Task<T> RunBackground<T>(Func<T> action, string logMethodName)
    {
        return _backgroundTasksWorker.Run(w => w.ExecuteInThreadFunc(action), logMethodName);
    }
}