using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Workers;

namespace Invictus.Nomenklatura.App2;

public interface IBackgroundTasksWorker : IWorker<IBackgroundTasksWorker>
{
    [CanFail(3)]
    void ExecuteInThread(Action action);

    [CanFail(3)]
    T ExecuteInThreadFunc<T>(Func<T> action);
}

public class BackgroundTasksWorker : IWorkerImpl<IBackgroundTasksWorker>, IBackgroundTasksWorker
{
    public WorkerConfiguration WorkerConfiguration { get; } = new(
        "BCKGR0",
        new WorkerConfiguration.Thread("BackgroundTasks", ThreadPriority.Lowest, IsBackground: true),
        LogEventLevel.Verbose,
        AllowDirectCall: false
    );

    public ILogger Log { get; } = InvLog.Logger<BackgroundTasksWorker>();

    public WorkerCore Core { get; set; }

    IBackgroundTasksWorker IWorkerImpl<IBackgroundTasksWorker>.PublicInterface { get; set; }

    public void ExecuteInThread(Action action)
    {
        action();
    }

    public T ExecuteInThreadFunc<T>(Func<T> action)
    {
        return action();
    }
}