namespace Invictus.Nomenklatura.App2;

public static class TasksExtensions
{
    // Short running

    public static Task ContinueWithShortThread(this Task task, Action<Task> continuationAction)
    {
        return task.ContinueWithShortThreadCore(continuationAction, default, TaskContinuationOptions.None);
    }

    public static Task ContinueWithShortThread(this Task task, Action<Task> continuationAction, CancellationToken cancellationToken)
    {
        return task.ContinueWithShortThreadCore(continuationAction, cancellationToken, TaskContinuationOptions.None);
    }

    public static Task ContinueWithShortThread(this Task task, Action<Task> continuationAction, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithShortThreadCore(continuationAction, default, continuationOptions);
    }

    public static Task ContinueWithShortThread(this Task task, Action<Task> continuationAction, CancellationToken cancellationToken,
                             TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithShortThreadCore(continuationAction, cancellationToken, continuationOptions);
    }

    private static Task ContinueWithShortThreadCore(this Task task, Action<Task> continuationAction, CancellationToken cancellationToken, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWith(continuationAction, cancellationToken, continuationOptions, GenericShortRunningOneThreadedForegroundTasksScheduler.Instance);
    }

    public static Task ContinueWithShortThread(this Task task, Action<Task, object> continuationAction, object state)
    {
        return task.ContinueWithShortThreadCore(continuationAction, state, default, TaskContinuationOptions.None);
    }

    public static Task ContinueWithShortThread(this Task task, Action<Task, object> continuationAction, object state, CancellationToken cancellationToken)
    {
        return task.ContinueWithShortThreadCore(continuationAction, state, cancellationToken, TaskContinuationOptions.None);
    }

    public static Task ContinueWithShortThread(this Task task, Action<Task, object> continuationAction, object state, TaskScheduler scheduler)
    {
        return task.ContinueWithShortThreadCore(continuationAction, state, default, TaskContinuationOptions.None);
    }

    public static Task ContinueWithShortThread(this Task task, Action<Task, object> continuationAction, object state, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithShortThreadCore(continuationAction, state, default, continuationOptions);
    }

    public static Task ContinueWithShortThread(this Task task, Action<Task, object> continuationAction, object state, CancellationToken cancellationToken,
                             TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithShortThreadCore(continuationAction, state, cancellationToken, continuationOptions);
    }

    private static Task ContinueWithShortThreadCore(this Task task, Action<Task, object> continuationAction, object state,
        CancellationToken cancellationToken, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWith(continuationAction, state, cancellationToken, continuationOptions, GenericShortRunningOneThreadedForegroundTasksScheduler.Instance);
    }


    public static Task<TResult> ContinueWithShortThread<TResult>(this Task task, Func<Task, TResult> continuationFunction)
    {
        return task.ContinueWithShortThreadCore(continuationFunction, default, TaskContinuationOptions.None);
    }

    public static Task<TResult> ContinueWithShortThread<TResult>(this Task task, Func<Task, TResult> continuationFunction, CancellationToken cancellationToken)
    {
        return task.ContinueWithShortThreadCore(continuationFunction, cancellationToken, TaskContinuationOptions.None);
    }

    public static Task<TResult> ContinueWithShortThread<TResult>(this Task task, Func<Task, TResult> continuationFunction, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithShortThreadCore(continuationFunction, default, continuationOptions);
    }

    public static Task<TResult> ContinueWithShortThread<TResult>(this Task task, Func<Task, TResult> continuationFunction, CancellationToken cancellationToken,
                                               TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithShortThreadCore(continuationFunction, cancellationToken, continuationOptions);
    }

    private static Task<TResult> ContinueWithShortThreadCore<TResult>(this Task task, Func<Task, TResult> continuationFunction, CancellationToken cancellationToken, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWith(continuationFunction, cancellationToken, continuationOptions, GenericShortRunningOneThreadedForegroundTasksScheduler.Instance);
    }


    public static Task<TResult> ContinueWithShortThread<TResult>(this Task task, Func<Task, object, TResult> continuationFunction, object state)
    {
        return task.ContinueWithShortThreadCore(continuationFunction, state, default, TaskContinuationOptions.None);
    }

    public static Task<TResult> ContinueWithShortThread<TResult>(this Task task, Func<Task, object, TResult> continuationFunction, object state, CancellationToken cancellationToken)
    {
        return task.ContinueWithShortThreadCore(continuationFunction, state, cancellationToken, TaskContinuationOptions.None);
    }

    public static Task<TResult> ContinueWithShortThread<TResult>(this Task task, Func<Task, object, TResult> continuationFunction, object state, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithShortThreadCore(continuationFunction, state, default, continuationOptions);
    }

    public static Task<TResult> ContinueWithShortThread<TResult>(this Task task, Func<Task, object, TResult> continuationFunction, object state, CancellationToken cancellationToken,
                                               TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithShortThreadCore(continuationFunction, state, cancellationToken, continuationOptions);
    }

    private static Task<TResult> ContinueWithShortThreadCore<TResult>(this Task task, Func<Task, object, TResult> continuationFunction, object state,
        CancellationToken cancellationToken, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWith(continuationFunction, state, cancellationToken, continuationOptions, GenericShortRunningOneThreadedForegroundTasksScheduler.Instance);
    }

    // ...

    public static Task ContinueWithShortThread<TResult>(this Task<TResult> task, Action<Task<TResult>> continuationAction)
    {
        return task.ContinueWithShortThreadCore(continuationAction, default, TaskContinuationOptions.None);
    }

    public static Task ContinueWithShortThread<TResult>(this Task<TResult> task, Action<Task<TResult>> continuationAction, CancellationToken cancellationToken)
    {
        return task.ContinueWithShortThreadCore(continuationAction, cancellationToken, TaskContinuationOptions.None);
    }

    public static Task ContinueWithShortThread<TResult>(this Task<TResult> task, Action<Task<TResult>> continuationAction, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithShortThreadCore(continuationAction, default, continuationOptions);
    }

    public static Task ContinueWithShortThread<TResult>(this Task<TResult> task, Action<Task<TResult>> continuationAction, CancellationToken cancellationToken,
                             TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithShortThreadCore(continuationAction, cancellationToken, continuationOptions);
    }

    private static Task ContinueWithShortThreadCore<TResult>(this Task<TResult> task, Action<Task<TResult>> continuationAction, CancellationToken cancellationToken,
                               TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWith(continuationAction, cancellationToken, continuationOptions, GenericShortRunningOneThreadedForegroundTasksScheduler.Instance);
    }


    public static Task ContinueWithShortThread<TResult>(this Task<TResult> task, Action<Task<TResult>, object> continuationAction, object state)
    {
        return task.ContinueWithShortThreadCore(continuationAction, state, default, TaskContinuationOptions.None);
    }

    public static Task ContinueWithShortThread<TResult>(this Task<TResult> task, Action<Task<TResult>, object> continuationAction, object state, CancellationToken cancellationToken)
    {
        return task.ContinueWithShortThreadCore(continuationAction, state, cancellationToken, TaskContinuationOptions.None);
    }

    public static Task ContinueWithShortThread<TResult>(this Task<TResult> task, Action<Task<TResult>, object> continuationAction, object state, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithShortThreadCore(continuationAction, state, default, continuationOptions);
    }

    public static Task ContinueWithShortThread<TResult>(this Task<TResult> task, Action<Task<TResult>, object> continuationAction, object state, CancellationToken cancellationToken,
                             TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithShortThreadCore(continuationAction, state, cancellationToken, continuationOptions);
    }

    private static Task ContinueWithShortThreadCore<TResult>(this Task<TResult> task, Action<Task<TResult>, object> continuationAction, object state, CancellationToken cancellationToken, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWith(continuationAction, state, cancellationToken, continuationOptions, GenericShortRunningOneThreadedForegroundTasksScheduler.Instance);
    }


    public static Task<TNewResult> ContinueWithShortThread<TResult, TNewResult>(this Task<TResult> task, Func<Task<TResult>, TNewResult> continuationFunction)
    {
        return task.ContinueWithShortThreadCore(continuationFunction, default, TaskContinuationOptions.None);
    }

    public static Task<TNewResult> ContinueWithShortThread<TResult, TNewResult>(this Task<TResult> task, Func<Task<TResult>, TNewResult> continuationFunction, CancellationToken cancellationToken)
    {
        return task.ContinueWithShortThreadCore(continuationFunction, cancellationToken, TaskContinuationOptions.None);
    }

    public static Task<TNewResult> ContinueWithShortThread<TResult, TNewResult>(this Task<TResult> task, Func<Task<TResult>, TNewResult> continuationFunction, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithShortThreadCore(continuationFunction, default, continuationOptions);
    }

    public static Task<TNewResult> ContinueWithShortThread<TResult, TNewResult>(this Task<TResult> task, Func<Task<TResult>, TNewResult> continuationFunction, CancellationToken cancellationToken,
        TaskContinuationOptions continuationOptions, TaskScheduler scheduler)
    {
        return task.ContinueWithShortThreadCore(continuationFunction, cancellationToken, continuationOptions);
    }

    private static Task<TNewResult> ContinueWithShortThreadCore<TResult, TNewResult>(this Task<TResult> task, Func<Task<TResult>, TNewResult> continuationFunction, CancellationToken cancellationToken, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWith(continuationFunction, cancellationToken, continuationOptions, GenericShortRunningOneThreadedForegroundTasksScheduler.Instance);
    }

    public static Task<TNewResult> ContinueWithShortThread<TResult, TNewResult>(this Task<TResult> task, Func<Task<TResult>, object, TNewResult> continuationFunction, object state)
    {
        return task.ContinueWithShortThreadCore(continuationFunction, state, default, TaskContinuationOptions.None);
    }

    public static Task<TNewResult> ContinueWithShortThread<TResult, TNewResult>(this Task<TResult> task, Func<Task<TResult>, object, TNewResult> continuationFunction, object state,
        CancellationToken cancellationToken)
    {
        return task.ContinueWithShortThreadCore(continuationFunction, state, cancellationToken, TaskContinuationOptions.None);
    }

    public static Task<TNewResult> ContinueWithShortThread<TResult, TNewResult>(this Task<TResult> task, Func<Task<TResult>, object, TNewResult> continuationFunction, object state,
        TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithShortThreadCore(continuationFunction, state, default, continuationOptions);
    }

    public static Task<TNewResult> ContinueWithShortThread<TResult, TNewResult>(this Task<TResult> task, Func<Task<TResult>, object, TNewResult> continuationFunction, object state,
        CancellationToken cancellationToken, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithShortThreadCore(continuationFunction, state, cancellationToken, continuationOptions);
    }

    private static Task<TNewResult> ContinueWithShortThreadCore<TResult, TNewResult>(this Task<TResult> task, Func<Task<TResult>, object, TNewResult> continuationFunction, object state, CancellationToken cancellationToken, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWith(continuationFunction, state, cancellationToken, continuationOptions, GenericShortRunningOneThreadedForegroundTasksScheduler.Instance);
    }

    // Default scheduler

    public static Task ContinueWithDefault(this Task task, Action<Task> continuationAction)
    {
        return task.ContinueWithDefaultCore(continuationAction, default, TaskContinuationOptions.None);
    }

    public static Task ContinueWithDefault(this Task task, Action<Task> continuationAction, CancellationToken cancellationToken)
    {
        return task.ContinueWithDefaultCore(continuationAction, cancellationToken, TaskContinuationOptions.None);
    }

    public static Task ContinueWithDefault(this Task task, Action<Task> continuationAction, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithDefaultCore(continuationAction, default, continuationOptions);
    }

    public static Task ContinueWithDefault(this Task task, Action<Task> continuationAction, CancellationToken cancellationToken,
                             TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithDefaultCore(continuationAction, cancellationToken, continuationOptions);
    }

    private static Task ContinueWithDefaultCore(this Task task, Action<Task> continuationAction, CancellationToken cancellationToken, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWith(continuationAction, cancellationToken, continuationOptions, TaskScheduler.Default);
    }

    public static Task ContinueWithDefault(this Task task, Action<Task, object> continuationAction, object state)
    {
        return task.ContinueWithDefaultCore(continuationAction, state, default, TaskContinuationOptions.None);
    }

    public static Task ContinueWithDefault(this Task task, Action<Task, object> continuationAction, object state, CancellationToken cancellationToken)
    {
        return task.ContinueWithDefaultCore(continuationAction, state, cancellationToken, TaskContinuationOptions.None);
    }

    public static Task ContinueWithDefault(this Task task, Action<Task, object> continuationAction, object state, TaskScheduler scheduler)
    {
        return task.ContinueWithDefaultCore(continuationAction, state, default, TaskContinuationOptions.None);
    }

    public static Task ContinueWithDefault(this Task task, Action<Task, object> continuationAction, object state, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithDefaultCore(continuationAction, state, default, continuationOptions);
    }

    public static Task ContinueWithDefault(this Task task, Action<Task, object> continuationAction, object state, CancellationToken cancellationToken,
                             TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithDefaultCore(continuationAction, state, cancellationToken, continuationOptions);
    }

    private static Task ContinueWithDefaultCore(this Task task, Action<Task, object> continuationAction, object state,
        CancellationToken cancellationToken, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWith(continuationAction, state, cancellationToken, continuationOptions, TaskScheduler.Default);
    }


    public static Task<TResult> ContinueWithDefault<TResult>(this Task task, Func<Task, TResult> continuationFunction)
    {
        return task.ContinueWithDefaultCore(continuationFunction, default, TaskContinuationOptions.None);
    }

    public static Task<TResult> ContinueWithDefault<TResult>(this Task task, Func<Task, TResult> continuationFunction, CancellationToken cancellationToken)
    {
        return task.ContinueWithDefaultCore(continuationFunction, cancellationToken, TaskContinuationOptions.None);
    }

    public static Task<TResult> ContinueWithDefault<TResult>(this Task task, Func<Task, TResult> continuationFunction, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithDefaultCore(continuationFunction, default, continuationOptions);
    }

    public static Task<TResult> ContinueWithDefault<TResult>(this Task task, Func<Task, TResult> continuationFunction, CancellationToken cancellationToken,
                                               TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithDefaultCore(continuationFunction, cancellationToken, continuationOptions);
    }

    private static Task<TResult> ContinueWithDefaultCore<TResult>(this Task task, Func<Task, TResult> continuationFunction, CancellationToken cancellationToken, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWith(continuationFunction, cancellationToken, continuationOptions, TaskScheduler.Default);
    }


    public static Task<TResult> ContinueWithDefault<TResult>(this Task task, Func<Task, object, TResult> continuationFunction, object state)
    {
        return task.ContinueWithDefaultCore(continuationFunction, state, default, TaskContinuationOptions.None);
    }

    public static Task<TResult> ContinueWithDefault<TResult>(this Task task, Func<Task, object, TResult> continuationFunction, object state, CancellationToken cancellationToken)
    {
        return task.ContinueWithDefaultCore(continuationFunction, state, cancellationToken, TaskContinuationOptions.None);
    }

    public static Task<TResult> ContinueWithDefault<TResult>(this Task task, Func<Task, object, TResult> continuationFunction, object state, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithDefaultCore(continuationFunction, state, default, continuationOptions);
    }

    public static Task<TResult> ContinueWithDefault<TResult>(this Task task, Func<Task, object, TResult> continuationFunction, object state, CancellationToken cancellationToken,
                                               TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithDefaultCore(continuationFunction, state, cancellationToken, continuationOptions);
    }

    private static Task<TResult> ContinueWithDefaultCore<TResult>(this Task task, Func<Task, object, TResult> continuationFunction, object state,
        CancellationToken cancellationToken, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWith(continuationFunction, state, cancellationToken, continuationOptions, TaskScheduler.Default);
    }

    // ...

    public static Task ContinueWithDefault<TResult>(this Task<TResult> task, Action<Task<TResult>> continuationAction)
    {
        return task.ContinueWithDefaultCore(continuationAction, default, TaskContinuationOptions.None);
    }

    public static Task ContinueWithDefault<TResult>(this Task<TResult> task, Action<Task<TResult>> continuationAction, CancellationToken cancellationToken)
    {
        return task.ContinueWithDefaultCore(continuationAction, cancellationToken, TaskContinuationOptions.None);
    }

    public static Task ContinueWithDefault<TResult>(this Task<TResult> task, Action<Task<TResult>> continuationAction, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithDefaultCore(continuationAction, default, continuationOptions);
    }

    public static Task ContinueWithDefault<TResult>(this Task<TResult> task, Action<Task<TResult>> continuationAction, CancellationToken cancellationToken,
                             TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithDefaultCore(continuationAction, cancellationToken, continuationOptions);
    }

    private static Task ContinueWithDefaultCore<TResult>(this Task<TResult> task, Action<Task<TResult>> continuationAction, CancellationToken cancellationToken,
                               TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWith(continuationAction, cancellationToken, continuationOptions, TaskScheduler.Default);
    }


    public static Task ContinueWithDefault<TResult>(this Task<TResult> task, Action<Task<TResult>, object> continuationAction, object state)
    {
        return task.ContinueWithDefaultCore(continuationAction, state, default, TaskContinuationOptions.None);
    }

    public static Task ContinueWithDefault<TResult>(this Task<TResult> task, Action<Task<TResult>, object> continuationAction, object state, CancellationToken cancellationToken)
    {
        return task.ContinueWithDefaultCore(continuationAction, state, cancellationToken, TaskContinuationOptions.None);
    }

    public static Task ContinueWithDefault<TResult>(this Task<TResult> task, Action<Task<TResult>, object> continuationAction, object state, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithDefaultCore(continuationAction, state, default, continuationOptions);
    }

    public static Task ContinueWithDefault<TResult>(this Task<TResult> task, Action<Task<TResult>, object> continuationAction, object state, CancellationToken cancellationToken,
                             TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithDefaultCore(continuationAction, state, cancellationToken, continuationOptions);
    }

    private static Task ContinueWithDefaultCore<TResult>(this Task<TResult> task, Action<Task<TResult>, object> continuationAction, object state, CancellationToken cancellationToken, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWith(continuationAction, state, cancellationToken, continuationOptions, TaskScheduler.Default);
    }


    public static Task<TNewResult> ContinueWithDefault<TResult, TNewResult>(this Task<TResult> task, Func<Task<TResult>, TNewResult> continuationFunction)
    {
        return task.ContinueWithDefaultCore(continuationFunction, default, TaskContinuationOptions.None);
    }

    public static Task<TNewResult> ContinueWithDefault<TResult, TNewResult>(this Task<TResult> task, Func<Task<TResult>, TNewResult> continuationFunction, CancellationToken cancellationToken)
    {
        return task.ContinueWithDefaultCore(continuationFunction, cancellationToken, TaskContinuationOptions.None);
    }

    public static Task<TNewResult> ContinueWithDefault<TResult, TNewResult>(this Task<TResult> task, Func<Task<TResult>, TNewResult> continuationFunction, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithDefaultCore(continuationFunction, default, continuationOptions);
    }

    public static Task<TNewResult> ContinueWithDefault<TResult, TNewResult>(this Task<TResult> task, Func<Task<TResult>, TNewResult> continuationFunction, CancellationToken cancellationToken,
        TaskContinuationOptions continuationOptions, TaskScheduler scheduler)
    {
        return task.ContinueWithDefaultCore(continuationFunction, cancellationToken, continuationOptions);
    }

    private static Task<TNewResult> ContinueWithDefaultCore<TResult, TNewResult>(this Task<TResult> task, Func<Task<TResult>, TNewResult> continuationFunction, CancellationToken cancellationToken, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWith(continuationFunction, cancellationToken, continuationOptions, TaskScheduler.Default);
    }

    public static Task<TNewResult> ContinueWithDefault<TResult, TNewResult>(this Task<TResult> task, Func<Task<TResult>, object, TNewResult> continuationFunction, object state)
    {
        return task.ContinueWithDefaultCore(continuationFunction, state, default, TaskContinuationOptions.None);
    }

    public static Task<TNewResult> ContinueWithDefault<TResult, TNewResult>(this Task<TResult> task, Func<Task<TResult>, object, TNewResult> continuationFunction, object state,
        CancellationToken cancellationToken)
    {
        return task.ContinueWithDefaultCore(continuationFunction, state, cancellationToken, TaskContinuationOptions.None);
    }

    public static Task<TNewResult> ContinueWithDefault<TResult, TNewResult>(this Task<TResult> task, Func<Task<TResult>, object, TNewResult> continuationFunction, object state,
        TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithDefaultCore(continuationFunction, state, default, continuationOptions);
    }

    public static Task<TNewResult> ContinueWithDefault<TResult, TNewResult>(this Task<TResult> task, Func<Task<TResult>, object, TNewResult> continuationFunction, object state,
        CancellationToken cancellationToken, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWithDefaultCore(continuationFunction, state, cancellationToken, continuationOptions);
    }

    private static Task<TNewResult> ContinueWithDefaultCore<TResult, TNewResult>(this Task<TResult> task, Func<Task<TResult>, object, TNewResult> continuationFunction, object state, CancellationToken cancellationToken, TaskContinuationOptions continuationOptions)
    {
        return task.ContinueWith(continuationFunction, state, cancellationToken, continuationOptions, TaskScheduler.Default);
    }
}