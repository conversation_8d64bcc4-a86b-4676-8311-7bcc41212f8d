using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.Database;
using Invictus.Nomenklatura.ExteriorServ;
using Invictus.Nomenklatura.Front;
using Invictus.Nomenklatura.Misc;

using JetBrains.Annotations;

using Microsoft.Extensions.DependencyInjection;

namespace Invictus.Nomenklatura.App2;

[UsedImplicitly]
public class NomenklaturaExteriorServiceBuilder : ExteriorServiceBuilderBase<NomenklaturaExteriorService>
{
    protected override void AddConcealedServices(ServiceCollection concealedServiceCollection)
    {
        // Generic services
        concealedServiceCollection.AddSingleton<IServiceScopes>(prov => new ServiceScopes());
        concealedServiceCollection.AddSingleton(prov => (IServiceScopesInternal)prov.GetRequiredService<IServiceScopes>());

        // Threads and Tasks
        concealedServiceCollection.AddWorkerSingleton<GenericShortRunningForegroundTasksWorker>();
        concealedServiceCollection.AddWorkerSingleton<BackgroundTasksWorker>();
        concealedServiceCollection.AddSingleton<GenericShortRunningOneThreadedForegroundTasksScheduler>();
        concealedServiceCollection.AddSingleton<InvTasks>();
        
        // Database
        concealedServiceCollection.AddKeyedSingleton<IDbOptionsCompletion>("default", (s, key) => new DefaultDbOptionsCompletion(s.GetRequiredService<InvAppConfig>(), (string)key));
        concealedServiceCollection.AddKeyedSingleton<IDbOptionsCompletion>("idws", (s, key) => new DefaultDbOptionsCompletion(s.GetRequiredService<InvAppConfig>(), (string)key));
        
        // Auth
        concealedServiceCollection.AddSingleton<FrontAuthDataSeeder>();
        concealedServiceCollection.AddDbAccess<AuthDbAccess>();
        
        // Other
        concealedServiceCollection.AddFactory<IMethodCallFactory, MethodCallFactory>(s => new object[] { s.GetRequiredService<IServiceScopesInternal>() });
        concealedServiceCollection.AddFactory<IConstructorCallFactory, ConstructorCallFactory>(s => new object[] { s.GetRequiredService<IServiceScopesInternal>() });
        
        concealedServiceCollection.AddSingleton<IServiceScopes>(prov => new ServiceScopes());
        concealedServiceCollection.AddSingleton(prov => (IServiceScopesInternal)prov.GetRequiredService<IServiceScopes>());

        this.RegisterDatabaseTypes(concealedServiceCollection);

        base.AddConcealedServices(concealedServiceCollection);
    }

    protected override void ExposeConcealedServices()
    {
        this.ExposeKeyedSingleton<IDbOptionsCompletion>("default");
        this.ExposeKeyedSingleton<IDbOptionsCompletion>("idws");
        
        this.ExposeSingleton<IServiceScopes>();
        this.ExposeSingleton<InvTasks>();
            
        this.ExposeFactory<IMethodCallFactory>();
        this.ExposeFactory<IConstructorCallFactory>();


        this.ExposeSingleton<IServiceScopes>();
        this.ExposeSingleton<IServiceScopesInternal>();
        // this.ExposeSingleton(typeof(IServiceFactory<>), typeof(ServiceFactory<>));
        this.ExposeSingleton<InvTasks>();
        
        this.ExposeDbAccess<AuthDbAccess>();
        this.ExposeDbContext<AuthDbAccess, AuthDbContext>();
        this.ExposeSingleton<FrontAuthDataSeeder>();
        
        base.ExposeConcealedServices();
    }
}