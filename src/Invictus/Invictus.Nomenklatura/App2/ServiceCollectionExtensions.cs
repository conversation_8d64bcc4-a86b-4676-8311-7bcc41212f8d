using Invictus.Nomenklatura.Misc;
using Invictus.Nomenklatura.Workers;

using Microsoft.Extensions.DependencyInjection;

namespace Invictus.Nomenklatura.App2;

public static partial class ServiceCollectionExtensions
{
    public static IServiceCollection AddNomenklaturaAmbient(this IServiceCollection services)
    {
        services.AddTransient(typeof(ILazyResolve<>), typeof(LazyResolveOnce<>));

        return services;
    }

    public static void ProxyNomenklaturaAmbient(this IServiceCollection services, IServiceProvider preBuiltServices)
    {
        services.AddTransient(typeof(ILazyResolve<>), _ => preBuiltServices.GetRequiredService(typeof(ILazyResolve<>)));
    }
    
    public static IServiceCollection AddFactory<TService>(this IServiceCollection services, Func<IServiceProvider, object[]> factoryAdditionalArgs = null, Type[] preferredConstructor = null)
        where TService : class
    {
        services.AddSingleton<IServiceFactory<TService>>(serviceProvider =>
            new ServiceFactory<TService>(
                serviceProvider,
                serviceProvider.GetService<IServiceScopesInternal>(),
                factoryAdditionalArgs == null ? Array.Empty<object>() : factoryAdditionalArgs(serviceProvider),
                preferredConstructor
            )
        );

        return services;
    }

    public static IServiceCollection AddFactory<TInterface, TImpl>(this IServiceCollection services, Func<IServiceProvider, object[]> factoryAdditionalArgs = null, Type[] preferredConstructor = null)
        where TInterface : class
        where TImpl : TInterface
    {
        services.AddSingleton(serviceProvider =>
            (IServiceFactory<TInterface>)new ServiceFactory<TImpl>(
                serviceProvider,
                serviceProvider.GetRequiredService<IServiceScopesInternal>(),
                factoryAdditionalArgs == null ? Array.Empty<object>() : factoryAdditionalArgs(serviceProvider),
                preferredConstructor
            )
        );

        return services;
    }

    public static IServiceCollection AddSingletonFromFactory<TService>(this IServiceCollection services)
        where TService : class
    {
        services.AddSingleton(serviceProvider => serviceProvider.GetService<IServiceFactory<TService>>().Create());

        return services;
    }

    public static IServiceCollection AddWorkerSingleton<TImpl>(this IServiceCollection services)
        where TImpl : class, IWorkerImpl
    {
        Type[] interfaces = typeof(TImpl).GetInterfaces();

        var workerImplInterfaceTypes = new List<Type>();

        foreach (Type @interface in interfaces)
        {
            if (!@interface.IsGenericType || @interface.GetGenericTypeDefinition() != typeof(IWorkerImpl<>))
                continue;

            Type workedImplType = @interface.GetGenericArguments()[0];

            if (!interfaces.Contains(workedImplType))
                throw new Exception($"Type {typeof(TImpl).FullName} should implement both {typeof(IWorkerImpl<>).Name} and underlying type.");

            workerImplInterfaceTypes.Add(workedImplType);
        }

        return services.AddWorkerSingleton<TImpl>(workerImplInterfaceTypes.ToArray());
    }

    private static IServiceCollection AddWorkerSingleton<TImpl>(this IServiceCollection services, params Type[] workerInterfaces)
        where TImpl : class, IWorkerImpl
    {
        services.AddSingleton<IWorkerFather<TImpl>, WorkerFather<TImpl>>();
        services.AddSingleton<TImpl>();

        foreach (Type workerInterface in workerInterfaces)
        {
            services.AddSingleton(workerInterface, sp => {
                    var wfw = sp.GetRequiredService<IWorkerFather<TImpl>>();

                    // If worker implements multiple worker interfaces and references itself - we want to pre-create all that interfaces.
                    foreach (Type @interface in workerInterfaces)
                        wfw.GetForInterfaceSingleton(@interface);

                    return wfw.GetForInterfaceSingleton(workerInterface);
                }
            );
        }

        return services;
    }
}