using System.ComponentModel.DataAnnotations;

namespace Invictus.Astra.Api;

public class RegisterModel
{
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    [Required]
    public string Username { get; set; } = string.Empty; // Identity uses Username by default

    [Required]
    [DataType(DataType.Password)]
    public string Password { get; set; } = string.Empty;
}

public class LoginModel
{
    [Required]
    // Allow login via Email or Username
    public string LoginIdentifier { get; set; } = string.Empty; // Can be Email or Username

    [Required]
    [DataType(DataType.Password)]
    public string Password { get; set; } = string.Empty;
}

public class TokenResponse
{
    public string Token { get; set; } = string.Empty;
    public DateTime Expiration { get; set; }
}