using Microsoft.IdentityModel.Tokens;

namespace Invictus.Nomenklatura.AppConfig;

public enum InvEnvEnum
{
    Invalid = 0,
    Dev,
    ProdServ,
    IntegrationTestLocal
}

public class NomenklaturaConfiguration
{
    public bool TimersAreAllowed { get; init; }
    public NomenklaturaConfigurationDirs Dirs { get; init; }
    
    public NomenklaturaDatabaseConfiguration Database { get; init; }
    
    public NomenklaturaApiConfiguration ExtApi { get; init; }
}

public class NomenklaturaConfigurationDirs
{
    public string ExeDir { get; init; }
    public string AppDataDir { get; init; }

    public string ExeResourcesDir => Path.Combine(ExeDir, "Resources");
}

public class NomenklaturaDatabaseConfiguration
{
    public string ConnectionString { get; init; }
    
    public string IdwsConnectionString { get; init; }
    
    public string MigrationsAssembly { get; init; }
}

public class NomenklaturaApiConfiguration
{
    public string Issuer { get; init; }
    public string Audience { get; init; }
    public int LifetimeMinutes { get; init; }
    public string SecretKeyRaw { get; init; }
    
    public string AstraHostUrl { get; init; }

    private readonly Lazy<SymmetricSecurityKey> _secretKeyLazy;
    public SymmetricSecurityKey SecretKey => _secretKeyLazy.Value;

    public NomenklaturaApiConfiguration()
    {
        _secretKeyLazy = new Lazy<SymmetricSecurityKey>(() => new SymmetricSecurityKey(
                Encoding.ASCII.GetBytes(SecretKeyRaw.Replace("A", "a").Replace("B", "A").Replace("a", "B"))
            )
        );
    }
}