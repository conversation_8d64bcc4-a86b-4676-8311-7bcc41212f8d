{

  "Nomenklatura": {
    "TimersAreAllowed": true,
    "Dirs": {
      "ExeDir": "${Nomenklatura:PREDEF:ExeDir}"
    },
    "Log": {
      "DefaultTemplate": "[{Level:u3} {MyThreadId}] {SourceClassShort:l} {Message:lj}{NewLine}{Exception}{ExceptionDetail}",
      "EntryTimestamp": "{Timestamp:MM-dd HH:mm:ss.fff}",
      "ShortEntryTimestamp": "{HH:mm:ss.fff}",
      "Dir": "logs"
    }
  },

  "Serilog": {
    "Using": [
      "Invictus.Nomenklatura",
      "Serilog.Extensions.Logging",
      "Serilog.Sinks.File",
      "Serilog.Sinks.Console",
      "Serilog.Expressions",
      "Serilog.Sinks.Debug",
      "Invictus.Jupiter.Shared"
    ],
    "MinimumLevel": {
      "Default": "Verbose",
      "Override": {
        // "Invictus.Nomenklatura.Misc.ResoluteEventBase": "Warning"
      }
    },
    "LevelSwitches": { "terminalControlSwitch": "Debug" },
    "WriteTo": [
      {
        // 1: Global logging
        "Name": "Logger",
        "Args": {
          "configureLogger": {
            "WriteTo": [
              {
                // Invictus console and terminal output
                "Name": "Logger",
                "Args": {
                  "controlLevelSwitch": "$terminalControlSwitch",
                  "configureLogger": {
                    "WriteTo": [
                      {
                        "Name": "Console",
                        "Args": {
                          "outputTemplate": "${Nomenklatura:Log:ShortEntryTimestamp} ${Nomenklatura:Log:DefaultTemplate}",
                          "theme": "Invictus.Nomenklatura.Logg.LogExtensions::InvictusConsoleTheme, Invictus.Nomenklatura"
                        }
                      },
                      {
                        "Name": "Terminal",
                        "Args": {
                          "formatter": {
                            "type": "Serilog.Formatting.Display.MessageTemplateTextFormatter, Serilog",
                            "outputTemplate": "${Nomenklatura:Log:ShortEntryTimestamp} ${Nomenklatura:Log:DefaultTemplate}"
                          }
                        }
                      }
                    ],
                    "MinimumLevel": {
                      "Default": "Debug",
                      "ControlledBy": "$terminalControlSwitch"
                    },

                    "Filter": [
                      {
                        "Name": "ByIncludingOnly",
                        "Args": {
                          "expression": "SourceContext like 'Inv%' or SourceContext like 'iris%' or @Level >= 'Information'"
                        }
                      }
                    ]
                  }
                }
              },
              {
                // Invictus file verbose
                "Name": "Logger",
                "Args": {
                  "configureLogger": {
                    "WriteTo": [
                      {
                        "Name": "File",
                        "Args": {
                          "formatter": {
                            "type": "Serilog.Formatting.Display.MessageTemplateTextFormatter, Serilog",
                            "outputTemplate": "${Nomenklatura:Log:EntryTimestamp} ${Nomenklatura:Log:DefaultTemplate}"
                          },
                          "path": "${Nomenklatura:Dirs:AppDataDir}/${Nomenklatura:Log:Dir}/${Nomenklatura:PREDEF:FileStartupTime2}_InvVrb.log",
                          "shared": "true",
                          "flushToDiskInterval": "00:00:01",
                          "fileSizeLimitBytes": 1048576001
                        }
                      }
                    ],
                    "MinimumLevel": {
                      "Default": "Verbose"
                    },
                    "Filter": [
                      {
                        "Name": "ByIncludingOnly",
                        "Args": {
                          "expression": "SourceContext like 'Inv%' or SourceContext like 'iris%' or @Level >= 'Information'"
                        }
                      }
                    ]
                  }
                }
              },
              {
                // All file information
                "Name": "Logger",
                "Args": {
                  "configureLogger": {
                    "WriteTo": [
                      {
                        "Name": "File",
                        "Args": {
                          "formatter": {
                            "type": "Serilog.Formatting.Display.MessageTemplateTextFormatter, Serilog",
                            "outputTemplate": "${Nomenklatura:Log:EntryTimestamp} ${Nomenklatura:Log:DefaultTemplate}"
                          },
                          "path": "${Nomenklatura:Dirs:AppDataDir}/${Nomenklatura:Log:Dir}/${Nomenklatura:PREDEF:FileStartupTime2}_Everything.log",
                          "shared": "true",
                          "flushToDiskInterval": "00:00:01",
                          "fileSizeLimitBytes": 1048576001
                        }
                      }
                    ],
                    "MinimumLevel": {
                      "Default": "Verbose"
                    }
                  }
                }
              }
              /*
              {
                "Name": "Logger",
                "Args": {
                  "configureLogger": {
                    "WriteTo": [
                      {
                        "Name": "Sejil",
                        "Args": {
                          "enabled": "FaceApplicationConfig.SejilLoggingEnabled",
                          "restrictedToMinimumLevel": "Verbose"
                        }
                      }
                    ],
                    "Filter": [
                      {
                        "Name": "ByExcluding",
                        "Args": {
                          "expression": "FrameworksContextsLogEventFilter.SourceContextsToFilterOutMostFormatted and @Level == 'Warning'"
                        }
                      }
                    ]
                  }
                }
              }
              */
            ]/*,
            "Filter": [
              {
                "Name": "ByIncludingOnly",
                "Args": {
                  "expression": "DefaultContext is not null"
                }
              }
            ]*/
          }  // 1: Global logging args:configureLogger
        }
      },
      {
        // 2: Request logging
        "Name": "Logger",
        "Args": {
          "configureLogger": {
            "WriteTo": [
              {
                "Name": "File",
                "Args": {
                  "formatter": {
                    "type": "Serilog.Formatting.Display.MessageTemplateTextFormatter, Serilog",
                    "outputTemplate": "${Nomenklatura:Log:EntryTimestamp} [{Level:u3} {MyThreadId} {RequestId}] {SourceClassShort:l} {Message:lj}{NewLine}{Exception}{ExceptionDetail}"
                  },
                  "path": "${Nomenklatura:Dirs:AppDataDir}/${Nomenklatura:Log:Dir}/web/${Nomenklatura:PREDEF:FileStartupTime2}_Request.log",
                  "shared": "true",
                  "flushToDiskInterval": "00:00:01",
                  "fileSizeLimitBytes": 1048576001
                }
              }
            ],
            "Filter": [
              {
                "Name": "ByIncludingOnly",
                "Args": {
                  "expression": "RequestContext is not null"
                }
              }
            ]
          } // 2: Request logging args:configureLogger
        }
      }
    ], // WriteTo
    "Enrich": [
      "WithMyThreadId",
      {
        "Name": "WithSourceContextPropertyAndAddPaddingEnricher",
        "Args": {
          "padding": 90,
          "trimClassName": true
        }
      },
      "FromGlobalLogContext",
      "FromLogContext",
      "WithExceptionDetails"
    ]
  }
}