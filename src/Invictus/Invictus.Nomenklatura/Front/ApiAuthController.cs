using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.AppConfig;
using Invictus.Nomenklatura.Logg;

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;

using SignInResult = Microsoft.AspNetCore.Identity.SignInResult;

namespace Invictus.Nomenklatura.Front;

[Route("api/[controller]")]
[ApiController]
public class ApiAuthController : ControllerBase
{
    private readonly UserManager<InvIdentityUser> _userManager;
    private readonly SignInManager<InvIdentityUser> _signInManager;
    private readonly NomenklaturaApiConfiguration _configuration;
    
    private readonly ILogger _logger = InvLog.Logger<ApiAuthController>();

    public ApiAuthController(
        UserManager<InvIdentityUser> userManager,
        SignInManager<InvIdentityUser> signInManager,
        InvAppConfig appConfig
    )
    {
        _userManager = userManager;
        _signInManager = signInManager;
        _configuration = appConfig.BasicConfiguration.ExtApi;
    }

    // Model for Login Request
    public record LoginRequest2(string Email, string Password);

    // Model for Login Response
    public record LoginResponse2(bool Success, string Token, string Message);

    [HttpPost("login")]
    [ProducesResponseType(typeof(LoginResponse2), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(LoginResponse2), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(LoginResponse2), StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Login([FromBody] LoginRequest2 request)
    {
        if (!ModelState.IsValid)
        {
            return this.BadRequest(new LoginResponse2(false, "", "Invalid login request."));
        }

        InvIdentityUser user = await _userManager.FindByEmailAsync(request.Email);

        if (user == null)
        {
            _logger.Warning("Login attempt failed for email {Email}: User not found.", request.Email);

            return this.Unauthorized(new LoginResponse2(false, "", "Invalid credentials."));
        }

        // Use PasswordSignInAsync for robust checks (lockout, etc.)
        SignInResult result = await _signInManager.CheckPasswordSignInAsync(user, request.Password, lockoutOnFailure: false); // Set lockoutOnFailure=true in prod

        if (!result.Succeeded)
        {
            _logger.Warning("Login attempt failed for email {Email}: Invalid password.", request.Email);

            return this.Unauthorized(new LoginResponse2(false, "", "Invalid credentials."));
        }

        // **** IMPORTANT: Check if the user has the required role for API access ****
        IList<string> roles = await _userManager.GetRolesAsync(user);

        if (!roles.Contains(FrontConst.API_USER_ROLE_NAME)) // Use the constant from DataSeeder
        {
            _logger.Warning("Login successful for {Email} but user lacks '{ApiRoleName}' role for API access.", request.Email, FrontConst.API_USER_ROLE_NAME);

            return this.Unauthorized(new LoginResponse2(false, "", $"User is not authorized to access the API. Required role: '{FrontConst.API_USER_ROLE_NAME}'."));
        }

        _logger.Information("User {Email} logged in successfully and has API access.", request.Email);

        // User is valid and has the ApiUser role, generate JWT token
        string token = this.GenerateJwtToken(user, roles);

        return this.Ok(new LoginResponse2(true, token, "Login successful."));
    }

    private string GenerateJwtToken(InvIdentityUser user, IList<string> roles)
    {
        var claims = new List<Claim>
        {
            new Claim(JwtRegisteredClaimNames.Sub,   user.Id), // Subject (usually user ID)
            new Claim(JwtRegisteredClaimNames.Email, user.Email!),
            new Claim(JwtRegisteredClaimNames.Jti,   Guid.NewGuid().ToString()), // Unique token ID
            new Claim(ClaimTypes.NameIdentifier,     user.Id),                   // Standard claim for User.Identity.NameIdentifier
            new Claim(ClaimTypes.Name,               user.UserName!)             // Standard claim for User.Identity.Name
        };

        // Add role claims
        foreach (string role in roles)
        {
            claims.Add(new Claim(ClaimTypes.Role, role));
        }
        
        var creds = new SigningCredentials(_configuration.SecretKey, SecurityAlgorithms.HmacSha256);
        DateTime expires = DateTime.UtcNow.AddHours(200); // Token expiration time (adjust as needed)

        var token = new JwtSecurityToken(
            issuer: _configuration.Issuer,
            audience: _configuration.Audience,
            claims: claims,
            expires: expires,
            signingCredentials: creds
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }
}