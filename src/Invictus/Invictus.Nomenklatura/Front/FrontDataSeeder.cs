using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Logg;

using Microsoft.AspNetCore.Identity;

namespace Invictus.Nomenklatura.Front;

public class FrontDataSeeder
{
    private readonly UserManager<InvIdentityUser> _userManager;
    private readonly RoleManager<InvIdentityRole> _roleManager;
    private readonly ILogger _logger = InvLog.Logger<FrontDataSeeder>();

    public FrontDataSeeder(UserManager<InvIdentityUser> userManager, RoleManager<InvIdentityRole> roleManager)
    {
        _userManager = userManager;
        _roleManager = roleManager;
    }

    public async Task SeedRolesAsync()
    {
        string[] roleNames =
        {
            FrontConst.ADMIN_ROLE_NAME, FrontConst.API_USER_ROLE_NAME, FrontConst.NOBODY_ROLE_NAME
        }; // Add roles you need

        foreach (string roleName in roleNames)
        {
            bool roleExist = await _roleManager.RoleExistsAsync(roleName);

            if (roleExist)
                continue;

            // Create the roles and seed them to the database
            IdentityResult result = await _roleManager.CreateAsync(new InvIdentityRole(roleName));

            if (result.Succeeded)
            {
                _logger.Information("Role '{RoleName}' created successfully.", roleName);
            } else
            {
                // Log errors if creation fails
                foreach (IdentityError error in result.Errors)
                {
                    _logger.Error("Error creating role '{RoleName}': {ErrorDescription}", roleName, error.Description);
                }
            }
        }
    }

    public async Task SeedUsersAsync()
    {
        // --- Seed an Admin User ---
        InvIdentityUser adminUser = await _userManager.FindByEmailAsync("<EMAIL>");

        if (adminUser == null)
        {
            adminUser = new InvIdentityUser()
            {
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                EmailConfirmed = true // Typically confirm emails
            };

            IdentityResult result = await _userManager.CreateAsync(adminUser, "GivenBasic22Medicine");

            if (result.Succeeded)
            {
                _logger.Information("Admin user created successfully.");

                // Assign roles
                await _userManager.AddToRoleAsync(adminUser, FrontConst.ADMIN_ROLE_NAME);
                await _userManager.AddToRoleAsync(adminUser, FrontConst.API_USER_ROLE_NAME); // Admin can also use API
            } else
            {
                _logger.Error("Error creating admin user: {Errors}", string.Join(", ", result.Errors.Select(e => e.Description)));
            }
        }
        
        // --- Seed a specific API User ---
        InvIdentityUser apiUser = await _userManager.FindByEmailAsync("<EMAIL>");

        if (apiUser == null)
        {
            apiUser = new InvIdentityUser()
            {
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                EmailConfirmed = true
            };

            IdentityResult result = await _userManager.CreateAsync(apiUser, "KorpuskularGabenKraft3");

            if (result.Succeeded)
            {
                _logger.Information("API user created successfully.");

                // Assign ONLY the ApiUser role
                await _userManager.AddToRoleAsync(apiUser, FrontConst.API_USER_ROLE_NAME);
            } else
            {
                _logger.Error("Error creating API user: {Errors}", string.Join(", ", result.Errors.Select(e => e.Description)));
            }
        }
    }
}