using System.Net;
using System.Text.Json.Serialization;

namespace Invictus.Nomenklatura.Web;

public interface IWebResponse
{
    public HttpStatusCode StatusCode { get; }

    public bool IsSuccess { get; }
    public bool IsFail { get; }
    
    public object Result { get; }
}

[JsonConverter(typeof(WebResponseJsonConverterFactory))]

public readonly struct WebResponse<TSuccess, TFail> : IWebResponse
    where TSuccess : class
    where TFail : class
{
    public TSuccess Success { get; init; }
    public TFail Fail { get; init; }

    public HttpStatusCode StatusCode { get; }

    public bool IsSuccess => Success != null;
    public bool IsFail => Fail != null;
    
    object IWebResponse.Result => IsSuccess ? Success : Fail;

    public static WebResponse<TSuccess, TFail> SuccessNew(TSuccess success, HttpStatusCode statusCode)
    {
        return new WebResponse<TSuccess, TFail>(success, statusCode);
    }

    public static WebResponse<TSuccess, TFail> FailNew(TFail fail, HttpStatusCode statusCode)
    {
        return new WebResponse<TSuccess, TFail>(fail, statusCode);
    }

    private WebResponse(TSuccess success, HttpStatusCode statusCode)
    {
        Success = success;
        Fail = null;
        StatusCode = statusCode;
    }

    private WebResponse(TFail fail, HttpStatusCode statusCode)
    {
        Fail = fail;
        Success = null;
        StatusCode = statusCode;
    }
}