using System.Net;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Text.Json.Serialization;

using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;

using Serilog.Context;

namespace Invictus.Nomenklatura.Web;

public class NomenklaturaWebResponseHandler : DelegatingHandler
{
    private static int _NextRequestId;
    
    private readonly ILogger _logger;
    private readonly ILogger _requestLogger;

    public NomenklaturaWebResponseHandler(ILogger logger, ILogger requestLogger)
    {
        _logger = logger;
        _requestLogger = requestLogger;
    }
    
    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        object responseObject = await WebRequestWithRetry.WebCallWithRetry(
            _logger,
            () => this.InnerSendAsync(request, cancellationToken),
            this.InterceptRetry,
            cancellationToken
        );
        
        var response = responseObject as HttpResponseMessage;
        
        // We'll create a custom content type that knows how to handle WebResponse deserialization
        response.Content = new NomenklaturaWebResponseContent(
            response.Content,
            response.StatusCode,
            response.Headers
        );

        return response;
    }

    private async Task<HttpResponseMessage> InnerSendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        string requestBody = "";
        
        if (request.Content != null)
        {
            requestBody = await request.Content.ReadAsStringAsync(cancellationToken);
        }
        
        int requestId = Interlocked.Increment(ref _NextRequestId);
        
        using IDisposable requestIdProp = LogContext.PushProperty("RequestId", requestId);
        
        _logger.Debug($"Web Request #{requestId} {request.Method} {request.RequestUri}");
        
        _requestLogger.Debug("Web Request: {Method} {Uri}\n{Headers}\n{Body} \n\n",
            request.Method,
            request.RequestUri,
            string.Join("\n", request.Headers.Select(h => h.Key + ": " + string.Join(", ", h.Value))),
            InvJsonSerializer.TryPrettifyJson(requestBody)
        );
        
        HttpResponseMessage response = await base.SendAsync(request, cancellationToken);

        string responseBody = await response.Content.ReadAsStringAsync(cancellationToken);
        
        _logger.Debug($"Web Response #{requestId} {request.Method} {request.RequestUri}");

        _requestLogger.Debug("Web Response: {StatusCode}\n{Headers}\n{Body}\n\n",
            response.StatusCode,
            string.Join("\n", response.Headers.Select(h => h.Key + ": " + string.Join(", ", h.Value))),
            InvJsonSerializer.TryPrettifyJson(responseBody)
        );

        return response;
    }
    
    private async Task<WebRequestWithRetryResult> InterceptRetry(Func<Task<HttpResponseMessage>> action)
    {
        try
        {
            HttpResponseMessage res = await action();

            return WebRequestWithRetryResult.Success(res);
        }
        catch (Exception exc)
        {
            _logger.Information("WebRequest Interceptor Exception");

            RetryAdvice advice = WebRequestRetryExceptionBeHandler.GetWebRequestRetryAdvice(_logger, exc);

            if (advice == RetryAdvice.WaitALot)
                advice = RetryAdvice.ThrowFurther;

            return WebRequestWithRetryResult.FromExceptionAdvice(exc, advice);
        }
    }
}

public class NomenklaturaWebResponseContent : HttpContent
{
    private readonly HttpContent _originalContent;
    private readonly HttpStatusCode _statusCode;
    
    public HttpStatusCode StatusCode => _statusCode;
    
    public NomenklaturaWebResponseContent(
        HttpContent originalContent,
        HttpStatusCode statusCode,
        HttpResponseHeaders headers)
    {
        _originalContent = originalContent;
        _statusCode = statusCode;

        // Copy headers from original content
        foreach (KeyValuePair<string, IEnumerable<string>> header in originalContent.Headers)
        {
            Headers.TryAddWithoutValidation(header.Key, header.Value);
        }
    }
    
    protected override Task SerializeToStreamAsync(
        Stream stream,
        TransportContext context,
        CancellationToken cancellationToken)
    {
        return _originalContent.CopyToAsync(stream, context, cancellationToken);
    }

    protected override Task SerializeToStreamAsync(Stream stream, TransportContext context)
    {
        return this.SerializeToStreamAsync(stream, context, CancellationToken.None);
    }

    protected override bool TryComputeLength(out long length)
    {
        length = 0;
        return false;
    }
}

public class WebResponseJsonConverterFactory : JsonConverterFactory
{
    public static SemaphoreSlim StatusCodeSemaphore { get; } = new (1, 1);
    public static HttpStatusCode StatusCodeToDeserializeFor { get; set; }
    
    private readonly ILogger _logger = InvLog.Logger<WebResponseJsonConverterFactory>();
    
    public override bool CanConvert(Type typeToConvert)
    {
        if (!typeToConvert.IsGenericType)
        {
            return false;
        }

        return typeToConvert.GetGenericTypeDefinition() == typeof(WebResponse<,>);
    }

    public override JsonConverter CreateConverter(
        Type typeToConvert,
        JsonSerializerOptions options)
    {
        Type successType = typeToConvert.GetGenericArguments()[0];
        Type failType = typeToConvert.GetGenericArguments()[1];

        Type converterType = typeof(WebResponseJsonConverter<,>).MakeGenericType(successType, failType);

        return (JsonConverter)Activator.CreateInstance(converterType, _logger);
    }

    private class WebResponseJsonConverter<TSuccess, TFail> : JsonConverter<WebResponse<TSuccess, TFail>>
        where TSuccess : class
        where TFail : class
    {
        private readonly ILogger _logger;

        public WebResponseJsonConverter(ILogger logger)
        {
            _logger = logger;
        }
        
        public override WebResponse<TSuccess, TFail> Read(
            ref Utf8JsonReader reader,
            Type typeToConvert,
            JsonSerializerOptions options)
        {
            if (reader.TokenType != JsonTokenType.StartObject)
            {
                throw new JsonException();
            }
            
            HttpStatusCode statusCode = StatusCodeToDeserializeFor;

            JsonDocument content = JsonDocument.ParseValue(ref reader);

            bool retried = false;
        _retry:

            try
            {
                if (statusCode.IsSuccessStatusCode())
                {
                    var success = content.Deserialize<TSuccess>(options);

                    return WebResponse<TSuccess, TFail>.SuccessNew(success, statusCode);
                } else
                {
                    var fail = content.Deserialize<TFail>(options);

                    return WebResponse<TSuccess, TFail>.FailNew(fail, statusCode);
                }
            }
            catch (JsonException exc)
            {
                if (!retried && options.UnmappedMemberHandling == JsonUnmappedMemberHandling.Disallow)
                {
                    options = new JsonSerializerOptions(options);
                    options.UnmappedMemberHandling = JsonUnmappedMemberHandling.Skip;
                    retried = true;
                    
                    _logger.Warning(exc, "First attempt to deserialize WebResponse failed.");
                    
                    goto _retry;
                }

                throw;
            }
            finally
            {
                if (retried)
                    options.UnmappedMemberHandling = JsonUnmappedMemberHandling.Disallow;
            }
        }

        public override void Write(
            Utf8JsonWriter writer,
            WebResponse<TSuccess, TFail> value,
            JsonSerializerOptions options)
        {
            if (value.IsSuccess)
            {
                JsonSerializer.Serialize(writer, value.Success, options);
            }
            else
            {
                JsonSerializer.Serialize(writer, value.Fail, options);
            }
        }
    }
}
