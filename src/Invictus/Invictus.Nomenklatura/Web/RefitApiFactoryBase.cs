using System.Net;
using System.Reflection;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;

using Refit;

namespace Invictus.Nomenklatura.Web;

public abstract class RefitApiFactoryBase<T> : IProvider<T>
{
    private readonly ILogger _logger = InvLog.Logger<T>();
    private readonly ILogger _requestLogger;
    
    private readonly string _baseUrl;

    private readonly RefitSettings _refitSettings;
    
    protected RefitApiFactoryBase(InvTasks invTasks, string baseUrl)
    {
        _baseUrl = baseUrl;

        _requestLogger = InvLog.RequestLogger<T>();

        _refitSettings = new RefitSettings
        {
            ContentSerializer =
                new CustomJsonContentSerializer(
                    invTasks,
                    new SystemTextJsonContentSerializer(
                        new InvJsonSerializer().GetSettingsForApis()
                    )
                )
        };
    }

    public T Create()
    {
        var handler = new HttpClientHandler();
        
        var webResponseHandler = new NomenklaturaWebResponseHandler(_logger, _requestLogger);
        webResponseHandler.InnerHandler = handler;

        var httpClient = new HttpClient(webResponseHandler)
        {
            BaseAddress = new Uri(_baseUrl)
        };

        this.ModifyHttpClient(httpClient);

        return RestService.For<T>(httpClient, _refitSettings);
    }

    protected abstract void ModifyHttpClient(HttpClient httpClient);
}

public class CustomJsonContentSerializer : IHttpContentSerializer
{
    private readonly InvTasks _invTasks;
    private readonly IHttpContentSerializer _innerSerializer;

    public CustomJsonContentSerializer(InvTasks invTasks, IHttpContentSerializer innerSerializer)
    {
        _invTasks = invTasks;
        _innerSerializer = innerSerializer ?? throw new ArgumentNullException(nameof(innerSerializer));
    }

    public HttpContent ToHttpContent<T>(T item)
    {
        var content = _innerSerializer.ToHttpContent(item);
        
        return content;
    }

    public async Task<T> FromHttpContentAsync<T>(HttpContent content, CancellationToken cancellationToken = default)
    {
        if (!typeof(T).IsAssignableTo(typeof(IWebResponse)))
            return await _innerSerializer.FromHttpContentAsync<T>(content, cancellationToken);
        
        var httpResponseMessage = content as NomenklaturaWebResponseContent;
        HttpStatusCode statusCode = httpResponseMessage?.StatusCode ?? 0;

        try
        {
            // I am tired, this cannot be solved in a different way
            await WebResponseJsonConverterFactory.StatusCodeSemaphore.WaitAsync(cancellationToken);
            
            WebResponseJsonConverterFactory.StatusCodeToDeserializeFor = statusCode;
            
            return await _innerSerializer.FromHttpContentAsync<T>(content, cancellationToken);
        }
        finally
        {
#pragma warning disable CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed
            _invTasks.RunShort(() => WebResponseJsonConverterFactory.StatusCodeSemaphore.Release(), "FromHttpContentAsyncFreeStatusSemaphore");
#pragma warning restore CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed
        }
    }
    
    public string GetFieldNameForProperty(PropertyInfo propertyInfo)
    {
        string fieldName = _innerSerializer.GetFieldNameForProperty(propertyInfo);
        
        return fieldName;
    }
}