using Castle.DynamicProxy;

using Invictus.Nomenklatura.Workers;

namespace Invictus.Nomenklatura.Web;

public abstract class WebRequestWithRetryWorkerInterceptor<T> : IInterceptor
    where T : IWorkerImpl
{
    protected readonly T _worker;
    
    protected ILogger Log => _worker.Log;

    protected WebRequestWithRetryWorkerInterceptor(T worker)
    {
        _worker = worker;
    }

    public void Intercept(IInvocation invocation)
    {
        invocation.ReturnValue = this.InterceptAsync(invocation);
    }

    private Task InterceptAsync(IInvocation invocation)
    {
        return WebRequestWithRetry.WebCallWithRetry(_worker.Log, invocation, this.InterceptRetry, _worker.Core.StopWorkCancellationToken);
    }

    protected abstract Task<WebRequestWithRetryResult> InterceptRetry(IInvocation invocation);
}