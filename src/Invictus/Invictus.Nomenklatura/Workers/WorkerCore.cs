using System.Collections.Concurrent;
using System.Diagnostics;
using System.Linq.Expressions;
using System.Reflection;
using System.Runtime.CompilerServices;

using Castle.Core.Internal;
using Castle.DynamicProxy;

using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Misc;

using JetBrains.Annotations;

using OneOf;

namespace Invictus.Nomenklatura.Workers;

public class WorkerCore
{
    private static readonly ProxyGenerator _ProxyGenerator = new();

    private static int _NextGlobalWorkItemId;

    internal static int GetNextGlobalWorkItemId()
    {
        return Interlocked.Increment(ref _NextGlobalWorkItemId);
    }

    private readonly IInvAppLifetime _applicationAppLifetime;
    private readonly ILogger _logger;

    public WorkerConfiguration Configuration { get; }

    private readonly IWorkerImpl _methods;

    private readonly record struct MethodKey(
        string InterfaceFullName,
        string MethodName,
        Type[] GenericArguments
    );

    internal readonly record struct MethodParameterCharacteristics(string ParamName, bool Log);

    internal readonly record struct MethodCharacteristics(
        OneOf<CompiledDelegateWithInstance, Action> CompiledDelegate,
        List<MethodParameterCharacteristics> Params,
        bool Log,
        CanFailAttribute CanFail
    );

    private readonly Dictionary<MethodKey, MethodCharacteristics> _methodNameToCharacteristics = new(MethodKeyEqualityComparer.Instance);

    private Thread _thread;

    private Task _taskCycle;
    private Thread _taskThread;
    private volatile bool _taskCycleIsRunning;

    private readonly MyTaskScheduler _taskScheduler;
    private readonly bool _isThread;

    private readonly CancellationTokenSource _stopWorkThreadCancellationTokenSource;
    private readonly CancellationTokenSource _mainCancellationTokenSource;
    private volatile bool _ignoringCancellationRegion;
    private WhenAllCancellationTokenRegistration _currentCancelMainRegistration;
    private readonly Lock _cancellationSyncRoot = new ();

    private readonly Dictionary<string, int> _failuresInARow = new();

    private readonly ConcurrentDictionary<Type, object> _forInterfaces = new();

    private readonly DynamicPriorityTimedQueue<WorkItemBase> _queue = new();

    private readonly TimelinePerformanceCounter _performanceCounter;
    private readonly string _name;

    public TimelinePerformanceCounter.Report GetPerformanceReportAndFlush()
    {
        return _performanceCounter.GetReportAndFlush();
    }

    private int _nextWorkItemId;

    internal int GetNextWorkItemId()
    {
        return Interlocked.Increment(ref _nextWorkItemId);
    }
    
    public CancellationToken StopWorkCancellationToken => _stopWorkThreadCancellationTokenSource.Token;

    public bool WasStarted { get; private set; }
    public bool IsRunning { get; private set; }
    
    public bool HasWorkItemsInQueue => _queue.Count != 0;
    public string Name => _name;

    public WorkerCore(IInvAppLifetime applicationAppLifetime, ILogger logger, WorkerConfiguration configuration, IWorkerImpl methods)
    {
        _applicationAppLifetime = applicationAppLifetime;
        _logger = logger;
        Configuration = configuration;
        _methods = methods;
        _isThread = Configuration.Mechanism.IsT0;
        _name = Configuration.Mechanism.Match(thread => thread.Name, scheduler => scheduler.Name);

        _stopWorkThreadCancellationTokenSource = configuration.Mechanism.IsT0 && configuration.Mechanism.AsT0.IsBackground 
            ? new CancellationTokenSource() 
            : CancellationTokenSource.CreateLinkedTokenSource(_applicationAppLifetime.ApplicationStopping);
        
        _mainCancellationTokenSource = new CancellationTokenSource();

        this.InitialCancellationToken();

        _taskScheduler = new MyTaskScheduler(this);

        _performanceCounter = new TimelinePerformanceCounter(this.IsMyThread);
    }

    public void Initialize()
    {
        WasStarted = true;

        Configuration.Mechanism.Switch(
            thread => {
                if (_thread != null)
                    throw new Exception("");

                _thread = new Thread(this.RunDedicatedThreadCycle);
                _thread.Name = "[InvThread] " + thread.Name;
                _thread.Priority = thread.ThreadPriority;
                _thread.IsBackground = thread.IsBackground;
                _thread.UnsafeStart();
            },
            scheduler => {
                if (scheduler.MaximumConcurrencyLevel != 1)
                    throw new NotImplementedException();

                _mainCancellationTokenSource.Token.Register(this.EnsureTaskCycle);

                IsRunning = true;

                this.EnsureTaskCycle();
            }
        );
    }

    private void EnsureTaskCycle()
    {
        lock (this)
        {
            if (_taskCycleIsRunning)
                return;

            _taskCycleIsRunning = true;
            _taskCycle = Task.Factory.StartNew(this.RunTaskCycle, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default);
            _taskCycle.ContinueWithShortThread(this.OnAfterTaskCycleExit);
        }
    }

    private void OnAfterTaskCycleExit(Task obj)
    {
        if (_queue.Count > 0)
            this.EnsureTaskCycle();
    }

    public static ThreadLocal<bool> IsInLogEmit = new ThreadLocal<bool>();

    private void EnqueueWorkItem(WorkItemBase workItem)
    {
        if (_mainCancellationTokenSource.IsCancellationRequested)
        {
            if (!IsInLogEmit.Value)
                _logger.Warning($"{workItem.LogTaskString} was queued cancelled. " + new StackTrace());
            workItem.CancellationTokenSource.Cancel();
        }

        try
        {
            checked
            {
                long priorityAdd = SynchronizationContext.Current is WorkItemBase synchCtx
                    ? -synchCtx.GlobalWorkItemId // Tasks arrive in queued sequence but too long telegram messages may be split...
                    : int.MaxValue * 1000L;

                long priority = workItem.GlobalWorkItemId * 1000000000L + priorityAdd;

                _queue.Enqueue(workItem, priority);
            }
        }
        catch (OverflowException e)
        {
            Task.Factory.StartNew(() => {
                    if (!IsInLogEmit.Value)
                        _logger.Fatal(e, "Stopping application because of WorkerCore ids overflow.");

                    Task.Factory.StartNew(() => {
                            _applicationAppLifetime.StopApplication();
                        }
                    );
                }
            );
        }

        if (!_isThread)
        {
            this.EnsureTaskCycle();
        }
    }

    private void RunDedicatedThreadCycle()
    {
        try
        {
            WorkersGod.AddWorkerThreadId(this);

            _performanceCounter.EnterBlock("Idle");

            _logger.Verbose($"INV Thread '{_name}' has started");

            IsRunning = true;

            while (true)
            {
                if (_queue.Count == 0 && _mainCancellationTokenSource.IsCancellationRequested)
                    return;

                this.ProcessNextWorkItem(null);
            }
        }
        catch (Exception exc)
        {
            _logger.Fatal(exc, $"INV Thread '{_name}' is Faulted. Stopping application.");

            IsRunning = false;

            _applicationAppLifetime.StopApplication();
        }
        finally
        {
            IsRunning = false;

            _logger.Information($"INV Thread '{_name}' has ended.");

            WorkersGod.RemoveWorkerThreadId(this);
        }
    }

    private void RunTaskCycle()
    {
        bool returningIntentionally = false;

        try
        {
            WorkersGod.AddWorkerThreadId(this);

            _performanceCounter.EnterBlock("Idle");

            _logger.Verbose($"INV Task cycle '{_name}' has started");

            _taskThread = Thread.CurrentThread;
            _taskCycleIsRunning = true;

            while (true)
            {
                if (!this.ProcessNextWorkItem(TimeSpan.FromSeconds(5)))
                    break;
            }

            if (_mainCancellationTokenSource.IsCancellationRequested)
                returningIntentionally = true;
        }
        catch (Exception exc)
        {
            _logger.Fatal(exc, $"INV Task cycle '{_name}' is Faulted. Stopping application.");

            IsRunning = false;

            _applicationAppLifetime.StopApplication();
        }
        finally
        {
            if (returningIntentionally)
                IsRunning = false;

            _taskCycleIsRunning = false;
            _taskThread = null;
            _taskCycle = null;

            _logger.Verbose($"INV Task cycle '{_name}' has ended.");

            WorkersGod.RemoveWorkerThreadId(this);
        }
    }

    private bool ProcessNextWorkItem(TimeSpan? timeout)
    {
        WorkItemBase item = null;

        if (_mainCancellationTokenSource.IsCancellationRequested && _queue.Count > 0)
        {
            item = _queue.Take(default);
        } else
        {
            CancellationTokenSource cts = timeout == null
                ? _mainCancellationTokenSource
                : CancellationTokenSource.CreateLinkedTokenSource(_mainCancellationTokenSource.Token, new CancellationTokenSource(timeout.Value).Token);

            item = _queue.Take(cts.Token);
        }

        if (item == null)
            return false;
        
        _performanceCounter.EnterBlock("ExecuteOuter");

        item.CallTaskOrCallback();

        _performanceCounter.EnterBlock("Idle");
        
        return true;
    }

    private void InitialCancellationToken()
    {
        lock (_cancellationSyncRoot)
        {
            _currentCancelMainRegistration = new WhenAllCancellationTokenRegistration(_mainCancellationTokenSource, _stopWorkThreadCancellationTokenSource.Token);
        }
    }

    public IDisposable IgnoreCancellationRegion()
    {
        lock (_cancellationSyncRoot)
        {
            if (_ignoringCancellationRegion)
                return VoidDisposable.Instance;

            _currentCancelMainRegistration.Dispose();
            _currentCancelMainRegistration = null;

            _logger.Verbose("Entered no-cancel region.");

            return new ActionDisposable(this.EndIgnoringCancellationRegion);
        }
    }

    private void EndIgnoringCancellationRegion(bool finalizer)
    {
        lock (_cancellationSyncRoot)
        {
            if (finalizer)
                throw new Exception($"Should have disposed {nameof(this.IgnoreCancellationRegion)} region.");

            _logger.Verbose("Exited no-cancel region.");

            this.InitialCancellationToken();

            _ignoringCancellationRegion = false;
        }
    }

    internal abstract class WorkItemBase : SynchronizationContext, IGetTimeToWait
    {
        private readonly WorkerCore _worker;

        public abstract Task InitialRunTask { get; }
        public abstract Task FinalTask { get; }

        public CancellationTokenSource CancellationTokenSource { get; } = new();

        public string MethodIdentifier => SpecificLogNameOrNull ?? _methodKey.MethodName;

        public int GlobalWorkItemId { get; }

        private readonly int _workItemId;
        private int _nextInvokationId;
        private bool _isFullyCompleted;

        public string SpecificLogNameOrNull { get; set; }

        public TimeSpan GetTimeToWait() => _worker.Configuration.ThrottlingConfiguration == null
                                           ||
                                           (_worker.Configuration.ThrottlingConfiguration.ExceptMethods != null &&
                                            _worker.Configuration.ThrottlingConfiguration.ExceptMethods.Contains(MethodIdentifier))
            ? TimeSpan.Zero
            : _worker.Configuration.ThrottlingConfiguration.GetTimeToWait(_throttlingArguments);

        internal MethodCharacteristics MethodCharacteristics { get; }
        private readonly MethodKey _methodKey;

        private readonly object[] _arguments;
        private readonly Dictionary<string, object> _throttlingArguments;
        private Dictionary<string, object> _contextData;

        public string LogId => _nextInvokationId > 0 ? $"{{{_workItemId:###}.{_nextInvokationId:##}}}" : $"{{{_workItemId:###}}}";
        public string LogTaskString { get; private set; }

        private (SendOrPostCallback, object) NextAsyncCallback { get; set; }

        private WorkItemBase(WorkerCore worker)
        {
            _worker = worker;
            _workItemId = worker.GetNextWorkItemId();
            GlobalWorkItemId = GetNextGlobalWorkItemId();
        }

        protected WorkItemBase(WorkerCore worker, LambdaExpression expression)
            : this(worker)
        {
            (_methodKey, MethodCharacteristics, _arguments) = this.GetMethodCharacteristicsAndArguments(expression);
            _throttlingArguments = this.GetThrottlingArguments();
        }

        protected WorkItemBase(WorkerCore worker, IInvocation invocation)
            : this(worker)
        {
            (_methodKey, MethodCharacteristics, _arguments) = this.GetMethodCharacteristicsAndArguments(invocation);
            _throttlingArguments = this.GetThrottlingArguments();
        }
        
        public void Initialize()
        {
            LogTaskString = this.GetLogTaskString();
            
            IReadOnlyDictionary<string, object> prevCtxContextData = WorkersGod.GetAllSyncCtxDataOrNull();
            
            _contextData = new (prevCtxContextData?.Count ?? 1);

            if (prevCtxContextData != null)
            {
                foreach (KeyValuePair<string, object> keyValuePair in prevCtxContextData)
                {
                    this.SetContextData(keyValuePair.Key, keyValuePair.Value);
                }
            }

            this.LogQueued();
        }

        private Dictionary<string, object> GetThrottlingArguments()
        {
            if (_worker.Configuration.ThrottlingConfiguration == null
                || _worker.Configuration.ThrottlingConfiguration.ParametersByType == null
                || (_worker.Configuration.ThrottlingConfiguration.ExceptMethods != null && _worker.Configuration.ThrottlingConfiguration.ExceptMethods.Contains(MethodIdentifier))
               )
                return null;

            var res = new Dictionary<string, object>();

            foreach (object argument in _arguments) // Last argument wins
            {
                if (argument == null)
                    continue;

                Type argumentType = argument.GetType();

                foreach (KeyValuePair<string, Type> kv in _worker.Configuration.ThrottlingConfiguration.ParametersByType)
                {
                    if (kv.Value == argumentType)
                        res[kv.Key] = argument;
                }
            }

            return res;
        }

        private (MethodKey, MethodCharacteristics, object[]) GetMethodCharacteristicsAndArguments(LambdaExpression expr)
        {
            if (expr.Body is InvocationExpression invocationExpression) // DESIGN: do not allow this by default, also be a bit more elegant
            {
                if (invocationExpression.Arguments.Any())
                    throw new ArgumentException("Invokation with arguments is not allowed.");
                
                if (invocationExpression.Expression is not UnaryExpression unaryExpression)
                    throw new ArgumentException("Invokation with arguments is not allowed.");

                object actionObj = GetMethodCharacteristicsAndArgument(unaryExpression);
                var action = (Action)actionObj;

                return (_worker.GetMethodKey(expr), new MethodCharacteristics(action, null, true, null), []);
            }
            
            if (expr.Body is not MethodCallExpression methodCallExpression)
                throw new ArgumentException("Should be a simple method call.");

            MethodKey mk = _worker.GetMethodKey(expr);
            MethodCharacteristics mc = _worker.GetOrCreateMethodCharacteristics(mk);

            object[] arguments = new object[methodCallExpression.Arguments.Count];

            for (int i = 0; i < methodCallExpression.Arguments.Count; i++)
            {
                Expression expression = methodCallExpression.Arguments[i];

                arguments[i] = GetMethodCharacteristicsAndArgument(expression);
            }

            return (mk, mc, arguments);
        }

        private static object GetMethodCharacteristicsAndArgument(Expression expression)
        {
            // PERF
            object getArgumentValueSlow(Expression expression, string errNo)
            {
                UnaryExpression convertResToObject = Expression.Convert(expression, typeof(object));

                LambdaExpression l = Expression.Lambda(convertResToObject);

                if (l.Parameters.Count > 0)
                    throw new NotImplementedException(errNo);

                var func = (Func<object>)l.Compile(); // PERF: this is slow

                return func();
            }

            try
            {
                switch (expression)
                {
                    case ConstantExpression constantExpression:     return constantExpression.Value;
                    case MemberExpression memberExpression:         return GetMemberExpressionValue(memberExpression);
                    case MemberInitExpression memberInitExpression: return getArgumentValueSlow(memberInitExpression, "8");
                    case NewExpression newExpression:               return getArgumentValueSlow(newExpression,        "9");
                    case UnaryExpression unaryExpression:           return getArgumentValueSlow(unaryExpression,      "7");
                    case MethodCallExpression methodCallExpression: return getArgumentValueSlow(methodCallExpression, "11");
                    default:                                        throw new NotImplementedException("1");
                }
            }
            catch (NotImplementedException e)
            {
                throw new NotImplementedException($"Expression (argument, code place:{e.Message}) " + expression + " is not supported.");
            }
        }

        private static object GetMemberExpressionValue(MemberExpression memberExpression)
        {
            if (memberExpression.NodeType == ExpressionType.MemberAccess && memberExpression.Expression == null)
            {
                switch (memberExpression.Member)
                {
                    case FieldInfo fieldInfo:       return fieldInfo.GetValue(null);    // PERF: cache
                    case PropertyInfo propertyInfo: return propertyInfo.GetValue(null); // PERF: cache
                    default:                        throw new NotImplementedException("23");
                }
            }

            switch (memberExpression.Expression)
            {
                case ConstantExpression innerConstantExpression:
                    switch (memberExpression.Member)
                    {
                        case FieldInfo fieldInfo:       return fieldInfo.GetValue(innerConstantExpression.Value);    // PERF: cache
                        case PropertyInfo propertyInfo: return propertyInfo.GetValue(innerConstantExpression.Value); // PERF: cache
                        default:                        throw new NotImplementedException("2");
                    }
                case MemberExpression innerMemberExpression:
                    object intermediateRes = GetMemberExpressionValue(innerMemberExpression);

                    if (memberExpression.Member is FieldInfo fieldInfo2)
                    {
                        return fieldInfo2.GetValue(intermediateRes); // PERF: cache
                    }

                    if (memberExpression.Member is PropertyInfo propInfo2)
                    {
                        return propInfo2.GetValue(intermediateRes); // PERF: cache
                    }

                    throw new NotImplementedException("3");
                default: throw new NotImplementedException("4");
            }
        }

        private (MethodKey, MethodCharacteristics, object[]) GetMethodCharacteristicsAndArguments(IInvocation invocation)
        {
            MethodKey mk = _worker.GetMethodKey(invocation);
            MethodCharacteristics mc = _worker.GetOrCreateMethodCharacteristics(mk);

            return (mk, mc, invocation.Arguments);
        }

        private string GetLogTaskString()
        {
            if (SpecificLogNameOrNull != null)
            {
                return SpecificLogNameOrNull;
            }
            
            return _methodKey.MethodName
                   + (_methodKey.GenericArguments == null || _methodKey.GenericArguments.Length == 0
                       ? ""
                       : ("<" + string.Join(",", _methodKey.GenericArguments.Select(t => t.Name)) + ">")
                   )
                   + "("
                   + string.Join(
                       ",",
                       _arguments.Select((arg, i) =>
                           MethodCharacteristics.Params[i].Log ? ArgToStringOrEmptyIfTypeName(arg) : "(" + MethodCharacteristics.Params[i].ParamName + ")"
                       )
                   )
                   + ")";
        }

        private static string ArgToStringOrEmptyIfTypeName(object obj)
        {
            if (obj == null)
                return "(null)";
            if (obj is string str)
                return str;

            string res = obj.ToString();

            if (res == obj.GetType().ToString())
                return "..";

            return res;
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        protected void ExecuteAction()
        {
            if (MethodCharacteristics.CompiledDelegate.IsT0)
                MethodCharacteristics.CompiledDelegate.AsT0.Call(_arguments);
            else
                MethodCharacteristics.CompiledDelegate.AsT1.Invoke();
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        protected object ExecuteFunc()
        {
            if (MethodCharacteristics.CompiledDelegate.IsT0)
                return MethodCharacteristics.CompiledDelegate.AsT0.CallWithReturn(_arguments);

            throw new NotSupportedException();
        }

        public bool ExecuteFirstExceptionHandler(Exception exc)
        {
            if (!ExceptionUtil.IsPureCancellation(exc))
                return true;
            
            _worker._logger.Verbose(exc, "");

            _worker.RegisterCancellation(this, exc);

            // Pure Cancellation happens and it is allowed to happen.
            CancellationTokenSource.Cancel();
            
            return false;
        }

        public abstract object GetFinalTaskResultBlocking();

        public override SynchronizationContext CreateCopy()
        {
            throw new NotImplementedException();
        }

        public override int Wait(IntPtr[] waitHandles, bool waitAll, int millisecondsTimeout)
        {
            throw new NotImplementedException();
        }

        public override void Send(SendOrPostCallback d, object state)
        {
            throw new NotImplementedException();
        }

        public override void Post(SendOrPostCallback d, object state)
        {
            if (_isFullyCompleted)
                throw new Exception($"WorkItem's {_workItemId} invokation {_nextInvokationId} should have been a last one. Posting to this synch ctx is not permitted.");

            // _worker._logger.Debug($"{WorkItemIdStr} Post {d.Target} {d.Target} {state}");
            
            Thread.MemoryBarrier();

            if (NextAsyncCallback.Item1 != null)
                throw new Exception("SynchCtx.Post() call before a prev. one completed");

            if (_nextInvokationId > 500)
                throw new Exception("");

            Interlocked.Increment(ref _nextInvokationId);

            this.LogQueuedContinuation();

            NextAsyncCallback = (d, state);

            if (_worker._mainCancellationTokenSource.IsCancellationRequested)
                throw new OperationCanceledException("_worker._mainCancellationTokenSource.IsCancellationRequested");

            _worker.EnqueueWorkItem(this);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void CallTaskOrCallback()
        {
            SynchronizationContext prevCtx = Current;

            try
            {
                SetSynchronizationContext(this);

                var sw = Stopwatch.StartNew();

                if (NextAsyncCallback.Item1 != null)
                {
                    this.LogStartContinuation();

                    _worker._performanceCounter.EnterBlock("ExecuteCallback_" + MethodIdentifier);

                    NextAsyncCallback.Item1(NextAsyncCallback.Item2);
                    NextAsyncCallback = (null, null);
                    
                    Thread.MemoryBarrier();

                    sw.Stop();

                    _worker._performanceCounter.EnterBlock("ExecuteOuter");

                    if (FinalTask.IsCompleted)
                    {
                        bool shouldThrow = _worker.ShouldThrowRegisterSuccessFailure(this, FinalTask.Exception?.InnerException, true);
                        
                        _isFullyCompleted = true;

                        this.LogFullyComplete(sw);
                        
                        if (shouldThrow)
                            throw new WorkerFaultedException($"Worker {_worker.Name} is Faulted", FinalTask.Exception.InnerException);
                    } else
                    {
                        this.LogCompletedContinuation();
                    }
                } else
                {
                    if (InitialRunTask.IsCanceled)
                        return;

                    this.LogStart();

                    _worker._performanceCounter.EnterBlock("ExecuteCallback_" + MethodIdentifier);

                    InitialRunTask.RunSynchronously(_worker._taskScheduler);

                    _worker._performanceCounter.EnterBlock("ExecuteOuter");

                    sw.Stop();

                    bool isFinalTask = InitialRunTask == FinalTask;

                    bool shouldThrow = _worker.ShouldThrowRegisterSuccessFailure(this, InitialRunTask.Exception?.InnerException, isFinalTask);

                    if (isFinalTask)
                    {
                        _isFullyCompleted = true;

                        this.LogFullyComplete(sw);
                    } else
                    {
                        this.LogCompletedFirst();
                    }

                    if (shouldThrow)
                        throw new WorkerFaultedException($"Worker {_worker.Name} is Faulted", InitialRunTask.Exception.InnerException);
                }
            }
            finally
            {
                SetSynchronizationContext(prevCtx);

                _worker._performanceCounter.EnterBlock("ExecuteOuter");
            }
        }
        
        public void SetContextData(string key, object value)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentNullException(nameof(key));

            lock (_contextData)
            {
                _contextData[key] = value;
            }
        }

        // Method to retrieve data from the context
        public object GetContextData(string key)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentNullException(nameof(key));

            lock (_contextData)
            {
                return _contextData.TryGetValue(key, out object value) ? value : null;
            }
        }

        public IReadOnlyDictionary<string, object> GetAllContextData()
        {
            lock (_contextData)
            {
                return _contextData;
            }
        }
        
        private void LogQueued()
        {
            if (!MethodCharacteristics.Log)
                return;
            
            _worker._logger.Write(_worker.Configuration.LogLevel, $"Q=> {LogId} {LogTaskString}");
        }

        private void LogStart()
        {
            if (!MethodCharacteristics.Log)
                return;

            _worker._logger.Write(_worker.Configuration.LogLevel, $"==> {LogId} {LogTaskString}");
        }

        private void LogCompletedFirst()
        {
            if (!MethodCharacteristics.Log)
                return;

            _worker._logger.Write(_worker.Configuration.LogLevel, $"<=) {LogId} {LogTaskString}");
        }

        private void LogQueuedContinuation()
        {
            if (!MethodCharacteristics.Log)
                return;

            _worker._logger.Write(_worker.Configuration.LogLevel, $"C=> {LogId} {LogTaskString}");
        }

        private void LogStartContinuation()
        {
            if (!MethodCharacteristics.Log)
                return;

            _worker._logger.Write(_worker.Configuration.LogLevel, $"(=> {LogId} {LogTaskString}");
        }

        private void LogCompletedContinuation()
        {
            if (!MethodCharacteristics.Log)
                return;

            _worker._logger.Write(_worker.Configuration.LogLevel, $"<=) {LogId} {LogTaskString}");
        }

        private void LogFullyComplete(Stopwatch sw)
        {
            if (!MethodCharacteristics.Log)
                return;

            _worker._logger.Write(_worker.Configuration.LogLevel, $"<== {LogId} {LogTaskString} @ " + sw?.Elapsed);
        }
    }

    private void RegisterCancellation(WorkItemBase item, Exception exception)
    {
        if (_stopWorkThreadCancellationTokenSource.IsCancellationRequested)
        {
            _logger.Debug($"Task {item.LogId} {item.LogTaskString} exited due to cancellation. Proceeding until queue is zero due to shutdown request.");

            if (!_mainCancellationTokenSource.IsCancellationRequested)
                _logger.Debug($"{item.LogId} Also waiting for the end of no-exit zone");
        } else
        {
            _logger.Debug($"Task {item.LogId} {item.LogTaskString} exited due to cancellation. Proceeding with other tasks.");
        }

        _logger.Verbose(exception, "");
    }

    private void RegisterFailure(WorkItemBase item, Exception exception)
    {
        if (!_failuresInARow.ContainsKey(item.MethodIdentifier))
            _failuresInARow[item.MethodIdentifier] = 1;
        else
            _failuresInARow[item.MethodIdentifier]++;

        LogEventLevel logLevel = item.MethodCharacteristics.CanFail?.LogLevel ?? LogEventLevel.Error;

        if (_stopWorkThreadCancellationTokenSource.IsCancellationRequested)
        {
            _logger.Write(logLevel, $"Task {item.LogId} {item.LogTaskString} has failed. Maybe proceeding until queue is zero due to shutdown request.");

            if (!_mainCancellationTokenSource.IsCancellationRequested)
                _logger.Debug("Also waiting for the end of no-exit zone");
        } else
        {
            _logger.Write(logLevel, $"Task {item.LogId} {item.LogTaskString} has failed. Maybe proceeding with other tasks.");
        }

        _logger.Write(logLevel, exception, "");
    }

    private void RegisterSuccess(WorkItemBase item)
    {
        _failuresInARow.Remove(item.MethodIdentifier);
    }

    private bool ShouldThrowRegisterSuccessFailure(WorkItemBase item, Exception exception, bool isFinalTask)
    {
        if (exception == null)
            return false;

        bool failed = item.ExecuteFirstExceptionHandler(exception);

        if (failed)
        {
            this.RegisterFailure(item, exception);
        } else if (isFinalTask)
        {
            this.RegisterSuccess(item);
        }

        if (!_failuresInARow.TryGetValue(item.MethodIdentifier, out int value))
            return false;

        if (value <= (item.MethodCharacteristics.CanFail?.TimesInARow ?? 3))
            return false;

        _logger.Error($"{item.LogId} Rethrowing exception out of task scope.");

        return true;
    }

    private MethodKey GetMethodKey(IInvocation invocation)
    {
        return new MethodKey(invocation.Method.DeclaringType.FullName, invocation.Method.Name, invocation.GenericArguments);
    }

    private MethodKey GetMethodKey(LambdaExpression expr)
    {
        if (expr.Body is InvocationExpression)
        {
            return new MethodKey("Invokation", "Invokation", []);
        }
        
        if (expr.Body is not MethodCallExpression methodCallExpression)
            throw new ArgumentException("Should be a simple method call.");

        return new MethodKey(methodCallExpression.Method.DeclaringType.FullName, methodCallExpression.Method.Name, methodCallExpression.Method.GetGenericArguments());
    }

    private MethodCharacteristics GetOrCreateMethodCharacteristics(MethodKey mk)
    {
        MethodCharacteristics mc;

        lock (_methodNameToCharacteristics)
        {
            if (!_methodNameToCharacteristics.ContainsKey(mk))
            {
                Type @interface = TypeResolver.GetType(mk.InterfaceFullName);

                var interfaceCanFailAttribute = @interface.GetCustomAttribute<CanFailAttribute>();

                MethodInfo methodInfo = @interface.GetMethod(mk.MethodName, BindingFlags.Instance | BindingFlags.Public);

                bool log = methodInfo.GetAttribute<DoNotLogAttribute>() == null;

                if (methodInfo.IsGenericMethodDefinition)
                {
                    if (mk.GenericArguments.Length != methodInfo.GetGenericArguments().Length)
                        throw new Exception("Should supply this method with generic args.");

                    methodInfo = methodInfo.MakeGenericMethod(mk.GenericArguments);
                }

                var cd = CompiledDelegateWithInstance.Build(methodInfo, _methods);

                List<MethodParameterCharacteristics> @params = methodInfo.GetParameters()
                    .Select(param => new MethodParameterCharacteristics(param.Name, param.GetCustomAttribute<DoNotLogAttribute>() == null))
                    .ToList();

                _methodNameToCharacteristics[mk] = mc = new MethodCharacteristics(
                    cd,
                    @params,
                    log,
                    methodInfo.GetAttribute<CanFailAttribute>() ?? interfaceCanFailAttribute
                );
            } else
            {
                mc = _methodNameToCharacteristics[mk];
            }
        }

        return mc;
    }

    private class MyTaskScheduler : TaskScheduler
    {
        public override int MaximumConcurrencyLevel => _worker.Configuration.Mechanism.Match(
            thread => 1,
            scheduler => scheduler.MaximumConcurrencyLevel
        );

        private readonly WorkerCore _worker;

        public MyTaskScheduler(WorkerCore worker)
        {
            _worker = worker;
        }

        protected override IEnumerable<Task> GetScheduledTasks()
        {
            throw new NotImplementedException();
        }

        public void QueueTask2(Task task)
        {
            this.QueueTask(task);
        }

        protected override void QueueTask(Task task)
        {
            throw new NotImplementedException();
        }

        protected override bool TryExecuteTaskInline(Task task, bool taskWasPreviouslyQueued)
        {
            if (_worker.IsMyThread())
            {
                this.TryExecuteTask(task);

                return true;
            }

            return false;
        }
    }

    public ForInterface<T> GetForInterface<T>()
        where T : IWorker
    {
        return (ForInterface<T>)_forInterfaces.GetOrAdd(typeof(T),
            _ => {
                return new ForInterface<T>(this);
            }
        );
    }

    public object GetForInterfaceObject(Type t)
    {
        return _forInterfaces.GetOrAdd(t,
            _ => {
                return Activator.CreateInstance(typeof(ForInterface<>).MakeGenericType(t),
                    this
                );
            }
        );
    }

    public object GetOrCreatePublicInterfaceObject(Type t)
    {
        object forInterface = this.GetForInterfaceObject(t);

        MethodInfo crateInterfaceMethod =
            forInterface.GetType().GetMethod(nameof(ForInterface<IBackgroundTasksWorker>.CreatePublicInterface), BindingFlags.Public | BindingFlags.Instance);

        return crateInterfaceMethod.Invoke(forInterface, Array.Empty<object>());
    }

    private bool IsMyThread()
    {
        return Configuration.Mechanism.Match(
            thread => Thread.CurrentThread.ManagedThreadId == _thread.ManagedThreadId,
            scheduler => _taskThread != null && Thread.CurrentThread.ManagedThreadId == _taskThread.ManagedThreadId
        );
    }

    public class ForInterface<TWorkerInterface>
        where TWorkerInterface : IWorker
    {
        private readonly MiddleInterceptor _middleInterceptorAndFilter;

        private readonly record struct ReturnAndParameterTypes(Type Return, Type Parameter);

        private readonly Dictionary<ReturnAndParameterTypes, CompiledDelegate> _returnTypeAndConstructorParamTypeToCompiledDelegates = new();

        private readonly WorkerCore _worker;

        public ForInterface(WorkerCore worker)
        {
            _worker = worker;
            _middleInterceptorAndFilter = new MiddleInterceptor(_worker, this);
        }

        public TWorkerInterface CreatePublicInterface()
        {
            lock (_ProxyGenerator)
            {
                object internalProxy = _ProxyGenerator.CreateInterfaceProxyWithTargetInterface(typeof(TWorkerInterface),
                    _worker._methods,
                    new ProxyGenerationOptions(_middleInterceptorAndFilter),
                    _middleInterceptorAndFilter
                );

                if (_worker.Configuration.GetCustomInterceptor == null)
                    return (TWorkerInterface)internalProxy;

                object customProxy = _ProxyGenerator.CreateInterfaceProxyWithTargetInterface(typeof(TWorkerInterface),
                    internalProxy,
                    new ProxyGenerationOptions(_middleInterceptorAndFilter),
                    _worker.Configuration.GetCustomInterceptor(_worker._methods)
                );

                return (TWorkerInterface)customProxy;
            }
        }

        private WorkItemBase CreateWorkItemWithResult(Type returnType, object constructorParameter, string logName)
        {
            var key = new ReturnAndParameterTypes(returnType, constructorParameter.GetType());

            CompiledDelegate createWorkItem;

            lock (_returnTypeAndConstructorParamTypeToCompiledDelegates)
            {
                if (!_returnTypeAndConstructorParamTypeToCompiledDelegates.TryGetValue(key, out CompiledDelegate @delegate))
                {
                    Type workItemT;
                    
                    if (key.Return.IsGenericType && key.Return.GetGenericTypeDefinition() == typeof(Task<>))
                    {
                        workItemT = key.Return.GetGenericArguments()[0];
                    } else
                    {
                        workItemT = key.Return;
                    }
                    
                    ConstructorInfo[] constructors = typeof(WorkItemRes<>)
                        .MakeGenericType(typeof(TWorkerInterface), workItemT)
                        .GetConstructors();

                    ConstructorInfo constructor = constructors.Single(
                        ci => {
                            ParameterInfo[] parameters = ci.GetParameters();

                            return parameters[0].ParameterType == typeof(ForInterface<TWorkerInterface>) &&
                                   parameters[1].ParameterType.IsAssignableFrom(key.Parameter);
                        }
                    );

                    createWorkItem = CompiledDelegate.Build(constructor);

                    _returnTypeAndConstructorParamTypeToCompiledDelegates[key] = createWorkItem;
                } else
                {
                    createWorkItem = @delegate;
                }
            }

            var workItem = (WorkItemBase)createWorkItem.CallWithReturn(null, this, constructorParameter);
            workItem.SpecificLogNameOrNull = logName;
            workItem.Initialize();
            
            return workItem;
        }

        public void Do(Expression<Action<TWorkerInterface>> action, string logName)
        {
            using (_worker._performanceCounter.CaptureBlock())
            {
                _worker._performanceCounter.EnterBlock("Enqueue");

                var workItem = new WorkItemVoid(this, action);
                workItem.SpecificLogNameOrNull = logName;
                workItem.Initialize();
                _worker.EnqueueWorkItem(workItem);

                _worker._performanceCounter.EnterBlock("Wait");

                workItem.InitialRunTask.Wait();
            }
        }

        public TRes Do<TRes>(Expression<Func<TWorkerInterface, TRes>> func, string logName)
        {
            using (_worker._performanceCounter.CaptureBlock())
            {
                _worker._performanceCounter.EnterBlock("Enqueue");

                WorkItemBase workItem = this.CreateWorkItemWithResult(typeof(TRes), func, logName);
                _worker.EnqueueWorkItem(workItem);

                _worker._performanceCounter.EnterBlock("Wait");

                object res = workItem.GetFinalTaskResultBlocking();

                return (TRes)res;
            }
        }

        public Task Run(Expression<Action<TWorkerInterface>> action, string logName)
        {
            using (_worker._performanceCounter.CaptureBlock())
            {
                _worker._performanceCounter.EnterBlock("Enqueue");

                WorkItemBase workItem = new WorkItemVoid(this, action);
                workItem.SpecificLogNameOrNull = logName;
                workItem.Initialize();
                
                _worker.EnqueueWorkItem(workItem);

                return workItem.InitialRunTask;
            }
        }

        public Task<TRes> Run<TRes>(Expression<Func<TWorkerInterface, TRes>> func, string logName)
        {
            using (_worker._performanceCounter.CaptureBlock())
            {
                _worker._performanceCounter.EnterBlock("Enqueue");

                WorkItemBase workItem = this.CreateWorkItemWithResult(typeof(TRes), func, logName);
                _worker.EnqueueWorkItem(workItem);

                return (Task<TRes>)workItem.InitialRunTask;
            }
        }

        public void Do(Expression<Func<TWorkerInterface, Task>> func, string logName)
        {
            using (_worker._performanceCounter.CaptureBlock())
            {
                _worker._performanceCounter.EnterBlock("Enqueue");

                WorkItemBase workItem = new WorkItemVoid(this, func);
                workItem.SpecificLogNameOrNull = logName;
                workItem.Initialize();
                
                _worker.EnqueueWorkItem(workItem);

                _worker._performanceCounter.EnterBlock("Wait");

                workItem.FinalTask.Wait();
            }
        }

        public TRes Do<TRes>(Expression<Func<TWorkerInterface, Task<TRes>>> func, string logName)
        {
            using (_worker._performanceCounter.CaptureBlock())
            {
                _worker._performanceCounter.EnterBlock("Enqueue");

                WorkItemBase workItem = this.CreateWorkItemWithResult(typeof(TRes), func, logName);
                _worker.EnqueueWorkItem(workItem);

                _worker._performanceCounter.EnterBlock("Wait");

                object res = workItem.GetFinalTaskResultBlocking();

                return (TRes)res;
            }
        }

        public Task Run(Expression<Func<TWorkerInterface, Task>> func, string logName)
        {
            using (_worker._performanceCounter.CaptureBlock())
            {
                _worker._performanceCounter.EnterBlock("Enqueue");

                WorkItemBase workItem = new WorkItemVoid(this, func);
                workItem.SpecificLogNameOrNull = logName;
                workItem.Initialize();
                
                _worker.EnqueueWorkItem(workItem);

                return workItem.FinalTask;
            }
        }

        public Task<TRes> Run<TRes>(Expression<Func<TWorkerInterface, Task<TRes>>> func, string logName)
        {
            using (_worker._performanceCounter.CaptureBlock())
            {
                _worker._performanceCounter.EnterBlock("Enqueue");

                WorkItemBase workItem = this.CreateWorkItemWithResult(typeof(TRes), func, logName);
                _worker.EnqueueWorkItem(workItem);

                return (Task<TRes>)workItem.FinalTask;
            }
        }

        private class WorkItemVoid : WorkItemBase
        {
            private class OneOf : OneOfBase<IInvocationProceedInfo, Expression<Action<TWorkerInterface>>, Expression<Func<TWorkerInterface, Task>>>
            {
                private OneOf(OneOf<IInvocationProceedInfo, Expression<Action<TWorkerInterface>>, Expression<Func<TWorkerInterface, Task>>> input)
                    : base(input)
                {
                }

                public OneOf(IInvocationProceedInfo t)
                    : this(OneOf<IInvocationProceedInfo, Expression<Action<TWorkerInterface>>, Expression<Func<TWorkerInterface, Task>>>.FromT0(t))
                {
                }

                public OneOf(Expression<Action<TWorkerInterface>> t)
                    : this(OneOf<IInvocationProceedInfo, Expression<Action<TWorkerInterface>>, Expression<Func<TWorkerInterface, Task>>>.FromT1(t))
                {
                }

                public OneOf(Expression<Func<TWorkerInterface, Task>> t)
                    : this(OneOf<IInvocationProceedInfo, Expression<Action<TWorkerInterface>>, Expression<Func<TWorkerInterface, Task>>>.FromT2(t))
                {
                }
            }

            private readonly ForInterface<TWorkerInterface> _forInterface;

            private OneOf ToExecute { get; }
            private OneOf<Task, Task<Task>> Task { get; }

            public override Task InitialRunTask => Task.Match(t => t, t => t);
            public override Task FinalTask => Task.Match(t => t,      t => t.Unwrap());

            public WorkItemVoid(ForInterface<TWorkerInterface> workerCoreForInterface, IInvocation invocation)
                : base(workerCoreForInterface._worker, invocation)
            {
                _forInterface = workerCoreForInterface;

                var task = new Task(this.ExecuteAction, CancellationTokenSource.Token);
                Task = OneOf<Task, Task<Task>>.FromT0(task);

                ToExecute = new OneOf(invocation.CaptureProceedInfo());
            }

            public WorkItemVoid(ForInterface<TWorkerInterface> workerCoreForInterface, Expression<Action<TWorkerInterface>> expr)
                : base(workerCoreForInterface._worker, expr)
            {
                _forInterface = workerCoreForInterface;

                var task = new Task(this.ExecuteAction, CancellationTokenSource.Token);
                Task = OneOf<Task, Task<Task>>.FromT0(task);

                ToExecute = new OneOf(expr);
            }

            public WorkItemVoid(ForInterface<TWorkerInterface> workerCoreForInterface, Expression<Func<TWorkerInterface, Task>> expr)
                : base(workerCoreForInterface._worker, expr)
            {
                _forInterface = workerCoreForInterface;

                var task = new Task<Task>(this.TaskCallbackAsync, CancellationTokenSource.Token);
                Task = OneOf<Task, Task<Task>>.FromT1(task);

                ToExecute = new OneOf(expr);
            }

            public override object GetFinalTaskResultBlocking()
            {
                throw new NotImplementedException();
            }

            private Task TaskCallbackAsync()
            {
                return (Task)this.ExecuteFunc();
            }
        }

        private class WorkItemRes<TRes> : WorkItemBase
        {
            private class OneOf : OneOfBase<IInvocationProceedInfo, Expression<Func<TWorkerInterface, TRes>>, Expression<Func<TWorkerInterface, Task<TRes>>>>
            {
                private OneOf(OneOf<IInvocationProceedInfo, Expression<Func<TWorkerInterface, TRes>>, Expression<Func<TWorkerInterface, Task<TRes>>>> input)
                    : base(input)
                {
                }

                public OneOf(IInvocationProceedInfo t)
                    : this(OneOf<IInvocationProceedInfo, Expression<Func<TWorkerInterface, TRes>>, Expression<Func<TWorkerInterface, Task<TRes>>>>.FromT0(t))
                {
                }

                public OneOf(Expression<Func<TWorkerInterface, TRes>> t)
                    : this(OneOf<IInvocationProceedInfo, Expression<Func<TWorkerInterface, TRes>>, Expression<Func<TWorkerInterface, Task<TRes>>>>.FromT1(t))
                {
                }

                public OneOf(Expression<Func<TWorkerInterface, Task<TRes>>> t)
                    : this(OneOf<IInvocationProceedInfo, Expression<Func<TWorkerInterface, TRes>>, Expression<Func<TWorkerInterface, Task<TRes>>>>.FromT2(t))
                {
                }
            }

            private readonly ForInterface<TWorkerInterface> _forInterface;

            private OneOf ToExecute { get; }
            private OneOf<Task<TRes>, Task<Task<TRes>>> Task { get; }

            public override Task InitialRunTask => Task.Match(t => (Task)t, t => t);
            public override Task FinalTask => Task.Match(t => (Task)t,      t => t.Unwrap());

            public override object GetFinalTaskResultBlocking()
            {
                return Task.Match(t => t.Result, t => t.Unwrap().Result);
            }

            [UsedImplicitly]
            public WorkItemRes(ForInterface<TWorkerInterface> workerCoreForInterface, IInvocation invocation)
                : base(workerCoreForInterface._worker, invocation)
            {
                _forInterface = workerCoreForInterface;
                
                if (invocation.Method.ReturnType.IsGenericType && invocation.Method.ReturnType.GetGenericTypeDefinition() == typeof(Task<>))
                {
                    var task = new Task<Task<TRes>>(this.TaskCallbackAsync, CancellationTokenSource.Token);
                    Task = OneOf<Task<TRes>, Task<Task<TRes>>>.FromT1(task);
                } else
                {
                    var task = new Task<TRes>(this.TaskCallback, CancellationTokenSource.Token);
                    Task = OneOf<Task<TRes>, Task<Task<TRes>>>.FromT0(task);
                }

                ToExecute = new OneOf(invocation.CaptureProceedInfo());
            }

            [UsedImplicitly]
            public WorkItemRes(ForInterface<TWorkerInterface> workerCoreForInterface, Expression<Func<TWorkerInterface, TRes>> expr)
                : base(workerCoreForInterface._worker, expr)
            {
                _forInterface = workerCoreForInterface;

                var task = new Task<TRes>(this.TaskCallback, CancellationTokenSource.Token);
                Task = OneOf<Task<TRes>, Task<Task<TRes>>>.FromT0(task);

                ToExecute = new OneOf(expr);
            }

            [UsedImplicitly]
            public WorkItemRes(ForInterface<TWorkerInterface> workerCoreForInterface, Expression<Func<TWorkerInterface, Task<TRes>>> expr)
                : base(workerCoreForInterface._worker, expr)
            {
                _forInterface = workerCoreForInterface;

                var task = new Task<Task<TRes>>(this.TaskCallbackAsync, CancellationTokenSource.Token);
                Task = OneOf<Task<TRes>, Task<Task<TRes>>>.FromT1(task);

                ToExecute = new OneOf(expr);
            }

            private TRes TaskCallback()
            {
                return (TRes)this.ExecuteFunc();
            }

            private Task<TRes> TaskCallbackAsync()
            {
                var res = (Task)this.ExecuteFunc();
                
                return (Task<TRes>) res;
            }
        }

        private class MiddleInterceptor : IInterceptor, IProxyGenerationHook
        {
            private readonly WorkerCore _worker;
            private readonly ForInterface<TWorkerInterface> _workerCoreForInterface;

            private int _totalDoMethods;
            private int _totalRunMethods;

            public MiddleInterceptor(WorkerCore worker, ForInterface<TWorkerInterface> workerCoreForInterface)
            {
                _worker = worker;
                _workerCoreForInterface = workerCoreForInterface;
            }

            public void Intercept(IInvocation invocation)
            {
                if (!_worker.Configuration.AllowDirectCall)
                    throw new Exception("Direct calls are not allowed for this worker.");

                using (_worker._performanceCounter.CaptureBlock())
                {
                    _worker._performanceCounter.EnterBlock("Enqueue");
                    
                    Type methodReturnType = invocation.Method.ReturnType;
                    
                    if (methodReturnType == typeof(void))
                    {
                        var workItem = new WorkItemVoid(_workerCoreForInterface, invocation);
                        workItem.Initialize();
                        
                        _worker.EnqueueWorkItem(workItem);

                        _worker._performanceCounter.EnterBlock("Wait");
                        
                        workItem.FinalTask.Wait();
                    } else if (methodReturnType == typeof(Task) || (methodReturnType.IsGenericType && methodReturnType.GetGenericTypeDefinition() == typeof(Task<>)))
                    {
                        WorkItemBase workItem = _workerCoreForInterface.CreateWorkItemWithResult(methodReturnType, invocation, null);
                        _worker.EnqueueWorkItem(workItem);

                        invocation.ReturnValue = workItem.FinalTask;
                    } else
                    {
                        WorkItemBase workItem = _workerCoreForInterface.CreateWorkItemWithResult(methodReturnType, invocation, null);
                        _worker.EnqueueWorkItem(workItem);

                        _worker._performanceCounter.EnterBlock("Wait");

                        invocation.ReturnValue = workItem.GetFinalTaskResultBlocking();
                    }
                }
            }

            public void MethodsInspected()
            {
            }

            public void NonProxyableMemberNotification(Type type, MemberInfo memberInfo)
            {
            }

            public bool ShouldInterceptMethod(Type type, MethodInfo methodInfo)
            {
                if (methodInfo.Name is "Do")
                {
                    if (++_totalDoMethods > 4)
                        throw new Exception("Do() and Run() method names are reserved.");

                    return false;
                }

                if (methodInfo.Name is "Run")
                {
                    if (++_totalRunMethods > 4)
                        throw new Exception("Do() and Run() method names are reserved.");

                    return false;
                }

                if (methodInfo.Name is "get_ForInterface" or "set_ForInterface" or "get_Core" or "set_Core")
                    return false;

                if (methodInfo.Name.StartsWith("add_") || methodInfo.Name.StartsWith("remove_") || methodInfo.Name.StartsWith("get_") || methodInfo.Name.StartsWith("set_"))
                    return false;

                return true;
            }
        }
    }

    private class MethodKeyEqualityComparer : IEqualityComparer<MethodKey>
    {
        public static readonly MethodKeyEqualityComparer Instance = new();

        private MethodKeyEqualityComparer()
        {
        }

        public bool Equals(MethodKey x, MethodKey y)
        {
            return x.MethodName == y.MethodName && x.InterfaceFullName == y.InterfaceFullName &&
                   (x.GenericArguments ?? Array.Empty<Type>()).SequenceEqual(y.GenericArguments ?? Array.Empty<Type>(), SimpleTypeComparer.Instance);
        }

        public int GetHashCode(MethodKey obj)
        {
            var hashCode = new HashCode();
            hashCode.Add(obj.MethodName);
            hashCode.Add(obj.InterfaceFullName);

            if (obj.GenericArguments != null)
            {
                foreach (Type objGenericArgument in obj.GenericArguments)
                {
                    hashCode.Add(objGenericArgument.FullName);
                }
            }

            return hashCode.ToHashCode();
        }
    }
}