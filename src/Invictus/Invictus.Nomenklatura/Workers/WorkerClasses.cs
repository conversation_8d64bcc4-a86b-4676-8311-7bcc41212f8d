using System.Linq.Expressions;
using System.Reflection;
using System.Runtime.CompilerServices;

using Castle.DynamicProxy;

using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.Misc;

using OneOf;

namespace Invictus.Nomenklatura.Workers;

[Serializable]
[AttributeUsage(AttributeTargets.Parameter | AttributeTargets.Method)]
public class DoNotLogAttribute : Attribute
{
}

[Serializable]
[AttributeUsage(AttributeTargets.Method | AttributeTargets.Interface, Inherited = false)]
public sealed class CanFailAttribute : Attribute
{
    public int TimesInARow { get; set; } = 99999;
    public LogEventLevel LogLevel { get; set; } = LogEventLevel.Warning;

    public CanFailAttribute()
    {
    }

    public CanFailAttribute(int timesInARow)
    {
        TimesInARow = timesInARow;
    }
}

public class WorkerFaultedException : Exception
{
    public WorkerFaultedException(string message, Exception inner)
        : base(message, inner)
    {
    }
}

public class ThrottlingConfiguration
{
    public Dictionary<string, Type> ParametersByType { get; set; }
    public List<string> ExceptMethods { get; set; }
    public Func<Dictionary<string, object>, TimeSpan> GetTimeToWait { get; set; }
}

public record WorkerConfiguration(
    string SixCharName,
    OneOf<WorkerConfiguration.Thread, WorkerConfiguration.TaskScheduler> Mechanism,
    LogEventLevel LogLevel,
    bool AllowDirectCall,
    bool RestrictToSingleAwait = false,
    Func<IWorkerImpl, IInterceptor> GetCustomInterceptor = null,
    ThrottlingConfiguration ThrottlingConfiguration = null
)
{
    public readonly record struct Thread(string Name, ThreadPriority ThreadPriority, bool IsBackground);

    public readonly record struct TaskScheduler(string Name, int MaximumConcurrencyLevel);
}

public static class WorkersGod
{
    public static List<WorkerFather> List { get; } = new();

    private static readonly Dictionary<int, WorkerFather> _WorkersByThreadIs = new();
    private static readonly Dictionary<int, string> _ThreadIdToFmtShortName = new(100);

    private static readonly Dictionary<int, string> _ThreadIdToFormatted = new(10 * 1000);

    static WorkersGod()
    {
        foreach (int i in Enumerable.Range(0, 10 * 1000))
        {
            _ThreadIdToFormatted[i] = ThreadIdToFmtStr(i);
        }
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    private static string ThreadIdToFmtStr(int threadId)
    {
        return "T" + threadId.ToString().PadLeft(6, ' ');
    }

    public static Dictionary<string, TimelinePerformanceCounter.Report> GetPerformanceReportsAndFlush()
    {
        var res = new Dictionary<string, TimelinePerformanceCounter.Report>();

        foreach (WorkerFather workerFather in List)
        {
            res[workerFather.GetWorkerCore().Name] = workerFather.GetWorkerCore().GetPerformanceReportAndFlush();
        }

        return res;
    }

    public static void AddWorkerThreadId(WorkerCore worker)
    {
        int threadId = Thread.CurrentThread.ManagedThreadId;

        lock (_WorkersByThreadIs)
        {
            _WorkersByThreadIs[threadId] = List.Single(w => w.GetWorkerCore() == worker);

            _ThreadIdToFmtShortName[threadId] = (worker.Configuration.Mechanism.IsT1 ? "C" : "T") +
                                                _WorkersByThreadIs[threadId].GetWorkerCore().Configuration.SixCharName.PadLeft(6, ' ');
        }
    }

    public static void RemoveWorkerThreadId(WorkerCore worker)
    {
        int threadId = Thread.CurrentThread.ManagedThreadId;

        lock (_WorkersByThreadIs)
        {
            _WorkersByThreadIs.Remove(threadId);
            _ThreadIdToFmtShortName.Remove(threadId);
        }
    }

    public static string GetFormattedThreadIdOrShrtWorkerName()
    {
        int threadId = Thread.CurrentThread.ManagedThreadId;

        lock (_WorkersByThreadIs)
        {
            if (_ThreadIdToFmtShortName.TryGetValue(threadId, out string v))
                return v;
        }

        return _ThreadIdToFormatted[threadId];
    }
    
    public static void SetSyncCtxData(string key, object value)
    {
        var ctx = SynchronizationContext.Current as WorkerCore.WorkItemBase;
        
        if (ctx == null)
            throw new Exception("Should be called from a work item");
        
        ctx.SetContextData(key, value);
    }

    public static object GetSyncCtxData(string key)
    {
        var ctx = SynchronizationContext.Current as WorkerCore.WorkItemBase;
        
        if (ctx == null)
            throw new Exception("Should be called from a work item");
        
        return ctx.GetContextData(key);
    }

    public static IReadOnlyDictionary<string, object> GetAllSyncCtxDataOrNull()
    {
        var ctx = SynchronizationContext.Current as WorkerCore.WorkItemBase;

        if (ctx == null)
            return null;

        return ctx.GetAllContextData();
    }
}

public class WorkerFather
{
    protected WorkerCore WorkerCore { get; }

    public WorkerCore GetWorkerCore() => WorkerCore;

    protected WorkerFather(WorkerCore workerCore)
    {
        WorkerCore = workerCore;
    }
}

public interface IWorkerFather<TImpl>
    where TImpl : class, IWorkerImpl
{
    WorkerCore GetWorkerCore();

    object GetForInterfaceSingleton(Type interfaceType);
}

public class WorkerFather<TImpl> : WorkerFather, IWorkerFather<TImpl>
    where TImpl : class, IWorkerImpl
{
    protected readonly TImpl _impl;
    
    public WorkerFather(TImpl impl, IInvAppLifetime applicationAppLifetime)
        : base(new WorkerCore(applicationAppLifetime, impl.Log, impl.WorkerConfiguration, impl))
    {
        _impl = impl;
        _impl.Core = WorkerCore;

        WorkersGod.List.Add(this);

        WorkerCore.Initialize();
    }

    public virtual object GetOrCreatePublicInterfaceObject(Type interfaceType)
    {
        return WorkerCore.GetOrCreatePublicInterfaceObject(interfaceType);
    }

    public object GetForInterfaceSingleton(Type interfaceType)
    {
        Type workerImplTypeForThisInterface = typeof(IWorkerImpl<>).MakeGenericType(interfaceType);

        if (!workerImplTypeForThisInterface.IsInstanceOfType(_impl) || !interfaceType.IsInstanceOfType(_impl))
            throw new ArgumentException($"interfaceType {interfaceType} should implement IWorkerImpl<T> and T");

        PropertyInfo publicInterfaceProp = workerImplTypeForThisInterface.GetProperty("PublicInterface", BindingFlags.Public | BindingFlags.Instance);

        if (publicInterfaceProp.GetValue(_impl) != null)
            return publicInterfaceProp.GetValue(_impl);

        object publicInterfaceObject = this.GetOrCreatePublicInterfaceObject(interfaceType);

        publicInterfaceProp.SetValue(_impl, publicInterfaceObject);

        return publicInterfaceObject;
    }
}

public interface IWorker
{
    public WorkerCore Core { get; set; }

    public ILogger Log { get; }
}

public interface IWorker<TThisWorkerInterface> : IWorker
    where TThisWorkerInterface : IWorker<TThisWorkerInterface>, IWorker
{
    protected WorkerCore.ForInterface<TThisWorkerInterface> ForInterface => Core.GetForInterface<TThisWorkerInterface>();

    public void Do(Expression<Action<TThisWorkerInterface>> action, string logName = null)
    {
        ForInterface.Do(action, logName);
    }

    public TRes Do<TRes>(Expression<Func<TThisWorkerInterface , TRes>> func, string logName = null)
    {
        return ForInterface.Do(func, logName);
    }

    public Task Run(Expression<Action<TThisWorkerInterface>> action, string logName = null)
    {
        return ForInterface.Run(action, logName);
    }

    public Task<TRes> Run<TRes>(Expression<Func<TThisWorkerInterface, TRes>> func, string logName = null)
    {
        return ForInterface.Run(func, logName);
    }

    public void Do(Expression<Func<TThisWorkerInterface, Task>> func, string logName = null)
    {
        ForInterface.Do(func, logName);
    }

    public TRes Do<TRes>(Expression<Func<TThisWorkerInterface, Task<TRes>>> func, string logName = null)
    {
        return ForInterface.Do(func, logName);
    }

    public Task Run(Expression<Func<TThisWorkerInterface, Task>> func, string logName = null)
    {
        return ForInterface.Run(func, logName);
    }

    public Task<TRes> Run<TRes>(Expression<Func<TThisWorkerInterface, Task<TRes>>> func, string logName = null)
    {
        return ForInterface.Run(func, logName);
    }
}

public interface IWorkerImpl : IWorker
{
    WorkerConfiguration WorkerConfiguration { get; }
}

public interface IWorkerImpl<TThisWorkerInterface> : IWorkerImpl, IWorker<TThisWorkerInterface>
    where TThisWorkerInterface : IWorker<TThisWorkerInterface>
{
    TThisWorkerInterface PublicInterface { get; set; }
}