<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <Configurations>RemoteRelease;RemoteDebug;LocalDebug</Configurations>
        <Platforms>AnyCPU</Platforms>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'RemoteRelease' ">
      <Optimize Condition=" '$(Optimize)' == '' ">true</Optimize>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'LocalDebug' ">
      <DebugSymbols Condition=" '$(DebugSymbols)' == '' ">true</DebugSymbols>
      <Optimize Condition=" '$(Optimize)' == '' ">false</Optimize>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="JetBrains.Annotations" Version="2024.3.0" />
        
    <PackageReference Include="GSoft.Extensions.Configuration.Substitution" Version="1.1.1" />
        
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.16" />
        
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="8.0.16" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.16" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.16">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.16" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.16">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.16" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.3" />
    <PackageReference Include="Refit" Version="8.0.0" />
    <PackageReference Include="Refit.HttpClientFactory" Version="8.0.0" />

    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Enrichers.GlobalLogContext" Version="3.0.0" />
    <PackageReference Include="Serilog.Exceptions.EntityFrameworkCore" Version="8.4.0" />
    <PackageReference Include="Serilog.Expressions" Version="5.0.0" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="9.0.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.2.*" />
    <PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="6.2.*" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="6.2.*" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="6.2.*" />
    </ItemGroup>

    <ItemGroup>
        <Compile Include="..\..\void\GlobalUsings.cs">
            <Link>GlobalUsings.cs</Link>
        </Compile>
    </ItemGroup>

    <ItemGroup>
      <None Update="AppConfig\*.json">
          <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\Invictus\Invictus.Jupiter.Shared\Invictus.Jupiter.Shared.csproj" />
      <ProjectReference Include="..\..\Invictus\Invictus.Nomenklatura.Shared\Invictus.Nomenklatura.Shared.csproj" />
    </ItemGroup>

</Project>
