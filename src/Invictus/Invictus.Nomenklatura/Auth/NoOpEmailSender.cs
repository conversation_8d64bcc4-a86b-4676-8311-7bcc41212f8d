using Invictus.Nomenklatura.App2;

using Microsoft.AspNetCore.Identity;

namespace Invictus.Nomenklatura.Auth;

public class NoOpEmailSender<T> : IEmailSender<T>
    where T : class
{
    public Task SendConfirmationLinkAsync(T user, string email, string confirmationLink)
    {
        return Task.CompletedTask;
    }

    public Task SendPasswordResetLinkAsync(T user, string email, string resetLink)
    {
        return Task.CompletedTask;
    }

    public Task SendPasswordResetCodeAsync(T user, string email, string resetCode)
    {
        return Task.CompletedTask;
    }
}