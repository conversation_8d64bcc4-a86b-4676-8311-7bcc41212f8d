using System.Reflection;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Invictus.Nomenklatura.ExteriorServ;

public abstract class ExteriorServiceBuilderBase
{
    protected void RegisterDatabaseTypes(IServiceCollection serviceCollection, Assembly assembly = null)
    {
        string ns = null;

        if (assembly == null)
        {
            assembly = this.GetType().Assembly;
            ns = this.GetType().Namespace;
        }

        // PERF: do not call GetTypes() once more.
        IEnumerable<Type> dbAccessTypes = ReflectionUtil.GetAllTypesBasedOn(
            assembly.GetTypes(),
            [ typeof(DbAccessBase) ],
            ns
        );

        foreach (Type dbAccessTypeToRegister in dbAccessTypes)
        {
            serviceCollection.AddDbAccess(dbAccessTypeToRegister);
        }
    }
}

public abstract class ExteriorServiceBuilderBase<T> : ExteriorServiceBuilderBase, IExteriorServiceBuilder<T>
    where T : class
{
    private ILogger Logger => InvLog.Logger<ExteriorServiceBuilderBase>(true);

    private IServiceCollection _parentServiceCollection;

    public ServiceProvider ConcealedServiceProvider { get; private set; }

    protected virtual void AddConcealedServices(ServiceCollection concealedServiceCollection)
    {
        // Add Exterior service to it's own service collection
        concealedServiceCollection.AddSingleton<T>();
    }

    public void SetParentServiceCollection(IServiceCollection parentServiceCollection)
    {
        _parentServiceCollection = parentServiceCollection;

        this.ExposeConcealedServices();
    }

    protected virtual void ExposeConcealedServices()
    {
        // Expose this exterior service to the outer service collection.
        this.ExposeSingleton<T>();
    }

    private ServiceCollection _initConcealedServicesCollection;

    public virtual void Initialize1WithParentServiceProvider(IServiceProvider parentServiceProvider)
    {
        _initConcealedServicesCollection = this.MakeACopyOfParentServicesCollection(parentServiceProvider);

        _parentServiceCollection = null;
    }

    public void Initialize2WithParentServiceProvider(IServiceProvider parentServiceProvider, Action<ServiceCollection> serviceCollectionHook)
    {
        this.AddConcealedServices(_initConcealedServicesCollection);

        serviceCollectionHook?.Invoke(_initConcealedServicesCollection);

        ServiceProvider serviceProvider = _initConcealedServicesCollection.BuildServiceProvider();

        ConcealedServiceProvider = serviceProvider;

        Logger.Verbose($"Created Exterior Service {typeof(T).FullName}");

        _initConcealedServicesCollection = null;
    }

    protected void ExposeSingleton<TOtherClass>()
        where TOtherClass : class
    {
        _parentServiceCollection.AddSingleton(
            _ => {
                Logger.Verbose($"Proxying to outer service provider: {typeof(TOtherClass).FullName}");
                return ConcealedServiceProvider.GetRequiredService<TOtherClass>();
            }
        );
    }
    
    protected void ExposeKeyedSingleton<TOtherClass>(string key)
        where TOtherClass : class
    {
        _parentServiceCollection.AddKeyedSingleton<TOtherClass>(
            key,
            (s, key) => {
                Logger.Verbose($"Proxying to outer service provider: {typeof(TOtherClass).FullName}");
                return ConcealedServiceProvider.GetRequiredKeyedService<TOtherClass>(key);
            }
        );
    }

    protected void ExposeFactory<TOtherClass>()
        where TOtherClass : class
    {
        _parentServiceCollection.AddSingleton(
            _ => {
                Logger.Verbose($"Proxying to outer factory service provider: {typeof(TOtherClass).FullName}");
                return ConcealedServiceProvider.GetRequiredService<IServiceFactory<TOtherClass>>();
            }
        );
    }
    
    protected void ExposeDefaultFactoryCreationTransient<TOtherClass>()
        where TOtherClass : class
    {
        _parentServiceCollection.AddTransient(
            _ => {
                Logger.Verbose($"Proxying to outer factory service provider: {typeof(TOtherClass).FullName}");
                return ConcealedServiceProvider.GetRequiredService<IServiceFactory<TOtherClass>>().Create();
            }
        );
    }

    protected void ExposeSingleton<TInterface, TImplementation>()
        where TInterface : class
        where TImplementation : TInterface
    {
        _parentServiceCollection.AddSingleton<TInterface>(
            _ => {
                Logger.Verbose($"Proxying to outer service provider implementation: {typeof(TInterface).FullName}");
                return ConcealedServiceProvider.GetRequiredService<TImplementation>();
            }
        );
    }

    protected void ExposeSingleton(Type tInterface, Type tImplementation)
    {
        if (!tInterface.IsInterface)
            throw new Exception("tInterface should an interface");

        _parentServiceCollection.AddSingleton(tInterface,
            _ => {
                Logger.Verbose($"Proxying to outer service provider implementation: {tInterface.FullName}");
                return ConcealedServiceProvider.GetRequiredService(tImplementation);
            }
        );
    }
    
    protected void ExposeDbAccess<TDbAccess> ()
        where TDbAccess : DbAccessBase
    {
        this.ExposeSingleton<IDbAccessFactory<TDbAccess>>();
    }

    protected void ExposeDbContext<TDbAccess, TDbContext>()
        where TDbAccess : DbAccessBase
        where TDbContext : DbContext
    {
        _parentServiceCollection.AddTransient(_ => {
                var dbAccessFactory = ConcealedServiceProvider.GetRequiredService<IDbAccessFactory<TDbAccess>>();

                TDbAccess dbAccess = dbAccessFactory.CreateAccessForExternalCode();

                return (TDbContext)((IDbAccessPrivate)dbAccess).GetDbContext();
            }
        );
    }

    private ServiceCollection MakeACopyOfParentServicesCollection(IServiceProvider parentServiceProvider)
    {
        var concealedServicesCollection = new ServiceCollection();

        foreach (ServiceDescriptor parentServiceDescriptor in _parentServiceCollection)
        {
            Type serviceType = parentServiceDescriptor.ServiceType;

            if (serviceType == typeof(T))
                continue;

            ServiceDescriptor concealedServiceDescriptor;

            if (parentServiceDescriptor.ImplementationInstance != null)
            {
                concealedServiceDescriptor = parentServiceDescriptor;
            } else if (parentServiceDescriptor.ImplementationFactory != null)
            {
                concealedServiceDescriptor = new ServiceDescriptor(
                    serviceType,
                    sp =>
                        parentServiceDescriptor.ImplementationFactory(sp),
                    parentServiceDescriptor.Lifetime
                );
            } else if (parentServiceDescriptor.ImplementationType != null)
            {
                switch (parentServiceDescriptor.Lifetime)
                {
                    case ServiceLifetime.Singleton:

                        if (serviceType.IsGenericTypeDefinition)
                        {
                            concealedServiceDescriptor = parentServiceDescriptor;
                        }
                        else
                        {
                            concealedServiceDescriptor = new ServiceDescriptor(
                                serviceType,
                                sp =>
                                    parentServiceProvider.GetService(serviceType),
                                parentServiceDescriptor.Lifetime
                            );
                        }

                        break;

                    case ServiceLifetime.Transient:
                        concealedServiceDescriptor = parentServiceDescriptor;

                        break;

                    case ServiceLifetime.Scoped:
                        concealedServiceDescriptor = parentServiceDescriptor;

                        break;
                    default: throw TypeAbominationException.Enum(typeof(ServiceLifetime), parentServiceDescriptor.Lifetime);
                }
            } else if (parentServiceDescriptor.KeyedImplementationFactory != null)
            {
                switch (parentServiceDescriptor.Lifetime)
                {
                    case ServiceLifetime.Singleton:
                        concealedServiceDescriptor = parentServiceDescriptor;
                        break;
                    default: throw TypeAbominationException.Enum(typeof(ServiceLifetime), parentServiceDescriptor.Lifetime + "1");
                }
            } else
                throw new Exception("InitializeWithParentServiceProvider error [1].");

            if (concealedServiceDescriptor != null)
                ((ICollection<ServiceDescriptor>)concealedServicesCollection).Add(concealedServiceDescriptor);
        }

        return concealedServicesCollection;
    }
}