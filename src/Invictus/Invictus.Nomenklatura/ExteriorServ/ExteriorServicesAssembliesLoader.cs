using System.Reflection;

using Invictus.Nomenklatura.App.Launchpad;
using Invictus.Nomenklatura.Misc;

using Microsoft.Extensions.DependencyInjection;

namespace Invictus.Nomenklatura.ExteriorServ;

public class ExteriorServicesAssembliesLoader : IInvictusAssembliesLoader
{
    private static readonly Dictionary<Type, Action<IServiceCollection>> _BuildingHooks = new();
    
    private readonly Type[] _allExteriorServiceBuilderTypes;
    private readonly TaskCompletionSource _allStartedTcs = new ();
    
    private List<IExteriorService> _exteriorServices;

    public ExteriorServicesAssembliesLoader(Assembly[] allAssemblies, Type[] allTypes)
    {
        _allExteriorServiceBuilderTypes = ReflectionUtil
            .GetTypesImplementingInterface(allTypes, typeof(IExteriorServiceBuilder))
            .Where(t => !t.IsAbstract)
            .ToArray();
    }

    public void AddServices(IServiceCollection serviceCollection)
    {
        foreach (Type exteriorServiceBuilderType in _allExteriorServiceBuilderTypes)
        {
            var exteriorServiceBuilderInstance = (IExteriorServiceBuilder)Activator.CreateInstance(exteriorServiceBuilderType, Array.Empty<object>());

            exteriorServiceBuilderInstance.SetParentServiceCollection(serviceCollection);

            Type concealedServiceProviderInterfaceType = exteriorServiceBuilderType
                .GetInterfaces()
                .Single(itype => itype.IsGenericType && itype.GetGenericTypeDefinition() == typeof(IExteriorServiceBuilder<>));

            serviceCollection.AddSingleton(concealedServiceProviderInterfaceType, exteriorServiceBuilderInstance);
        }
    }

    public void Load(IServiceProvider serviceProvider)
    {
        List<IExteriorService> res = new();

        foreach (Type exteriorServiceBuilderType in _allExteriorServiceBuilderTypes)
        {
            Type concealedServiceProviderInterfaceType = exteriorServiceBuilderType
                .GetInterfaces()
                .Single(itype => itype.IsGenericType && itype.GetGenericTypeDefinition() == typeof(IExteriorServiceBuilder<>));

            var exteriorServiceBuilder = (IExteriorServiceBuilder)serviceProvider.GetService(concealedServiceProviderInterfaceType);

            exteriorServiceBuilder.Initialize1WithParentServiceProvider(serviceProvider);
        }

        foreach (Type exteriorServiceBuilderType in _allExteriorServiceBuilderTypes)
        {
            Type concealedServiceProviderInterfaceType = exteriorServiceBuilderType
                .GetInterfaces()
                .Single(itype => itype.IsGenericType && itype.GetGenericTypeDefinition() == typeof(IExteriorServiceBuilder<>));

            var exteriorServiceBuilder = (IExteriorServiceBuilder)serviceProvider.GetService(concealedServiceProviderInterfaceType);

            Action<IServiceCollection> h;

            _BuildingHooks.TryGetValue(exteriorServiceBuilderType, out h);

            exteriorServiceBuilder.Initialize2WithParentServiceProvider(serviceProvider, h);

            _BuildingHooks[concealedServiceProviderInterfaceType] = null;
        }

        foreach (Type exteriorServiceBuilderType in _allExteriorServiceBuilderTypes)
        {
            Type concealedServiceProviderInterfaceType = exteriorServiceBuilderType
                .GetInterfaces()
                .Single(itype => itype.IsGenericType && itype.GetGenericTypeDefinition() == typeof(IExteriorServiceBuilder<>));
            
            Type exteriorServiceType = concealedServiceProviderInterfaceType.GetGenericArguments()[0];

            var exteriorService = (IExteriorService)serviceProvider.GetService(exteriorServiceType);

            res.Add(exteriorService);
        }

        _exteriorServices = res;
    }

    public Task Run()
    {
        var startTasks = new Task[_exteriorServices.Count];

        for (int i = 0; i < _exteriorServices.Count; i++)
        {
            IExteriorService exteriorService = _exteriorServices[i];
            
            startTasks[i] = exteriorService.Run();
        }

        Task.WhenAll(startTasks).ContinueWith(_ => _allStartedTcs.SetResult());

        return _allStartedTcs.Task;
    }
    
    public static void HookExteriorServiceBuilding<TExteriorServiceBuilderType>(Action<IServiceCollection> h)
    {
        ArgumentNullException.ThrowIfNull(h);

        if (_BuildingHooks.ContainsKey(typeof(TExteriorServiceBuilderType)))
            throw new Exception("Multiple hooks are not supported.");

        if (_BuildingHooks.TryGetValue(typeof(TExteriorServiceBuilderType), out Action<IServiceCollection> oldVal))
        {
            if (oldVal == null)
                throw new Exception("It is too late to call this method, ServiceProvider is already built.");
        }

        _BuildingHooks[typeof(TExteriorServiceBuilderType)] = h;
    }

    public static TService GetPrivateService<TExteriorService, TService>(IServiceProvider serviceProvider)
        where TService : class
    {
        throw new NotImplementedException();
    }

    public TService GetPrivateServiceImpl<TExteriorService, TService>(IServiceProvider serviceProvider)
        where TService : class
    {
        foreach (Type exteriorServiceBuilderType in _allExteriorServiceBuilderTypes)
        {
            Type concealedServiceProviderInterfaceType = exteriorServiceBuilderType
                .GetInterfaces()
                .Single(itype => itype.IsGenericType && itype.GetGenericTypeDefinition() == typeof(IExteriorFacade<>));

            if (concealedServiceProviderInterfaceType.GetGenericArguments().Single() != typeof(TExteriorService))
                continue;

            var concealedServiceProvider = (IConcealedServiceProvider)serviceProvider.GetService(concealedServiceProviderInterfaceType);

            return concealedServiceProvider.ConcealedServiceProvider.GetRequiredService<TService>();
        }

        throw new Exception("Exterior service not found.");
    }
}
