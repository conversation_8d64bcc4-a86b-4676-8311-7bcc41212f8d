using Microsoft.Extensions.DependencyInjection;

namespace Invictus.Nomenklatura.ExteriorServ;

public interface IConcealedServiceProvider
{
    ServiceProvider ConcealedServiceProvider { get; }
}

// ReSharper disable once UnusedTypeParameter
public interface IConcealedServiceProvider<T> : IConcealedServiceProvider
    where T : class
{
}

public interface IExteriorServiceBuilder : IConcealedServiceProvider
{
    void SetParentServiceCollection(IServiceCollection services);

    void Initialize1WithParentServiceProvider(IServiceProvider parentServiceProvider);
    void Initialize2WithParentServiceProvider(IServiceProvider parentServiceProvider, Action<ServiceCollection> serviceCollectionHook);
}

public interface IExteriorServiceBuilder<T> : IConcealedServiceProvider<T>, IExteriorServiceBuilder
    where T : class
{
}

public interface IExteriorFacade<T> : IConcealedServiceProvider
    where T : class
{
}
public interface IExteriorService
{
    Task Run();
}