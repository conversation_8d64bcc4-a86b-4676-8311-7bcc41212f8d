using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Logg;

using Microsoft.Extensions.Hosting;

namespace Invictus.Nomenklatura.App;

public class InvGlobalExceptionHandling
{
    private readonly ILogger _logger = InvLog.Logger<InvGlobalExceptionHandling>();

    private readonly IHostApplicationLifetime _applicationLifetime;
    private readonly List<Action<Exception>> _preHaltActions = new();

    public InvGlobalExceptionHandling(IHostApplicationLifetime hostApplicationLifetime)
    {
        _applicationLifetime = hostApplicationLifetime;

        AppDomain currentDomain = AppDomain.CurrentDomain;
        currentDomain.UnhandledException += this.CurrentDomainOnUnhandledException;

        TaskScheduler.UnobservedTaskException += this.UnobservedTaskException;
    }

    public void Halt(Exception exception)
    {
        this.LogExceptionAndStopApplication(exception, "DirectHaltRequest", true);
    }

    public void AddPreHaltAction(Action<Exception> action)
    {
        _preHaltActions.Add(action);
    }
    
    private void UnobservedTaskException(object sender, UnobservedTaskExceptionEventArgs args)
    {
        if (args.Observed)
            return;

        bool skip =
            ExceptionUtil.IsPureOrNestedCancellation(args.Exception)
            ||
            ExceptionUtil.IsRandomNetworkIOException(args.Exception);
            
        if (skip)
        {
            args.SetObserved();

            return;
        }

        this.LogExceptionAndStopApplication(args.Exception, "UnobservedTask", false);
    }

    private void CurrentDomainOnUnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        if (e.ExceptionObject is OperationCanceledException)
            return;

        this.LogExceptionAndStopApplication(e.ExceptionObject as Exception, "AppDomain", true);
    }

    // ReSharper disable once ParameterOnlyUsedForPreconditionCheck.Local
    private void LogExceptionAndStopApplication(Exception exc, string exceptionSource, bool terminateInsteadOfStopping)
    {
        if (exc == null)
            _logger.Fatal(exceptionSource + " unhandled exception: e is not an Exception");
        else // Stock it in one log message to be sure that e.g. telegram will reach until application terminates.
            _logger.Fatal(exceptionSource + " unhandled exception:\r\n" + ExceptionUtil.ExceptionToMessage(exc));

        foreach (Action<Exception> preHaltAction in _preHaltActions)
        {
            preHaltAction(exc);
        }
        _applicationLifetime?.StopApplication();

        if (terminateInsteadOfStopping)
        {
            Environment.Exit(1);
        }
    }

}