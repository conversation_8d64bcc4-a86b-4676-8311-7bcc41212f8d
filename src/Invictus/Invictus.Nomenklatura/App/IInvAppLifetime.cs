namespace Invictus.Nomenklatura.App;

public interface IInvAppLifetime
{
    CancellationToken ApplicationStarting { get; }
    
    /// <summary>
    /// Triggered when the application host has fully started.
    /// USE THIS INSTEAD OF IHostApplicationLifetime
    /// </summary>
    CancellationToken ApplicationStarted { get; }
    
    CancellationToken ApplicationStopping { get; }

    void StopApplication();
}