using Invictus.Jupiter.Shared;
using Invictus.Nomenklatura.AppConfig;

using Microsoft.Extensions.Configuration;

namespace Invictus.Nomenklatura.App;

public class InvAppConfig
{
    private readonly IConfiguration _configurationManager;

    public NomenklaturaConfiguration BasicConfiguration => this.GetSection<NomenklaturaConfiguration>("Nomenklatura");
    
    public JupiterConfiguration Jupiter => this.GetSection<JupiterConfiguration>("Jupiter");
    
    public T GetSection<T>(params string[] sectionNames)
    {
        IConfiguration s = _configurationManager;

        foreach (string sectionName in sectionNames)
            s = s.GetSection(sectionName);

        return s.Get<T>();
    }

    public IConfiguration GetSection(string sectionName)
    {
        return _configurationManager.GetSection(sectionName);
    }

    public InvAppConfig(IConfiguration configurationManager)
    {
        _configurationManager = configurationManager;
    }
}