using Invictus.Nomenklatura.App.Launchpad;
using Invictus.Nomenklatura.AppConfig;

using Microsoft.Extensions.Hosting;

namespace Invictus.Nomenklatura.App;

public class InvEnv
{
    public IHostEnvironment WebHostEnvironment { get; }
    public CancellationToken ApplicationStarted { get; }
    public CancellationToken ApplicationStopping { get; }
    public CancellationToken ApplicationStopped { get; }
    public InvAppConfig AppConfig { get; }
    
    public InvEnvEnum EnvEnum { get; }

    public InvEnv(
        IHostEnvironment webHostEnvironment,
        IInvAppLifetime invictusAppLifetime,
        IHostApplicationLifetime applicationLifetime,
        InvAppConfig appConfig,
        InvAppConfigBuilder invAppConfigBuilder
    )
    {
        WebHostEnvironment = webHostEnvironment;
        ApplicationStopping = invictusAppLifetime.ApplicationStopping;
        ApplicationStopped = applicationLifetime.ApplicationStopped;
        ApplicationStarted = invictusAppLifetime.ApplicationStarted;
        AppConfig = appConfig;
        
        EnvEnum = invAppConfigBuilder.EnvironmentEnum;
    }
}