using System.Reflection;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Auth;
using Invictus.Nomenklatura.Database;
using Invictus.Nomenklatura.ExteriorServ;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;

using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;

using Serilog.Context;
using Serilog.Debugging;
using Serilog.Settings.Configuration;

namespace Invictus.Nomenklatura.App.Launchpad;

public class InvictusBasicWebAppBuilder
{
    private readonly string[] _args;
    
    private readonly List<Assembly> _assembliesToLoad = new();
    private readonly List<Type> _assemblyLoaderTypes  = new();

    private WebApplicationBuilder _builderPre;
    public IServiceProvider PreBuiltServices { get; private set; }
    public WebApplicationBuilder Builder { get; private set; }

    private readonly InvAppConfigBuilder _appConfigBuilder;
    
    public bool IsDesignTime { get; set; }

    public InvictusBasicWebAppBuilder(string[] args)
    {
        _args = args;
        
        Various.SetApplicationWideCultureInvariantCulture();
        
        // 1: Some things are necessary before main builder creation
        _builderPre = WebApplication.CreateBuilder(_args);
        
        _appConfigBuilder = new InvAppConfigBuilder(_builderPre.Environment);
        
        _builderPre.Services.AddSingleton(_ => _appConfigBuilder);
        _builderPre.Services.AddSingleton<InvAppConfig>();
        _builderPre.Services.AddSingleton<InvLog>();
        
        this.AddAppConfigurationJsonFile("nomenklatura");
        this.AddAssembly(typeof(NomenklaturaExteriorService).Assembly);
    }
    
    public void CreateBuilder()
    {
        _configurationManager = _appConfigBuilder.CreateWebHostAppConfiguration();
        _appConfigBuilder.AssignWebHostAppConfiguration(_builderPre, _configurationManager);
        
        PreBuiltServices = _builderPre.Build().Services;

        PreBuiltServices.GetRequiredService<InvLog>();

        Builder = WebApplication.CreateBuilder(_args);
        
        _appConfigBuilder.AssignWebHostAppConfiguration(Builder, _configurationManager);

        // 2: Main app builder
        Builder.Services.AddSingleton(_ => PreBuiltServices.GetRequiredService<InvAppConfigBuilder>());
        Builder.Services.AddSingleton(_ => PreBuiltServices.GetRequiredService<InvAppConfig>());
        Builder.Services.AddSingleton(_ => PreBuiltServices.GetRequiredService<InvLog>());
        // Builder.Services.ProxyNomenklaturaAmbient(preApp.Services);
        Builder.Services.AddNomenklaturaAmbient();
        
        Builder.Services.AddSingleton<InvEnv>();
        Builder.Services.AddSingleton<IInvAppLifetime, InvictusAppLifetime>();
        Builder.Services.AddSingleton<InvGlobalExceptionHandling>();
        
        Builder.Services.AddSignalR();
        
        Builder.Host.UseSerilog(this.ConfigureSerilog);
    }
    
    public void AddAppConfigurationJsonFile(string path)
    {
        _appConfigBuilder.AddAppConfigurationJsonFile(path);
    }

    private ConfigurationManager _configurationManager;

    private void ConfigureSerilog(HostBuilderContext context, LoggerConfiguration loggerConfiguration)
    {
        SelfLog.Enable(msg => {
                if (msg.Contains("Required properties not provided"))
                    return;
                
                if (msg.Contains("PeriodicFlushToDiskSink") && msg.Contains("ObjectDisposedException"))
                    return;

                if (IsDesignTime && msg.Contains("Ignoring added logger provider"))
                    return;



                string addLogDir = Path.GetFullPath("serilog_fail.txt");
                File.AppendAllText(addLogDir, msg);
                
                // throw new Exception("Logger error: " + msg);
            }
        );
            
        loggerConfiguration.ReadFrom.Configuration(
            context.Configuration,
            new ConfigurationReaderOptions(ConfigurationAssemblySource.AlwaysScanDllFiles)
            {
                SectionName = "Serilog",
                OnLevelSwitchCreated = (s, sw) => {
                    if (s == "$terminalControlSwitch")
                    {
                        // IDK why it doesn't read if form the config
                        sw.MinimumLevel = LogEventLevel.Debug;
                            
                        InvLog.LevelSwitch.TerminalSwitch = sw;
                    }
                }
            }
        );
        
        // TODO: DESIGN: move this elsewhere
        GlobalLogContext.PushProperty("EmOp", ""); // Emerald, to avoid logging errors due to {EmOp} in templates.
    }

    public void AddAssembly(Assembly assembly)
    {
        _assembliesToLoad.Add(assembly);
    }

    public void AddAssembliesLoader<T>()
    {
        _assemblyLoaderTypes.Add(typeof(T));
    }
    
    public AuthenticationBuilder AddMvc()
    {
        Builder.Services.AddEndpointsApiExplorer();
        Builder.Services.AddControllers();
        
        this.AddAssembliesLoader<MvcAssembliesLoader>();
        
        Builder.Services.AddSwaggerGen(opt =>
        {
            opt.SwaggerDoc("v1", new OpenApiInfo { Title = "MyAPI", Version = "v1" });
            opt.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                Type = SecuritySchemeType.Http,
                Scheme = "bearer",
                BearerFormat = "JWT"
            });

            opt.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type=ReferenceType.SecurityScheme,
                            Id="Bearer"
                        }
                    },
                    new string[]{}
                }
            });
        });
        
        Builder.Services
            .AddIdentityApiEndpoints<InvIdentityUser>()
            .AddRoles<InvIdentityRole>()
            .AddEntityFrameworkStores<AuthDbContext>();

        Builder.Services.AddAuthorization();
        
        return Builder.Services.AddAuthentication()
            .AddScheme<AuthenticationSchemeOptions, AllowAlwaysAuthenticationHandler>(BasicAuthenticationDefaults.FREE_FOR_ALL_AUTHENTICATION_SCHEME, null);
    }

    public void AddRazorPages(string relativePath)
    {
        Builder.Services.AddRazorPages(o => {
                if (relativePath != null)
                    o.RootDirectory = relativePath;
            }
        );
    }
    
    public InvBaseApp Build()
    {
        this.AddAssembliesLoader<ExteriorServicesAssembliesLoader>();
        
        Type[] allAssembliesTypes = _assembliesToLoad.SelectMany(a => a.GetTypes()).ToArray();

        object[] loadersParameters = [ _assembliesToLoad.ToArray(), allAssembliesTypes ];

        IInvictusAssembliesLoader[] assembliesLoaders = 
            _assemblyLoaderTypes
                .Select(t => (IInvictusAssembliesLoader)ActivatorUtilities.CreateInstance(PreBuiltServices, t, loadersParameters))
                .ToArray();
        
        _assembliesToLoad.Clear();
        _assemblyLoaderTypes.Clear();
        
        foreach (IInvictusAssembliesLoader invictusAssembliesLoader in assembliesLoaders)
        {
            invictusAssembliesLoader.AddServices(Builder.Services);
        }
        
        WebApplication built = Builder.Build();
        
        InvLog.Logger<InvictusBasicWebAppBuilder>().Information("... App is Starting ...");

        return new InvBaseApp(built, assembliesLoaders);
    }
    
    private class MvcAssembliesLoader : IInvictusAssembliesLoader
    {
        private readonly Assembly[] _assemblies;

        public MvcAssembliesLoader(Assembly[] assemblies, Type[] types)
        {
            _assemblies = assemblies;
        }

        public void AddServices(IServiceCollection serviceCollection)
        {
            IMvcBuilder mvc = serviceCollection.AddMvc();
            
            foreach (Assembly assembly in _assemblies)
            {
                mvc.AddApplicationPart(assembly);
            }
        }

        public void Load(IServiceProvider serviceProvider)
        {
        }
        
        public Task Run()
        {
            return Task.Delay(100);
        }
    }
}