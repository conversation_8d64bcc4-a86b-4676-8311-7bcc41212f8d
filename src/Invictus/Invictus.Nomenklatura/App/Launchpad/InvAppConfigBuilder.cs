using Invictus.Nomenklatura.AppConfig;
using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Misc;

using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;

namespace Invictus.Nomenklatura.App.Launchpad;

public class InvAppConfigBuilder
{
    private readonly List<string> _addJsonFiles = new();
    
    public InvEnvEnum EnvironmentEnum { get; }
   
    public InvAppConfigBuilder(IHostEnvironment hostEnvironment)
    {
        string hostEnvironmentName = hostEnvironment.EnvironmentName;

        if (hostEnvironmentName == Environments.Development)
        {
            EnvironmentEnum = InvEnvEnum.Dev;
        }
        else if (hostEnvironmentName == Environments.Production)
        {
            EnvironmentEnum = InvEnvEnum.ProdServ;
        }
        else if (hostEnvironmentName == "DevelopmentIntegrationTesting")
        {
            EnvironmentEnum = InvEnvEnum.IntegrationTestLocal;
        }
        else
        {
            throw TypeAbominationException.String("webHostEnvironment.EnvironmentName", hostEnvironmentName);
        }
    }

    public void AddAppConfigurationJsonFile(string namePart)
    {
        _addJsonFiles.Add(namePart);
    }

    public ConfigurationManager CreateWebHostAppConfiguration()
    {
        string envEnumName = Enum.GetName(EnvironmentEnum).ToLowerInvariant();

        var innerConfigurationManager = new ConfigurationManager();
        
        this.AddFiles(innerConfigurationManager, envEnumName);
        this.AddPredefined(innerConfigurationManager);

        var outerConfigurationManager = new ConfigurationManager();

        outerConfigurationManager.Sources.Add(new MergingConfigurationSource(innerConfigurationManager));
        // builder.Configuration.AddEnvironmentVariables();
        outerConfigurationManager.AddSubstitution();

        return outerConfigurationManager;
    }

    public void AssignWebHostAppConfiguration(WebApplicationBuilder builder, ConfigurationManager configurationManager)
    {
        foreach (IConfigurationSource configurationSource in configurationManager.Sources)
        {
            builder.Configuration.Sources.Add(configurationSource);
        }
    }

    private void AddFiles(IConfigurationManager configurationManager, string envEnumName)
    {
        configurationManager.SetBasePath(Path.Combine(AppContext.BaseDirectory, "AppConfig"));

        foreach (string jsonFilePart in _addJsonFiles)
            this.AddJsonFile(configurationManager, jsonFilePart + ".cfg.shared.json", false, true);

        foreach (string jsonFilePart in _addJsonFiles)
            this.AddJsonFile(configurationManager, jsonFilePart + ".sec.shared.json", false, true);

        foreach (string jsonFilePart in _addJsonFiles)
            this.AddJsonFile(configurationManager, jsonFilePart + ".cfg." + envEnumName + ".json", false, true);

        foreach (string jsonFilePart in _addJsonFiles)
            this.AddJsonFile(configurationManager, jsonFilePart + ".sec." + envEnumName + ".json", false, true);
    }
    
    private void AddPredefined(IConfigurationManager configurationManager)
    {
        string exeDir = AppContext.BaseDirectory;

        configurationManager.GetSection("Nomenklatura:PREDEF:FileStartupTime1").Value = ServerClock.StartTime.ToString("yy-MM-dd HHmm ss");
        configurationManager.GetSection("Nomenklatura:PREDEF:FileStartupTime2").Value = ServerClock.StartTime.ToString("yy-MM-dd HH");
        configurationManager.GetSection("Nomenklatura:PREDEF:ExeDir").Value = exeDir;
        configurationManager.GetSection("Environment.MachineName").Value = Environment.MachineName;
    }

    private void AddJsonFile(IConfigurationManager configurationManager, string path, bool optional, bool reloadOnChange)
    {
        configurationManager.AddJsonFile(path, optional, reloadOnChange);
    }
}

public class MergingConfigurationSource : IConfigurationSource
{
    private readonly IConfigurationRoot _baseConfiguration;

    public MergingConfigurationSource(IConfigurationRoot baseConfiguration)
    {
        _baseConfiguration = baseConfiguration;
    }

    public IConfigurationProvider Build(IConfigurationBuilder builder)
    {
        return new MergingConfigurationProvider(_baseConfiguration);
    }
}

public class MergingConfigurationProvider : ConfigurationProvider
{
    private readonly IConfiguration _baseConfiguration;

    public MergingConfigurationProvider(IConfiguration baseConfiguration)
    {
        _baseConfiguration = baseConfiguration;
    }

    public override void Load()
    {
        var mergedData = new Dictionary<string, string>(StringComparer.Ordinal);

        Dictionary<string, int> nextArrayIndex = new();

        KeyValuePair<string, string>[] dataKvArray = _baseConfiguration.AsEnumerable().ToArray();
        
        foreach (KeyValuePair<string,string> kv in dataKvArray)
        {
            if (kv.Key.Contains("$merge"))
                continue;
            
            string[] keySpl = kv.Key.Split(":");

            for (int keySplIndex = 0; keySplIndex < keySpl.Length; keySplIndex++)
            {
                string keyPart = keySpl[keySplIndex];

                if (int.TryParse(keyPart, out int keyPartInt))
                {
                    string arrayKey = string.Join(":", keySpl.Take(keySplIndex));

                    nextArrayIndex.TryAdd(arrayKey, 0);
                    nextArrayIndex[arrayKey] = Math.Max(nextArrayIndex[arrayKey], keyPartInt + 1);
                }
            }
            
            mergedData[kv.Key] = kv.Value;
        }

        Dictionary<string, string> mergeStrToNormalStr = new();

        foreach (KeyValuePair<string, string> kv in dataKvArray)
        {
            string[] keySpl = kv.Key.Split(":");

            int mergeSplIndex = keySpl.IndexOf(k => k == "$merge");

            if (mergeSplIndex == -1 || mergeSplIndex + 2 >= keySpl.Length)
                continue;

            int mergeI = int.Parse(keySpl[mergeSplIndex + 2]);
            
            string sourceArrayKey = string.Join(":", keySpl.Take(mergeSplIndex));
            string replacementKey = string.Join(":", keySpl.Take(mergeSplIndex + 3));
            
            if (mergeStrToNormalStr.ContainsKey(replacementKey))
                continue;

            if (!nextArrayIndex.ContainsKey(sourceArrayKey))
                throw new KeyNotFoundException($"Source json configuration array {sourceArrayKey} was not found.");
            
            mergeStrToNormalStr[replacementKey] = sourceArrayKey + ":" + nextArrayIndex[sourceArrayKey];
            
            nextArrayIndex[sourceArrayKey]++;
        }

        foreach (KeyValuePair<string, string> kv in dataKvArray)
        {
            foreach (KeyValuePair<string,string> toMergeKvs in mergeStrToNormalStr)
            {
                if (!kv.Key.StartsWith(toMergeKvs.Key))
                    continue;

                mergedData[kv.Key.Replace(toMergeKvs.Key, toMergeKvs.Value)] = kv.Value;
            }
        }

        Data = mergedData;
    }
}
