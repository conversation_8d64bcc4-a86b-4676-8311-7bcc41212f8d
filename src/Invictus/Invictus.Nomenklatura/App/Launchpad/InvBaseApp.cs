using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Rewrite;

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace Invictus.Nomenklatura.App.Launchpad;

public class InvBaseApp : IInvBaseApp
{
    private readonly WebApplication _webApplication;
    private IInvictusAssembliesLoader[] _assembliesLoaders;
    
    public IServiceProvider ServiceProvider => _webApplication.Services;
    public WebApplication MsWebApp => _webApplication;

    public event Action<IServiceProvider> Startup
    {
        add => _webApplication.Services.GetService<InvEnv>().ApplicationStarted.Register(() => value(ServiceProvider));
        remove => throw new NotImplementedException();
    }

    public InvBaseApp(WebApplication app, IInvictusAssembliesLoader[] assembliesLoaders)
    {
        _webApplication = app;
        _assembliesLoaders = assembliesLoaders;
    }

    public void UseMain()
    {
        // This sets up the whole routing system.
        _webApplication.UseRouting();

        // Redirect to https. These two are different but I don't care.
        _webApplication.UseRewriter(new RewriteOptions().AddRedirectToHttps());
        _webApplication.UseHttpsRedirection();
        
        _webApplication.UseSerilogRequestLogging(options =>
        {
            // Customize the message template
            options.MessageTemplate = "{RemoteIpAddress} {RequestScheme} {RequestHost} {RequestMethod} {RequestPath} responded {StatusCode} in {Elapsed:0.0000} ms";

            // Emit debug-level events instead of the defaults
            options.GetLevel = (httpContext, elapsed, ex) => LogEventLevel.Debug;

            // Attach additional properties to the request completion event
            options.EnrichDiagnosticContext = (diagnosticContext, httpContext) =>
            {
                diagnosticContext.Set("RequestHost",     httpContext.Request.Host.Value);
                diagnosticContext.Set("RequestScheme",   httpContext.Request.Scheme);
                diagnosticContext.Set("RemoteIpAddress", httpContext.Connection.RemoteIpAddress);
            };
        });
    }

    public void UseAuth()
    {
        _webApplication.UseAuthentication();
        _webApplication.UseAuthorization();
    }

    public void UseMvc()
    {
        // Configure the HTTP request pipeline.
        // if (_webApplication.Environment.IsDevelopment())
        {
            _webApplication.UseDeveloperExceptionPage();
            _webApplication.UseSwagger();
            _webApplication.UseSwaggerUI(options =>
            {
                options.SwaggerEndpoint("/swagger/v1/swagger.json", "IdentityJwtApiNet9 v1");
                options.EnablePersistAuthorization();
            });
            
            // _webApplication.MapGet("/debug/routes", (IEnumerable<EndpointDataSource> endpointSources) =>
            //     string.Join("\n", endpointSources.SelectMany(source => source.Endpoints)));
        }
    }
    
    public void Run()
    {
        _webApplication.Services.GetRequiredService<IInvAppLifetime>();
        _webApplication.Services.GetRequiredService<InvGlobalExceptionHandling>();

        for (int i = 0; i < _assembliesLoaders.Length; i++)
        {
            IInvictusAssembliesLoader assembliesLoader = _assembliesLoaders[i];
            
            assembliesLoader.Load(_webApplication.Services);
        }

        var assemblyRunTasks = new Task[_assembliesLoaders.Length];

        for (int i = 0; i < _assembliesLoaders.Length; i++)
        {
            IInvictusAssembliesLoader assembliesLoader = _assembliesLoaders[i];
            
            assemblyRunTasks[i] = assembliesLoader.Run();
        }

        Task.WhenAll(assemblyRunTasks).ContinueWith(this.OnAssembliesLoaded);
    }

    private void OnAssembliesLoaded(Task obj)
    {
        var appLifetime = _webApplication.Services.GetRequiredService<IInvAppLifetime>();
        
        ((InvictusAppLifetime)appLifetime).SignalStarting();
        
        _webApplication.Services.GetRequiredService<IHostApplicationLifetime>().ApplicationStarted.Register(this.OnStartup);

        _webApplication.Run();
    }

    private void OnStartup()
    {
        _assembliesLoaders = null;
        
        ((InvictusAppLifetime) _webApplication.Services.GetRequiredService<IInvAppLifetime>()).SignalMsAppStarted();
    }
}