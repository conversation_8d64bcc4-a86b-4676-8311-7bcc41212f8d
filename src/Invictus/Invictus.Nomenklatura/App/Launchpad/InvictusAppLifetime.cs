using Invictus.Nomenklatura.Exceptions;

using Microsoft.Extensions.Hosting;

namespace Invictus.Nomenklatura.App.Launchpad;

class InvictusAppLifetime : IInvAppLifetime
{
    private readonly IHostApplicationLifetime _microsoftApplicationLifetime;
    private readonly InvGlobalExceptionHandling _thisAppGlobalExceptionHandling;

    private readonly CancellationTokenSource _startingSource = new();
    private readonly CancellationTokenSource _startedSource = new();
    private readonly CancellationTokenSource _stoppingSource = new();
    
    public CancellationToken ApplicationStarting => _startingSource.Token;
    
    public CancellationToken ApplicationStarted => _startedSource.Token;
    
    public CancellationToken ApplicationStopping => _stoppingSource.Token;

    public InvictusAppLifetime(IHostApplicationLifetime microsoftApplicationLifetime, InvGlobalExceptionHandling thisAppGlobalExceptionHandling)
    {
        _microsoftApplicationLifetime = microsoftApplicationLifetime;
        _thisAppGlobalExceptionHandling = thisAppGlobalExceptionHandling;

        ApplicationStarted.Register(this.OnMyApplicationStarted);
        
        _microsoftApplicationLifetime.ApplicationStopping.Register(this.DelayedApplicationStopSignal);
    }

    private void DelayedApplicationStopSignal()
    {
        if (_stoppingSource.IsCancellationRequested)
            throw new JustNoWayException();
        
        _stoppingSource.CancelAfter(750);
    }

    public void StopApplication()
    {
        _microsoftApplicationLifetime.StopApplication();
    }

    public void SignalStarting()
    {
        if (_startingSource.IsCancellationRequested)
            throw new JustNoWayException();

        try
        {
            _startingSource.Cancel(throwOnFirstException: true);
        }
        catch (Exception exc)
        {
            _thisAppGlobalExceptionHandling.Halt(exc);
        }
    }

    public void SignalMsAppStarted()
    {
        if (_startedSource.IsCancellationRequested)
            throw new JustNoWayException();

        try
        {
            _startedSource.Cancel(throwOnFirstException: true);
        }
        catch (Exception exc)
        {
            _thisAppGlobalExceptionHandling.Halt(exc);
        }
    }

    private void OnMyApplicationStarted()
    {
        if (!_microsoftApplicationLifetime.ApplicationStarted.IsCancellationRequested)
            throw new JustNoWayException();
    }
}