using System.Reflection;

using Invictus.Nomenklatura.App.Launchpad;

namespace Invictus.Nomenklatura.App;

public class DesignTimeNomenklaturaHost
{
    public static DesignTimeNomenklaturaHost Instance = new();

    private InvBaseApp _app;

    public bool IsRunning { get; private set; }

    public void Run(Assembly[] addAssemblies, string[] addConfigFiles, Action<IServiceProvider> onRun)
    {
        if (_app != null)
            throw new Exception("Already running.");
        
        var appBuilder = new InvictusBasicWebAppBuilder(Array.Empty<string>());

        appBuilder.IsDesignTime = true;
        appBuilder.CreateBuilder();

        foreach (string configFile in addConfigFiles)
            appBuilder.AddAppConfigurationJsonFile(configFile);

        foreach (Assembly assembly in addAssemblies)
            appBuilder.AddAssembly(assembly);
        
        _app = appBuilder.Build();

        IsRunning = true;
        
        _app.Startup += onRun;
        _app.Run();
    }

    public void RunAsynchronously(Assembly[] addAssemblies, string[] addConfigFiles, Action<IServiceProvider> onRun)
    {
        Task.Run(() => this.Run(addAssemblies, addConfigFiles, onRun));
    }
}