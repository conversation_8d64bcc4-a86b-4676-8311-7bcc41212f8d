using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.AppConfig;

using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;

namespace Invictus.Nomenklatura.Database;

public class AuthDbAccess : DbAccessBase
{
    public AuthDbAccess(DbAccessOptions accessOptions, AuthDbContext context)
        : base(accessOptions, context)
    {
    }
}

public class AuthDbContext : IdentityDbContext<InvIdentityUser, InvIdentityRole, string>
{
    public AuthDbContext()
    {
    }

    public AuthDbContext(DbContextOptions options)
        : base(options)
    {
    }
}