using System.Net;

using Invictus.AI.AppData;
using Invictus.AI.Models;
using Invictus.AI.Services;
using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Web;
using Invictus.Nomenklatura.Workers;

using Refit;

namespace Invictus.AI;

public interface IAIWorker : IWorker<IAIWorker>
{
    Task<WebResponse<string, string>> TranslateUkrainianToRussian(string text);
}

public class AIWorker : IWorkerImpl<IAIWorker>, IAIWorker
{
    public WorkerCore Core { get; set; }
    public ILogger Log { get; } = InvLog.Logger<AIWorker>();

    public WorkerConfiguration WorkerConfiguration { get; } = new WorkerConfiguration(
        "AIWORK",
        new WorkerConfiguration.TaskScheduler("AIProcessing", 1),
        LogEventLevel.Information,
        AllowDirectCall: true
    );

    IAIWorker IWorkerImpl<IAIWorker>.PublicInterface { get; set; }

    private readonly AiConfiguration _configuration;
    private readonly IGeminiApiClient _geminiApiClient;

    public AIWorker(InvAppConfig appConfig, GeminiApiFactory geminiApiFactory)
    {
        _configuration = appConfig.GetSection<AiConfiguration>("AI");
        
        _geminiApiClient = geminiApiFactory.Create();
    }

    public Task<WebResponse<string, string>> TranslateUkrainianToRussian(string text)
    {
        Log.Information("Translating Ukrainian text to Russian: {TextLength} characters", text?.Length ?? 0);

        return this.ProcessWithGemini(
            $"Translate the following Ukrainian text to Russian: {text}. " +
            $"Please output only the translated text. If case you don't recognize input string as Ukrainian, output <ERROR>."
        );
    }

    /*
    public async Task<string> ProcessRequest(string input, CancellationToken ct)
    {
        Log.Information("Processing AI request: {Input}", input);

        string result = await this.ProcessWithGemini(input, ct);
        
        return result;
    }
    */

    private async Task<WebResponse<string, string>> ProcessWithGemini(string prompt, CancellationToken ct = default)
    {
        WebResponse<string, string> res = await this.ProcessWithGeminiInner(prompt, ct);
        
        if (res.IsFail)
            Log.Warning(res.Fail);

        return res;
    }
    
    private async Task<WebResponse<string, string>> ProcessWithGeminiInner(string prompt, CancellationToken ct = default)
    {
        try
        {
            if (string.IsNullOrEmpty(_configuration.GeminiApiKey))
            {
                Log.Error("Gemini API Key is missing in configuration");
                return WebResponse<string, string>.FailNew("Error: API configuration is missing", HttpStatusCode.InternalServerError);
            }

            var geminiRequest = new GeminiRequest
            {
                Contents = new List<Content>
                {
                    new Content
                    {
                        Parts = new List<Part>
                        {
                            new Part { Text = prompt }
                        }
                    }
                }
            };

            WebResponse<GeminiResponse, string> response = await _geminiApiClient.GenerateContentAsync(
                _configuration.GeminiDefaultModel,
                _configuration.GeminiApiKey,
                geminiRequest
            );

            if (response.IsSuccess && response.Success?.Candidates?.Count > 0)
            {
                string generatedText = response.Success.Candidates
                    .FirstOrDefault()?
                    .Content?
                    .Parts?
                    .FirstOrDefault()?
                    .Text;

                if (generatedText == null)
                    return WebResponse<string, string>.FailNew("No content generated", HttpStatusCode.InternalServerError);

                return WebResponse<string, string>.SuccessNew(generatedText, HttpStatusCode.OK);
            }

            string errorContent = response.Fail;
            
            Log.Error("Gemini API call failed. Status: {StatusCode}, Error: {ErrorContent}", response.StatusCode, errorContent);

            return WebResponse<string, string>.SuccessNew($"Error: Failed to get response from AI. Status: {response.StatusCode}", response.StatusCode);
        }
        catch (ApiException apiEx)
        {
            string errorContent = await apiEx.GetContentAsAsync<string>() ?? apiEx.Message;
            Log.Error(apiEx, "Refit API exception calling Gemini. Status: {StatusCode}, Content: {Content}",
                apiEx.StatusCode, errorContent);

            return WebResponse<string, string>.SuccessNew($"Error: API communication error. {apiEx.Message}", HttpStatusCode.InternalServerError);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "An unexpected error occurred while calling Gemini API");
            return WebResponse<string, string>.SuccessNew($"Error: An internal error occurred. {ex.Message}", HttpStatusCode.InternalServerError);
        }
    }
}
