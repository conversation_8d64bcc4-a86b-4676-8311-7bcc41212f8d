using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.ExteriorServ;

using JetBrains.Annotations;

using Microsoft.Extensions.DependencyInjection;

namespace Invictus.AI;

[UsedImplicitly]
public class AIExteriorServiceBuilder : ExteriorServiceBuilderBase<AIExteriorService>
{
    protected override void AddConcealedServices(ServiceCollection concealedServiceCollection)
    {
        concealedServiceCollection.AddSingleton<GeminiApiFactory>();
        
        concealedServiceCollection.AddWorkerSingleton<AIWorker>();
        
        this.RegisterDatabaseTypes(concealedServiceCollection);

        base.AddConcealedServices(concealedServiceCollection);
    }

    protected override void ExposeConcealedServices()
    {
        this.ExposeSingleton<IAIWorker>();
        
        base.ExposeConcealedServices();
    }
}