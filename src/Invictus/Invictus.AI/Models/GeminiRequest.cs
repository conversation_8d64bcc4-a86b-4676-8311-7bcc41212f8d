using System.Text.Json.Serialization;

namespace Invictus.AI.Models
{
    public class GeminiRequest
    {
        [JsonPropertyName("contents")]
        public List<Content> Contents { get; set; }
    }

    public class Content
    {
        [JsonPropertyName("parts")]
        public List<Part> Parts { get; set; }

        // Optional: Add role if needed for more complex scenarios or multi-turn chat
        [JsonPropertyName("role")]
        public string Role { get; set; } // e.g., "user"
    }

    public class Part
    {
        [JsonPropertyName("text")]
        public string Text { get; set; }
    }
}
