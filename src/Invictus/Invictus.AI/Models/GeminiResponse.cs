using System.Text.Json.Serialization;

namespace Invictus.AI.Models
{
    public class GeminiResponse
    {
        [JsonPropertyName("candidates")]
        public List<Candidate> Candidates { get; set; }

        [JsonPropertyName("promptFeedback")]
        public PromptFeedback PromptFeedback { get; set; }
        
        [JsonPropertyName("usageMetadata")]
        public UsageMetadata UsageMetadata { get; set; }
        
        [JsonPropertyName("modelVersion")]
        public string ModelVersion { get; set; }
        
        [JsonPropertyName("responseId")]
        public string ResponseId { get; set; }
    }
    
    public class TokenDetails
    {
        [JsonPropertyName("modality")]
        public string Modality { get; set; } // Made nullable in case it can be missing
        
        [JsonPropertyName("tokenCount")]
        public int TokenCount { get; set; }
    }
    
    public class UsageMetadata
    {
        [JsonPropertyName("promptTokenCount")]
        public int PromptTokenCount { get; set; }
        
        [JsonPropertyName("candidatesTokenCount")]
        public int CandidatesTokenCount { get; set; }
        
        [JsonPropertyName("totalTokenCount")]
        public int TotalTokenCount { get; set; }
        
        [JsonPropertyName("promptTokensDetails")]
        public List<TokenDetails> PromptTokensDetails { get; set; } // Using List<T>, made nullable
        
        [JsonPropertyName("candidatesTokensDetails")]
        public List<TokenDetails> CandidatesTokensDetails { get; set; } // Using List<T>, made nullable
    }

    public class Candidate
    {
        [JsonPropertyName("content")]
        public Content Content { get; set; }

        [JsonPropertyName("finishReason")]
        public string FinishReason { get; set; }

        [JsonPropertyName("avgLogprobs")]
        public decimal AvgLogprobs { get; set; }
        
        [JsonPropertyName("index")]
        public int Index { get; set; }

        [JsonPropertyName("safetyRatings")]
        public List<SafetyRating> SafetyRatings { get; set; }
        
        // avgLogprobs
    }

    public class SafetyRating
    {
        [JsonPropertyName("category")]
        public string Category { get; set; }

        [JsonPropertyName("probability")]
        public string Probability { get; set; }
    }

    public class PromptFeedback
    {
        [JsonPropertyName("safetyRatings")]
        public List<SafetyRating> SafetyRatings { get; set; }
    }
}
