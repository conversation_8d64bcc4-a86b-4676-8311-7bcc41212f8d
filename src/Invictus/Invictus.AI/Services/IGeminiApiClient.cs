using Invictus.AI.Models;
using Invictus.Nomenklatura.Web;

using Refit;

namespace Invictus.AI.Services
{
    public interface IGeminiApiClient
    {
        [Post("/v1beta/models/{modelName}:generateContent")]
        Task<WebResponse<GeminiResponse, string>> GenerateContentAsync(
            [AliasAs("modelName")] string modelName,
            [Query, AliasAs("key")] string apiKey,
            [Body] GeminiRequest request
        );
    }
}
