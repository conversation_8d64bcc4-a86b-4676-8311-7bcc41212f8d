using Invictus.Nomenklatura.Front;
using Invictus.Nomenklatura.Web;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Invictus.AI.Controllers;

[Authorize(Roles = FrontConst.API_USER_ROLE_NAME)]
[ApiController]
[Route("[controller]/[action]")]
public class AiController : ControllerBase
{
    private readonly IAIWorker _aiWorker;

    public AiController(IAIWorker aiWorker)
    {
        _aiWorker = aiWorker;
    }
    
    [HttpGet]
    public async Task<StatusCodeResult> Ping()
    {
        await Task.Delay(200);

        return this.Ok();
    }

    [HttpPost]
    public async Task<IActionResult> TranslateUkrainianToRussianWithGemini([FromBody] string text)
    {
        WebResponse<string, string> res = await _aiWorker.TranslateUkrainianToRussian(text);

        if (res.IsSuccess)
            return this.Ok(res.Success);

        return this.StatusCode((int)res.StatusCode, res.Fail);
    }
}