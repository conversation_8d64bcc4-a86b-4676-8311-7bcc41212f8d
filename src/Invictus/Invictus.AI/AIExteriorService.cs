using Invictus.Nomenklatura.ExteriorServ;
using Invictus.Nomenklatura.Logg;

using JetBrains.Annotations;

namespace Invictus.AI;

[UsedImplicitly]
public class AIExteriorService : IExteriorService
{
    private readonly ILogger _logger = InvLog.Logger<AIExteriorService>();
    private readonly IAIWorker _aiWorker;
    
    public AIExteriorService(IAIWorker aiWorker)
    {
        _aiWorker = aiWorker;
        _logger.Information("AI Exterior Service initialized");
    }

    public Task Run()
    {
        _logger.Information("AI Exterior Service running");
        return Task.CompletedTask;
    }
}
