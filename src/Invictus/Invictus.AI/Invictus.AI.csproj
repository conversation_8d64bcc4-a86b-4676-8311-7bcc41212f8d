<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <Configurations>RemoteRelease;RemoteDebug;LocalDebug</Configurations>
        <Platforms>AnyCPU</Platforms>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'RemoteRelease' ">
      <Optimize Condition=" '$(Optimize)' == '' ">true</Optimize>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'LocalDebug' ">
      <DebugSymbols Condition=" '$(DebugSymbols)' == '' ">true</DebugSymbols>
      <Optimize Condition=" '$(Optimize)' == '' ">false</Optimize>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Invictus\Invictus.Nomenklatura\Invictus.Nomenklatura.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Compile Include="..\..\void\GlobalUsings.cs">
            <Link>GlobalUsings.cs</Link>
        </Compile>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Refit.HttpClientFactory" Version="8.0.0" />
        <PackageReference Include="System.Text.Json" Version="8.0.16" />
    </ItemGroup>

    <ItemGroup>
        <None Update="AppConfig\*.json">
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>
</Project>
