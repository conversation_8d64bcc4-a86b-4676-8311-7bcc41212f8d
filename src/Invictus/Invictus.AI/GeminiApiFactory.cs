using Invictus.AI.AppData;
using Invictus.AI.Services;
using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Web;

namespace Invictus.AI;

public class GeminiApiFactory : RefitApiFactoryBase<IGeminiApiClient>
{
    public GeminiApiFactory(InvTasks invTasks, InvAppConfig invAppConfig)
        : base (invTasks, invAppConfig.GetSection<AiConfiguration>("AI").GeminiHost)
    {
    }

    protected override void ModifyHttpClient(HttpClient httpClient)
    {
        GC.KeepAlive(0);
    }
}