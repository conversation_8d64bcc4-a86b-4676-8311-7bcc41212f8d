<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <Configurations>RemoteRelease;RemoteDebug;LocalDebug</Configurations>
        <Platforms>AnyCPU</Platforms>
    </PropertyGroup>

    <ItemGroup>
        <Compile Include="..\..\void\GlobalUsings.cs">
            <Link>GlobalUsings.cs</Link>
        </Compile>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Invictus.Emerald\Invictus.Emerald.csproj" />
      <ProjectReference Include="..\Invictus.Nomenklatura\Invictus.Nomenklatura.csproj" />
    </ItemGroup>

    <ItemGroup>
      <None Remove="em.script" />
      <EmbeddedResource Include="em.script">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </EmbeddedResource>
      <None Remove="Emerald.json" />
      <EmbeddedResource Include="Emerald.json" />
    </ItemGroup>

    <PropertyGroup>
        <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
        <PackageId>Invictus.Nomenklatura.Emerald</PackageId>
        <Version>1.0.0</Version>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'RemoteRelease' ">
      <Optimize Condition=" '$(Optimize)' == '' ">true</Optimize>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'LocalDebug' ">
      <DebugSymbols Condition=" '$(DebugSymbols)' == '' ">true</DebugSymbols>
      <Optimize Condition=" '$(Optimize)' == '' ">false</Optimize>
    </PropertyGroup>

</Project>
