using Invictus.Emerald.Shared;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Logg;

namespace Invictus.Nomenklatura.Emerald;

[EmeraldFeature("Log")]
public class InvLogManagement
{
    private readonly InvTasks _invTasks;
    private readonly Lazy<ILogger> _myLoggerLazy = new(() => Log.ForContext(typeof(InvLogManagement)));
    
    public InvLogManagement(InvTasks invTasks)
    {
        _invTasks = invTasks;
    }
    
    [EmeraldOperation("CleanupLogs")]
    public Task CleanupLogs(CancellationToken ct, IFileSystemService fileSystemService)
    {
        return _invTasks.RunBackground(() => this.DoCleanupLogs(fileSystemService), "CleanupLogs");
    }

    [EmeraldOperation("SetVerbose")]
    public void SetLogLevelTrace()
    {
        InvLog.LevelSwitch.TerminalSwitch.MinimumLevel = LogEventLevel.Verbose;
    }

    private void DoCleanupLogsDir(IFileSystemService fileSystemService, string subDir)
    {
        FileInfo[] allLogFilesInfoSorted = fileSystemService.GetFiles(subDir, "*")
            .OrderBy(fi => fi.LastWriteTimeUtc)
            .ToArray();

        ILogger logger = _myLoggerLazy.Value;

        const int MAX_LOG_DIR_SIZE_MB = 1280;
        long allLogFilesSizeBytes = allLogFilesInfoSorted.Sum(fi => fi.Length);
        int allLogFilesSizeMegabytes = (int)(allLogFilesSizeBytes / (1024 * 1024));
        int diskSpaceToFreeMb = allLogFilesSizeMegabytes - MAX_LOG_DIR_SIZE_MB;

        logger.Information($"CleanupLogFiles: diskSpaceToFreeMb is {diskSpaceToFreeMb}");

        if (diskSpaceToFreeMb > 0 && allLogFilesInfoSorted.Length < 5)
        {
            logger.Warning("Attempting to free space that is used by log files, but these log files might be in use. Please restart service.");

            return;
        }

        foreach (FileInfo fileInfo in allLogFilesInfoSorted)
        {
            if (diskSpaceToFreeMb <= 0)
                break;

            try
            {
                logger.Information($"CleanupLogFiles: Deleting file {fileInfo.FullName}");
                long fileSize = fileInfo.Length;
                fileInfo.Delete();
                diskSpaceToFreeMb -= (int)(fileSize / (1024 * 1024));
            }
            catch (Exception exc)
            {
                logger.Error(exc, "Exception during log files cleanup.");
            }
        }
    }
    
    private void DoCleanupLogs(IFileSystemService fileSystemService)
    {
        this.DoCleanupLogsDir(fileSystemService, "logs");
        this.DoCleanupLogsDir(fileSystemService, Path.Combine("logs", "web"));
    }
}