using Invictus.Emerald.Shared;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Misc;

namespace Invictus.Nomenklatura.Emerald;


[EmeraldTriggerDefinition("FixedDailyRepeat")]
public class FixedDailyRepeatTriggerDefinition : IEmTriggerDefinition
{
    public IEmTrigger CreateTrigger(IEmTriggerTarget triggerTarget, string arg)
    {
        string[] spl = arg.Split(";");
        
        if (spl.Length != 2)
            throw new EmeraldException($"Invalid trigger configuration {arg}");

        var timesOfDay = new List<TimeSpan>();
        TimeSpan start = IntStringUtil.ParseTimeOfDay(spl[0]);
        TimeSpan step = IntStringUtil.ParseTimeOfDay(spl[1]);
        TimeSpan last = start - step;

        do
        {
            last += step;
            timesOfDay.Add(last);
        } while (last + step < TimeSpan.FromDays(1));
        
        return new EmeraldFixedTimesOfDayTrigger(triggerTarget, timesOfDay.AsReadOnly());
    }
}

[EmeraldTriggerDefinition("FixedTimeOfDay")]
public class FixedDailyTriggerDefinition : IEmTriggerDefinition
{
    public IEmTrigger CreateTrigger(IEmTriggerTarget triggerTarget, string arg)
    {
        string[] spl = arg.Split(";");
        
        List<TimeSpan> timesOfDay = [..spl.Select(IntStringUtil.ParseTimeOfDay)];

        return new EmeraldFixedTimesOfDayTrigger(triggerTarget, timesOfDay.AsReadOnly());
    }
}

class EmeraldFixedTimesOfDayTrigger : EmeraldTriggerBase, IEmTrigger
{
    public RoutineFlags TriggerRoutineType => RoutineFlags.Timers;

    private readonly IReadOnlyList<TimeSpan> _timesOfDay;
    private FixedTimeEvent _fixedTimeEvent;
    private CancellationTokenSource _cancellationTokenSource;
    private int _triggerVersion;

    public EmeraldFixedTimesOfDayTrigger(IEmTriggerTarget triggerTarget, IReadOnlyList<TimeSpan> timesOfDay)
        : base(triggerTarget)
    {
        _timesOfDay = timesOfDay;
    }

    public bool TryRoutine()
    {
        if (_cancellationTokenSource != null)
            return false;

        _triggerVersion++;
        int triggerVersion = _triggerVersion;
        _cancellationTokenSource = new CancellationTokenSource();
        _fixedTimeEvent = new FixedTimeEvent(() => this.OnTimerFired(triggerVersion), TimeSpan.FromDays(1), _cancellationTokenSource.Token, "Emerald", _timesOfDay.ToArray());
        _fixedTimeEvent.Start();

        return true;
    }

    public bool TryRoutineStop()
    {
        if (_cancellationTokenSource == null)
            return false;

        _triggerVersion++;
        _cancellationTokenSource.Cancel();

        return true;
    }

    private Task OnTimerFired(int version)
    {
        if (version != _triggerVersion)
            return Task.CompletedTask;

        return this.Invoke();
    }
}

/*
class EmeraldResourceTrigger : EmeraldTriggerBase, IEmTrigger
{
    public RoutineFlags TriggerRoutineType => RoutineFlags.ResourceTriggers;

    private readonly IEmResource _resource;
    private readonly Segregation _segregation;
    private bool _handlerAdded;
    
    public EmeraldResourceTrigger(IEmTriggerTarget triggerTarget, OperationInvocationFlags flags, IEmResource resource, Segregation segregation)
        : base(triggerTarget, flags)
    {
        _resource = resource;
        _segregation = segregation;
    }

    public bool TryRoutine()
    {
        if (_handlerAdded)
            return false;

        _resource.OnUpdated += this.OnResourceUpdated;
        _handlerAdded = true;

        return true;
    }

    public bool TryRoutineStop()
    {
        if (!_handlerAdded)
            return false;

        _resource.OnUpdated -= this.OnResourceUpdated;
        _handlerAdded = false;

        return true;
    }

    private void OnResourceUpdated(Segregation segregation)
    {
        if (_segregation == Segregation.Unspecified)
            this.Invoke();
        else
        {
            if (segregation == Segregation.Unspecified)
                throw new EmeraldException("Trigger requires a segregation.");

            if (_segregation == segregation)
                this.Invoke();
        }
    }
}
*/
