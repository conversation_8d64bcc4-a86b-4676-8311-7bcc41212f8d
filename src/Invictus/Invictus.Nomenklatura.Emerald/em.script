// load Invictus.Nomenklatura.Emerald <-- infinite recursion

trigdef new FixedTimeOfDay
trigdef new FixedDailyRepeat

fea new LogManagement
op new LogManagement Test
op new LogManagement CleanupLogs

fea new FileSystem
res new FileSystem SharedAppDataFileSystem

// op run LogManagement Test Run&Cancel&Rerun
// op run LogManagement Test Run&Cancel&Rerun
// op run LogManagement Test Run&Cancel&Rerun

// trig new LogManagement Test FixedDailyRepeat 00:00;00:00:01

// op routine LogManagement Test 7