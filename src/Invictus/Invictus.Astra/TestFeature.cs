using Invictus.Emerald.Shared;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Workers;

namespace Invictus.Astra;

[EmeraldFeature("AstraTest")]
public interface ITestFeature : IWorker<ITestFeature>
{
    [EmeraldOperation]
    Task AutomaticOperation(CancellationToken cancellationToken);

    [EmeraldOperation]
    Task ManualOperation1(TestResource1 resource1);
}

[EmeraldResource("AstraTest::TestResource1")]
public class TestResource1
{
    public string Foo => "Bar1";
}

[EmeraldResource("AstraTest::TestResource2")]
public class TestResource2
{
    public string Foo => "Bar2";
}

[EmeraldResource("AstraTest::TestResource")]
public class TestResource
{
    public string Foo => "Bar";
}

public class TestFeature : IWorkerImpl<ITestFeature>, ITestFeature
{
    public WorkerCore Core { get; set; }
    public ILogger Log { get; } = InvLog.Logger<TestFeature>();
    public WorkerConfiguration WorkerConfiguration { get; } = new WorkerConfiguration("ASTEST", 
        new WorkerConfiguration.Thread("AstraTestFeatureThread", ThreadPriority.AboveNormal, IsBackground: false),
        LogEventLevel.Verbose,
        AllowDirectCall: false
    );
    public ITestFeature PublicInterface { get; set; }
    
    public async Task AutomaticOperation(CancellationToken cancellationToken)
    {
        Log.Information("This is an automatic operation begin");
        
        await Task.Delay(2000);
        
        await Task.Delay(2000);

        if (cancellationToken.IsCancellationRequested)
        {
            Log.Information("Automatic operation did not complete due to cancellation.");
            return;
        }
        
        Log.Information("This is an automatic operation end");
    }

    public Task ManualOperation1(TestResource1 resource1)
    {
        Log.Information("This is a manual operation 1");
        
        return Task.CompletedTask;
        // return Task.FromResult(Task.FromException(new Exception("Test")));
    }
    
    public Task ManualOperation2()
    {
        Log.Information("This is a manual operation 2");

        return Task.CompletedTask;
    }
}

[EmeraldOperation("AstraTest::ManualOperation2")]
public class TestInstanceOperation : IEmOperation
{
    private readonly CancellationToken _cancellationToken;

    public TestInstanceOperation(CancellationToken cancellationToken, TestResource2 resource2)
    {
        _cancellationToken = cancellationToken;
    }
    
    public void Execute()
    {
        InvLog.Logger<TestInstanceOperation>().Information("Hey!");
    }
}