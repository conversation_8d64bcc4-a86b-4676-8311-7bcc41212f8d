{"Nomenklatura": {}, "Jupiter": {"ServiceId": "Astra-Dev"}, "Customers": {"Admin": {"Messaging": {"ChatRefs": {"Telegram-Chat-DevChat": "<PERSON>", "Telegram-User-DeveloperUser": "DeveloperUser", "Telegram-Bot-InvDev": "InvDev"}}}, "IRIS": {"Messaging": {"ChatRefs": {"Telegram-Bot-Staff": "DevTestStaff", "Telegram-Chat-ReservationsChat": "DevTestReservations", "Telegram-Chat-ToPackAndSendOrdersChat": "DevTestStaffSend", "Telegram-Chat-ShopAdminAttentionChat": "<PERSON>", "Telegram-Chat-ReduceInStockChat": "<PERSON>", "Telegram-Chat-NewOrdersChat": "DevTestNewOrders", "Telegram-Chat-TTNSourceChannel": "DevTestNewOrders", "Telegram-Chat-DuplicateCustomerMessagesChat": "DevTestOther", "Telegram-Chat-GarbageChat": "Garbage"}}}}}