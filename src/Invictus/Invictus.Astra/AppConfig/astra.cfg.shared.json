{
  "AllowedHosts": "*",
  "Nomenklatura": {
    "Log": {
      "DefaultTemplate": "[{Level:u3} {MyThreadId}] {SourceClassShort:l} {{{CustomerNames}-{EmOp}}} {Message:lj}{NewLine}{Exception}{ExceptionDetail}",
      "TelegramTemplate": "[{Level:u3}] {SourceClassName:l} {{{CustomerNames}-{EmOp}}} {Message:lj}{NewLine}{Exception}{ExceptionDetail}"
    },
    "Database": {
      "MigrationsAssembly": "Invictus.Astra"
    },
    "ExtApi": {
      "Issuer": "https://antares.irisdrop.com.ua",
      "Audience": "https://antares.irisdrop.com.ua",
      "LifetimeMinutes": 60
    }
  },
  "CustomerRelations": {
    "Allowed": [
      { "Customer1": "IRIS", "Customer2": "Admin" }
    ]
  },
  "Kasta": {
    "ApiUrl": "https://hub.kasta.ua/api/"
  },
  "Serilog": {
    "Using:$merge:$astra": [
      "Invictus.Platform",
      "Invictus.Messaging.Telegram"
    ],
    "WriteTo:$merge:$astra": [
      {
        // Telegram chatbot reporting
        "Name": "TelegramChatBot",
        "Args": {
          "restrictedToMinimumLevel": "Warning",
          "formatter": {
            "type": "Serilog.Formatting.Display.MessageTemplateTextFormatter, Serilog",
            "outputTemplate": "${Nomenklatura:Log:TelegramTemplate}"
          }
        }
      }
    ],
    "Enrich:$merge:$astra": [
      "WithCustomer"  
    ]
  }
}