<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>disable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <Configurations>RemoteRelease;RemoteDebug;LocalDebug</Configurations>
        <Platforms>AnyCPU</Platforms>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'RemoteRelease' ">
      <Optimize Condition=" '$(Optimize)' == '' ">true</Optimize>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'LocalDebug' ">
      <DebugSymbols Condition=" '$(DebugSymbols)' == '' ">true</DebugSymbols>
      <Optimize Condition=" '$(Optimize)' == '' ">false</Optimize>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\Inventory\Inventory.Epicentr\Inventory.Epicentr.csproj" />
      <ProjectReference Include="..\..\Inventory\Inventory.Kasta\Inventory.Kasta.csproj" />
      <ProjectReference Include="..\..\Inventory\Inventory.Prom\Inventory.Prom.csproj" />
      <ProjectReference Include="..\..\Inventory\Inventory.Rozetka\Inventory.Rozetka.csproj" />
      <ProjectReference Include="..\..\Invictus\Invictus.AI\Invictus.AI.csproj" />
      <ProjectReference Include="..\..\Inventory\Invictus.Inventory\Invictus.Inventory.csproj" />
      <ProjectReference Include="..\..\Invictus\Invictus.Messaging.Telegram\Invictus.Messaging.Telegram.csproj" />
      <ProjectReference Include="..\..\Invictus\Invictus.Messaging\Invictus.Messaging.csproj" />
      <ProjectReference Include="..\..\Invictus\Invictus.Nomenklatura.Emerald\Invictus.Nomenklatura.Emerald.csproj" />
      <ProjectReference Include="..\..\Invictus\Invictus.Nomenklatura\Invictus.Nomenklatura.csproj" />
      <ProjectReference Include="..\..\Invictus\Invictus.Platform\Invictus.Platform.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="9.0.0" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.16">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
    </ItemGroup>

    <ItemGroup>
      <Content Update="AppConfig\*.json">
          <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <Compile Include="..\..\void\GlobalUsings.cs">
        <Link>GlobalUsings.cs</Link>
      </Compile>
    </ItemGroup>

    <ItemGroup>
      <Content Remove="Emerald.json" />
      <EmbeddedResource Include="Emerald.json" />
    </ItemGroup>

    <ItemGroup>
      <None Update="SqlMigrations\001_MigrationScript.sql">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
    </ItemGroup>

</Project>
