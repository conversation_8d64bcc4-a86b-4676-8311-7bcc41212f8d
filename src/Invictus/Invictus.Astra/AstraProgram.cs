using System.Diagnostics;

using Inventory.Kasta;

using Invictus.AI;
using Invictus.Emerald;
using Invictus.Inventory;
using Invictus.Jupiter.Shared;
using Invictus.Messaging;
using Invictus.Messaging.Telegram;
using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.App.Launchpad;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.AppConfig;
using Invictus.Nomenklatura.Database;
using Invictus.Nomenklatura.Emerald;
using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Logg;
using Invictus.Platform;

using Microsoft.AspNetCore.Hosting.Server;
using Microsoft.AspNetCore.Hosting.Server.Features;

namespace Invictus.Astra;

class AstraProgram
{
    private static readonly Stopwatch _Stopwatch = Stopwatch.StartNew();
    
    private static InvBaseApp _WebApp;
    private static Action<string> _SendTelegramMessage;
    
    public static void Main(string[] args)
    {
        var appBuilder = new InvictusBasicWebAppBuilder(args);

        appBuilder.AddAssembly(typeof(AstraExteriorService).Assembly);
        appBuilder.AddAssembly(typeof(EmeraldExteriorService).Assembly);
        appBuilder.AddAssembly(typeof(BackgroundMaintenanceTrigger).Assembly);
        
        appBuilder.AddAssembly(typeof(AIExteriorService).Assembly);
        
        appBuilder.AddAssembly(typeof(PlatformExteriorService).Assembly);
        appBuilder.AddAssembly(typeof(MessagingExteriorService).Assembly);
        appBuilder.AddAssembly(typeof(TelegramExteriorService).Assembly);
        
        appBuilder.AddAssembly(typeof(InventoryExteriorService).Assembly);
        appBuilder.AddAssembly(typeof(KastaExteriorService).Assembly);
        // appBuilder.AddAssembly(typeof(TelegramExteriorService).Assembly);
        // appBuilder.AddAssembly(typeof(TelegramExteriorService).Assembly);
        // appBuilder.AddAssembly(typeof(TelegramExteriorService).Assembly);
        
        appBuilder.AddAppConfigurationJsonFile("astra");
        appBuilder.AddAppConfigurationJsonFile("ai");
        appBuilder.CreateBuilder();
        
        appBuilder.AddMvc(true);
        
        appBuilder.Builder.Services.AddJupiter(
            services => services.GetRequiredService<InvAppConfig>().Jupiter,
            (services, cmd) => services.GetRequiredService<EmeraldConsole>().CommandUser(cmd)
        );
        
        appBuilder.Builder.Services.AddWorkerSingleton<TestFeature>();
        
        InvBaseApp app = _WebApp = appBuilder.Build();
        
        app.UseMain();
        app.UseMvc();
        app.UseAuth();

        app.MsWebApp.MapGroup("/identity").MapIdentityApi<InvIdentityUser>();
        app.MsWebApp.MapControllers();
        
        // app.MsWebApp.MapRazorPages();
        
        /*
         app.MapGet("/protected", () => "This is a protected endpoint")
            .RequireAuthorization();

        app.MapGet("/admin-only", () => "This is an admin-only endpoint")
            .RequireAuthorization("RequireAdminRole");
        */
        
        app.MsWebApp.UseJupiter();
        
        app.Startup += Run;
        app.Run();
    }
    
    private static void Run(IServiceProvider serviceProvider)
    {
        serviceProvider.GetRequiredService<IHostApplicationLifetime>().ApplicationStopping.Register(OnStopping);
        
        string serverName = GetServerName(serviceProvider);
        string message = $"Astra starting on {serverName}";
        
        var chatRefService = serviceProvider.GetRequiredService<ChatRefService>();
        var telegramSendService = serviceProvider.GetRequiredService<ITelegramSendWorker>();

        ChatRef botRef = chatRefService.FromRef(MessagingPlatform.Telegram,     "Admin", "Bot-InvDev");
        ChatRef devChatRef = chatRefService.FromRef(MessagingPlatform.Telegram, "Admin", "Chat-DevChat");
        
        _SendTelegramMessage = m => telegramSendService.Run(w => w.SendMessage(botRef, devChatRef, m, new PostToTelegramOptions { Silent = true }));
        _SendTelegramMessage(message);
        
        _Stopwatch.Stop();
        
        var sw = Stopwatch.StartNew();
        
        ILogger logger = InvLog.Logger<AstraProgram>();
        logger.Warning("Astra constructed in " + _Stopwatch.Elapsed);

        var console = serviceProvider.GetRequiredService<EmeraldConsole>();
        console.CommandProgram("load Invictus.Nomenklatura.Emerald");
        
        console.CommandProgram("load Invictus.Inventory");
        console.CommandProgram("load Inventory.Kasta");
        
        console.CommandProgram("load Invictus.AI");
        console.CommandProgram("load Invictus.Astra");
        
        console.JoinLoad();
        
        sw.Stop();
        logger.Warning("Astra Emerald constructed in " + sw.Elapsed);
        
        serviceProvider.GetRequiredService<NomenklaturaExteriorService>().SeedAuthData();
        
        console.CommandProgram("run_loaded");
        
        Task.Delay(TimeSpan.FromSeconds(1)).ContinueWith(_ => {
                var aiWorker = serviceProvider.GetRequiredService<IAIWorker>();

                // Task<string> res = aiWorker.TranslateUkrainianToRussian("Футболка посадки овер\nЗ накатом\nВеликомірить\nТканина-кулір\nГарної якості!");

                // res.Wait();
                
                // GC.KeepAlive(res);

                // var s = serviceProvider.GetRequiredService<EmeraldService>();

                // s.RunOperation("AstraTest::ManualOperation2", OperationInvocationFlags.All);
            }
        );
    }

    private static void OnStopping()
    {
        string serverName = GetServerName(_WebApp.ServiceProvider);
        string message = $"Astra stopping on {serverName}";
        _SendTelegramMessage?.Invoke(message);
    }

    private static string GetServerName(IServiceProvider services)
    {
        var appConfigBuilder = services.GetRequiredService<InvAppConfigBuilder>();

        switch (appConfigBuilder.EnvironmentEnum)
        {
            case InvEnvEnum.Dev: 
                return "Astra-Dev";
            case InvEnvEnum.ProdServ:
                string[] appUrls = GetApplicationUrls(services)
                    .Select(url => {
                            url = url.Replace("http://", "").Replace("https://", "");
                            int firstDot;
                            if ((firstDot = url.IndexOf(".")) != -1)
                                url = url.Substring(0, firstDot + 1);
                            return url;
                        }
                    )
                    .ToArray();

                return string.Join(";", appUrls);
            default: 
                throw TypeAbominationException.Enum(typeof(InvEnvEnum), appConfigBuilder.EnvironmentEnum);
        }
    }
    
    private static ICollection<string> GetApplicationUrls(IServiceProvider services)
    {
        var server = services.GetService<IServer>();

        var addresses = server?.Features.Get<IServerAddressesFeature>();

        return addresses?.Addresses ?? Array.Empty<string>();
    }
}