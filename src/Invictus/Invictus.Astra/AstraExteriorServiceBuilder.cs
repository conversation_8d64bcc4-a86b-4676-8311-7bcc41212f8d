using Invictus.Nomenklatura.ExteriorServ;

namespace Invictus.Astra;

public class AstraExteriorServiceBuilder : ExteriorServiceBuilderBase<AstraExteriorService>
{
    protected override void AddConcealedServices(ServiceCollection concealedServiceCollection)
    {
        this.RegisterDatabaseTypes(concealedServiceCollection);

        base.AddConcealedServices(concealedServiceCollection);
    }

    protected override void ExposeConcealedServices()
    {
        base.ExposeConcealedServices();
    }
}