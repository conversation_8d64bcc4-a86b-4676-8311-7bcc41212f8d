-- It's assumed you will select the 'sybreed_dev' database before running this script.
-- For example:
-- CREATE DATABASE IF NOT EXISTS sybreed_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE sybreed_dev;

-- Dropping tables if they exist (optional, for idempotent script)
DROP TABLE IF EXISTS `InvTra`;
DROP TABLE IF EXISTS `InvSpace`;
DROP TABLE IF EXISTS `InvPla`;
DROP TABLE IF EXISTS `InvLang`;
DROP TABLE IF EXISTS `InvCust`;
DROP TABLE IF EXISTS `AspNetUserTokens`;
DROP TABLE IF EXISTS `AspNetUserRoles`;
DROP TABLE IF EXISTS `AspNetUserLogins`;
DROP TABLE IF EXISTS `AspNetUserClaims`;
DROP TABLE IF EXISTS `AspNetRoleClaims`;
DROP TABLE IF EXISTS `AspNetUsers`;
DROP TABLE IF EXISTS `AspNetRoles`;


CREATE TABLE `AspNetRoles`(
                              `Id` VARCHAR(127) NOT NULL,
                              `Name` VARCHAR(256) NULL,
                              `NormalizedName` VARCHAR(256) NULL,
                              `ConcurrencyStamp` TEXT NULL,
                              CONSTRAINT `PK_AspNetRoles` PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `AspNetUsers`(
                              `Id` VARCHAR(127) NOT NULL,
                              `UserName` VARCHAR(256) NULL,
                              `NormalizedUserName` VARCHAR(256) NULL,
                              `Email` VARCHAR(256) NULL,
                              `NormalizedEmail` VARCHAR(256) NULL,
                              `EmailConfirmed` BOOLEAN NOT NULL,
                              `PasswordHash` TEXT NULL,
                              `SecurityStamp` TEXT NULL,
                              `ConcurrencyStamp` TEXT NULL,
                              `PhoneNumber` TEXT NULL,
                              `PhoneNumberConfirmed` BOOLEAN NOT NULL,
                              `TwoFactorEnabled` BOOLEAN NOT NULL,
                              `LockoutEnd` TIMESTAMP(6) NULL,
                              `LockoutEnabled` BOOLEAN NOT NULL,
                              `AccessFailedCount` INT NOT NULL,
                              CONSTRAINT `PK_AspNetUsers` PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `AspNetRoleClaims`(
                                   `Id` INT NOT NULL AUTO_INCREMENT,
                                   `RoleId` VARCHAR(127) NOT NULL,
                                   `ClaimType` TEXT NULL,
                                   `ClaimValue` TEXT NULL,
                                   CONSTRAINT `PK_AspNetRoleClaims` PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `AspNetUserClaims`(
                                   `Id` INT NOT NULL AUTO_INCREMENT,
                                   `UserId` VARCHAR(127) NOT NULL,
                                   `ClaimType` TEXT NULL,
                                   `ClaimValue` TEXT NULL,
                                   CONSTRAINT `PK_AspNetUserClaims` PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `AspNetUserLogins`(
                                   `LoginProvider` VARCHAR(127) NOT NULL,
                                   `ProviderKey` VARCHAR(127) NOT NULL,
                                   `ProviderDisplayName` TEXT NULL,
                                   `UserId` VARCHAR(127) NOT NULL,
                                   CONSTRAINT `PK_AspNetUserLogins` PRIMARY KEY (`LoginProvider`, `ProviderKey`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `AspNetUserRoles`(
                                  `UserId` VARCHAR(127) NOT NULL,
                                  `RoleId` VARCHAR(127) NOT NULL,
                                  CONSTRAINT `PK_AspNetUserRoles` PRIMARY KEY (`UserId`, `RoleId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `AspNetUserTokens`(
                                   `UserId` VARCHAR(127) NOT NULL,
                                   `LoginProvider` VARCHAR(127) NOT NULL,
                                   `Name` VARCHAR(127) NOT NULL,
                                   `Value` TEXT NULL,
                                   CONSTRAINT `PK_AspNetUserTokens` PRIMARY KEY (`UserId`, `LoginProvider`, `Name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `InvCust`(
                          `Id` INT NOT NULL AUTO_INCREMENT,
                          `Name` VARCHAR(255) NOT NULL,
                          CONSTRAINT `PK_InvCust` PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `InvLang`(
                          `Id` INT NOT NULL AUTO_INCREMENT,
                          `Iso6393` CHAR(3) NOT NULL,
                          CONSTRAINT `PK_InvLang` PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `InvPla`(
                         `Id` INT NOT NULL AUTO_INCREMENT,
                         `Name` VARCHAR(255) NOT NULL,
                         `LangKey` VARCHAR(80) NOT NULL,
                         CONSTRAINT `PK_InvPla` PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `InvSpace`(
                           `Id` INT NOT NULL AUTO_INCREMENT,
                           `CustId` INT NOT NULL,
                           `SpaceNumber` VARCHAR(50) NOT NULL,
                           `Name` VARCHAR(255) NOT NULL,
                           `LangKey` VARCHAR(80) NOT NULL,
                           CONSTRAINT `PK_InvSpace` PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `InvTra`(
                         `LangId` INT NOT NULL,
                         `Key` VARCHAR(80) NOT NULL,
                         `Value` TEXT NOT NULL,
                         CONSTRAINT `PK_InvTra` PRIMARY KEY (`LangId`, `Key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- INSERT Statements
-- The existing INSERT statements for AspNetRoles, AspNetUserRoles, AspNetUsers
-- use GUID-like strings for Ids, which are 36 characters long.
-- These will fit perfectly within VARCHAR(127).

INSERT INTO `AspNetRoles` (`Id`, `Name`, `NormalizedName`, `ConcurrencyStamp`) VALUES ('1a2e34db-5d0e-482c-9dad-47f99755bb35', 'ApiUser', 'APIUSER', NULL);
INSERT INTO `AspNetRoles` (`Id`, `Name`, `NormalizedName`, `ConcurrencyStamp`) VALUES ('6623cc38-2b20-4d01-817a-1235669469ad', 'Nobody', 'NOBODY', NULL);
INSERT INTO `AspNetRoles` (`Id`, `Name`, `NormalizedName`, `ConcurrencyStamp`) VALUES ('82eebf72-30fb-4ae4-ace3-f2563c0119e7', 'Admin', 'ADMIN', NULL);

INSERT INTO `AspNetUserRoles` (`UserId`, `RoleId`) VALUES ('81468bf2-1f3f-48f0-8a64-5847dec10087', '1a2e34db-5d0e-482c-9dad-47f99755bb35');
INSERT INTO `AspNetUserRoles` (`UserId`, `RoleId`) VALUES ('c62c951a-b65d-46df-bfb0-49c2a4b35452', '1a2e34db-5d0e-482c-9dad-47f99755bb35');
INSERT INTO `AspNetUserRoles` (`UserId`, `RoleId`) VALUES ('c62c951a-b65d-46df-bfb0-49c2a4b35452', '82eebf72-30fb-4ae4-ace3-f2563c0119e7');

INSERT INTO `AspNetUsers` (`Id`, `UserName`, `NormalizedUserName`, `Email`, `NormalizedEmail`, `EmailConfirmed`, `PasswordHash`, `SecurityStamp`, `ConcurrencyStamp`, `PhoneNumber`, `PhoneNumberConfirmed`, `TwoFactorEnabled`, `LockoutEnd`, `LockoutEnabled`, `AccessFailedCount`) VALUES ('0d235749-78e4-44bb-9d5a-8b344ef646df', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', 0, 'AQAAAAIAAYagAAAAEERwtenegUUsDuPjUjiCqIMWU6Fqyyjh40XZs38H0EjGeTtMcGLHtIzMnXI4yZbV5Q==', 'NAD3HXASJ52BA4Z662BIVMKBZ2ZZBT3E', '14dde5ad-dd7c-418b-8430-648f21023a6b', NULL, 0, 0, NULL, 1, 0);
INSERT INTO `AspNetUsers` (`Id`, `UserName`, `NormalizedUserName`, `Email`, `NormalizedEmail`, `EmailConfirmed`, `PasswordHash`, `SecurityStamp`, `ConcurrencyStamp`, `PhoneNumber`, `PhoneNumberConfirmed`, `TwoFactorEnabled`, `LockoutEnd`, `LockoutEnabled`, `AccessFailedCount`) VALUES ('241e1e45-9ece-4b17-ad51-cb4ab92f9eb9', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', 0, 'AQAAAAIAAYagAAAAEOhkCt+HjOAcAOQ+71X2JkDJi24/+d0tURbgpVz/HjQsmkDg1EcP7/gvLESY1TZF5Q==', 'NESTM4KPDMIJIFIHRBY7IKHOTNVQPCJ3', 'a6c0823e-f1a3-4ed0-9d37-db9b6394dbc7', NULL, 0, 0, NULL, 1, 0);
INSERT INTO `AspNetUsers` (`Id`, `UserName`, `NormalizedUserName`, `Email`, `NormalizedEmail`, `EmailConfirmed`, `PasswordHash`, `SecurityStamp`, `ConcurrencyStamp`, `PhoneNumber`, `PhoneNumberConfirmed`, `TwoFactorEnabled`, `LockoutEnd`, `LockoutEnabled`, `AccessFailedCount`) VALUES ('81468bf2-1f3f-48f0-8a64-5847dec10087', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', 1, 'AQAAAAIAAYagAAAAEIv1tcUyNaC0/mI+IXCYyRK0azETsnp4REXe8+AuPFIfe9uV2aW3ZbFcC2XIs3la+w==', 'PATG4X4XPTGFUNOBYPPJUJ2SAJKGVF55', 'a9d1e9d2-5123-4076-97b5-a4ed07bbc783', NULL, 0, 0, NULL, 1, 0);
INSERT INTO `AspNetUsers` (`Id`, `UserName`, `NormalizedUserName`, `Email`, `NormalizedEmail`, `EmailConfirmed`, `PasswordHash`, `SecurityStamp`, `ConcurrencyStamp`, `PhoneNumber`, `PhoneNumberConfirmed`, `TwoFactorEnabled`, `LockoutEnd`, `LockoutEnabled`, `AccessFailedCount`) VALUES ('c62c951a-b65d-46df-bfb0-49c2a4b35452', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', 1, 'AQAAAAIAAYagAAAAEL/umDB6SgNkWX8DYKMYSykxvk8qRM3qmUTe8PeUoL1xEytCqGoxdRIC5HJc3YNwWg==', 'QOJ5D6LT2PSQS74DANZL5DUUYNXVELZ4', '246ce6bf-f647-45fe-994b-48dee4b154a7', NULL, 0, 0, NULL, 1, 0);

INSERT INTO `InvCust` (`Id`, `Name`) VALUES (1, 'IRIS');
INSERT INTO `InvCust` (`Id`, `Name`) VALUES (2, 'Admin');

INSERT INTO `InvLang` (`Id`, `Iso6393`) VALUES (3, 'eng');
INSERT INTO `InvLang` (`Id`, `Iso6393`) VALUES (2, 'rus');
INSERT INTO `InvLang` (`Id`, `Iso6393`) VALUES (1, 'ukr');

INSERT INTO `InvPla` (`Id`, `Name`, `LangKey`) VALUES (1, 'VK', 'place.vk.name');
INSERT INTO `InvPla` (`Id`, `Name`, `LangKey`) VALUES (2, 'PromUA', 'place.promua.name');
INSERT INTO `InvPla` (`Id`, `Name`, `LangKey`) VALUES (3, 'KastaUA', 'place.kastaua.name');
INSERT INTO `InvPla` (`Id`, `Name`, `LangKey`) VALUES (4, 'EpicentrUA', 'place.epicentrua.name');
INSERT INTO `InvPla` (`Id`, `Name`, `LangKey`) VALUES (5, 'Rozetka', 'place.rozetka.name');

INSERT INTO `InvSpace` (`Id`, `CustId`, `SpaceNumber`, `Name`, `LangKey`) VALUES (1, 1, '1', 'Warehouse', 'space.warehouse.name');
INSERT INTO `InvSpace` (`Id`, `CustId`, `SpaceNumber`, `Name`, `LangKey`) VALUES (2, 1, '2', 'Drop', 'space.drop.name');
INSERT INTO `InvSpace` (`Id`, `CustId`, `SpaceNumber`, `Name`, `LangKey`) VALUES (3, 1, '3', 'DropTest', 'space.droptest.name');
INSERT INTO `InvSpace` (`Id`, `CustId`, `SpaceNumber`, `Name`, `LangKey`) VALUES (4, 1, '4', 'WarehouseTest', 'space.warehousetest.name');

INSERT INTO `InvTra` (`LangId`, `Key`, `Value`) VALUES (1, 'auto_com_2473566df82049c099988d5b19cc45e1', 'aab');
INSERT INTO `InvTra` (`LangId`, `Key`, `Value`) VALUES (1, 'auto_com_8cd8eae7da504e4186d39b31b1e614c3', 'aabc');
INSERT INTO `InvTra` (`LangId`, `Key`, `Value`) VALUES (1, 'customer.admin.name', 'Admin');
INSERT INTO `InvTra` (`LangId`, `Key`, `Value`) VALUES (1, 'place.epicentrua.name', 'EpicentrK');
INSERT INTO `InvTra` (`LangId`, `Key`, `Value`) VALUES (1, 'place.kastaua.name', 'Kasta.UA');
INSERT INTO `InvTra` (`LangId`, `Key`, `Value`) VALUES (1, 'place.promua.name', 'prom');
INSERT INTO `InvTra` (`LangId`, `Key`, `Value`) VALUES (1, 'place.rozetka.name', 'Rozetka');
INSERT INTO `InvTra` (`LangId`, `Key`, `Value`) VALUES (1, 'place.vk.name', 'vk.com');
INSERT INTO `InvTra` (`LangId`, `Key`, `Value`) VALUES (1, 'space.drop.name', 'Дроп');
INSERT INTO `InvTra` (`LangId`, `Key`, `Value`) VALUES (1, 'space.droptest.name', 'DropTest');
INSERT INTO `InvTra` (`LangId`, `Key`, `Value`) VALUES (1, 'space.warehouse.name', 'Склад');
INSERT INTO `InvTra` (`LangId`, `Key`, `Value`) VALUES (1, 'space.warehousetest.name', 'WarehouseTest');

-- FOREIGN KEY Constraints
ALTER TABLE `AspNetRoleClaims` ADD CONSTRAINT `FK_AspNetRoleClaims_AspNetRoles_RoleId` FOREIGN KEY(`RoleId`)
    REFERENCES `AspNetRoles` (`Id`)
    ON DELETE CASCADE;

ALTER TABLE `AspNetUserClaims` ADD CONSTRAINT `FK_AspNetUserClaims_AspNetUsers_UserId` FOREIGN KEY(`UserId`)
    REFERENCES `AspNetUsers` (`Id`)
    ON DELETE CASCADE;

ALTER TABLE `AspNetUserLogins` ADD CONSTRAINT `FK_AspNetUserLogins_AspNetUsers_UserId` FOREIGN KEY(`UserId`)
    REFERENCES `AspNetUsers` (`Id`)
    ON DELETE CASCADE;

ALTER TABLE `AspNetUserRoles` ADD CONSTRAINT `FK_AspNetUserRoles_AspNetRoles_RoleId` FOREIGN KEY(`RoleId`)
    REFERENCES `AspNetRoles` (`Id`)
    ON DELETE CASCADE;

ALTER TABLE `AspNetUserRoles` ADD CONSTRAINT `FK_AspNetUserRoles_AspNetUsers_UserId` FOREIGN KEY(`UserId`)
    REFERENCES `AspNetUsers` (`Id`)
    ON DELETE CASCADE;

ALTER TABLE `AspNetUserTokens` ADD CONSTRAINT `FK_AspNetUserTokens_AspNetUsers_UserId` FOREIGN KEY(`UserId`)
    REFERENCES `AspNetUsers` (`Id`)
    ON DELETE CASCADE;

ALTER TABLE `InvSpace` ADD CONSTRAINT `FK_InvSpace_InvCust_CustId` FOREIGN KEY(`CustId`)
    REFERENCES `InvCust` (`Id`);

ALTER TABLE `InvTra` ADD CONSTRAINT `FK_InvTra_InvLang_LangId` FOREIGN KEY(`LangId`)
    REFERENCES `InvLang` (`Id`);

-- Add recommended indexes for AspNet Identity tables
CREATE INDEX `IX_AspNetRoleClaims_RoleId` ON `AspNetRoleClaims` (`RoleId`);
CREATE INDEX `IX_AspNetUserClaims_UserId` ON `AspNetUserClaims` (`UserId`);
CREATE INDEX `IX_AspNetUserLogins_UserId` ON `AspNetUserLogins` (`UserId`);
CREATE INDEX `IX_AspNetUserRoles_RoleId` ON `AspNetUserRoles` (`RoleId`);
CREATE INDEX `EmailIndex` ON `AspNetUsers` (`NormalizedEmail`);
CREATE UNIQUE INDEX `UserNameIndex` ON `AspNetUsers` (`NormalizedUserName`);

CREATE INDEX `IX_InvSpace_CustId` ON `InvSpace` (`CustId`);
-- IX_InvTra_LangId is implicitly created by the composite primary key (LangId, Key)
-- but if you query often only by LangId, an explicit index might be slightly more performant
-- However, for most cases the PK will cover it. If needed:
-- CREATE INDEX `IX_InvTra_LangId` ON `InvTra` (`LangId`);