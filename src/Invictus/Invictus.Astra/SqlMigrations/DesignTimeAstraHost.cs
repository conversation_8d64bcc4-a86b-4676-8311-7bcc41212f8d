using Invictus.Inventory;
using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Database;
using Invictus.Platform;

using JetBrains.Annotations;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace Invictus.Astra.SqlMigrations;

[UsedImplicitly]
public class AstraAppContextFactory : 
    IDesignTimeDbContextFactory<AuthDbContext>,
    IDesignTimeDbContextFactory<PlatformDbContext>,
    IDesignTimeDbContextFactory<InventoryDbContext>
{
    public static IServiceProvider DesignTimeServices { get; private set; }

    public AstraAppContextFactory()
    {
        if (DesignTimeNomenklaturaHost.Instance.IsRunning)
            return;
        
        var servicesTcs = new TaskCompletionSource<IServiceProvider>();

        DesignTimeNomenklaturaHost.Instance.RunAsynchronously(
            [typeof(AstraExteriorService).Assembly, typeof(PlatformExteriorService).Assembly, typeof(InventoryExteriorService).Assembly],
            ["astra"],
            s => servicesTcs.SetResult(s)
        );

        DesignTimeServices = servicesTcs.Task.Result;
    }

    AuthDbContext IDesignTimeDbContextFactory<AuthDbContext>.CreateDbContext(string[] args)
    {
        return (AuthDbContext)this.CreateDbContext<AuthDbAccess>();
    }
    
    PlatformDbContext IDesignTimeDbContextFactory<PlatformDbContext>.CreateDbContext(string[] args)
    {
        return (PlatformDbContext)this.CreateDbContext<PlatformDbAccess>();
    }
    
    InventoryDbContext IDesignTimeDbContextFactory<InventoryDbContext>.CreateDbContext(string[] args)
    {
        return (InventoryDbContext)this.CreateDbContext<InventoryDbAccess>();
    }

    private DbContext CreateDbContext<T>() 
        where T : DbAccessBase
    {
        var dbAccessFactory = DesignTimeServices.GetRequiredService<IDbAccessFactory<T>>();

        T dbAccess = dbAccessFactory.CreateAccessForExternalCode();
        
        return ((IDbAccessPrivate)dbAccess).GetDbContext();
    }
}