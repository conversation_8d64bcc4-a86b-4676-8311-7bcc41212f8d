using Invictus.InvApiClient.Models;

using Refit;

namespace Invictus.InvApiClient.Services
{
    public interface IApiClient
    {
        [Get("/api/data")]
        Task<Refit.ApiResponse<List<Dictionary<string, object>>>> GetDataAsync(
            [Query("key")] string apiKey,
            [Query] string query = null
        );
        
        [Post("/api/query")]
        Task<Refit.ApiResponse<Dictionary<string, object>>> QueryAsync(
            [Query("key")] string apiKey,
            [Body] ApiRequest request
        );
        
        [Get("/api/status")]
        Task<Refit.ApiResponse<Dictionary<string, object>>> GetStatusAsync(
            [Query("key")] string apiKey
        );
    }
}
