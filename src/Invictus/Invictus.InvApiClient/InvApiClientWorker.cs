using Invictus.InvApiClient.Api;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Web;
using Invictus.Nomenklatura.Workers;
using Invictus.Platform.Api;

namespace Invictus.InvApiClient;

public interface IInvApiClientWorker : IWorker<IInvApiClientWorker>
{
    Task<string> GetOrAddTraKey(int langId, string text, string traKey);
    Task<string> GetTra(int langId, string traKey);

    Task<string> TranslateUkrainianToRussianWithAI(string text);
}

public class InvApiClientWorker : IWorkerImpl<IInvApiClientWorker>, IInvApiClientWorker
{
    public WorkerCore Core { get; set; }
    public ILogger Log { get; } = InvLog.Logger<InvApiClientWorker>();

    public WorkerConfiguration WorkerConfiguration { get; } = new WorkerConfiguration(
        "INVAPI",
        new WorkerConfiguration.TaskScheduler("ApiClientProcessing", 1),
        LogEventLevel.Information,
        AllowDirectCall: true
    );

    IInvApiClientWorker IWorkerImpl<IInvApiClientWorker>.PublicInterface { get; set; }

    private readonly ITranslationRefit _translationRefit;
    private readonly IAIRefit _aiRefit;

    public InvApiClientWorker(TranslationRefitFactory translationRefitFactory, AIRefitFactory aiRefitFactory)
    {
        _translationRefit = translationRefitFactory.Create();
        _aiRefit = aiRefitFactory.Create();
    }

    public async Task<string> GetOrAddTraKey(int langId, string text, string traKey)
    {
        WebResponse<string, string> r = await _translationRefit.GetOrAddTraKey(langId, text, traKey);

        if (r.IsFail)
            throw new Exception(r.Fail);

        return r.Success;
    }

    public async Task<string> GetTra(int langId, string traKey)
    {
        WebResponse<string, string> r = await _translationRefit.GetTra(langId, traKey);

        if (r.IsFail)
            throw new Exception(r.Fail);

        return r.Success;
    }

    public async Task<string> TranslateUkrainianToRussianWithAI(string text)
    {
        WebResponse<string, string> r = await _aiRefit.TranslateUkrainianToRussian(text);

        if (r.IsFail)
            return null;
            // TODO: sometimes it is a server failure, sometimes just no free AI tokens
            // throw new Exception(r.Fail); 

        return r.Success;
    }
}
