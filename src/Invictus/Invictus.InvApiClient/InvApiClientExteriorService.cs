using Invictus.Nomenklatura.ExteriorServ;
using Invictus.Nomenklatura.Logg;

using JetBrains.Annotations;

namespace Invictus.InvApiClient;

[UsedImplicitly]
public class InvApiClientExteriorService : IExteriorService
{
    private readonly ILogger _logger = InvLog.Logger<InvApiClientExteriorService>();
    private readonly IInvApiClientWorker _apiWorker;
    
    public InvApiClientExteriorService(IInvApiClientWorker apiWorker)
    {
        _apiWorker = apiWorker;
        _logger.Information("InvApiClient Exterior Service initialized");
    }

    public Task Run()
    {
        _logger.Information("InvApiClient Exterior Service running");
        return Task.CompletedTask;
    }
}
