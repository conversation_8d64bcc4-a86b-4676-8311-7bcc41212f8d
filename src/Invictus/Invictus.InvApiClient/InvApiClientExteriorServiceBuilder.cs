using Invictus.InvApiClient.Api;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.ExteriorServ;

using JetBrains.Annotations;

using Microsoft.Extensions.DependencyInjection;

namespace Invictus.InvApiClient;

[UsedImplicitly]
public class InvApiClientExteriorServiceBuilder : ExteriorServiceBuilderBase<InvApiClientExteriorService>
{
    protected override void AddConcealedServices(ServiceCollection concealedServiceCollection)
    {
        // Register the API worker
        concealedServiceCollection.AddWorkerSingleton<InvApiClientWorker>();
        concealedServiceCollection.AddSingleton<AIRefitFactory>();
        
        // Register database types if needed
        this.RegisterDatabaseTypes(concealedServiceCollection);

        base.AddConcealedServices(concealedServiceCollection);
    }

    protected override void ExposeConcealedServices()
    {
        // Expose the API worker interface
        this.ExposeSingleton<IInvApiClientWorker>();
        this.ExposeSingleton<AIRefitFactory>();
        
        base.ExposeConcealedServices();
    }
}
