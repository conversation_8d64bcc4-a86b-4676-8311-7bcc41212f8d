using System.Text.Json.Serialization;

namespace Invictus.InvApiClient.Models
{
    public class ApiRequest
    {
        [JsonPropertyName("query")]
        public string Query { get; set; }
        
        [JsonPropertyName("parameters")]
        public Dictionary<string, string> Parameters { get; set; } = new Dictionary<string, string>();
        
        [JsonPropertyName("options")]
        public RequestOptions Options { get; set; } = new RequestOptions();
    }
    
    public class RequestOptions
    {
        [JsonPropertyName("timeout")]
        public int TimeoutSeconds { get; set; } = 30;
        
        [JsonPropertyName("cache")]
        public bool UseCache { get; set; } = true;
    }
}
