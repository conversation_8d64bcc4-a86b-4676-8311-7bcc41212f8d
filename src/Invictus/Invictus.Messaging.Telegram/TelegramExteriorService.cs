using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.ExteriorServ;

using JetBrains.Annotations;

namespace Invictus.Messaging.Telegram;

[UsedImplicitly]
public class TelegramExteriorService : IExteriorService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ITelegramSendWorker _telegramSendWorker;

    public TelegramExteriorService(IServiceProvider serviceProvider, ITelegramSendWorker telegramSendWorker, InvGlobalExceptionHandling globalExceptionHandling)
    {
        _serviceProvider = serviceProvider;
        _telegramSendWorker = telegramSendWorker;

        globalExceptionHandling.AddPreHaltAction(this.PreHaltAction);
    }

    public Task Run()
    {
        WriteLogToTelegramBotOrStatisticsLogEventSink.SetServiceProvider(_serviceProvider);

        return Task.CompletedTask;
    }
    
    private void PreHaltAction(Exception obj)
    {
        if (!WriteLogToTelegramBotOrStatisticsLogEventSink.WasCreatedAndIsProbablyActive)
            return;
        
        // Wait for logger to queue error to telegram chatbot.
        SpinWait.SpinUntil(() => !_telegramSendWorker.Core.HasWorkItemsInQueue, millisecondsTimeout: 750);
    }
}