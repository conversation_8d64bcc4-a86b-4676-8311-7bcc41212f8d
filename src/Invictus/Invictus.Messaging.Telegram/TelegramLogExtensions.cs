using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.AppConfig;

using Microsoft.Extensions.DependencyInjection;

using Serilog.Configuration;
using Serilog.Core;
using Serilog.Formatting;

namespace Invictus.Messaging.Telegram;

public static class TelegramLogExtensions
{
    public static LoggerConfiguration TelegramChatBot(
        this LoggerSinkConfiguration sinkConfiguration,
        ITextFormatter formatter,
        LogEventLevel restrictedToMinimumLevel = LogEventLevel.Verbose,
        LoggingLevelSwitch levelSwitch = null
    )
    {
        if (sinkConfiguration == null)
        {
            throw new ArgumentNullException(nameof(sinkConfiguration));
        }

        if (formatter == null)
        {
            throw new ArgumentNullException(nameof(formatter));
        }

        return sinkConfiguration.Sink(new WriteLogToTelegramBotOrStatisticsLogEventSink(formatter), restrictedToMinimumLevel, levelSwitch);
    }
}

public class WriteLogToTelegramBotOrStatisticsLogEventSink : ILogEventSink
{
    public static bool WasCreatedAndIsProbablyActive { get; private set; }

    private static IServiceProvider _ServiceProvider;
    private static ITelegramSendWorker _TelegramSendWorker;

    // TODO: find a better way
    public static void SetServiceProvider(IServiceProvider serviceProvider)
    {
        _ServiceProvider = serviceProvider;
    }

    private readonly ITextFormatter _textFormatter;
    private ChatRef _chatFrom;
    private ChatRef _chatTo;
    private InvEnvEnum _invEnvEnum;
    
    public WriteLogToTelegramBotOrStatisticsLogEventSink(ITextFormatter formatter)
    {
        _textFormatter = formatter;

        WasCreatedAndIsProbablyActive = true;
    }

    public void Emit(LogEvent logEvent)
    {
        if (_ServiceProvider == null)
            return;
        
        if (logEvent.Level < LogEventLevel.Warning)
            return;

        if (_TelegramSendWorker == null)
        {
            _TelegramSendWorker = _ServiceProvider.GetService<ITelegramSendWorker>();
            
            if (_TelegramSendWorker == null)
                return; // What else to do.
            
            var chatRefService = _ServiceProvider.GetService<ChatRefService>();
        
            _chatFrom = chatRefService.FromRef(MessagingPlatform.Telegram, "Admin", "Bot-InvDev");
            _chatTo = chatRefService.FromRef(MessagingPlatform.Telegram,   "Admin", "Chat-DevChat");

            _invEnvEnum = _ServiceProvider.GetService<InvEnv>().EnvEnum;
            
            _ServiceProvider = null;
        }
        
        var ms = new MemoryStream();
        var textWriter = new StreamWriter(ms);
        _textFormatter.Format(logEvent, textWriter);
        textWriter.Flush();
        ms.Position = 0;
        var textReader = new StreamReader(ms);
        string logText = textReader.ReadToEnd();

        string addPrefix = "";
        switch (_invEnvEnum)
        {
            case InvEnvEnum.IntegrationTestLocal: 
                addPrefix = "🔬";
                break;
            case InvEnvEnum.Dev: 
                addPrefix = "🧪";
                break;
            case InvEnvEnum.ProdServ: 
                addPrefix = "";
                break;
            default:
                addPrefix = "⁉️ ";
                break;
        }
        string msg = addPrefix + logText;

        switch (logEvent.Level)
        {
            case LogEventLevel.Warning:
                _TelegramSendWorker.Run(w => w.SendMessage(_chatFrom, _chatTo, msg, new PostToTelegramOptions { Silent = true }));
                break;
            case LogEventLevel.Error:
            case LogEventLevel.Fatal:
                bool fatal = logEvent.Level == LogEventLevel.Fatal;
                _TelegramSendWorker.Run(w => w.SendMessage(_chatFrom, _chatTo, msg, new PostToTelegramOptions { Silent = !fatal }));
                break;
        }
    }
}