using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.ExteriorServ;

using JetBrains.Annotations;

using Microsoft.Extensions.DependencyInjection;

using Telegram.Bot;

namespace Invictus.Messaging.Telegram;

[UsedImplicitly]
public class TelegramExteriorServiceBuilder : ExteriorServiceBuilderBase<TelegramExteriorService>
{
    protected override void AddConcealedServices(ServiceCollection concealedServiceCollection)
    {
        concealedServiceCollection.AddWorkerSingleton<TelegramChatBotSendWorker>();
        concealedServiceCollection.AddFactory<ITelegramBotClient, TelegramBotClient>(preferredConstructor: new[] { typeof(string), typeof(HttpClient), typeof(CancellationToken) });
        
        base.AddConcealedServices(concealedServiceCollection);
    }

    protected override void ExposeConcealedServices()
    {
        this.ExposeSingleton<ITelegramSendWorker>();
        
        base.ExposeConcealedServices();
    }
}