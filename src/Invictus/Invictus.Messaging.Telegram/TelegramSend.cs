using System.Web;

using Castle.DynamicProxy;

using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;
using Invictus.Nomenklatura.Web;
using Invictus.Nomenklatura.Workers;

using OneOf;

using Telegram.Bot;
using Telegram.Bot.Exceptions;
using Telegram.Bot.Types;
using Telegram.Bot.Types.Enums;
using Telegram.Bot.Types.ReplyMarkups;

namespace Invictus.Messaging.Telegram;

public interface ITelegramSendWorker : IWorker<ITelegramSendWorker>
{
    Task SendChatAction(ChatRef botRef, ChatRef chatRef, ChatAction chatAction);
    Task<Message[]> SendMessage(ChatRef botRef, ChatRef chatRef, [DoNotLog] OneOf<string, Sticker> text, PostToTelegramOptions options = null);
    Task<Message> ForwardMessage(ChatRef botRef, ChatRef from, ChatRef to, int messageId);
    Task<Message> SendAttach(ChatRef botRef, ChatRef chatRef, [DoNotLog] byte[] contents, string fileName, string caption);
}

public interface ITelegramSendWorkerPrivate : IWorker<ITelegramSendWorkerPrivate>
{
    Task<Message> PostToTelegramSingleMessage(ChatRef botRef, ChatRef chatRef, [DoNotLog] OneOf<string, Sticker> text, PostToTelegramOptions options);
    
    Task SendKeyboard(ChatRef botRef, ChatRef chatRef, [DoNotLog] string text, ReplyKeyboardMarkup keyboard);
}

public class TelegramChatBotSendWorker :
    IWorkerImpl<ITelegramSendWorker>, ITelegramSendWorker,
    IWorkerImpl<ITelegramSendWorkerPrivate>, ITelegramSendWorkerPrivate
{
    public WorkerConfiguration WorkerConfiguration { get; } = new(
        "TGSEND",
        new WorkerConfiguration.TaskScheduler("TelegramChatBotSend", 1),
        LogEventLevel.Verbose,
        AllowDirectCall: false,
        GetCustomInterceptor: w => new Interceptor((TelegramChatBotSendWorker)w),
        ThrottlingConfiguration: new ThrottlingConfiguration
        {
            ParametersByType = new Dictionary<string, Type>
            {
                {
                    "botRef", typeof(ChatRef)
                },
                {
                    "chatRef", typeof(ChatRef)
                }
            },
            GetTimeToWait = GetTimeToWait,
            ExceptMethods = new List<string>
            {
                nameof(ITelegramSendWorkerPrivate.SendKeyboard),
                nameof(ITelegramSendWorker.SendChatAction),
                nameof(ITelegramSendWorker.SendMessage),
            }
        }
    );

    private static TimeSpan GetTimeToWait(Dictionary<string, object> vars)
    {
        return TelegramThrottling.NonStrict.GetWaitTime(
            (ChatRef)vars["botRef"],
            (ChatRef)vars["chatRef"]
        );
    }
    
    private const int TELEGRAM_MAX_MESSAGE_LENGTH = 3750;
    
    ITelegramSendWorker IWorkerImpl<ITelegramSendWorker>.PublicInterface { get; set; }
    ITelegramSendWorkerPrivate IWorkerImpl<ITelegramSendWorkerPrivate>.PublicInterface { get; set; }
    
    private ITelegramSendWorkerPrivate SelfPublic => ((IWorkerImpl<ITelegramSendWorkerPrivate>)this).PublicInterface;

    public ILogger Log { get; } = InvLog.Logger<TelegramChatBotSendWorker>();

    public WorkerCore Core { get; set; }
    
    private readonly IServiceFactory<ITelegramBotClient> _botClientsFactory;

    private readonly Dictionary<ChatRef, ITelegramBotClient> _telegramChatBotClients = new();

    public TelegramChatBotSendWorker(IServiceFactory<ITelegramBotClient> botClientsFactory)
    {
        _botClientsFactory = botClientsFactory;
    }

#region Public

    public Task SendChatAction(ChatRef botRef, ChatRef chatRef, ChatAction chatAction)
    {
        ITelegramBotClient botClient = this.GetBotClient(botRef);

        return botClient.SendChatAction(chatRef.ChatId, chatAction);
    }

    public async Task<Message[]> SendMessage(ChatRef botRef, ChatRef chatRef, [DoNotLog] OneOf<string, Sticker> text, PostToTelegramOptions options = null)
    {
        var messages = new List<Task<Message>>();

        if (text.IsT1)
        {
            messages.Add(SelfPublic.Run(w => w.PostToTelegramSingleMessage(botRef, chatRef, text, options)));
        } else
        {
            List<string> lines = IntStringUtil.MultilineStrings(text.AsT0.Split('\n'), TELEGRAM_MAX_MESSAGE_LENGTH);

            foreach (string strChunk in lines)
            {
                PostToTelegramOptions privateOptionsCl = options with { };

                messages.Add(SelfPublic.Run(w => w.PostToTelegramSingleMessage(botRef, chatRef, strChunk, privateOptionsCl)));

                options.RemoveKeyboard = false;
            }

            Log.Verbose($"Telegram bot message sent, text={(text.AsT0.Length > 92 ? text.AsT0.Substring(0, 92) + "..." : text.AsT0)}");
        }

        await Task.WhenAll(messages.ToArray());

        return messages.Select(m => m.Result).ToArray();
    }

    public Task<Message> ForwardMessage(ChatRef botRef, ChatRef from, ChatRef to, int messageId)
    {
        TelegramThrottling.NonStrict.RegisterActionIrregardless(botRef, to);

        ITelegramBotClient botClient = this.GetBotClient(botRef);

        try
        {
            return botClient.ForwardMessage(to.ChatId, from.ChatId, messageId);
        }
        catch (ApiRequestException exc)
        {
            if (exc.Message.Contains("message to forward not found"))
                return Task.FromResult((Message)null);

            throw;
        }
    }

    public Task<Message> SendAttach(ChatRef botRef, ChatRef chatRef, [DoNotLog] byte[] contents, string fileName, string caption)
    {
        TelegramThrottling.NonStrict.RegisterActionIrregardless(botRef, chatRef);

        ITelegramBotClient botClient = this.GetBotClient(botRef);

        return botClient.SendDocument(
            chatRef.ChatId,
            InputFile.FromStream(new MemoryStream(contents), fileName),
            // chatId.MessageThreadId,
            caption: caption,
            disableContentTypeDetection: false
        );
    }

#endregion
        
#region Private

    private ITelegramBotClient GetBotClient(ChatRef chatRef)
    {
        if (_telegramChatBotClients.TryGetValue(chatRef, out ITelegramBotClient botClient))
            return botClient;
        
        _telegramChatBotClients[chatRef] = _botClientsFactory.Create(chatRef.ChatToken, CancellationToken.None);
        return _telegramChatBotClients[chatRef];
    }
    
    public Task SendKeyboard(ChatRef botRef, ChatRef chatRef, [DoNotLog] string text, ReplyKeyboardMarkup keyboard)
    {
        TelegramThrottling.NonStrict.RegisterActionIrregardless(botRef, chatRef);

        ITelegramBotClient botClient = this.GetBotClient(botRef);

        return botClient.SendMessage(
            chatRef.ChatId,
            text,
            // messageThreadId: chatId.MessageThreadId,
            replyMarkup: keyboard
        );
    }
    
    public async Task<Message> PostToTelegramSingleMessage(ChatRef botRef, ChatRef chatRef, [DoNotLog] OneOf<string, Sticker> text, PostToTelegramOptions options)
    {
        TelegramThrottling.NonStrict.RegisterActionIrregardless(botRef, chatRef);

        ITelegramBotClient botClient = this.GetBotClient(botRef);

        var cts = new CancellationTokenSource();

        Message message = null;
        
        if (text.IsT0)
        {
            string encodedText = options.HtmlFormattingEnabled
                ? text.AsT0
                : HttpUtility.HtmlEncode(text.AsT0);

            try
            {
                message = botClient.SendMessage(
                    chatId: chatRef.ChatId,
                    // messageThreadId: chatId.MessageThreadId,
                    replyMarkup: (options.RemoveKeyboard != null && options.RemoveKeyboard.Value) ? new ReplyKeyboardRemove() : null,
                    replyParameters: options.ReplyToMessageId.HasValue 
                        ? new ReplyParameters { MessageId = options.ReplyToMessageId.Value } 
                        : null,
                    linkPreviewOptions: new LinkPreviewOptions
                    { 
                        IsDisabled  = options.DisableWebPagePreview
                    },
                    text: encodedText,
                    parseMode: ParseMode.Html,
                    disableNotification: options.Silent,
                    cancellationToken: cts.Token
                ).GetAwaiter().GetResult();
            }
            catch (ApiRequestException exc)
            {
                if (exc.Message.Contains("can't parse entities") && options.HtmlFormattingEnabled)
                {
                    options.HtmlFormattingEnabled = false;

                    encodedText = options.HtmlFormattingEnabled
                        ? text.AsT0
                        : HttpUtility.HtmlEncode(text.AsT0);
                    
                    message = await botClient.SendMessage(
                        chatId: chatRef.ChatId,
                        // messageThreadId: chatId.MessageThreadId,
                        replyMarkup: (options.RemoveKeyboard != null && options.RemoveKeyboard.Value) ? new ReplyKeyboardRemove() : null,
                        replyParameters: options.ReplyToMessageId.HasValue 
                            ? new ReplyParameters { MessageId = options.ReplyToMessageId.Value } 
                            : null,
                        linkPreviewOptions: new LinkPreviewOptions
                        { 
                            IsDisabled  = options.DisableWebPagePreview
                        },
                        text: encodedText,
                        parseMode: ParseMode.Html,
                        disableNotification: options.Silent,
                        cancellationToken: cts.Token
                    );
                }
            }
        } else
        {
            message = await botClient.SendSticker(
                chatId: chatRef.ChatId,
                // messageThreadId: chatId.MessageThreadId,
                replyMarkup: (options.RemoveKeyboard != null && options.RemoveKeyboard.Value) ? new ReplyKeyboardRemove() : null,
                replyParameters: options.ReplyToMessageId.HasValue 
                    ? new ReplyParameters { MessageId = options.ReplyToMessageId.Value } 
                    : null,
                sticker: new InputFileId(text.AsT1.FileUniqueId),
                disableNotification: options.Silent,
                cancellationToken: cts.Token
            );
        }
        
        // if (message != null)
        //    _telegramDbHandler.SaveOutgoingUpdate(message);

        return message;
    }
    
#endregion
    
    private class Interceptor : WebRequestWithRetryWorkerInterceptor<TelegramChatBotSendWorker>
    {
        public Interceptor(TelegramChatBotSendWorker worker)
            : base(worker)
        {
        }
        
        protected override Task<WebRequestWithRetryResult> InterceptRetry(IInvocation invocation)
        {
            try
            {
                invocation.Proceed();

                return Task.FromResult(WebRequestWithRetryResult.Success(null));
            }
            catch (ApiRequestException exc)
            {
                if (exc.Message.Contains("Forbidden: bot was blocked by the user"))
                    return Task.FromResult(WebRequestWithRetryResult.Success(null));
                        
                bool retry = exc.Message.Contains("Bad Gateway");

                if (retry)
                {
                    _worker.Log.Verbose(exc, "PostToTelegram ApiRequestException exception.");

                    return Task.FromResult(WebRequestWithRetryResult.MightRetry(exc));
                }

                _worker.Log.Error(exc, "PostToTelegram ApiRequestException exception.");

                bool waitLong = exc.Message.Contains("Too Many Requests: retry");

                if (waitLong)
                    return Task.FromResult(WebRequestWithRetryResult.FromExceptionAdvice(exc, RetryAdvice.WaitALot));

                return Task.FromResult(WebRequestWithRetryResult.FromExceptionAdvice(exc, RetryAdvice.ThrowFurther));
            }
        }
    }
}