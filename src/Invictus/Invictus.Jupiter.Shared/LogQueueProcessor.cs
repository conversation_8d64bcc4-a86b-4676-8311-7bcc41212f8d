using System.Collections.Concurrent;

using Microsoft.Extensions.Hosting;

using Serilog;

namespace Invictus.Jupiter.Shared;

public class LogQueueProcessor
{
    private readonly BlockingCollection<LogMessage> _logQueue;
    private readonly TerminalClient _terminalClient;
    private readonly ILogger _logger;
    private readonly CancellationToken _cancellationToken;

    public LogQueueProcessor(TerminalClient terminalClient, IHostApplicationLifetime hostApplicationLifetime, int maxQueueSize = 800)
    {
        _terminalClient = terminalClient;
        _logger = Log.ForContext<LogQueueProcessor>();
        _logQueue = new BlockingCollection<LogMessage>(maxQueueSize);
        _cancellationToken = hostApplicationLifetime.ApplicationStopping;

        Task.Run(this.ProcessQueueLoop);
    }

    public void EnqueueLogMessage(LogMessage message)
    {
        try
        {
            if (!_logQueue.TryAdd(message))
            {
                LogMessage oldestMessage;

                if (_logQueue.TryTake(out oldestMessage))
                {
                    _logger.Debug("Log queue full. Dropping oldest message: {Message}", oldestMessage.FormattedMessage);

                    _logQueue.TryAdd(message);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to enqueue log message");
        }
    }

    private async Task ProcessQueueLoop()
    {
        while (!_cancellationToken.IsCancellationRequested)
        {
            try
            {
                LogMessage message = _logQueue.Take(_cancellationToken);
                
                await this.ProcessLogMessageWithRetry(message);
            }
            catch (OperationCanceledException) when (_cancellationToken.IsCancellationRequested)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error processing log message");

                try
                {
                    await Task.Delay(1000, _cancellationToken); // Brief pause before retrying
                }
                catch (TaskCanceledException)
                {
                    break;
                }
            }
        }

        // Process remaining messages on shutdown
        while (_logQueue.TryTake(out LogMessage message))
        {
            try
            {
                this.ProcessLogMessageWithRetry(message).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error processing remaining log message during shutdown");
            }
        }
    }

    private async Task ProcessLogMessageWithRetry(LogMessage message)
    {
        const int MAX_RETRIES = 3;
        const int INITIAL_DELAY_MS = 1000;

        for (int attempt = 0; attempt < MAX_RETRIES; attempt++)
        {
            try
            {
                await _terminalClient.SendLogEntry(message.FormattedMessage);

                return;
            }
            catch (Exception ex) when (attempt < MAX_RETRIES - 1)
            {
                _logger.Debug(ex, "Retry attempt {Attempt} failed for log message", attempt + 1);
                
                await Task.Delay(INITIAL_DELAY_MS * (attempt + 1), _cancellationToken);
            }
        }
    }
}