using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.Hosting;

using Serilog;

namespace Invictus.Jupiter.Shared;

public class TerminalClient
{
    private readonly Action<string> _executeCommand;
    private readonly ILogger _logger;
    
    private readonly string _serviceId;
    private readonly string _hubUrl;
    private readonly CancellationToken _cancellationToken;
    private HubConnection _connection;

    public TerminalClient(JupiterConfiguration configuration, Action<string> executeCommand, IHostApplicationLifetime hostApplicationLifetime)
    {
        _executeCommand = executeCommand;
        _logger = Log.ForContext<TerminalClient>();
        _serviceId = configuration.ServiceId;
        _hubUrl = configuration.TerminalHubUrl;
        _cancellationToken = hostApplicationLifetime.ApplicationStopping;
        
        this.StartMaintainingConnection();
    }

    private void StartMaintainingConnection()
    {
        Task.Run(this.AttemptAsync, _cancellationToken);
    }

    private async Task AttemptAsync()
    {
        _connection = new HubConnectionBuilder()
            .WithUrl(_hubUrl, options =>
            {
                options.AccessTokenProvider = () => Task.FromResult("SLDFKLSKDOFKOWPEKFSDLFSDFSvbVCFScD");
                options.Transports = HttpTransportType.WebSockets;
            })
            .WithAutomaticReconnect(new RetryPolicy())
            .Build();

        _connection.On<string, string>("ReceiveCommand", this.HandleCommand);
        
        while (!_cancellationToken.IsCancellationRequested)
        {
            try
            {
                if (_connection.State != HubConnectionState.Connected && _connection.State != HubConnectionState.Connecting && _connection.State != HubConnectionState.Reconnecting)
                    await _connection.StartAsync(_cancellationToken);
                
                await _connection.InvokeAsync("RegisterService", _serviceId, cancellationToken: _cancellationToken);
                
                _logger.Information("Connected to Terminal Hub");

                while (_connection.State == HubConnectionState.Connected || _connection.State == HubConnectionState.Connecting || _connection.State == HubConnectionState.Reconnecting)
                {
                    await Task.Delay(1000, _cancellationToken);
                }
            }
            catch (Exception ex)
            {
                _logger.Verbose("Failed to connect to Terminal Hub: {Message}", ex.Message);
                
                await Task.Delay(1000, _cancellationToken);
            }
        }
    }

    private void HandleCommand(string serviceId, string command)
    {
        if (serviceId != _serviceId) 
            return;

        _logger.Information("Received command: {Command}", command);

        _executeCommand(command);
    }

    public async Task SendLogEntry(string message)
    {
        try
        {
            if (_connection?.State == HubConnectionState.Connected)
            {
                await _connection.InvokeAsync("SendLogEntry", _serviceId, message);
            }
        }
        catch (HubException hubException)
        {
            if (hubException.Message.Contains("The server closed the connection"))
                return; // Business as usual
            
            _logger.Warning(hubException, "Failed to send log entry");
        }
        catch (Exception ex)
        {
            _logger.Warning(ex, "Failed to send log entry");
        }
    }

    public async Task StopAsync(CancellationToken cancellationToken)
    {
        if (_connection != null)
        {
            await _connection.DisposeAsync();
        }
    }
    
    private class RetryPolicy : IRetryPolicy
    {
        private readonly TimeSpan _reconnectInterval = TimeSpan.FromSeconds(4);

        public TimeSpan? NextRetryDelay(RetryContext retryContext)
        {
            return _reconnectInterval;
        }
    }
}