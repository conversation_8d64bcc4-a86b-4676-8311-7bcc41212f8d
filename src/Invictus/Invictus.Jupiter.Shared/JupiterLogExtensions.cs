using Serilog;
using Serilog.Configuration;
using Serilog.Events;
using Serilog.Formatting;

namespace Invictus.Jupiter.Shared;

public static class JupiterLogExtensions
{
    public static LoggerConfiguration Terminal(this LoggerSinkConfiguration loggerConfiguration, LogEventLevel restrictedToMinimumLevel = LevelAlias.Minimum, ITextFormatter formatter = null)
    {
        return loggerConfiguration.Sink(new TerminalSink(restrictedToMinimumLevel, formatter));
    }
}