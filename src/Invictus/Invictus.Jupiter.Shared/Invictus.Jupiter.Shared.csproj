<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <TargetFrameworks>net8.0;net9.0</TargetFrameworks>
        <Configurations>RemoteRelease;RemoteDebug;LocalDebug</Configurations>
        <Platforms>AnyCPU</Platforms>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'RemoteRelease' ">
      <Optimize Condition=" '$(Optimize)' == '' ">true</Optimize>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'LocalDebug' ">
      <DebugSymbols Condition=" '$(DebugSymbols)' == '' ">true</DebugSymbols>
      <Optimize Condition=" '$(Optimize)' == '' ">false</Optimize>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
      <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="8.0.16" />
      <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.16" />
      <PackageReference Include="Serilog" Version="4.2.0" />
    </ItemGroup>
    
</Project>
