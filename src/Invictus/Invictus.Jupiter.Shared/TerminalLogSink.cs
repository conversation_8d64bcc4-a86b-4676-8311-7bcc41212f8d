using Serilog.Core;
using Serilog.Events;
using Serilog.Formatting;

namespace Invictus.Jupiter.Shared;

public class TerminalSink : ILogEventSink
{
    public static TerminalSink Instance { get; private set; }

    private readonly LogEventLevel _restrictedToMinimumLevel;
    private readonly ITextFormatter _formatter;
    private readonly MemoryStream _memoryStream;
    private readonly StreamWriter _streamWriter;
    private readonly StreamReader _streamReader;
    private LogQueueProcessor _queueProcessor;

    public TerminalSink(LogEventLevel restrictedToMinimumLevel = LevelAlias.Minimum, ITextFormatter formatter = null)
    {
        _restrictedToMinimumLevel = restrictedToMinimumLevel;
        _formatter = formatter;

        _memoryStream = new MemoryStream();
        _streamWriter = new StreamWriter(_memoryStream);
        _streamReader = new StreamReader(_memoryStream);

        Instance = this;
    }

    public void SetQueueProcessor(LogQueueProcessor queueProcessor)
    {
        _queueProcessor = queueProcessor;
    }

    public void Emit(LogEvent logEvent)
    {
        if (_queueProcessor == null)
            return;
        if (logEvent.Level < _restrictedToMinimumLevel)
            return;

        string message;
        
        lock (this)
        {
            _memoryStream.SetLength(0);
            
            _formatter.Format(logEvent, _streamWriter);
            _streamWriter.Flush();
            _memoryStream.Position = 0;
            message = _streamReader.ReadToEnd();
        }
        
        var logMessage = new LogMessage(message);
        
        _queueProcessor.EnqueueLogMessage(logMessage);
    }
}