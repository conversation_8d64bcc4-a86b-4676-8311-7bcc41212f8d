using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace Invictus.Jupiter.Shared;

public static class JupiterDependencyInjectionExtensions
{
    public static IServiceCollection AddJupiter(this IServiceCollection services, Func<IServiceProvider, JupiterConfiguration> getConfiguration, Action<IServiceProvider,string> executeCommand)
    {
        ArgumentNullException.ThrowIfNull(services);

        services.AddSingleton(s => 
            new TerminalClient(getConfiguration(s), cmd => executeCommand(s, cmd), s.GetRequiredService<IHostApplicationLifetime>())
        );
        services.AddSingleton<LogQueueProcessor>();

        return services;
    }

    public static void UseJupiter(this IApplicationBuilder applicationBuilder)
    {
        TerminalSink.Instance.SetQueueProcessor(applicationBuilder.ApplicationServices.GetRequiredService<LogQueueProcessor>());
    }
}