namespace Invictus.Messaging;

public readonly record struct ChatRef
{
    private readonly string _chatDefinitionName;
    
    public long ChatId { get; }
    public string ChatToken { get; }
    
    public string CustomerName { get; }
    public string Ref { get; }

    public ChatRef(string customerName, string chatRef, string chatDefinitionName, long chatId)
    {
        CustomerName = customerName;
        Ref = chatRef;
        _chatDefinitionName = chatDefinitionName;
        ChatId = chatId;
    }
    
    public ChatRef(string customerName, string chatRef, string chatDefinitionName, string chatToken)
    {
        CustomerName = customerName;
        Ref = chatRef;
        _chatDefinitionName = chatDefinitionName;
        ChatToken = chatToken;
    }

    public override string ToString()
    {
        return "(ChatRef:" + Ref + ")";
    }
}