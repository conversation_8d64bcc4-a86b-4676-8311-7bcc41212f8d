using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Misc;
using Invictus.Platform;

using Microsoft.Extensions.Configuration;

using OneOf;

namespace Invictus.Messaging;

public class ChatRefService
{
    private readonly MessagingConfigurationCustomer[] _customers;
    
    public ChatRefService(InvAppConfig appConfig)
    {
        IConfigurationSection[] customersSections = appConfig.GetCustomers();

        _customers = customersSections
            .Select(InitializeCustomerSection)
            .ToArray();
    }

    private static MessagingConfigurationCustomer InitializeCustomerSection(IConfigurationSection customerSection)
    {
        IConfigurationSection messagingSection = customerSection.GetSection("Messaging");
        IConfigurationSection definitionsSection = messagingSection.GetSection("ChatDefinitions");
        IConfigurationSection chatRefsSection = messagingSection.GetSection("ChatRefs");

        return new MessagingConfigurationCustomer
        {
            CustomerName = customerSection.Key,
            ChatDefinitions = definitionsSection
                .GetChildren()
                .ToDictionary(s => s.Key, defSection => {
                        if (long.TryParse(defSection.Value, out long l))
                            return (OneOf<long, string>)l;
                        return defSection.Value;
                    }
                ),
            ChatRefs = chatRefsSection
                .GetChildren()
                .ToDictionary(s => s.Key, defSection => defSection.Value)
        };
    }

    public ChatRef FromUnknownChatNameOrUser(MessagingPlatform messagingPlatform, long chatId)
    {
        throw new NotImplementedException();
    }

    public ChatRef FromUnknownChat(MessagingPlatform messagingPlatform, string chatDefinitionName)
    {
        throw new NotImplementedException();
    }
    
    public ChatRef FromRef(MessagingPlatform messagingPlatform, string customerName, string chatRef)
    {
        if (messagingPlatform == MessagingPlatform.None)
            throw new ArgumentException("Invalid messaging platform", nameof(messagingPlatform));
        if (chatRef.Count(c => c == '-') != 1)
            throw new ArgumentException("Invalid chatRef format", nameof(chatRef));
        
        MessagingConfigurationCustomer customer = _customers
            .Where(c => c.CustomerName == customerName)
            .SingleWith(customerName);

        string postPlatformPrefix = chatRef.Remove(chatRef.LastIndexOf('-'));
        string fullChatRef = AddMessagingPlatform(messagingPlatform, chatRef);
        
        KeyValuePair<string, string> chatRefKv = customer.ChatRefs
            .Where(cr => cr.Key == fullChatRef)
            .SingleWith(fullChatRef);

        string fullChatDef = AddMessagingPlatform(messagingPlatform, postPlatformPrefix + "-" + chatRefKv.Value);
        
        OneOf<long, string> chatId = customer.ChatDefinitions
            .Where(cd => cd.Key == fullChatDef)
            .Select(kv => kv.Value)
            .SingleWith(fullChatDef);

        return chatId.Match(l => new ChatRef(customerName, fullChatRef, fullChatDef, l), s => new ChatRef(customerName, fullChatRef, fullChatDef, s));
    }
    
    private static string AddMessagingPlatform(MessagingPlatform messagingPlatform, string identifier)
    {
        switch (messagingPlatform)
        {
            case MessagingPlatform.Telegram: 
                return "Telegram-" + identifier;
            default: throw TypeAbominationException.Enum(typeof(MessagingPlatform), messagingPlatform);
        }
    }

}