using Invictus.Nomenklatura.ExteriorServ;

using JetBrains.Annotations;

using Microsoft.Extensions.DependencyInjection;

namespace Invictus.Messaging;

[UsedImplicitly]
public class MessagingExteriorServiceBuilder : ExteriorServiceBuilderBase<MessagingExteriorService>
{
    protected override void AddConcealedServices(ServiceCollection concealedServiceCollection)
    {
        concealedServiceCollection.AddSingleton<ChatRefService>();

        this.RegisterDatabaseTypes(concealedServiceCollection);

        base.AddConcealedServices(concealedServiceCollection);
    }

    protected override void ExposeConcealedServices()
    {
        this.ExposeSingleton<ChatRefService>();
        
        base.ExposeConcealedServices();
    }
}