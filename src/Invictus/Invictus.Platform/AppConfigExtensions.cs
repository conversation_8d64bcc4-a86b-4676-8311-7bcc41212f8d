using Invictus.Nomenklatura.App;

using Microsoft.Extensions.Configuration;

namespace Invictus.Platform;

public static class AppConfigExtensions
{
    public static IConfigurationSection[] GetCustomers(this InvAppConfig invAppConfig)
    {
        return invAppConfig.GetSection("Customers").GetChildren().ToArray();
    }
    
    public static CustomerSubconfig<T>[] GetCustomersSections<T>(this InvAppConfig invAppConfig, string sectionName)
    {
        return invAppConfig.GetSection("Customers")
            .GetChildren()
            .Select(c => new CustomerSubconfig<T>(c.Key, c.GetSection(sectionName).Get<T>()))
            .ToArray();
    }
    
    internal static IConfigurationSection[] GetCustomersRelations(this InvAppConfig invAppConfig, string subsection)
    {
        return invAppConfig.GetSection("CustomerRelations").GetSection(subsection).GetChildren().ToArray();
    }
}