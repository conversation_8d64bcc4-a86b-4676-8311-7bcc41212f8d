using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Front;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Invictus.Platform.Controllers;

[Authorize(Roles = FrontConst.API_USER_ROLE_NAME)]
[ApiController]
[Route("[controller]/[action]")]
public class TranslationController : ControllerBase
{
    private readonly IDbAccessFactory<PlatformDbAccess> _platformDbAccessFactory;

    public TranslationController(IDbAccessFactory<PlatformDbAccess> platformDbAccessFactory)
    {
        _platformDbAccessFactory = platformDbAccessFactory;
    }

    [AllowAnonymous]
    [HttpGet]
    public string GetOrAddTraKey(int langId, string text, string traKey = null)
    {
        using PlatformDbAccess dbAccess = _platformDbAccessFactory.CreateAccess();
        PlatformDbContext dbContext = dbAccess.DbContext;

        // If key is provided, check if it exists
        if (traKey != null)
        {
            InvTra existingKeyTranslation = dbContext.Translations
                .FirstOrDefault(t => t.LangId == langId && t.Key == traKey);

            if (existingKeyTranslation != null)
            {
                // Update the existing translation with the new text
                existingKeyTranslation.Value = text;
                dbAccess.SaveChanges();
                return existingKeyTranslation.Key;
            }
        }
        else
        {
            // If no key provided, try to find an existing translation with the same text
            InvTra existingTranslation = dbContext.Translations
                .FirstOrDefault(t => t.LangId == langId && t.Value == text);

            if (existingTranslation != null)
            {
                return existingTranslation.Key;
            }
        }

        // If not found or key doesn't exist, create a new translation
        // Generate a unique key if none provided
        if (traKey == null)
        {
            traKey = $"ac_{Guid.NewGuid():N}";
        }

        // Create and save the new translation
        var newTranslation = new InvTra
        {
            LangId = langId,
            Key = traKey,
            Value = text
        };

        dbContext.Translations.Add(newTranslation);
        dbAccess.SaveChanges();

        return newTranslation.Key;
    }

    [AllowAnonymous]
    [HttpGet]
    public string GetTra(int langId, string traKey)
    {
        using PlatformDbAccess dbAccess = _platformDbAccessFactory.CreateAccess();
        PlatformDbContext dbContext = dbAccess.DbContext;

        InvTra translation = dbContext.Translations
            .FirstOrDefault(t => t.LangId == langId && t.Key == traKey);

        if (translation != null)
        {
            return translation.Value;
        }

        return null;
    }
}
