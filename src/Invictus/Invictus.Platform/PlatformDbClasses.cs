namespace Invictus.Platform;

// Models/InvLang.cs
public class InvLang
{
    public int Id { get; set; }
    public string Iso6393 { get; set; }

    public virtual ICollection<InvTra> Translations { get; set; }

    public InvLang()
    {
        Translations = new HashSet<InvTra>();
    }
}

// Models/InvTra.cs
public class InvTra
{
    public int LangId { get; set; }
    public string Key { get; set; }
    public string Value { get; set; }

    public virtual InvLang Language { get; set; }
    public virtual ICollection<InvPla> Places { get; set; }
    public virtual ICollection<InvSpace> Spaces { get; set; }

    public InvTra()
    {
        Places = new HashSet<InvPla>();
        Spaces = new HashSet<InvSpace>();
    }
}

public class InvTempQtysBlockCache
{
    public int Id { get; set; }
    
    public byte[] Value { get; set; }
}

// Models/InvPla.cs
public class InvPla
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string LangKey { get; set; }

    public virtual ICollection<InvTra> Translation { get; set; }
}

// Models/InvCust.cs
public class InvCust
{
    public int Id { get; set; }
    public string Name { get; set; }

    public virtual ICollection<InvSpace> Spaces { get; set; }

    public InvCust()
    {
        Spaces = new HashSet<InvSpace>();
    }
}

// Models/InvSpace.cs
public class InvSpace
{
    public int Id { get; set; }
    public int CustId { get; set; }
    public string SpaceNumber { get; set; }
    public string Name { get; set; }
    public string LangKey { get; set; }

    public virtual InvCust Customer { get; set; }
    public virtual ICollection<InvTra> Translation { get; set; }
}