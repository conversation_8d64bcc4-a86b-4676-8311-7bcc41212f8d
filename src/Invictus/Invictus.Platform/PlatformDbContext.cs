using Invictus.Nomenklatura.App2;

using Microsoft.EntityFrameworkCore;

namespace Invictus.Platform;

public class PlatformDbAccess : DbAccessBase
{
    public PlatformDbContext DbContext { get; }

    public PlatformDbAccess(DbAccessOptions dbAccessOptions, PlatformDbContext dbContext)
        : base(dbAccessOptions, dbContext)
    {
        DbContext = dbContext;
    }
}

public class PlatformDbContext : DbContext
{
    public PlatformDbContext(DbContextOptions<PlatformDbContext> options)
        : base(options)
    {
    }

    protected PlatformDbContext(DbContextOptions options)
        : base(options)
    {
    }
    
    public virtual DbSet<InvLang> Languages { get; set; }
    public virtual DbSet<InvTra> Translations { get; set; }
    public virtual DbSet<InvPla> Platforms { get; set; }
    public virtual DbSet<InvCust> Customers { get; set; }
    public virtual DbSet<InvSpace> Spaces { get; set; }
    
    public virtual DbSet<InvTempQtysBlockCache> QtyBlocks { get; set; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Entity<InvLang>(entity =>
        {
            entity.ToTable("InvLang");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Iso6393)
                .IsRequired()
                .HasMaxLength(3)
                .IsFixedLength();
            entity.HasIndex(e => e.Iso6393)
                .IsUnique();
        });

        modelBuilder.Entity<InvTra>(entity =>
        {
            entity.ToTable("InvTra");
            entity.HasKey(e => new { e.LangId, e.Key });
            entity.Property(e => e.Key)
                .IsRequired()
                .HasMaxLength(80);
            entity.Property(e => e.Value)
                .IsRequired();

            entity.HasOne(d => d.Language)
                .WithMany(p => p.Translations)
                .HasForeignKey(d => d.LangId)
                .OnDelete(DeleteBehavior.NoAction);

            entity.HasIndex(e => new { e.LangId, e.Key })
                .IsUnique();
        });
        
        modelBuilder.Entity<InvPla>(entity =>
        {
            entity.ToTable("InvPla");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(255);

            entity.HasMany(d => d.Translation);
        });

        modelBuilder.Entity<InvCust>(entity =>
        {
            entity.ToTable("InvCust");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(255);
        });

        modelBuilder.Entity<InvSpace>(entity =>
        {
            entity.ToTable("InvSpace");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.SpaceNumber)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(255);

            entity.HasOne(d => d.Customer)
                .WithMany(p => p.Spaces)
                .HasForeignKey(d => d.CustId)
                .OnDelete(DeleteBehavior.NoAction);

            entity.HasMany(d => d.Translation);
        });
    }
}