using Invictus.Nomenklatura.ExteriorServ;
using Invictus.Nomenklatura.Logg;

using JetBrains.Annotations;

namespace Invictus.Platform;

[UsedImplicitly]
public class PlatformExteriorService : IExteriorService
{
    private readonly ILogger _logger = InvLog.Logger<PlatformExteriorService>();

    private readonly CustomerRelations _chatRefService;
    private readonly CustomerRelations _customerRelations;
    private readonly PlatformService _platformService;

    public PlatformExteriorService(CustomerRelations chatRefService, CustomerRelations customerRelations, PlatformService platformService)
    {
        _chatRefService = chatRefService;
        _customerRelations = customerRelations;
        _platformService = platformService;
    }

    public Task Run()
    {
       
        
        return Task.CompletedTask;
    }
}