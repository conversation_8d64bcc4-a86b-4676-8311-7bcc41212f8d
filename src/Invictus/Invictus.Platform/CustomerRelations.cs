using Invictus.Nomenklatura.App;

using Microsoft.Extensions.Configuration;

namespace Invictus.Platform;

public class CustomerRelations
{
    private readonly (string, string)[] _allowedCustomersRelations;
    
    public CustomerRelations(InvAppConfig invAppConfig)
    {
        _allowedCustomersRelations = invAppConfig.GetCustomersRelations("Allowed")
            .Select(cr => (cr.GetValue<string>("Customer1"), cr.GetValue<string>("Customer2")))
            .ToArray();
    }

    public bool IsCrossCustomerMessagingAllowed(string customer1, string customer2)
    {
        return _allowedCustomersRelations.Contains((customer1, customer2)) || _allowedCustomersRelations.Contains((customer2, customer1));
    }
}