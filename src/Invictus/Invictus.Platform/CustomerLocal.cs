using Invictus.Nomenklatura.Misc;

namespace Invictus.Platform;

public class CustomerLocal<T>
{
    private readonly IProvider<T> _provider;
    private readonly Dictionary<string, T> _custData = new ();

    public CustomerLocal(IProvider<T> provider)
    {
        _provider = provider;
    }

    public T GetOrCreate(short customerNumber)
    {
        string customerName = CustomerContext.GetCustomerName(customerNumber);
        
        if (customerName == null)
            throw new Exception($"Customer env({customerNumber}) is not set.");
        
        if (_custData.TryGetValue(customerName, out T res))
            return res;

        res = _provider.Create();
        
        _custData[customerName] = res;

        return res;
    }
}