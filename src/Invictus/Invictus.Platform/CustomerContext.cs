using Invictus.Nomenklatura.Misc;
using Invictus.Nomenklatura.Workers;

namespace Invictus.Platform;

public static class CustomerContext
{
    public static IDisposable WithContext(short customerNumber, string customerName)
    {
        string dataKey = "PlaCust" + customerNumber;

        if (WorkersGod.GetSyncCtxData(dataKey) is string)
        {
            throw new Exception($"Context for customer {customerNumber} is already set." );
        }
        
        WorkersGod.SetSyncCtxData(dataKey, customerName);
        
        return new ActionDisposable(isFinalizer => {
            if (isFinalizer)
                throw new Exception("CustCtx.WithContext() should be disposed properly.");
            
            WorkersGod.SetSyncCtxData(dataKey, null);
        });
    }
    
    public static string GetCustomerName(short customerNumber)
    {
        string dataKey = "PlaCust" + customerNumber;
        
        return WorkersGod.GetSyncCtxData(dataKey) as string;
    }

    public static string GetAllCustomerNamesOrNull()
    {
        IReadOnlyDictionary<string, object> ctxData = WorkersGod.GetAllSyncCtxDataOrNull();

        if (ctxData == null)
            return null;
        
        return string.Join(",", ctxData.Where(kv => kv.Key.StartsWith("PlaCust")).Select(kv => kv.Value));
    }
}