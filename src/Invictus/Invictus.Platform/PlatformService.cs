using Invictus.Nomenklatura.App2;

namespace Invictus.Platform;

public class PlatformService
{
    private readonly IDbAccessFactory<PlatformDbAccess> _platformDbAccess;
    
    public InvLang[] Languages { get; private set; }
    public InvTra[] Translations { get; private set; }
    public InvPla[] Platforms { get; private set; }
    public InvCust[] Customers { get; private set; }
    public InvSpace[] Spaces { get; private set; }

    public PlatformService(IDbAccessFactory<PlatformDbAccess> platformDbAccess)
    {
        _platformDbAccess = platformDbAccess;
        
        this.Refresh();
    }

    public void Refresh()
    {
        using PlatformDbAccess access = _platformDbAccess.CreateAccess();

        Languages = access.DbContext.Languages.ToArray();
        Translations = access.DbContext.Translations.ToArray();
        Platforms = access.DbContext.Platforms.ToArray();
        Customers = access.DbContext.Customers.ToArray();
        Spaces = access.DbContext.Spaces.ToArray();
    }

    public InvPla GetPlatform(PlatformEnum platformEnum)
    {
        switch (platformEnum)
        {
            case PlatformEnum.Vk: return Platforms.SingleOrDefault(p => p.Name == "VK");
            case PlatformEnum.Kasta: return Platforms.SingleOrDefault(p => p.Name == "KastaUA");
            case PlatformEnum.Prom: return Platforms.SingleOrDefault(p => p.Name == "PromUA");
            case PlatformEnum.Epicentr: return Platforms.SingleOrDefault(p => p.Name == "EpicentrUA");
            case PlatformEnum.Rozetka: return Platforms.SingleOrDefault(p => p.Name == "Rozetka");
            default: throw new ArgumentException("Unknown platform.");
        }
    }

    public int GetLanguageId(string iso6393)
    {
        return Languages.SingleOrDefault(l => l.Iso6393 == iso6393).Id;
    }
}