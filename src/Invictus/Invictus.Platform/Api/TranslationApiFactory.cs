using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Web;

namespace Invictus.Platform.Api;

public class TranslationRefitFactory : RefitApiFactoryBase<ITranslationRefit>
{
    public TranslationRefitFactory(InvTasks invTasks, InvAppConfig invAppConfig)
        : base (invTasks, invAppConfig.BasicConfiguration.ExtApi.AstraHostUrl)
    {
    }

    protected override void ModifyHttpClient(HttpClient httpClient)
    {
        GC.KeepAlive(0);
    }
}