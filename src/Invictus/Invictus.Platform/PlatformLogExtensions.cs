using JetBrains.Annotations;

using Serilog.Configuration;
using Serilog.Core;

namespace Invictus.Platform;

public static class PlatformLogExtensions
{    
    [UsedImplicitly]
    public static LoggerConfiguration WithCustomer(this LoggerEnrichmentConfiguration enrichmentConfiguration)
    {
        return enrichmentConfiguration != null ? enrichmentConfiguration.With<PlatformCustomerLogEnricher>() : throw new ArgumentNullException(nameof(enrichmentConfiguration));
    }
}

public class PlatformCustomerLogEnricher : ILogEventEnricher
{
    public const string CUSTOMER_NAMES_PROPERTY_NAME = "CustomerNames";

    public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
    {
        string customerNames = CustomerContext.GetAllCustomerNamesOrNull();
        
        if (string.IsNullOrWhiteSpace(customerNames))
            return;
        
        logEvent.AddPropertyIfAbsent(new LogEventProperty(CUSTOMER_NAMES_PROPERTY_NAME, new ScalarValue(customerNames)));
    }
}