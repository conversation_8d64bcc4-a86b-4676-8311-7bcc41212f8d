using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.ExteriorServ;
using Invictus.Platform.Api;

using JetBrains.Annotations;

using Microsoft.Extensions.DependencyInjection;

namespace Invictus.Platform;

[UsedImplicitly]
public class PlatformExteriorServiceBuilder : ExteriorServiceBuilderBase<PlatformExteriorService>
{
    protected override void AddConcealedServices(ServiceCollection concealedServiceCollection)
    {
        concealedServiceCollection.AddSingleton<PlatformService>();
        concealedServiceCollection.AddSingleton<CustomerRelations>();
        
        concealedServiceCollection.AddSingleton<TranslationRefitFactory>();

        this.RegisterDatabaseTypes(concealedServiceCollection);

        base.AddConcealedServices(concealedServiceCollection);
    }

    protected override void ExposeConcealedServices()
    {
        this.ExposeSingleton<PlatformService>();
        this.ExposeSingleton<CustomerRelations>();
        
        this.ExposeSingleton<TranslationRefitFactory>();
        
        // TODO: controllers should get it from interior services provider
        this.ExposeDbAccess<PlatformDbAccess>();
        
        base.ExposeConcealedServices();
    }
}