using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using Bukimedia.PrestaSharp.Entities;

using irisdropwebservice.AppConfig;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.ExteriorServ;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;

using irisdropwebservice.IntegrationTests.Misc;
using irisdropwebservice.IntegrationTests.Services;
using irisdropwebservice.Libs.PrestaSharp;
using irisdropwebservice.Services;
using irisdropwebservice.Services.Chats;
using irisdropwebservice.Services.Chats.Ins;
using irisdropwebservice.Services.LinkService;
using irisdropwebservice.Services.PersistentTasks;
using irisdropwebservice.Services.PrestaSync;
using irisdropwebservice.Services.PrestaSync.Ins;
using irisdropwebservice.Services.PrestaSync.Remote;
using irisdropwebservice.Services.PrestaSync.ServiceConductors;
using irisdropwebservice.Services.PrestaSync.SiteFillConductors;
using irisdropwebservice.Services.ThisApp;
using irisdropwebservice.Services.VkScrap;


using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Interactions;
using OpenQA.Selenium.Support.UI;

using SeleniumExtras.WaitHelpers;

using Serilog;

using Telegram.Bot;
using Telegram.Bot.Types;

using Xunit.Abstractions;

namespace irisdropwebservice.IntegrationTests.Tests
{
    public class PrestaStockAvailablesTest : IClassFixture<PrestaFlowsWebApplicationFactory<Program>>
    {
        private record LinkedProduct(product Presta, VkScrapItem Vk);

        private readonly record struct ProductAndAttributeId(long ProductId, string Reference, long ProductAttributeId);

        private readonly ILogger _logger;

        private readonly PrestaFlowsWebApplicationFactory<Program> _factory;
        private readonly VkScrapSimulator _vkScrapSimulator;
        private readonly TelegramBotClientSimulator _telegramBotClientSimulator;
        private readonly ITelegramSendWorker _telegramSendWorker;
        private readonly ITelegramSendWorkerAutotest _telegramSendWorkerAutotestUser;
        private readonly IPrestaProductSyncWorker _prestaProductSyncWorker;
        private readonly IPrestaSyncWorker _prestaSyncWorker;
        private readonly IPrestaSiteWebApiCallsWorkerTest _prestaSiteApiCalls;
        private readonly AllProductsDataConductorService _allProductsDataConductor;
        private readonly CountryAndStateConductorService _countryAndStateConductor;
        private readonly LangAndTemplateConductorService _langAndTemplateConductor;
        private readonly PersistentTasksService _persistentTasksService;

        private readonly LinkedProduct[] _linkedProducts;
        private readonly Dictionary<ProductAndAttributeId, int> _stockAvailableOffsets = new();
        private readonly Dictionary<ProductAndAttributeId, int> _stockAvailableOffsets2 = new();
        private readonly Dictionary<long, ProductOption[]> _productOptionsById = new();

        private readonly long _testCustomerId;

        public PrestaStockAvailablesTest(PrestaFlowsWebApplicationFactory<Program> factory, ITestOutputHelper testOutputHelper)
        {
            var sw = Stopwatch.StartNew();

            Console.SetOut(new XunitTextWriter(testOutputHelper));

            Task seleniumInitTask = Task.Run(() => {
                    this.SeleniumInitialize();
                    this.SeleniumLogin("<EMAIL>", "vrqyvz1vcw9ZDCxwb");
                }
            );

            _factory = factory;
            _factory.CreateClient();

            _logger = InvLog.Logger<PrestaStockAvailablesTest>();
            _logger.Information("Server created in " + sw.Elapsed);

            var dbAccessFactory1 = 
                (DbAccessFactory)ExteriorServicesAssembliesLoader.GetPrivateService<ChatsExteriorService, IDbAccessFactory<TelegramChatMessagesDbAccess>>(_factory.Services);
            var dbAccessFactory2 =
                (DbAccessFactory)ExteriorServicesAssembliesLoader.GetPrivateService<NomenklaturaExteriorService, IDbAccessFactory<GenericPersistentTaskDbAccess>>(_factory.Services);

            DbContext dbContext1 = dbAccessFactory1.AutotestCreateDbContext();
            DbContext dbContext2 = dbAccessFactory2.AutotestCreateDbContext();
            UberDbContext.PrepareDatabase(dbContext1, dbContext2);

            _vkScrapSimulator = (VkScrapSimulator)_factory.Services.GetRequiredService<IVkScrapGetLast>();
            
            _telegramBotClientSimulator = (TelegramBotClientSimulator)
                ExteriorServicesAssembliesLoader
                    .GetPrivateService<ChatsExteriorService, IServiceFactory<ITelegramBotClient>>(_factory.Services)
                    .Create("autotest");

            _telegramSendWorker = _factory.Services.GetRequiredService<ITelegramSendWorker>();
            _persistentTasksService = _factory.Services.GetRequiredService<PersistentTasksService>();

            _telegramBotClientSimulator.Expect(IdwsExteriorService.APP_HAS_FULLY_STARTED_TELEGRAM_MSG, TimeSpan.FromSeconds(60));

            _telegramSendWorkerAutotestUser = ExteriorServicesAssembliesLoader.GetPrivateService<ChatsExteriorService, ITelegramSendWorkerAutotest>(_factory.Services);
            _prestaProductSyncWorker = ExteriorServicesAssembliesLoader.GetPrivateService<PrestaSyncExteriorService, IPrestaProductSyncWorker>(_factory.Services);
            _prestaSyncWorker = ExteriorServicesAssembliesLoader.GetPrivateService<PrestaSyncExteriorService, IPrestaSyncWorker>(_factory.Services);
            _prestaSiteApiCalls = ExteriorServicesAssembliesLoader.GetPrivateService<PrestaSyncExteriorService, IPrestaSiteWebApiCallsWorkerTest>(_factory.Services);
            _allProductsDataConductor = ExteriorServicesAssembliesLoader.GetPrivateService<PrestaSyncExteriorService, AllProductsDataConductorService>(_factory.Services);
            _countryAndStateConductor = ExteriorServicesAssembliesLoader.GetPrivateService<PrestaSyncExteriorService, CountryAndStateConductorService>(_factory.Services);
            _langAndTemplateConductor = ExteriorServicesAssembliesLoader.GetPrivateService<PrestaSyncExteriorService, LangAndTemplateConductorService>(_factory.Services);

            _testCustomerId = _prestaSiteApiCalls.GetAllCustomers().Single(c => c.firstname == "AUTOTEST" && c.lastname == "AUTOTESTOVICH").id.Val();

            VkScrapResult scrap = _vkScrapSimulator.GetAnyLastScrapResult(ScrapReadMode.Full);

            PrestaFlowsWebApplicationFactory<Program>.PrestaApiInterceptor.SetAutotestVkPhotoIds(scrap.Items.Select(vksi => vksi.FullId).ToArray());

            List<address> allAddresses = _prestaSiteApiCalls.GetAddresses(PrestaSiteWebApiCallsExtensions.CreatePrestaFilterByOneField(
                    "id_customer",
                    new string[] { _testCustomerId.ToString() }
                ),
                null,
                null
            );

            address testAddress = allAddresses.FirstOrDefault(addr => addr.address1 == "AUTOTEST");

            if (testAddress == null)
            {
                _prestaSiteApiCalls.AddAddress(
                    new address()
                    {
                        address1 = "AUTOTEST",
                        id_country = 214,
                        id_state = 356,
                        id_customer = _testCustomerId,
                        alias = "My address UA",
                        firstname = "AUTOTEST",
                        lastname = "AUTOTESTOVICH",
                        deleted = 0,
                        city = "Черкаси",
                        phone_mobile = "380688274424",
                        phone = "380688274424",
                        postcode = "10000",
                        id_supplier = 0,
                        id_manufacturer = 0,
                        id_warehouse = 0
                    }
                );
            }

            Task.Run(() => this.DoSyncPasses());

            // Perform first sync
            this.ProductSyncPass();

            List<product> products = _prestaSiteApiCalls.GetProducts(
                PrestaSiteWebApiCallsExtensions.CreatePrestaFilterByOneField(
                    "reference",
                    scrap.Items.Select(vksi => LinkedNaming.PrestaSiteReferenceFromVkAuthorityArt(vksi.Art))
                ),
                null,
                null
            );

            _linkedProducts = products
                .Select(
                    p => new LinkedProduct(
                        p,
                        scrap.Items.Single(vksi => vksi.Art == LinkedNaming.GetVkAuthorityArtFromPrestaSiteReference(p.reference))
                    )
                )
                .ToArray();

            List<StockAvailableExtended> stockAvailables = _prestaSiteApiCalls.MyApiProducts_GetFullStockAvaliInfo(
                _linkedProducts
                    .Select(lp => lp.Presta.id.Val())
                    .ToArray()
            );

            List<order> allOrders = _prestaSiteApiCalls.GetOrders(null, null, null);

            foreach (order order in allOrders)
            {
                if (order.id_customer == _testCustomerId && order.current_state != CCAppConfig.PrestaIrisDrop.CancelledOrderStatus)
                {
                    order.current_state = CCAppConfig.PrestaIrisDrop.CancelledOrderStatus;

                    _prestaSiteApiCalls.UpdateOrders(new List<order>() { order });
                }
            }

            foreach (LinkedProduct lp in _linkedProducts)
            {
                long productId = lp.Presta.id.Val();

                VkScrapItem vksi = scrap.Items.FirstOrDefault(s => s.Art == LinkedNaming.GetVkAuthorityArtFromPrestaSiteReference(lp.Presta.reference));

                _productOptionsById[productId] = _allProductsDataConductor.GetProductOptionsByProductId(productId).ToArray();

                foreach (ProductOption productOption in _productOptionsById[productId])
                {
                    string optionName = productOption.AttributeOptions.Single().Name;

                    short mustQuantity = vksi.QtyObjects
                        .OfType<VkScrapItemQtyUnit>()
                        .Single(qtyUnit => qtyUnit.SizeAllIdentifier.Contains(optionName))
                        .Quantity;

                    StockAvailableExtended stockAvailable = stockAvailables.Single(sa => sa.StockAvailable.id_product_attribute == productOption.Id);

                    // It must be like this
                    // 140/51/57/53/81/58/27/34/40-100шт
                    // 146/53/60/54/85/61/27/35/43-1шт
                    // 152/56/64/56/92/67/28/39/46-3шт
                    // 158/58/66/57/95/69/29/41/48-3шт
                    // 164 0

                    var key = new ProductAndAttributeId(productId, lp.Presta.reference, stockAvailable.StockAvailable.id_product_attribute.Val());

                    _stockAvailableOffsets[key] = mustQuantity - stockAvailable.RealPhysicalQuantity;
                    _stockAvailableOffsets2[key] = mustQuantity - stockAvailable.StockAvailable.quantity;

                    if (_stockAvailableOffsets[key] != 0 || _stockAvailableOffsets2[key] != 0)
                    {
                        GC.KeepAlive(0);
                    }
                }
            }

            _logger.Information("Test env. started in " + sw.Elapsed);

            seleniumInitTask.Wait();

            _logger.Information("Test env. started in " + sw.Elapsed);
        }

        // 
        [Fact]
        public void StraightforwardOrderFlow()
        {
            this.AssertQuantity(0, 140, 100, 100, true);
            this.AddToCart(0, 140, 1);
            string orderRef = this.MakeOrder();

            this.AssertQuantity(0, 140, 100, 99);
            this.ProductSyncPass();
            this.AssertQuantity(0, 140, 100, 99);

            this.ChangeOrderStatus(orderRef, CCAppConfig.PrestaIrisDrop.PaidOrderStatus);
            this.AssertQuantity(0, 140, 100, 99);
            this.ProductSyncPass();

            Message msg = _telegramBotClientSimulator.Expect("ЗНІМІТЬ З НАЯВНОСТІ або зв’яжіться з замовником", TimeSpan.FromSeconds(5));

            _telegramSendWorkerAutotestUser.Do(w => w.PostToTelegramFromUserAccount("+",
                CCAppConfig.TelegramIrisDrop.ReduceInStockChat,
                new AutotestUserPostToTelegramOptions()
                {
                    ReplyToMessageId = msg.MessageId,
                    IsChannelPost = true
                }
            ));

            _telegramBotClientSimulator.Expect("Успішно оброблено", TimeSpan.FromSeconds(10));

            // After '+' response to the chat above until vk quantity is changed quantities are invalid
            this.AssertQuantity(0, 140, 100, 99);
            this.ProductSyncPass();
            this.AssertQuantity(0, 140, 100, 100);

            // Now they're valid
            _vkScrapSimulator.Change(0, "140", -1);
            this.ProductSyncPass();
            this.AssertQuantity(0, 140, 100, 99);

            // Final status quantity change
            this.ChangeOrderStatus(orderRef, CCAppConfig.PrestaIrisDrop.SentOrderStatus);
            this.AssertQuantity(0, 140, 99, 99);
            this.ProductSyncPass();
            this.AssertQuantity(0, 140, 99, 99);
        }

        // Act

        private void AddToCart(int productN, int size, ushort quantity)
        {
            this.AddToCart(productN, size.ToString(), quantity);
        }

        private void AddToCart(int productN, string size, ushort quantity)
        {
            ProductAndAttributeId productAndAttributeId = this.GetProductWithSize(productN, size);
            
            this.SeleniumGoToProductPage(productAndAttributeId.Reference);
            this.SeleniumAddToCartOnProductPage(size, quantity);
        }

        private string MakeOrder()
        {
            return this.SeleniumMakeOrder("AUTOTEST ORDER");
        }

        private void ChangeOrderStatus(string orderRef, short status)
        {
            order order = _prestaSiteApiCalls
                .GetOrders(PrestaSiteWebApiCallsExtensions.CreatePrestaFilterByOneField("reference", new [] { orderRef }), null, null)
                .SingleOrDefault();

            order.current_state = status;

            _prestaSiteApiCalls.UpdateOrders(new List<order>() { order });
        }

        private void DoSyncPasses()
        {
            while (true)
            {
                this.SyncPass();
                Thread.Sleep(100);
            }
        }

        private void SyncPass()
        {
            bool anyPersistentTaskExecuted;
            do
            {
                _prestaSyncWorker.Do(w => w.DoPollingCycle());
                anyPersistentTaskExecuted = _persistentTasksService.ExecuteAllAndWait();
            } while (anyPersistentTaskExecuted);
        }

        private void ProductSyncPass()
        {
            VkScrapResult scrap = _vkScrapSimulator.GetAnyLastScrapResult(ScrapReadMode.Full);

            _prestaProductSyncWorker.Do(w => w.Push(scrap));
        }

        // Assert

        private void AssertQuantity(int productN, int size, int physical, int quantity, bool firstRun = false)
        {
            ProductAndAttributeId productAndAttributeId = this.GetProductWithSize(productN, size.ToString());

            List<StockAvailableExtended> stockAvailables = _prestaSiteApiCalls.MyApiProducts_GetFullStockAvaliInfo(new long[] { productAndAttributeId.ProductId });

            StockAvailableExtended stockAvailable = stockAvailables.Single(sa => sa.StockAvailable.id_product_attribute == productAndAttributeId.ProductAttributeId);

            Assert.Equal(physical - (firstRun ? _stockAvailableOffsets[productAndAttributeId] : 0), stockAvailable.RealPhysicalQuantity);
            Assert.Equal(quantity - (firstRun ? _stockAvailableOffsets2[productAndAttributeId] : 0), stockAvailable.StockAvailable.quantity);
        }

        // Helpers

        private IWebDriver _driver;
        private IOptions _driverManager;

        private void SeleniumInitialize()
        {
            var chromeOptions = new ChromeOptions()
            {

            };

            // chromeOptions.AddArgument("--headless=new");

            _driver = new ChromeDriver(chromeOptions);
            _driverManager = _driver.Manage();
            _driverManager.Window.FullScreen();

            _driverManager.Timeouts().ImplicitWait = TimeSpan.FromSeconds(2);
            _driverManager.Timeouts().PageLoad = TimeSpan.FromSeconds(3);

            _driver.Navigate().GoToUrl("https://test.irisdrop.com.ua");
        }

        private void SeleniumLogin(string username, string password)
        {
            IWebElement loginElement = _driver.FindElement(By.XPath("//div[@id='tvcmsdesktop-account-button']/div/button"));

            var actions = new Actions(_driver);

            actions.MoveToElement(loginElement).Perform();

            _driver.FindElement(By.CssSelector(".tvhedaer-sign-span")).Click();
            _driver.FindElement(By.Id("email")).Click();
            _driver.FindElement(By.Id("email")).SendKeys(username);
            _driver.FindElement(By.Id("field-password")).Click();
            _driver.FindElement(By.Id("field-password")).SendKeys(password);
            _driver.FindElement(By.Id("submit-login")).Click();
            _driver.FindElement(By.CssSelector(".tv-header-logo img")).Click();
        }

        private void SeleniumGoToProductPage(string art)
        {
            _driver.FindElement(By.CssSelector(".tvcmssearch-words")).Click();
            _driver.FindElement(By.CssSelector(".tvcmssearch-words")).SendKeys(art);

            _driver.JustWait(TimeSpan.FromSeconds(2));

            _driver.FindElement(By.CssSelector(".tvsearch-dropdown-title")).Click();
        }

        private void SeleniumAddToCartOnProductPage(string size, ushort quantity)
        {
            _driver.FindElement(By.XPath($"//span[contains(.,'{size}')]/preceding-sibling::input")).Click();

            // window.scrollTo(0,0)
            for (int i = 0; i < quantity - 1; i++)
            {
                _driver.FindElement(By.CssSelector(".touchspin-up")).Click();
            }

            // window.scrollTo(0,0)
            _driver.FindElement(By.CssSelector(".add-to-cart > span")).Click();
            _driver.JustWait(TimeSpan.FromSeconds(0.5));
            _driver.FindElement(By.ClassName("tv-addtocart-close")).Click();
        }

        private string SeleniumMakeOrder(string msg)
        {
            _driver.FindElement(By.CssSelector(".tv-cart-icon > #Layer_1")).Click();
            _driver.FindElement(By.CssSelector(".ttvcart-product-list-checkout > a")).Click();
            _driver.FindElement(By.Id("delivery_message")).Click();
            _driver.FindElement(By.Id("delivery_message")).SendKeys(msg);

            _driver.JustWait(TimeSpan.FromSeconds(0.5));
            _driver.FindElement(By.Id("conditions_to_approve[terms-and-conditions]")).Click();

            var wait = new WebDriverWait(_driver, TimeSpan.FromMinutes(1));
            wait.Until(ExpectedConditions.ElementExists(By.CssSelector(".to_checkout")));
            wait.Until(ExpectedConditions.ElementToBeClickable(By.CssSelector(".to_checkout")));

            _driver.JustWait(TimeSpan.FromSeconds(2));
            _driver.FindElement(By.CssSelector(".to_checkout")).Click();

            /*_driver.JustWait(TimeSpan.FromSeconds(1.5));
            try
            {
                _driver.FindElement(By.CssSelector(".to_checkout")).Click();
            }
            catch { }*/

            wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(12));
            wait.Until(ExpectedConditions.VisibilityOfAllElementsLocatedBy(By.XPath("//div[@id='content-hook_payment_return']/div/div/div/dl/dd[4]")));

            // wait?
            string text = _driver.FindElement(By.CssSelector("#order-details ul li")).Text;
            int orderRefAfter = text.IndexOf(": ", StringComparison.InvariantCultureIgnoreCase);
            string subText = text.Remove(0, orderRefAfter + 2);

            return subText;
        }

        private ProductAndAttributeId GetProductWithSize(int productN, string size)
        {
            LinkedProduct lp = this.GetLinkedProduct(productN);

            foreach (ProductOption productOption in _productOptionsById[lp.Presta.id.Val()])
            {
                if (productOption.AttributeOptions.Any(ao => ao.Name == size))
                    return new ProductAndAttributeId(productOption.ProductId, lp.Presta.reference, productOption.Id);
            }

            throw new Exception("Not found.");
        }

        private LinkedProduct GetLinkedProduct(int productN)
        {
            VkScrapResult scrap = _vkScrapSimulator.GetAnyLastScrapResult(ScrapReadMode.Full);

            return _linkedProducts.Single(p => p.Vk == scrap.Items[productN]);
        }
    }
}