using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

using irisdropwebservice.Services.Chats.Ins;


using Telegram.Bot;
using Telegram.Bot.Args;
using Telegram.Bot.Exceptions;
using Telegram.Bot.Requests;
using Telegram.Bot.Requests.Abstractions;
using Telegram.Bot.Types;


namespace irisdropwebservice.IntegrationTests.Services
{
    public class TelegramBotClientSimulator : ITelegramBotClient
    {
        private readonly BlockingCollection<Update> _updateQueue1 = new ();
        private readonly BlockingCollection<Update> _updateQueue2 = new();
        private int _nextMessageId = 1;

        public TelegramBotClientSimulator()
        {
        }

        private void AddUpdate(Update update)
        {
            _updateQueue1.Add(update);
            _updateQueue2.Add(update);
        }

        public Message Expect(string partOfMessage, TimeSpan timeSpan)
        {
            DateTime maxTimeUtc = DateTime.UtcNow + timeSpan;

            while (true)
            {
                TimeSpan canWait = maxTimeUtc - DateTime.UtcNow;

                if (canWait < TimeSpan.Zero)
                {
                    Assert.Fail($"Did not receive telegram message {partOfMessage} in time");
                    return null;
                }

                Update update = _updateQueue2.Take(new CancellationTokenSource((int)canWait.TotalMilliseconds).Token);

                bool gotMessage = update?.GetAnyMessage()?.Text?.Contains(partOfMessage, StringComparison.InvariantCultureIgnoreCase) ?? false;

                if (gotMessage)
                    return update.GetAnyMessage();
            }
        }

        public Task<TResponse> SendRequest<TResponse>(IRequest<TResponse> request, CancellationToken cancellationToken = new CancellationToken())
        {
            throw new NotImplementedException();
        }

        public Task<TResponse> MakeRequest<TResponse>(IRequest<TResponse> request, CancellationToken cancellationToken = new CancellationToken())
        {
            throw new NotImplementedException();
        }

        public Task<TResponse> MakeRequestAsync<TResponse>(IRequest<TResponse> request, CancellationToken cancellationToken = new ())
        {
            Update update;

            switch (request)
            {
                case GetUpdatesRequest getUpdatesRequest:
                    update = _updateQueue1.Take();

                    return Task.FromResult((TResponse)(object)new Update[] { update });

                case SendMessageRequest sendMessageRequest:
                    Debug.Assert(sendMessageRequest.ChatId.Identifier != null, "sendMessageRequest.ChatId.Identifier != null");

                    bool isUser = sendMessageRequest.Text.StartsWith("[__AT_USR");
                    bool isChannelPost = sendMessageRequest.Text.StartsWith("[__AT_USR1_]");

                    if (isChannelPost)
                    {
                        GC.KeepAlive(0);
                    }

                    var message = new Message()
                    {
                        Id = _nextMessageId++,
                        Text = isUser ? sendMessageRequest.Text.Remove(0, "[__AT_USR__]".Length) : sendMessageRequest.Text,
                        Chat = new Chat()
                        {
                            Id = sendMessageRequest.ChatId.Identifier.Value
                        },
                        SenderChat = new Chat()
                        {
                            Id = isUser ? 100 : BotId
                        },
                        ReplyToMessage = (sendMessageRequest.ReplyParameters?.MessageId).HasValue
                            ? new Message()
                            {
                                Id = sendMessageRequest.ReplyParameters.MessageId
                            }
                            : null
                    };

                    update = new Update();

                    if (isChannelPost)
                    {
                        update.ChannelPost = message;
                    } else
                    {
                        update.Message = message;
                    }

                    this.AddUpdate(update);
                    return Task.FromResult((TResponse)(object)update.Message);
                case SendChatActionRequest:
                    return Task.FromResult((TResponse)(object)true);
                default: 
                    throw new Exception($"Unexpected type {request.GetType()}");
            }
        }

        public Task<bool> TestApi(CancellationToken cancellationToken = new CancellationToken())
        {
            throw new NotImplementedException();
        }

        public Task DownloadFile(string filePath, Stream destination, CancellationToken cancellationToken = new CancellationToken())
        {
            throw new NotImplementedException();
        }

        public Task<bool> TestApiAsync(CancellationToken cancellationToken = new ())
        {
            throw new NotImplementedException();
        }

        public Task DownloadFileAsync(string filePath, Stream destination, CancellationToken cancellationToken = new ())
        {
            throw new NotImplementedException();
        }

        public bool LocalBotServer => throw new NotImplementedException();
        public long BotId => -1010001;
        public TimeSpan Timeout { get; set; }
        public IExceptionParser ExceptionsParser { get; set; } = null;

        public event AsyncEventHandler<ApiRequestEventArgs> OnMakingApiRequest;
        public event AsyncEventHandler<ApiResponseEventArgs> OnApiResponseReceived;
    }
}
