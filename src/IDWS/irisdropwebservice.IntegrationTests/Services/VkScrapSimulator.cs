using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;

using Invictus.Nomenklatura.Misc;

using irisdropwebservice.AppConfig;
using irisdropwebservice.Services.VkScrap;
using irisdropwebservice.Services.VkScrap.Ins;


namespace irisdropwebservice.IntegrationTests.Services
{
    public class VkScrapSimulator : IVkScrapServiceWebApiCallerInternal
    {
        private readonly List<LocalAlbum> _albums = new List<LocalAlbum>();
        private readonly List<VkScrapItem> _items = new List<VkScrapItem>();

        public VkScrapSimulator()
        {
            var album = new LocalAlbum()
            {
                AlbumTitle = "AUTOTEST",
                Description = "AUTOTEST",
                VkGroupId = RRAppConfig.SecretVkIrisDrop.GroupId,
                VkAlbumId = 100,
                ThumbSrc = null,
                Created = DateTime.UtcNow,
                Updated = DateTime.UtcNow
            };

            _albums.Add(album);

            for (int i = 0; i < 4; i++)
            {
                this.AddScrapItem(i);
            }
        }

        private void AddScrapItem(int n)
        {
            int art = -100 - n;

            var item1 = new VkScrapItem();
            item1.ScrapGeneration = 10;
            item1.MaxPhotoSizeUri = null;
            item1.FullId = RRAppConfig.SecretVkIrisDrop.GroupId + "_" + "100" + "_" + art;
            item1.VkPhotoCreationDate = DateTime.UtcNow;
            item1.OrderInAlbum = 1;

            item1.Text = @"арт "+art+@"

AUTOTEST"+art+ @"
Рекомендуємо!
ціна 1000 грн

зріст/довжина \рукав від горловини\пог-довжина штанів \шаг\пояс-довжина футболки/пог
похибка+-2см


140/51/57/53/81/58/27/34/40-100шт
146/53/60/54/85/61/27/35/43-1шт
152/56/64/56/92/67/28/39/46-3шт
158/58/66/57/95/69/29/41/48-3шт
------------------------
164/55/62/55/89/64/28/37/44-немає";

            var p = new VkProductParser(_albums, item1);
            p.Perform();

            string photoFilePath = CCAppConfig.VkScrapIrisDrop.GetPhotoFilePath(item1.FullId);

            if (!File.Exists(photoFilePath))
            {
                this.GetImage(n).Save(photoFilePath);
            }

            _items.Add(item1);
        }

        public byte[] DownloadFile(string vkFullPhotoId)
        {
            string photoFilePath = CCAppConfig.VkScrapIrisDrop.GetPhotoFilePath(vkFullPhotoId);

            FileStream fs = File.OpenRead(photoFilePath);
            byte[] res = new byte[fs.Length];
            int read = fs.Read(res, 0, res.Length);

            if (read != res.Length)
                throw new Exception("...");

            return res;
        }

        public string MakeScrapItemImgPhotoPublic(string vkFullPhotoId)
        {
            throw new NotImplementedException();
        }

        public VkScrapResult GetAnyLastScrapResult(ScrapReadMode readMode)
        {
            return new VkScrapResult()
            {
                GenerationId = 10,
                Items = _items.ToArray()
            };
        }

        public long GetRemoteLastCompleteScrapGeneration()
        {
            throw new NotImplementedException();
        }

        public VkScrapResult GetRemoteScrapResult(long generation, ScrapReadMode scrapReadMode)
        {
            throw new NotImplementedException();
        }

        public void Change(int productN, string size, short change)
        {
            VkScrapItemQtyUnit sz = _items[productN].QtyObjects.OfType<VkScrapItemQtyUnit>().First(q => q.Size_Height == size);

            sz.Quantity += change;

            _items[productN].Description += " 1CH";
            _items[productN].FullTextHash = IntStringUtil.GetStableHashCode(_items[productN].Description).ToString();
        }

        public VkScrapArtLogDto GetVkProductChangelog(string art)
        {
            throw new NotImplementedException();
        }

        public VkScrapArtLogDto[] GetAllVkProductsChangelog()
        {
            throw new NotImplementedException();
        }

        public Image GetImage(int n)
        {
            int height = 128;
            int width = 128;

            string text = "AT" + n;

            var rnd = new Random();
            string[] fonts = new[] { "Verdana" };
            float orientationAngle = rnd.Next(0, 359);

            int index0 = rnd.Next(0, fonts.Length);
            string familyName = fonts[index0];

            using (var bmpOut = new Bitmap(width, height))
            {
                Graphics g = Graphics.FromImage(bmpOut);

                SizeF size = g.MeasureString(text, new Font(familyName, 48, FontStyle.Bold));

                int x = Convert.ToInt32((width / 2.0) - (size.Width / 2));
                int y = Convert.ToInt32((height / 2.0) - (size.Height / 2));

                var gradientBrush = new LinearGradientBrush(new Rectangle(0, 0, width, height), FromHtml("#FFFFFF"), FromHtml("#FFFFFF"), orientationAngle);
                g.FillRectangle(gradientBrush, 0, 0, width, height);
                g.DrawString(text, new Font(familyName, 42, FontStyle.Bold), new SolidBrush(FromHtml("#169FE6")), x, y);
                var ms = new MemoryStream();
                bmpOut.Save(ms, ImageFormat.Png);
                byte[] bmpBytes = ms.GetBuffer();
                bmpOut.Dispose();
                ms.Close();

                using (var fileStream = new MemoryStream(bmpBytes))
                {
                    return Image.FromStream(fileStream);
                }
            }
        }
        public static Color FromHtml(string colorHtml)
        {
            var result = Color.Empty;
            if (!string.IsNullOrEmpty(colorHtml))
            {
                result = ColorTranslator.FromHtml(colorHtml);
            }
            return result;
        }
    }
}
