using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Castle.DynamicProxy;

using irisdropwebservice.Services.PrestaSync.Remote;
using irisdropwebservice.Libs.PrestaSharp;
using irisdropwebservice.IntegrationTests.MiscServices;
using Bukimedia.PrestaSharp.Entities.FilterEntities;

namespace irisdropwebservice.IntegrationTests.Services
{
    public class PrestaSiteWebApiCallsWorkerInterceptor : IInterceptorKnowsTarget
    {
        private IPrestaSiteWebApiCallsWorkerBase _target;
        private bool _firstCall = true;

        public void SetTarget(object target)
        {
            _target = (IPrestaSiteWebApiCallsWorkerBase)target;
        }

        private string[] _autotestVkPhotoIds;

        public void SetAutotestVkPhotoIds(string[] ids)
        {
            _autotestVkPhotoIds = ids;
        }

        public void Intercept(IInvocation invocation)
        {
            switch (invocation.Method.Name)
            {
                // TODO: detect whether this is new product for performance reasons
                case nameof(IPrestaSiteWebApiCallsWorkerBase.GetProductImages): 
                    invocation.ReturnValue = new List<declination>();
                    return;
                case nameof(IPrestaSiteWebApiCallsWorkerBase.DeleteProductImage):
                case nameof(IPrestaSiteWebApiCallsWorkerBase.AddProductImage):
                    return;

                case nameof(IPrestaSiteWebApiCallsWorkerBase.MyApiProductsSourcedFromVk_GetAllActiveProductsCacheInfo):
                    if (_autotestVkPhotoIds == null)
                    {
                        invocation.ReturnValue = new List<SourcedFromVkProductCacheInfo>(1);
                        return;
                    }
                    invocation.Proceed();
                    var origRes = (List<SourcedFromVkProductCacheInfo>)invocation.ReturnValue;
                    if (_firstCall)
                    {
                        origRes.RemoveAll(sfk => !_autotestVkPhotoIds.Contains(sfk.VkFullPhotoId));

                        foreach (SourcedFromVkProductCacheInfo sourcedFromVkProductCacheInfo in origRes)
                            _target.MyApiProductsSourcedFromVk_SetProductUpdatedPlusMostRecentProductVer(sourcedFromVkProductCacheInfo.ProductId, "(OUTDATED)");

                        invocation.Proceed();
                        origRes = (List<SourcedFromVkProductCacheInfo>)invocation.ReturnValue;
                        _firstCall = false;
                    }
                    
                    origRes.RemoveAll(sfk => !_autotestVkPhotoIds.Contains(sfk.VkFullPhotoId));
                    invocation.ReturnValue = origRes;
                    return;
            }

            invocation.Proceed();
        }
    }
}
