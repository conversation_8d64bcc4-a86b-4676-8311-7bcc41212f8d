<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
      <TargetFramework>net9.0-windows</TargetFramework>
	  <ImplicitUsings>disable</ImplicitUsings><Nullable>disable</Nullable>
	  <Configurations>LocalRelease;LocalDebug;RemoteRelease;RemoteDebug</Configurations>
      <IsPackable>false</IsPackable>
	  <Optimize>False</Optimize>
	  <UseWindowsForms>true</UseWindowsForms>
	  <Platforms>AnyCPU</Platforms>
  </PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='LocalRelease|AnyCPU'">
		<Optimize>True</Optimize>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='LocalDebug|AnyCPU'">
		<Optimize>False</Optimize>
		<DefineConstants>$(DefineConstants)TRACE;DEBUG</DefineConstants>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RemoteRelease|AnyCPU'">
		<Optimize>True</Optimize>
	</PropertyGroup>

  <ItemGroup>
	  <PackageReference Include="AngleSharp" Version="1.2.0" />
	  <PackageReference Include="DotNetSeleniumExtras.WaitHelpers" Version="3.11.0" />
	  <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="8.0.16" />
	  <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.16" />
	  <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.16" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.16" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.16" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.16">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>

	  <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />

	  <PackageReference Include="NSubstitute" Version="5.3.0" />

	  <PackageReference Include="NSubstitute.Analyzers.CSharp" Version="1.0.17">
	    <PrivateAssets>all</PrivateAssets>
	    <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>

	  <PackageReference Include="Selenium.WebDriver" Version="4.28.0" />

	  <PackageReference Include="Selenium.WebDriver.ChromeDriver" Version="132.0.6834.15900" />
	  <PackageReference Include="xunit" Version="2.9.3" />
	  <PackageReference Include="xunit.runner.visualstudio" Version="3.0.2">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
  </ItemGroup>

	<ItemGroup>
		<Content Update="xunit.runner.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\irisdropwebservice\irisdropwebservice.csproj" />
  </ItemGroup>

</Project>
