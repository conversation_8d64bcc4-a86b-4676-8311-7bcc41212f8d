using System;
using System.Collections.Generic;
using System.Linq;


using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.ExteriorServ;
using Invictus.Nomenklatura.Misc;

using Invictus.Nomenklatura.Workers;
using irisdropwebservice.IntegrationTests.MiscServices;
using irisdropwebservice.IntegrationTests.Services;
using irisdropwebservice.Services;
using irisdropwebservice.Services.Chats;
using irisdropwebservice.Services.DropUa;
using irisdropwebservice.Services.DropUa.Ins;
using irisdropwebservice.Services.PrestaSync;
using irisdropwebservice.Services.PrestaSync.Remote;
using irisdropwebservice.Services.PrestaSync.SiteFillConductors;
using irisdropwebservice.Services.PromSync;
using irisdropwebservice.Services.PromSync.Ins;
using irisdropwebservice.Services.PromSync.Remote;
using irisdropwebservice.Services.Statistics;
using irisdropwebservice.Services.Statistics.Ins;
using irisdropwebservice.Services.VkScrap;
using irisdropwebservice.Services.VkScrap.Ins;

using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;

using NSubstitute;

using Telegram.Bot;

namespace irisdropwebservice.IntegrationTests
{
    public class PrestaFlowsWebApplicationFactory<TProgram>
        : WebApplicationFactory<TProgram> where TProgram : class
    {
        private static void RemoveService<TService>(IServiceCollection serviceCollection)
        {
            IEnumerable<ServiceDescriptor> services = serviceCollection.Where(s => s.ServiceType == typeof(TService)).ToArray();

            foreach (ServiceDescriptor service in services)
            {
                serviceCollection.Remove(service);
            }
        }

        private static void ReplaceWithEmptySingleton<TService>(IServiceCollection serviceCollection)
            where TService : class
        {
            if (!typeof(TService).IsInterface)
                throw new ArgumentException("Only interfaces are supported.");

            IEnumerable<ServiceDescriptor> services = serviceCollection.Where(s => s.ServiceType == typeof(TService)).ToArray();

            foreach (ServiceDescriptor service in services)
            {
                serviceCollection.Remove(service);
            }

            serviceCollection.AddSingleton<TService>(_ => Substitute.For<TService>());
        }

        private static void ReplaceFactoryWithConcrete<TService>(IServiceCollection serviceCollection, TService concrete)
            where TService : class
        {
            Type factoryType = typeof(IServiceFactory<>).MakeGenericType(typeof(TService));

            IEnumerable<ServiceDescriptor> services = serviceCollection.Where(s => s.ServiceType == factoryType).ToArray();

            foreach (ServiceDescriptor service in services)
            {
                serviceCollection.Remove(service);
            }

            AddWithFactoryStub<TService>(serviceCollection, concrete);
        }

        public static IServiceCollection AddWithFactoryStub<TService>(IServiceCollection services, TService impl)
            where TService : class
        {
            services.AddSingleton<IServiceFactory<TService>>(_ =>
                new ServiceFactoryStub<TService>(impl)
            );

            return services;
        }

        private static void ReplaceSingletonService<TInterface, TNewImplementation>(IServiceCollection serviceCollection)
            where TInterface : class 
            where TNewImplementation : class, TInterface
        {
            IEnumerable<ServiceDescriptor> services = serviceCollection.Where(s => s.ServiceType == typeof(TInterface)).ToArray();

            foreach (ServiceDescriptor service in services)
            {
                if (service.Lifetime != ServiceLifetime.Singleton)
                    throw new Exception($"Service {typeof(TInterface).FullName} is expected to be singleton");

                serviceCollection.Remove(service);
            }

            serviceCollection.AddSingleton<TInterface, TNewImplementation>();
        }

        private static void ReplaceSingletonService<TInterface>(IServiceCollection serviceCollection, Func<TInterface> implementationFactory)
            where TInterface : class
        {
            IEnumerable<ServiceDescriptor> services = serviceCollection.Where(s => s.ServiceType == typeof(TInterface)).ToArray();

            foreach (ServiceDescriptor service in services)
            {
                if (service.Lifetime != ServiceLifetime.Singleton)
                    throw new Exception($"Service {typeof(TInterface).FullName} is expected to be singleton");

                serviceCollection.Remove(service);
            }

            serviceCollection.AddSingleton<TInterface>(_ => implementationFactory());
        }

        private static void ProxyWorkerFatherSingleton<TWorkerImpl>(IServiceCollection serviceCollection, Action<WorkerFatherProxy<TWorkerImpl>> modifyWorkers)
            where TWorkerImpl : class, IWorkerImpl
        {
            Type workerFatherType = typeof(IWorkerFather<>).MakeGenericType(typeof(TWorkerImpl));

            IEnumerable<ServiceDescriptor> services = serviceCollection.Where(s => s.ServiceType == workerFatherType).ToArray();

            foreach (ServiceDescriptor service in services)
            {
                serviceCollection.Remove(service);
            }

            serviceCollection.AddSingleton<IWorkerFather<TWorkerImpl>>(services => {
                    var wf = ActivatorUtilities.CreateInstance<WorkerFatherProxy<TWorkerImpl>>(services);
                    modifyWorkers(wf);
                    return wf;
                }
            );
        }

        protected override void ConfigureWebHost(IWebHostBuilder builder)
        {
            builder.ConfigureServices(ConfigureServices);
            builder.UseEnvironment("DevelopmentIntegrationTesting");
        }

        public static PrestaSiteWebApiCallsWorkerInterceptor PrestaApiInterceptor { get; private set; }

        private static void ConfigureServices(IServiceCollection services)
        {
            // Use empty in memory db
            ExteriorServicesAssembliesLoader.HookExteriorServiceBuilding<NomenklaturaExteriorServiceBuilder>(
                services => {
                    ReplaceSingletonService<IDbOptionsCompletion, SqliteDbOptionsCompletion>(services);
                }
            );

            // Some timed events are disabled via app configuration.

            // Disable statistics service
            var statisticsService = Substitute.For<IStatisticsService>();

            var statisticsPublicFlushContext = Substitute.For<IPublicStatisticsFlushContext>();

            statisticsPublicFlushContext
                .GetLastFlushTimeAndArgumentUtc(OperationType.None)
                .ReturnsForAnyArgs((DateTime.MaxValue, null));

            statisticsService.PublicFlushContext.Returns(statisticsPublicFlushContext);

            ReplaceSingletonService<IStatisticsService>(services, () => statisticsService);

            // TODO: WebRequestRetryExceptionBeHandler: always throw
            
            // Mock vk scrap.
            var vkScrapSimulator = new VkScrapSimulator();

            ExteriorServicesAssembliesLoader.HookExteriorServiceBuilding<VkScrapExteriorServiceBuilder>(
                services => {
                    // Substitute.ForPartsOf<TImpl>
                    ReplaceWithEmptySingleton<IVkScrapAndParseWorker>(services);
                    ReplaceWithEmptySingleton<IVkRemoteScrapWatcherWorker>(services);
                    ReplaceWithEmptySingleton<IVkProductHistory>(services);
                    ReplaceWithEmptySingleton<IVkScrapAndParseWorkerHelper>(services);

                    ReplaceSingletonService<IVkScrapFileDownload>(services, () => vkScrapSimulator);
                    ReplaceSingletonService<IVkScrapGetLast>(services, () => vkScrapSimulator);
                    ReplaceSingletonService<IVkScrapServiceWebApiCallerInternal>(services, () => vkScrapSimulator);
                }
            );

            // Mock telegram
            var telegramClientSimulator = new TelegramBotClientSimulator();

            ExteriorServicesAssembliesLoader.HookExteriorServiceBuilding<ChatsExteriorServiceBuilder>(
                services => {
                    ReplaceFactoryWithConcrete<ITelegramBotClient>(services, telegramClientSimulator);
                }
            );

            // Replace some presta API calls so we
            // 1: Don't deactivate out of autotest products
            // 2: Don't check categories
            // 3: Use stubbed product caching

            ExteriorServicesAssembliesLoader.HookExteriorServiceBuilding<PrestaSyncExteriorServiceBuilder>(
                services => {
                    RemoveService<IServiceFactory<CategoriesConductorService>>(services);
                    services.AddFactory<CategoriesConductorService>(_ => new object[] { CategoriesConductorServiceMode.NoChecks });

                    PrestaApiInterceptor = new PrestaSiteWebApiCallsWorkerInterceptor();

                    ProxyWorkerFatherSingleton<PrestaWebApiCallsWorkerUser>(services,
                        wf => {
                            wf.AddInterceptorForWorkerInterfaceType<IPrestaSiteWebApiCallsWorkerUser>(PrestaApiInterceptor);
                        }
                    );
                    ProxyWorkerFatherSingleton<PrestaWebApiCallsWorkerNonUser>(services,
                        wf => {
                            wf.AddInterceptorForWorkerInterfaceType<IPrestaSiteWebApiCallsWorkerNonUser>(PrestaApiInterceptor);
                        }
                    );
                }
            );

            // Remove IrisOffice
            ReplaceWithEmptySingleton<IIrisOfficeService>(services);

            // Remove prom
            ExteriorServicesAssembliesLoader.HookExteriorServiceBuilding<PromSyncExteriorServiceBuilder>(
                services => {
                    ReplaceWithEmptySingleton<IPromSyncWorker>(services);
                    ReplaceWithEmptySingleton<IPromSiteWebApiCallsWorker>(services);
                }
            );

            // Remove site scan
            ExteriorServicesAssembliesLoader.HookExteriorServiceBuilding<DropUaExteriorServiceBuilder>(
                services => {
                    ReplaceWithEmptySingleton<ISiteScanWorker>(services);
                }
            );
        }
    }
}