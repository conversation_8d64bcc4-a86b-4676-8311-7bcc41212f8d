using System;
using System.Collections.Generic;

using Castle.DynamicProxy;

using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.Workers;

using Microsoft.Extensions.Hosting;

namespace irisdropwebservice.IntegrationTests.MiscServices
{
    public interface IInterceptorKnowsTarget : IInterceptor
    {
        void SetTarget(object target);
    }

    public class WorkerFatherProxy<TImpl> : WorkerFather<TImpl>
        where TImpl : class, IWorkerImpl
    {
        private static readonly ProxyGenerator _ProxyGenerator = new();

        private readonly Dictionary<Type, IInterceptorKnowsTarget> _interceptors = new();

        public WorkerFatherProxy(TImpl impl, IInvAppLifetime applicationLifetime)
            : base(impl, applicationLifetime)
        {
        }

        public void AddInterceptorForWorkerInterfaceType<TWorkerInterface>(IInterceptorKnowsTarget interceptor)
        {
            _interceptors.Add(typeof(TWorkerInterface), interceptor);
        }

        public override object GetOrCreatePublicInterfaceObject(Type interfaceType)
        {
            if (_interceptors.TryGetValue(interfaceType, out IInterceptorKnowsTarget val))
            {
                object target = base.GetOrCreatePublicInterfaceObject(interfaceType);

                object res = _ProxyGenerator.CreateInterfaceProxyWithTarget(interfaceType, target, val);

                val.SetTarget(target);

                return res;
            }

            return base.GetOrCreatePublicInterfaceObject(interfaceType);
        }
    }
}
