using Invictus.Nomenklatura.Misc;



namespace irisdropwebservice.IntegrationTests.MiscServices
{
    public class ServiceFactoryStub<TDecorated> : IServiceFactory<TDecorated>
    {
        private readonly TDecorated _impl;

        public ServiceFactoryStub(TDecorated impl)
        {
            _impl = impl;
        }

        public TDecorated Create()
        {
            return _impl;
        }

        public TDecorated Create(params object[] addOrOverrideMoreArguments)
        {
            return _impl;
        }
    }
}
