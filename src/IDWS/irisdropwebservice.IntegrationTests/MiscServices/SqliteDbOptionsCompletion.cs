using irisdropwebservice.AppConfig.RemoteResources;
using irisdropwebservice.AppConfig;
using Invictus.Nomenklatura.App2;

using Microsoft.Data.Sqlite;
using Microsoft.EntityFrameworkCore;

namespace irisdropwebservice.IntegrationTests.MiscServices
{
    public class SqliteDbOptionsCompletion : IDbOptionsCompletion
    {
        private readonly SqliteConnection _connection;

        public bool GlobalAccessorsLock => true;

        public SqliteDbOptionsCompletion()
        {
            _connection = new SqliteConnection(null);
            _connection.Open();
        }

        public void CompleteDbOptions(DbContextOptionsBuilder options)
        {
            options.UseSqlite(_connection);
        }
    }
}
