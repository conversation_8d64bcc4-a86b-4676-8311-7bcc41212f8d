using System.IO;
using System.Text;
using System;
using Xunit.Abstractions;

namespace irisdropwebservice.IntegrationTests.Misc
{
    public class XunitTextWriter : TextWriter
    {
        private readonly ITestOutputHelper _output;
        private readonly StringBuilder _thisLineBuilder = new ();

        public override Encoding Encoding => Encoding.Unicode;

        public XunitTextWriter(ITestOutputHelper output)
        {
            _output = output;
        }

        private void WriteLineToOutput(string str)
        {
            try
            {
                _output.WriteLine(str);
            }
            catch (InvalidOperationException)
            {
            }
        }

        private void WriteLineToOutput(string str, object[] args)
        {
            try
            {
                _output.WriteLine(str, args);
            }
            catch (InvalidOperationException)
            {
            }
        }

        private void FlushThisLineBuilder()
        {
            if (_thisLineBuilder.Length > 0)
            {
                this.WriteLineToOutput(_thisLineBuilder.ToString());

                _thisLineBuilder.Clear();
            }
        }

        public override void WriteLine(string message)
        {
            this.FlushThisLineBuilder();

            this.WriteLineToOutput(message);
        }

        public override void WriteLine(string format, params object[] args)
        {
            this.FlushThisLineBuilder();

            this.WriteLineToOutput(format, args);
        }

        public override void Write(char value)
        {
            if (value == '\n')
            {
                this.FlushThisLineBuilder();
                return;
            }

            _thisLineBuilder.Append(value);
        }
    }
}