using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage;

namespace irisdropwebservice.IntegrationTests.Misc
{
    public class UberDbContext
    {
        public static void PrepareDatabase(DbContext test1DbContext, DbContext test2DbContext)
        {
            // this drops the *whole* database, not just the `test1` schema
            test1DbContext.Database.EnsureDeleted();

            // create `test1` schema and related tables from schema `test2`
            test1DbContext.Database.EnsureCreated();

            // try to create tables from `test2` schema - exception: some of them already exist,
            // they were created with test1DbContext.Database.EnsureCreated()
            test2DbContext.GetService<IRelationalDatabaseCreator>().CreateTables();
        }
    }
}
