using OpenQA.Selenium.Support.UI;
using System;
using System.Diagnostics;

using OpenQA.Selenium;

namespace irisdropwebservice.IntegrationTests.Misc
{
    public static class SeleniumExtensions
    {
        public static void JustWait(this IWebDriver webDriver, TimeSpan timeSpan)
        {
            var sw = Stopwatch.StartNew();
            var wait = new WebDriverWait(webDriver, TimeSpan.FromHours(1));
            wait.Until(_ => sw.Elapsed > timeSpan);
        }
    }
}
