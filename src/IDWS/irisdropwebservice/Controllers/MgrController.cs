using System.Text;

using irisdropwebservice.Libs.KastaSharp;
using irisdropwebservice.Models;
using irisdropwebservice.Services.Auth;
using irisdropwebservice.Services.KastaSync;

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Hosting;

namespace irisdropwebservice.Controllers
{
    [ApiController]
    [Route("mgr")]
    public class MgrController : Controller
    {
        private readonly IHostEnvironment _environment;
        private readonly KastaMgrModel _kastaMgrModel;

        public MgrController(KastaSyncCommon kastaSyncCommon, IHostEnvironment environment)
        {
            _environment = environment;

            // Create an instance of the model with some data
            var model = new KastaMgrModel();
            model.CategorySchemas = kastaSyncCommon.CategorySchemas;

            model.TreeNodes = new List<CategorySchemasTreeNode>();

            foreach (KastaMyCategorySchema kastaMyCategorySchema in model.CategorySchemas)
            {
                var categorySchemaNode = new CategorySchemasTreeNode();
                categorySchemaNode.Id = $"{kastaMyCategorySchema.Category.AffiliationId}";
                categorySchemaNode.Text = $"{kastaMyCategorySchema.Category.Name}  / {kastaMyCategorySchema.Category.NameAlias}";
                model.TreeNodes.Add(categorySchemaNode);

                foreach (KastaMyCategorySchemaKind kastaCategoryKind in kastaMyCategorySchema.KindsWithSchemas)
                {
                    var kastaCategoryKindNode = new CategorySchemasTreeNode();
                    kastaCategoryKindNode.Id = $"{kastaCategoryKind.CategoryKindItself.AffiliationId}_{kastaCategoryKind.CategoryKindItself.KindId}";
                    kastaCategoryKindNode.Text = $"{kastaCategoryKind.CategoryKindItself.Name}  / {kastaCategoryKind.CategoryKindItself.NameAlias}";
                    categorySchemaNode.Children.Add(kastaCategoryKindNode);

                    foreach (KastaCategorySchemaItem kastaCategorySchemaItem in kastaCategoryKind.SchemaItems)
                    {
                        var schemaNodeItem = new CategorySchemasTreeNode();
                        schemaNodeItem.Id = $"{kastaCategoryKind.CategoryKindItself.AffiliationId}_{kastaCategoryKind.CategoryKindItself.KindId}_{kastaCategorySchemaItem.KeyName}";
                        schemaNodeItem.Text = schemaNodeItem.Id + " " + KastaCategorySchemaItemToHtmlString(kastaCategorySchemaItem);
                        kastaCategoryKindNode.Children.Add(schemaNodeItem);

                        if (kastaCategorySchemaItem.Type != "characteristic")
                        {
                            GC.KeepAlive(0);
                        }

                        if (kastaCategorySchemaItem.SizeCharts != null)
                        {
                            foreach (KastaCategorySizeChart kastaCategorySizeChart in kastaCategorySchemaItem.SizeCharts)
                            {
                                var sizeChartNodeItem = new CategorySchemasTreeNode();
                                sizeChartNodeItem.Id = schemaNodeItem.Id + "_" + kastaCategorySizeChart.Name;
                                sizeChartNodeItem.Text = kastaCategorySizeChart.Name + ": " + string.Join(", ", kastaCategorySizeChart.Sizes.Select(s => s.Value));
                                schemaNodeItem.Children.Add(sizeChartNodeItem);
                            }
                        }
                    }
                }
            }

            _kastaMgrModel = model;
        }

        private static string KastaCategorySchemaItemToHtmlString(KastaCategorySchemaItem schemaItem)
        {
            var bdr = new StringBuilder();

            bdr.Append(schemaItem.Type);
            bdr.Append(" ");
            bdr.Append(schemaItem.HumanName);
            bdr.Append(" ");

            if (schemaItem.IsMandatory)
            {
                bdr.Append("<span style=\"color: red\"> " + schemaItem.Help + " </span>");
                bdr.Append("<BR/>");
            } else
            {
                bdr.Append("<span style=\"color: aqua\"> " + schemaItem.Help + " </span>");
                bdr.Append("<BR/>");
            }

            if (schemaItem.Requirements.IsMulti)
            {
                bdr.Append("Можна кілька значень.");
                bdr.Append(" ");
            }

            if (schemaItem.ValueIds != null)
            {
                bdr.Append("Приклади значень: ");
                foreach (KastaCategorySize kastaCategorySize in schemaItem.ValueIds)
                {
                    bdr.Append(kastaCategorySize.Value);
                    bdr.Append(", ");
                }
            }

            return bdr.ToString();
        }

        [HttpGet("kasta")]
        public IActionResult KastaIndex()
        {
            if (!_environment.IsDevelopment())
                return null;

            // Pass the model to the view
            return this.View("~/Pages/Mgr/Kasta/Index.cshtml", _kastaMgrModel);
        }

        [HttpGet("kasta/cat_nodes")]
        public IActionResult KastaCatNodes(string id = "#")
        {
            if (!_environment.IsDevelopment())
                return null;

            return this.Json(_kastaMgrModel.TreeNodes);
        }
    }
}
