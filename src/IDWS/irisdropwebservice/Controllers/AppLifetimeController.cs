using System.Threading.Tasks;

using irisdropwebservice.Services.Auth;

using Microsoft.AspNetCore.Mvc;

namespace irisdropwebservice.Controllers
{
    [ApiController]
    [Route("[controller]/[action]")]
    public class AppLifetimeController : ControllerBase
    {
        [HttpHead(Name = "Ping")]
        public async Task<StatusCodeResult> Ping()
        {
            await Task.Delay(5000); 
            
            return this.Ok();
        }
        
        [HttpGet(Name = "Index")]
        public async Task<ContentResult> Index()
        {
            await Task.Delay(5000); 
            
            string html = @"
            <!DOCTYPE html>
            <html>
            <head>
                <title>Direct HTML Example</title>
            </head>
            <body>
                <h1>Hello from Controller!</h1>
                <p>This HTML is served directly from the controller.</p>
            </body>
            </html>";

            return this.Content(html, "text/html");
        }
    }
}