using irisdropwebservice.Services.Auth;
using irisdropwebservice.Services.LinkService;
using irisdropwebservice.Services.VkScrap;
using irisdropwebservice.Services.VkScrap.Ins;

using Microsoft.AspNetCore.Mvc;

namespace irisdropwebservice.Controllers
{
    [BasicAuthorization]
    [ApiController]
    [Route("[controller]/[action]")]
    public class IrisDropVkScrapController
    {
        private readonly VkScrapServiceWebApiExecutor _vkScrapServiceWebApiExecutor;
        private readonly ExposedResourcesService _tempResDownload;

        public IrisDropVkScrapController(VkScrapServiceWebApiExecutor vkScrapServiceWebApiExecutor, ExposedResourcesService tempResDownload)
        {
            _vkScrapServiceWebApiExecutor = vkScrapServiceWebApiExecutor;
            _tempResDownload = tempResDownload;
        }

        [HttpGet(Name = "GetLastCompleteScrapGeneration")]
        public long GetLastCompleteScrapGeneration()
        {
            return _vkScrapServiceWebApiExecutor.GetLastCompleteScrapGeneration();
        }

        [HttpGet(Name = "GetScrappedItemsFromGeneration")]
        public VkScrapItemDto[] GetScrappedItemsFromGeneration(long scrapGenerationId, ScrapReadMode readMode)
        {
            if (scrapGenerationId < 0)
                return Array.Empty<VkScrapItemDto>();

            return _vkScrapServiceWebApiExecutor.GetScrappedItemsFromGeneration(scrapGenerationId, readMode);
        }

        [HttpGet(Name = "DownloadScrapItemImg")]
        public byte[] DownloadScrapItemImg(string vkFullPhotoId)
        {
            return _vkScrapServiceWebApiExecutor.GetScrapItemImgBytes(vkFullPhotoId);
        }

        [HttpGet(Name = "GetVkProductChangelog")]
        public VkScrapArtLogDto GetVkProductChangelog(string art)
        {
            return _vkScrapServiceWebApiExecutor.GetVkProductChangelog(art);
        }

        [HttpGet(Name = "GetAllVkProductsChangelog")]
        public VkScrapArtLogDto[] GetAllVkProductsChangelog()
        {
            return _vkScrapServiceWebApiExecutor.GetAllVkProductsChangelog();
        }

        [HttpPost(Name = "MakeScrapItemImgPhotoPublic")]
        public string MakeScrapItemImgPhotoPublic(string vkFullPhotoId)
        {
            return _tempResDownload.RegisterVkComImgFor1Day(vkFullPhotoId);
        }
    }
}