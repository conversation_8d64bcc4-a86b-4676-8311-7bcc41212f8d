using irisdropwebservice.Services.Auth;
using irisdropwebservice.Services.LinkService;

using Microsoft.AspNetCore.Mvc;

namespace irisdropwebservice.Controllers
{
    [ApiController]
    public class ResourcesFFAController : ControllerBase
    {
        private readonly ExposedResourcesService _tempResDownload;

        public ResourcesFFAController(ExposedResourcesService tempResDownload)
        {
            _tempResDownload = tempResDownload;
        }

        [HttpGet]
        [Route("res/get/img/{resourceType}/{resourceIdWithExtension}")]
        public IActionResult GetResource(string resourceType, string resourceIdWithExtension)
        {
            byte[] bytes = _tempResDownload.GetFileContents(resourceType, resourceIdWithExtension);

            if (bytes != null)
                return this.File(bytes, "image/jpeg");

            bytes = _tempResDownload.GetDefaultFileContents();

            return this.File(bytes, "image/jpeg");
        }
    }
}
