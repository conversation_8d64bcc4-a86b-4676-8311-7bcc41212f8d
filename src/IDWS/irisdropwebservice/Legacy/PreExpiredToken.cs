using Invictus.Nomenklatura.App2;

using irisdropwebservice.Services.LinkService.Ins;

namespace irisdropwebservice.Legacy;

abstract class PreExpiredToken
{
    private readonly IDbAccessFactory<VariousStoredDataDbAccess> _variousDbAccessFactory;
    private readonly ILogger _logger;
    private readonly string _tokenDbCalling2;
    private readonly string _tokenLogName;
    private readonly int _customerId;
    private readonly Func<string, string> _authorize;
    private readonly int _expiryHours;

    protected PreExpiredToken(IDbAccessFactory<VariousStoredDataDbAccess> access, ILogger logger, string tokenDbCalling2, string tokenLogName, int customerId, Func<string, string> authorize, int expiryHours)
    {
        _variousDbAccessFactory = access;
        _logger = logger;
        _tokenDbCalling2 = tokenDbCalling2;
        _tokenLogName = tokenLogName;
        _customerId = customerId;
        _authorize = authorize;
        _expiryHours = expiryHours;
    }

    public void Authorize()
    {
        _logger.Debug($"Authorize {_tokenLogName}");

        string token, tokenExpiryTime;
        
        using VariousStoredDataDbAccess access = _variousDbAccessFactory.CreateAccess();
        {
            token = access.TryGetValue(_tokenDbCalling2 + "_1",           _customerId);
            tokenExpiryTime = access.TryGetValue(_tokenDbCalling2 + "_2", _customerId);
        }

        if (token == null)
        {
            _logger.Debug("Expr1");
            this.BaseAuthorize();
            return;
        }
        
        if (tokenExpiryTime == null)
        {
            _logger.Debug("Expr2");
            this.BaseAuthorize();
            return;
        }

        if (!long.TryParse(tokenExpiryTime, out long expiryTicks))
        {
            _logger.Debug("Expr3");
            this.BaseAuthorize();
            return;
        }
        
        DateTime expiryTime = DateTime.FromFileTimeUtc(expiryTicks);

        if (expiryTime < DateTime.UtcNow)
        {
            _logger.Debug("Expr4");
            this.BaseAuthorize();
            return;
        }
        
        this.TokenAuthorize(token, expiryTime);
    }
    
    private void BaseAuthorize()
    {
        string newToken = _authorize(null);

        this.StoreToken(newToken, DateTime.UtcNow.AddHours(_expiryHours));
    }

    private void TokenAuthorize(string token, DateTime prevExpiryTimeUtc)
    {
        _logger.Debug($"Re-using prev {_tokenLogName} token");
            
        string newToken = _authorize(token);
            
        this.StoreToken(newToken, prevExpiryTimeUtc);
            
        _logger.Debug("Success");
    }

    private void StoreToken(string token, DateTime expiryTimeUtc)
    {
        _logger.Debug("Stor");
        
        using VariousStoredDataDbAccess access = _variousDbAccessFactory.CreateAccess();
            
        access.SetValue(_tokenDbCalling2 + "_1", _customerId, token);
        access.SetValue(_tokenDbCalling2 + "_2", _customerId, expiryTimeUtc.ToFileTimeUtc().ToString());
        
        access.SaveChanges();
    }
}