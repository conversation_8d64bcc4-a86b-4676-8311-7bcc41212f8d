using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;

using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Misc;
using Invictus.Nomenklatura.Web;

namespace irisdropwebservice.Legacy;

public class EnumAbominationException : Exception
{
    public EnumAbominationException()
    {
    }

    public EnumAbominationException(Type type)
        : base(type.FullName + " invalid enum value")
    {
    }

    public EnumAbominationException(string s)
        : base(s)
    {
    }
}

public class WebRequestWithRetryOld
{
    public static object WebCallWithRetry(ILogger logger, Func<WebRequestWithRetryResult> action/*, CancellationToken ct*/)
    {
        DateTimeOffset startTimeUtc = ServerClock.GetCurrentUtcTime();

        const int INITIAL_RETRY_INTERVAL_MS = 4 * 1000; // 4 sec.
        const int LONG_RETRY_ADDITIONAL_WAIT_MS = 3 * 60 * 1000; // 3 min.
        const int LOG_WARN_AFTER_TOTAL_TRIES_MS = 1 * 60 * 1000; // 1 min.
        const int TOTAL_ATTEMPTS_COUNT = 6;

        short waitALotWasAdded = 0;
        int nextRetryIntervalMs = INITIAL_RETRY_INTERVAL_MS;

        int retryCount = 0;

    _retry:

        void regRetryAttemptAndWait(bool forLong)
        {
            if (nextRetryIntervalMs >= LOG_WARN_AFTER_TOTAL_TRIES_MS)
            {
                logger.Warning($"Cannot get a proper response from remote resource after {(ServerClock.GetCurrentUtcTime() - startTimeUtc).TotalMinutes} minutes.");
            }

            if (++retryCount > TOTAL_ATTEMPTS_COUNT)
            {
                throw new Exception("Out of retry attempts. See [INF], [WRN] above for more information.");
            }

            if (forLong)
            {
                nextRetryIntervalMs += LONG_RETRY_ADDITIONAL_WAIT_MS;
                waitALotWasAdded++;
            }

            logger.Warning($"Retry in {(nextRetryIntervalMs / 1000)} sec.");

            Thread.Sleep(nextRetryIntervalMs);

            // TODO
            //bool cancelled = ct.WaitHandle.WaitOne(nextRetryIntervalMs);

            //if (cancelled)
            //    throw new OperationCanceledException();

            nextRetryIntervalMs = (int)(INITIAL_RETRY_INTERVAL_MS * 1.618 * retryCount) + (waitALotWasAdded * LONG_RETRY_ADDITIONAL_WAIT_MS);
        }

        WebRequestWithRetryResult r = action();

        switch (r.Advice)
        {
            case RetryAdvice.Proceed: return r.Res;

            case RetryAdvice.ThrowFurther:
                if (r.Exception == null)
                    throw new Exception($"{nameof(WebCallWithRetry)} caller should have supplied an exception.");

                throw new AggregateException(r.Exception);

            case RetryAdvice.Wait:
            case RetryAdvice.WaitOnce:
                regRetryAttemptAndWait(false);

                goto _retry;

            case RetryAdvice.WaitALot:
                regRetryAttemptAndWait(true);

                goto _retry;
            case RetryAdvice.RESERVED_FIRST:
            case RetryAdvice.RESERVED_LAST:
            default: throw new EnumAbominationException(typeof(RetryAdvice));
        }
    }
}

public readonly struct WebRequestWithRetryResult
{
    public Exception Exception { get; init; }
    public RetryAdvice Advice { get; init; }
    public object Res { get; init; }

    public static WebRequestWithRetryResult Success(object result)
    {
        return new WebRequestWithRetryResult
        {
            Advice = RetryAdvice.Proceed,
            Res = result
        };
    }

    public static WebRequestWithRetryResult MightRetry(Exception exception)
    {
        return new WebRequestWithRetryResult
        {
            Advice = RetryAdvice.Wait,
            Exception = exception
        };
    }

    public static WebRequestWithRetryResult ThrowFurther(Exception exception)
    {
        return new WebRequestWithRetryResult
        {
            Advice = RetryAdvice.ThrowFurther,
            Exception = exception
        };
    }

    public static WebRequestWithRetryResult FromExceptionAdvice(Exception exception, RetryAdvice advice)
    {
        return new WebRequestWithRetryResult
        {
            Advice = advice,
            Exception = exception
        };
    }
}

public enum ResoluteEventInitialState
    {
        CanFireNow,
        Default,
        CanFireAfterTold
    }

    public abstract class ResoluteEventBase
    {
        private readonly InvAppConfig _appConfig;

        /// <summary>
        /// This method should return as fast as possible.
        /// </summary>
        protected abstract Task OnTrigger(object arg);

        protected abstract object GetCurrentDefaultArg();
        protected abstract ILogger Logger { get; }

        private DateTime _lastFiringTimeEndUtc;
        private CancellationTokenSource _nextDoTriggerEventCancellation;
        private Task _nextDoTriggerEventContinuation;
        private int _relevantTriggerEventVersion;
        private DateTime _nextDoTriggerTimeUtc = DateTime.MinValue;
        private Task _currentFiringTask;
        private bool _isFiringNow;
        private bool _refireRightAfterCurrentFiring;
        private object _refireRightAfterCurrentFiringArg;
        private readonly object _syncRoot = new();
        private bool _isStopping;
        private bool _ignoreFirstError = true;

        public TimeSpan MinInterval { get; }
        public TimeSpan MaxInterval { get; }

        protected ResoluteEventBase(IInvAppLifetime applicationLifetime, InvAppConfig appConfig, TimeSpan minInterval, TimeSpan maxInterval, ResoluteEventInitialState initialState)
        {
            _appConfig = appConfig;

            if (!appConfig.BasicConfiguration.TimersAreAllowed)
                return;

            if (maxInterval < minInterval)
                throw new ArgumentException("maxInterval < minInterval");

            applicationLifetime.ApplicationStopping.Register(this.OnApplicationStopping);

            MinInterval = minInterval;
            MaxInterval = maxInterval;

            _canBeginFiring = initialState != ResoluteEventInitialState.CanFireAfterTold;

            _lastFiringTimeEndUtc = (initialState switch
            {
                ResoluteEventInitialState.CanFireAfterTold  => ServerClock.GetCurrentUtcTime() + TimeSpan.FromDays(1),
                ResoluteEventInitialState.CanFireNow        => ServerClock.GetCurrentUtcTime() - maxInterval,
                ResoluteEventInitialState.Default           => ServerClock.GetCurrentUtcTime(),
                _ => throw new EnumAbominationException(typeof(ResoluteEventInitialState))
            }).DateTime;

            if (initialState != ResoluteEventInitialState.CanFireAfterTold)
                Task.Delay(5).ContinueWithShortThread(this.Initialize);
        }

        private bool _canBeginFiring;

        public void CanBeginFiring()
        {
            if (_canBeginFiring)
                return;

            lock (_syncRoot)
            {
                _lastFiringTimeEndUtc = DateTime.UtcNow - MaxInterval;

                object arg;

                if (_refireRightAfterCurrentFiring)
                {
                    _refireRightAfterCurrentFiring = false;

                    arg = _refireRightAfterCurrentFiringArg;
                } else
                {
                    arg = this.GetCurrentDefaultArg();
                }

                _canBeginFiring = true;

                this.QueueTryTrigger(arg);
            }
        }

        private void Initialize(Task obj)
        {
            this.TryCreateNextTriggerEvent(this.GetCurrentDefaultArg(), false);
        }

        private void OnApplicationStopping()
        {
            lock (_syncRoot)
            {
                _isStopping = true;
            }
        }

        private TimeSpan GetTimeToWait(bool triggerAttempt, DateTime utcNow)
        {
            TimeSpan timePassedFromLastFireEnd = utcNow - _lastFiringTimeEndUtc;
            TimeSpan toWait;

            if (triggerAttempt)
            {
                // Use min time
                toWait = MinInterval - timePassedFromLastFireEnd;
            } else
            {
                // Use max time
                toWait = MaxInterval - timePassedFromLastFireEnd;
            }

            return toWait;
        }

        /// <returns> If Fired immediately </returns>
        private void TryCreateNextTriggerEvent(object arg, bool triggerAttempt)
        {
            lock (_syncRoot)
            {
                if (!_canBeginFiring)
                {
                    Logger.Verbose("TryCreateNextTriggerEvent ISFiringNow");

                    if (_nextDoTriggerEventCancellation != default(CancellationTokenSource))
                        _nextDoTriggerEventCancellation.Cancel();

                    _refireRightAfterCurrentFiring = true;
                    _refireRightAfterCurrentFiringArg = arg;

                    return;
                }

                if (_isFiringNow)
                {
                    Logger.Verbose("TryCreateNextTriggerEvent ISFiringNow");

                    if (_nextDoTriggerEventCancellation != default(CancellationTokenSource))
                        _nextDoTriggerEventCancellation.Cancel();

                    _refireRightAfterCurrentFiring = true;
                    _refireRightAfterCurrentFiringArg = arg;

                    return;
                }

                DateTime utcNow = ServerClock.GetCurrentUtcTime().DateTime;
                TimeSpan toWait = this.GetTimeToWait(triggerAttempt, utcNow);

                string debugInfo =
                    "";
                    // ServerClock.GetCurrentUtcTime() + "_" + new StackTrace() + "_" + DebugUtil.DumpObject(this);

                bool ignoreQueuedEarlier = false;

                if (toWait < TimeSpan.Zero)
                {
                    toWait = TimeSpan.FromMilliseconds(40);
                    ignoreQueuedEarlier = true;
                }

                DateTime thisDoTriggerTimeUtc = utcNow + toWait;

                if (!ignoreQueuedEarlier && _nextDoTriggerTimeUtc < thisDoTriggerTimeUtc && _nextDoTriggerTimeUtc != DateTime.MinValue)
                {
                    if (_ignoreFirstError)
                        return;

                    // Will already execute earlier
                    throw new Exception(
                        $"NextDoTriggerTime is {_nextDoTriggerTimeUtc:hh.mm.ss.ffffff}, while thisDoTriggerTimeUtc is " +
                        $"{thisDoTriggerTimeUtc:hh.mm.ss.ffffff}."
                    );
                }

                _relevantTriggerEventVersion++;
                int thisTriggerEventVersion = _relevantTriggerEventVersion;

                _nextDoTriggerTimeUtc = thisDoTriggerTimeUtc;

                Logger.Debug($"Waiting {toWait} to fire next time, ver = {_relevantTriggerEventVersion}");

                if (_nextDoTriggerEventCancellation != default(CancellationTokenSource))
                    _nextDoTriggerEventCancellation.Cancel();

                _nextDoTriggerEventCancellation = new CancellationTokenSource();

                _nextDoTriggerEventContinuation = Task
                    .Delay(toWait, _nextDoTriggerEventCancellation.Token)
                    .ContinueWithShortThread(
                        _ => this.OnNextFireTimer(arg, thisTriggerEventVersion, triggerAttempt, _nextDoTriggerEventCancellation.Token, debugInfo),
                        TaskContinuationOptions.NotOnCanceled
                    );
            }
        }

        private void PerformTriggerInternal(object arg, string debugInfo)
        {
            lock (_syncRoot)
            {
                if (_isStopping)
                {
                    Logger.Verbose("PerformTriggerInternal IsStopping, discarded");

                    return;
                }

                Logger.Information("Event is being triggered.");

                if (_isFiringNow)
                {
                    Task.Delay(4000).ContinueWith(_ =>
                        GC.Collect()
                    );

                    throw new Exception(
$"PerformTriggerInternal while _isFiringNow. Should not happen. My type is {this.GetType().FullName}. \r\n\r\nDebugInfo when queueing:\r\n{debugInfo}.\r\n\r\nDebugInfo now:\r\n{DebugUtil.DumpObject(this)}"
                    );
                }

                _isFiringNow = true;
                _ignoreFirstError = false;

                _currentFiringTask = this.OnTrigger(arg);
                _currentFiringTask.ContinueWithShortThread(_ => this.OnFireEnd());
            }
        }

        private void OnNextFireTimer(object arg, int triggerEventVersion, bool wasTriggerAttempt, CancellationToken cancellationToken, string debugInfo)
        {
            lock (_syncRoot)
            {
                if (_relevantTriggerEventVersion != triggerEventVersion)
                {
                    Logger.Verbose($"Fired with ver {triggerEventVersion}, revelant ver. is {_relevantTriggerEventVersion}");

                    return;
                }

                _nextDoTriggerTimeUtc = DateTime.MinValue;

                Logger.Verbose($"Firing with ver {triggerEventVersion}.");
                
                if (cancellationToken.IsCancellationRequested)
                    Logger.Error("Cancelled CancellationToken has slipped into OnNextFireTimer.");

                DateTime utcNow = ServerClock.GetCurrentUtcTime().DateTime;
                TimeSpan toWait = this.GetTimeToWait(_refireRightAfterCurrentFiring || wasTriggerAttempt, utcNow);

                if (toWait > TimeSpan.FromMilliseconds(50))
                {
                    Logger.Error($"OnNextFireTimer was called before toWait is less than zero-50ms., mismatch={toWait.TotalMilliseconds} ms. Server timings are way off.");

                    this.TryCreateNextTriggerEvent(arg, wasTriggerAttempt);

                    return;
                }

                this.PerformTriggerInternal(arg, debugInfo);
            }
        }

        private void OnFireEnd()
        {
            Logger.Verbose("OnFireEnd");

            lock (_syncRoot)
            {
                _nextDoTriggerTimeUtc = DateTime.MinValue;

                if (!_isFiringNow)
                    throw new Exception("There must be an error somewhere.");

                _isFiringNow = false;

                if (_currentFiringTask.IsFaulted && !ExceptionUtil.IsPureOrNestedCancellation(_currentFiringTask.Exception))
                {
                    Logger.Warning("_currentFiringTask is Faulted.");

                    if (_currentFiringTask.Exception != null)
                        Logger.Warning(_currentFiringTask.Exception);
                }

                if (_currentFiringTask == null)
                    throw new Exception("There must be an error somewhere.");

                _currentFiringTask = null;

                _lastFiringTimeEndUtc = ServerClock.GetCurrentUtcTime().DateTime;

                if (_isStopping)
                    return;

                bool force = _refireRightAfterCurrentFiring;

                object arg = force
                    ? _refireRightAfterCurrentFiringArg
                    : this.GetCurrentDefaultArg();

                _refireRightAfterCurrentFiring = false;
                this.TryCreateNextTriggerEvent(arg, force);
            }
        }

        /// <summary>
        /// Last arg wins.
        /// </summary>
        /// <param name="arg">Last arg wins.</param>
        /// <returns>If Fired immediately.</returns>
        public void QueueTryTrigger(object arg = null)
        {
            if (!_appConfig.BasicConfiguration.TimersAreAllowed)
                return;

            this.TryCreateNextTriggerEvent(arg, true);
        }

        public void Stop()
        {
            lock (_syncRoot)
            {
                _isStopping = true;
            }
        }

        [MethodImpl(MethodImplOptions.NoInlining | MethodImplOptions.NoOptimization)]
        public void ActivateIfNot()
        {
        }
    }