using System.IO;
using System.Threading;

using irisdropwebservice.AppConfig;

namespace irisdropwebservice.Legacy;

public class LeFileSystemService : FileSystemService
{
    public LeFileSystemService()
        : base(RRAppConfig.FileSystem.BaseDirFullPath)
    {
    }
}

public abstract class FileSystemService
{
    private readonly string _baseDir;

    public Lock SyncRoot { get; } = new();

    protected FileSystemService(string baseDir)
    {
        _baseDir = baseDir;
    }

    private string ModifyPath(string path)
    {
        return Path.Combine(_baseDir, path);
    }

    public void DownloadWithFileStream(string filePath, Action<FileStream> doDownload)
    {
        filePath = this.ModifyPath(filePath);

        lock (SyncRoot)
        {
            File.Delete(filePath);

            string tmpDownloadingFilePath = GetPathToDownload(filePath);
            
            File.Delete(tmpDownloadingFilePath);

            using (var fs = new FileStream(tmpDownloadingFilePath, FileMode.Create))
            {
                doDownload(fs);
            }

            this.FinalizeDownload(filePath);
        }
    }

    public T ReadWithFileStream<T>(string filePath, Func<FileStream, T> doRead)
    {
        filePath = this.ModifyPath(filePath);

        lock (SyncRoot)
        {
            if (!File.Exists(filePath))
                throw new IOException($"Requested file {filePath} does not exist.");

            string tmpDownloadingFilePath = GetPathToDownload(filePath);

            if (File.Exists(tmpDownloadingFilePath))
                throw new IOException($"Requested file {filePath} is either corrupted or not fully written yet.");

            using FileStream fs = File.OpenRead(filePath);

            T res = doRead(fs);

            GC.KeepAlive(fs);

            return res;
        }
    }

    private static string GetPathToDownload(string filePath)
    {
        return filePath + ".tmp";
    }

    private void FinalizeDownload(string filePath)
    {
        lock (SyncRoot)
        {
            File.Delete(filePath);

            string tmpDownloadingFilePath = GetPathToDownload(filePath);

            File.Copy(tmpDownloadingFilePath, filePath);
            File.Delete(tmpDownloadingFilePath);
        }
    }

    public bool FileExistsAndIsOk(string filePath)
    {
        filePath = this.ModifyPath(filePath);

        lock (SyncRoot)
        {
            string tmpDownloadingFilePath = GetPathToDownload(filePath);

            if (File.Exists(tmpDownloadingFilePath))
            {
                File.Delete(filePath);
                File.Delete(tmpDownloadingFilePath);

                return false;
            }

            return File.Exists(filePath);
        }
    }

    public void CopyFileLikeDownloadingTo(string srcPath, string destPath)
    {
        srcPath = this.ModifyPath(srcPath);
        destPath = this.ModifyPath(destPath);

        lock (SyncRoot)
        {
            if (!File.Exists(srcPath))
                throw new Exception($"File {srcPath} does not exist");

            string destTmpPath = GetPathToDownload(destPath);

            File.Copy(srcPath, destTmpPath, true);
            File.Copy(destTmpPath, destPath, true);
            File.Delete(destTmpPath);
        }
    }

    public void DeleteFile(string filePath)
    {
        filePath = this.ModifyPath(filePath);

        lock (SyncRoot)
        {
            string tmpFilePath = GetPathToDownload(filePath);
            
            File.Delete(tmpFilePath);
            File.Delete(filePath);
        }
    }

    public FileInfo[] GetFiles(string subDir, string pattern)
    {
        var logsDirInfo = new DirectoryInfo(subDir == null ? _baseDir : Path.Combine(_baseDir, subDir));

        return logsDirInfo.GetFiles(pattern).ToArray();
    }
}