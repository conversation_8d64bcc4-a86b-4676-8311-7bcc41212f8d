using System.Threading;
using System.Threading.Tasks;

using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Logg;

namespace irisdropwebservice.Legacy;

public class LeBackgroundTasks
{
    private readonly InvTasks _invTasks;
    private readonly IInvAppLifetime _invLifetime;
    private readonly InvAppConfig _invAppConfig;

    public class GenericActionResoluteEvent : ResoluteEventBase
    {
        protected override ILogger Logger => InvLog.Logger<GenericActionResoluteEvent>();

        private readonly Func<Task> _action;

        public GenericActionResoluteEvent(
            IInvAppLifetime applicationLifetime, InvAppConfig appConfig,
            TimeSpan minInterval,
            TimeSpan maxInterval,
            Func<Task> action
        )
            : base(
                applicationLifetime, appConfig,
                minInterval,
                maxInterval,
                ResoluteEventInitialState.Default
            )
        {
            _action = action;
        }

        protected override object GetCurrentDefaultArg()
        {
            return null;
        }

        protected override Task OnTrigger(object arg)
        {
            return _action();
        }
    }
    
    public class FixedTimeEventFactory
    {
        private readonly IInvAppLifetime _hostApplicationLifetime;

        public FixedTimeEventFactory(IInvAppLifetime hostApplicationLifetime)
        {
            _hostApplicationLifetime = hostApplicationLifetime;
        }

        public FixedTimeEvent RunMultipleDailyEvent(Func<Task> action, string actionMethodName, params TimeSpan[] triggerTimesUtc)
        {
            var ev = new FixedTimeEvent(action, TimeSpan.FromDays(1), _hostApplicationLifetime.ApplicationStopping, actionMethodName, triggerTimesUtc);
            ev.Start();

            return ev;
        }
    }

    public LeBackgroundTasks(InvTasks invTasks, IInvAppLifetime invLifetime, InvAppConfig invAppConfig, FixedTimeEventFactory fixedTimeEventFactory)
    {
        _invTasks = invTasks;
        _invLifetime = invLifetime;
        _invAppConfig = invAppConfig;
        _fixedTimeEventFactory = fixedTimeEventFactory;
    }
    
    private record PeriodicBackgroundTaskExecutionItem(GenericActionResoluteEvent Event, WeakReference<object> TimeToLive, Action<object> Action, string LogMethodName);
    
    private readonly FixedTimeEventFactory _fixedTimeEventFactory;

    // ReSharper disable once CollectionNeverQueried.Local
    private readonly List<PeriodicBackgroundTaskExecutionItem> _items = new();

    // ReSharper disable once CollectionNeverQueried.Local
    private readonly List<FixedTimeEvent> _fixedTimeEvents = new();
    
    public void AddFixedTimeOfDayShortRunningForegroundTask(Action action, string logMethodName, params TimeSpan[] timesOfDayUtc)
    {
        FixedTimeEvent ev = _fixedTimeEventFactory.RunMultipleDailyEvent(() => _invTasks.RunShort(action, logMethodName), logMethodName, timesOfDayUtc);

        _fixedTimeEvents.Add(ev);
    }

    public void AddFixedTimeOfDayBackgroundTask(Action action, string logMethodName, params TimeSpan[] timesOfDayUtc)
    {
        FixedTimeEvent ev = _fixedTimeEventFactory.RunMultipleDailyEvent(() => _invTasks.RunBackground(action, logMethodName), logMethodName, timesOfDayUtc);

        _fixedTimeEvents.Add(ev);
    }
    
    public void AddPeriodicBackgroundTask(object ttl, TimeSpan interval, Action<object> action, string logMethodName)
    {
        lock (_items)
        {
            var itemCl = new PeriodicBackgroundTaskExecutionItem[1];

            var ev = new GenericActionResoluteEvent(_invLifetime, _invAppConfig, interval, interval, () => this.OnEventFire(itemCl[0]));

            var item = new PeriodicBackgroundTaskExecutionItem(ev, new WeakReference<object>(ttl), action, logMethodName);

            itemCl[0] = item;

            _items.Add(item);

            ev.ActivateIfNot();
        }
    }
    
    private Task OnEventFire(PeriodicBackgroundTaskExecutionItem item)
    {
        object reference;

        lock (_items)
        {
            if (!item.TimeToLive.TryGetTarget(out reference))
            {
                item.Event.Stop();
                _items.Remove(item);

                return Task.FromCanceled(new CancellationToken(true));
            }
        }

        return _invTasks.RunBackground(() => item.Action(reference), item.LogMethodName);
    }
}