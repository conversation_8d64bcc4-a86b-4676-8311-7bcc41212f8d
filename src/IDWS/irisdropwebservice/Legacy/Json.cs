using System.Collections.Specialized;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;
using Invictus.Nomenklatura.Web;

using Serilog.Core;
using Serilog.Events;

namespace irisdropwebservice.Legacy;

public class RestClientPostParameter
{
    public HttpContent Content { get; set; }
    public string ParameterName { get; init; }
    public string FileName { get; init; }

    public RestClientPostParameter(HttpContent content, string parameterName, string fileName)
    {
        Content = content;
        ParameterName = parameterName;
        FileName = fileName;
    }
}

public class JsonRestClient
{
    private readonly ILogger _logger = InvLog.Logger<JsonRestClient>();

    private readonly Encoding _encoding;
    private readonly InvJsonSerializer _InvJsonSerializer = new ();
    private readonly BasicAsyncRestClient _basicAsyncRestClient;
    private readonly InvJsonSerializer _jsonSerializer = new();

    private readonly Uri _baseUrl;

    public JsonRestClient(InvTasks threadedTasks, string name, Encoding encoding, string baseUrl, bool logAllJsons)
    {
        _encoding = encoding;
        _baseUrl = new Uri(baseUrl);

        ILogger resClientLogger = logAllJsons ? _logger : Logger.None;

        _basicAsyncRestClient = new BasicAsyncRestClient(
            resClientLogger,
            new HttpClient(new HttpClientHandler()),
            encoding
        );
    }

    public void SetAuthorization(string authorizationToken)
    {
        _basicAsyncRestClient.SetAuthorization(authorizationToken);
    }

    public void SetAuthorizationBearer(string authorizationToken)
    {
        _basicAsyncRestClient.SetAuthorizationBearer(authorizationToken);
    }

    public WebResponse<TSuccess, TFail> Post<TSuccess, TFail>(string path, IList<RestClientPostParameter> parameters, bool log, CancellationToken ct)
        where TSuccess : class
        where TFail : class
    {
        var uri = new Uri(_baseUrl, path);

        foreach (RestClientPostParameter t in parameters)
        {
            RestClientPostParameter postParameter = t;

            if (postParameter.Content is not JsonContent jsonContent)
                continue;

            string text = _InvJsonSerializer.SerializeForApis(jsonContent.Value, jsonContent.ObjectType);

            t.Content = new StringContent(text, _encoding, "application/json");
        }

        WebResponse<string, string> response = _basicAsyncRestClient.PostAsync(
            uri,
            parameters,
            null,
            ct
        ).GetAwaiter().GetResult();

        return this.HandleResponse<TSuccess, TFail>(response, log);
    }

    public WebResponse<TSuccess, TFail> Get<TSuccess, TFail>(string path, KeyValuePair<string, string>[] getParameters, bool log, CancellationToken ct)
        where TSuccess : class
        where TFail : class
    {
        var uri = new Uri(_baseUrl, path);
        
        WebResponse<string, string> response = _basicAsyncRestClient.GetAsync(
            uri,
            getParameters ?? Array.Empty<KeyValuePair<string, string>>(),
            true,
            ct
        ).GetAwaiter().GetResult();

        return this.HandleResponse<TSuccess, TFail>(response, log);
    }

    private WebResponse<TSuccess, TFail> HandleResponse<TSuccess, TFail>(WebResponse<string, string> response, bool log)
        where TSuccess : class
        where TFail : class
    {
        string answer = response.Fail ?? response.Success;
        string sanitizedAnswer;
        
        try
        {
            sanitizedAnswer = InvJsonSerializer.PrettifyJson(answer);
        }
        catch (Exception)
        {
            sanitizedAnswer = answer;
        }
        
        try
        {
            if (log)
                _logger.Debug("Response: \r\n\r\n" + sanitizedAnswer);

            if (response.IsSuccess)
            {
                var res = _jsonSerializer.DeserializeForApis<TSuccess>(answer);

                return WebResponse<TSuccess, TFail>.SuccessNew(res, response.StatusCode);
            } else
            {
                var res = _jsonSerializer.DeserializeForApis<TFail>(answer);

                return WebResponse<TSuccess, TFail>.FailNew(res, response.StatusCode);
            }
        }
        catch (Exception)
        {
            _logger.Error("Response was: " + sanitizedAnswer);

            // if (ex.Any(e => e is JsonReaderException or JsonSerializationException))
            //     throw new Exception("Could not parse json response: " + answer + "\r\n Exception: " + ExceptionUtil.ExceptionToMessage(ex));

            throw;
        }
    }
}

public class BasicAsyncRestClient
{
    private readonly ILogger _logger;

    private readonly HttpClient _httpClient;
    private readonly Encoding _encoding;

    public BasicAsyncRestClient(ILogger logger, HttpClient httpClient, Encoding encoding)
    {
        _logger = logger;
        _httpClient = httpClient;
        _encoding = encoding;
    }

    public void SetAuthorization(string authorizationToken)
    {
        _httpClient.DefaultRequestHeaders.Add("Authorization", authorizationToken);
    }

    public void SetAuthorizationBearer(string authorizationToken)
    {
        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", authorizationToken);
    }

    public Task<WebResponse<string, string>> GetAsync(Uri uri, IEnumerable<KeyValuePair<string, string>> parameters, bool log, CancellationToken ct)
    {
        var uriBuilder = new UriBuilder(uri);

        NameValueCollection query = HttpUtility.ParseQueryString(uriBuilder.Query);

        foreach (KeyValuePair<string, string> kv in parameters)
        {
            query[kv.Key] = kv.Value;
        }

        uriBuilder.Query = query.ToString();

        Uri url = uriBuilder.Uri;

        if (log)
        {
            _logger.Debug("GET request: {Url}", url);
        }

        return this.CallAsync(() => _httpClient.GetAsync(url, ct), ct);
    }

    public Task<WebResponse<string, string>> PostAsync(Uri uri, IList<RestClientPostParameter> parameters, IList<KeyValuePair<string, string>> headers, CancellationToken ct)
    {
        if (_logger.IsEnabled(LogEventLevel.Debug))
        {
            _logger.Debug("POST request: {Uri}{NewLine}", uri, Environment.NewLine);
        }

        if (headers is not null && headers.Any())
        {
            headers.ToList()
                .ForEach(header =>
                {
                    if (header.Key.ToLower() == "content-type")
                    {
                        _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(header.Value));
                    }
                    else
                    {
                        _httpClient.DefaultRequestHeaders.Add(header.Key, header.Value);
                    }
                });
        }

        HttpContent requestContent;

        if (parameters.Count == 1 && string.IsNullOrWhiteSpace(parameters[0].ParameterName))
        {
            requestContent = parameters[0].Content;
        }
        else
        {
            requestContent = new MultipartFormDataContent();

            foreach (RestClientPostParameter pp in parameters)
            {
                if (pp.FileName == null)
                {
                    ((MultipartFormDataContent)requestContent).Add(pp.Content, pp.ParameterName);
                }
                else
                {
                    ((MultipartFormDataContent)requestContent).Add(pp.Content, pp.ParameterName, pp.FileName);
                }
            }
        }

        return this.CallAsync(() => _httpClient.PostAsync(uri, requestContent, ct), ct);
    }

    private async Task<WebResponse<string, string>> CallAsync(Func<Task<HttpResponseMessage>> method, CancellationToken ct)
    {
        HttpResponseMessage response = await method()
            .ConfigureAwait(false);

        byte[] bytes = await response.Content.ReadAsByteArrayAsync(ct)
            .ConfigureAwait(false);

        string content = _encoding.GetString(bytes, 0, bytes.Length);

        if (_logger.IsEnabled(LogEventLevel.Verbose))
        {
            // _logger.Debug("Response:{NewLine}{PrettyJson}", Environment.NewLine, Utilities.PrettyPrintJson(content));
        }

        // Uri requestUri = response.RequestMessage?.RequestUri;

        return response.IsSuccessStatusCode
            ? WebResponse<string, string>.SuccessNew(content, response.StatusCode)
            : WebResponse<string, string>.FailNew(content, response.StatusCode);
    }

    public void Dispose() => _httpClient?.Dispose();
}