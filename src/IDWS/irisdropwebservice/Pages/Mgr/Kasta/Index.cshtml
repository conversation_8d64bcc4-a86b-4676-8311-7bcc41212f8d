@using irisdropwebservice.Libs.KastaSharp
@using Microsoft.AspNetCore.Mvc.TagHelpers
@model irisdropwebservice.Models.KastaMgrModel

@{
    ViewData["Title"] = "Index";
}

<h1>Index</h1>

<p>
    <a asp-action="Create">Create New</a>
</p>

<div id="treeView"></div>

<script>
    $(function () {
        $('#treeView').jstree({
            'core': {
                 "themes": {
                    "variant": "dark"
                },
                'data': {
                    'url': '/mgr/kasta/cat_nodes',
                    'data': function (node) {
                        return { 'id': node.id };
                    }
                }
            },
            "checkbox": {
                "keep_selected_style": false
            },
            "plugins": ["wholerow", "checkbox"]
        });

        // Handle node selection (optional)
        /*$('#treeView').on("select_node.jstree", function (e, data) {
            var node = data.node;

         alert("Selected node: " + node.text);
        });*/

        $('#treeView')
          .on('changed.jstree', function (e, data) {
            var i, j, r = [];
            for(i = 0, j = data.selected.length; i < j; i++) {
              r.push(data.instance.get_node(data.selected[i]).text);
            }
            $('#event_result').html('Selected: ' + r.join(', '));
          })
          .jstree();
        });
</script>

<!--<table class="table">
    <thead>
    <tr>
        <th></th>
    </tr>
    </thead>
    <tbody>
    //@foreach (KastaMyCategorySchema item in Model.CategorySchemas) {
        <tr>
            <td>
                @item.Category.Name
            </td>
        </tr>
    }
    </tbody>
</table>
-->