using System.Text.Json.Serialization;

using irisdropwebservice.Libs.KastaSharp;

namespace irisdropwebservice.Models
{
    public class KastaMgrModel
    {
        public KastaMyCategorySchema[] CategorySchemas { get; set; }
        public List<CategorySchemasTreeNode> TreeNodes { get; set; }
    }

    [Serializable]
    public class CategorySchemasTreeNode
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("text")]
        public string Text { get; set; }

        [JsonPropertyName("children")]
        public List<CategorySchemasTreeNode> Children { get; set; } = new();
    }
}
