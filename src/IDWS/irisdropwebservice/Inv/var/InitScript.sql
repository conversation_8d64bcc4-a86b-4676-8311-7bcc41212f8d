-- <PERSON><PERSON><PERSON> to create the schema for INV database (compatible with SQL Server 2017)

USE [INV];
GO

-- Table: InvLanguage
CREATE TABLE InvLanguage (
                             LangId INT IDENTITY(1,1) PRIMARY KEY,
                             Iso6393 CHAR(3) NOT NULL
);
GO

INSERT INTO InvLanguage (Iso6393) VALUES ('ukr');
GO

-- Table: InvTranslate
CREATE TABLE InvTranslate (
                              TransId INT IDENTITY(1,1) PRIMARY KEY,
                              LangId INT NOT NULL FOREIGN KEY REFERENCES InvLanguage(LangId),
    [Key] NVARCHAR(80) NOT NULL,
    [Value] VARCHAR(MAX) NOT NULL
);
GO

-- Table: InvPlatform
CREATE TABLE InvPlatform (
                             PlatformId INT IDENTITY(1,1) PRIMARY KEY,
    [Name] NVARCHAR(255) NOT NULL,
    TransId INT NOT NULL FOREIGN KEY REFERENCES InvTranslate(TransId)
    );
GO

-- Table: InvCustomer
CREATE TABLE InvCustomer (
                             CustomerId INT IDENTITY(1,1) PRIMARY KEY,
    [Name] NVARCHAR(255) NOT NULL
    );
GO

-- Table: InvSpace
CREATE TABLE InvSpace (
                          SpaceId INT IDENTITY(1,1) PRIMARY KEY,
                          CustId INT NOT NULL FOREIGN KEY REFERENCES InvCustomer(CustomerId),
                          SpaceNumber NVARCHAR(50) NOT NULL,
    [Name] NVARCHAR(255) NOT NULL,
    TransId INT FOREIGN KEY REFERENCES InvTranslate(TransId)
    );
GO

-- Table: InvPlatformCategory
CREATE TABLE InvPlatformCategory (
                                     PlatformCategoryId INT IDENTITY(1,1) PRIMARY KEY,
                                     TransId INT NOT NULL FOREIGN KEY REFERENCES InvTranslate(TransId)
);
GO

-- Table: InvPlatformFeature
CREATE TABLE InvPlatformFeature (
                                    InvPlatformFeatureId INT IDENTITY(1,1) PRIMARY KEY,
                                    InvPlatformId INT NOT NULL FOREIGN KEY REFERENCES InvPlatform(PlatformId),
    [Name] NVARCHAR(255) NOT NULL,
    PlatformData BINARY(1000) NOT NULL,
    IsMandatory BIT NOT NULL,
    IsVisible BIT NOT NULL,
    TypeFlags TINYINT NOT NULL
    );
GO

-- Table: InvPlatformFeatureSelectValue
CREATE TABLE InvPlatformFeatureSelectValue (
                                               InvPlatformFeatureSelectValueId INT IDENTITY(1,1) PRIMARY KEY,
                                               TransId INT NOT NULL FOREIGN KEY REFERENCES InvTranslate(TransId),
                                               PlatformData BINARY(1000) NOT NULL
);
GO

-- Insert Platforms with translations in 'ukr'
DECLARE @LangId INT;
SELECT @LangId = LangId FROM InvLanguage WHERE Iso6393 = 'ukr';

INSERT INTO InvTranslate (LangId, [Key], [Value]) VALUES
                                                      (@LangId, 'VkCom', 'VK'),
                                                      (@LangId, 'PromUA', 'prom.ua'),
                                                      (@LangId, 'KastaUA', 'Kasta.UA'),
                                                      (@LangId, 'PrestashopIDWS', 'Сайт'),
                                                      (@LangId, 'EpicentrUA', 'EpicentrK'),
                                                      (@LangId, 'RozetkaUA', 'Rozekta');

INSERT INTO InvPlatform ([Name], TransId)
SELECT [Key], TransId FROM InvTranslate WHERE LangId = @LangId;
GO

-- Insert Customers
INSERT INTO InvCustomer ([Name]) VALUES
('TEST'),
('IRISDROP'),
('IRIS'),
('BESTBABY');
GO

-- Insert Spaces with 'Default' Name and Translation
DECLARE @CustomerTransId INT;
DECLARE @CustomerId INT;
        
DECLARE @LangId INT;
SELECT @LangId = LangId FROM InvLanguage WHERE Iso6393 = 'ukr';

-- Add Space for TEST
SELECT @CustomerId = CustomerId FROM InvCustomer WHERE [Name] = 'TEST';
INSERT INTO InvTranslate (LangId, [Key], [Value]) VALUES (@LangId, 'Cust_Test', 'Test');
SELECT @CustomerTransId = TransId FROM InvTranslate WHERE [Key] = 'Cust_Test';
INSERT INTO InvSpace (CustId, SpaceNumber, [Name], TransId) VALUES (@CustomerId, '1', 'Default', @CustomerTransId);

-- Add Space for IRISDROP
SELECT @CustomerId = CustomerId FROM InvCustomer WHERE [Name] = 'IRISDROP';
INSERT INTO InvTranslate (LangId, [Key], [Value]) VALUES (@LangId, 'Cust_IrisDrop', 'IrisDrop');
SELECT @CustomerTransId = TransId FROM InvTranslate WHERE [Key] = 'Cust_IrisDrop';
INSERT INTO InvSpace (CustId, SpaceNumber, [Name], TransId) VALUES (@CustomerId, '1', 'Default', @CustomerTransId);

-- Add Space for IRIS
SELECT @CustomerId = CustomerId FROM InvCustomer WHERE [Name] = 'IRIS';
INSERT INTO InvTranslate (LangId, [Key], [Value]) VALUES (@LangId, 'Cust_Iris', 'IRIS');
SELECT @CustomerTransId = TransId FROM InvTranslate WHERE [Key] = 'Cust_Iris';
INSERT INTO InvSpace (CustId, SpaceNumber, [Name], TransId) VALUES (@CustomerId, '1', 'Default', @CustomerTransId);

-- Add Space for BESTBABY
SELECT @CustomerId = CustomerId FROM InvCustomer WHERE [Name] = 'BESTBABY';
INSERT INTO InvTranslate (LangId, [Key], [Value]) VALUES (@LangId, 'Cust_BestBaby', 'BestBaby');
SELECT @CustomerTransId = TransId FROM InvTranslate WHERE [Key] = 'Cust_BestBaby';
INSERT INTO InvSpace (CustId, SpaceNumber, [Name], TransId) VALUES (@CustomerId, '1', 'Default', @CustomerTransId);
GO
