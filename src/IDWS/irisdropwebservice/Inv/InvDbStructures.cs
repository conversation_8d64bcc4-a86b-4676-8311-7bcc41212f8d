using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace irisdropwebservice.Inv;

public class InvLanguage
{
    [Key]
    public int LangId { get; set; }

    [Required]
    [StringLength(3)]
    public string Iso6393 { get; set; }

    public ICollection<InvTranslate> Translates { get; set; }
}

public class InvTranslate
{
    [Key]
    public int TransId { get; set; }

    [Required]
    public int LangId { get; set; }

    [ForeignKey("LangId")]
    public InvLanguage Language { get; set; }

    [Required]
    [StringLength(80)]
    public string Key { get; set; }

    [Required]
    public string Value { get; set; }
}

public class InvPlatform
{
    [Key]
    public int PlatformId { get; set; }

    [Required]
    [StringLength(255)]
    public string Name { get; set; }

    [Required]
    public int TransId { get; set; }

    [ForeignKey("TransId")]
    public InvTranslate Translation { get; set; }
}

public class InvCustomer
{
    [Key]
    public int CustomerId { get; set; }

    [Required]
    [StringLength(255)]
    public string Name { get; set; }

    public ICollection<InvSpace> Spaces { get; set; }
}

public class InvSpace
{
    [Key]
    public int SpaceId { get; set; }

    [Required]
    public int CustId { get; set; }

    [ForeignKey("CustId")]
    public InvCustomer Customer { get; set; }

    [Required]
    [StringLength(50)]
    public string SpaceNumber { get; set; }

    [Required]
    [StringLength(255)]
    public string Name { get; set; }

    public int? TransId { get; set; }

    [ForeignKey("TransId")]
    public InvTranslate Translation { get; set; }
}

public class InvPlatformCategory
{
    [Key]
    public int PlatformCategoryId { get; set; }

    [Required]
    public int TransId { get; set; }

    [ForeignKey("TransId")]
    public InvTranslate Translation { get; set; }
}

public class InvPlatformFeature
{
    [Key]
    public int InvPlatformFeatureId { get; set; }

    [Required]
    public int InvPlatformId { get; set; }

    [ForeignKey("InvPlatformId")]
    public InvPlatform Platform { get; set; }

    [Required]
    [StringLength(255)]
    public string Name { get; set; }

    [Required]
    public byte[] PlatformData { get; set; }

    [Required]
    public bool IsMandatory { get; set; }

    [Required]
    public bool IsVisible { get; set; }

    [Required]
    public byte TypeFlags { get; set; }
}

public class InvPlatformFeatureSelectValue
{
    [Key]
    public int InvPlatformFeatureSelectValueId { get; set; }

    [Required]
    public int TransId { get; set; }

    [ForeignKey("TransId")]
    public InvTranslate Translation { get; set; }

    [Required]
    public byte[] PlatformData { get; set; }
}
