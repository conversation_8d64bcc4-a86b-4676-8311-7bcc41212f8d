using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.ExteriorServ;

using irisdropwebservice.AppConfig;
using irisdropwebservice.Legacy;
using irisdropwebservice.Services.Auth.Ins;
using irisdropwebservice.Services.PersistentTasks;
using irisdropwebservice.Services.Statistics;
using irisdropwebservice.Services.Statistics.Ins;

using Microsoft.Extensions.DependencyInjection;

namespace irisdropwebservice.Services.ThisApp;

public class IdwsExteriorServiceBuilder : ExteriorServiceBuilderBase<IdwsExteriorService>
{
    protected override void AddConcealedServices(ServiceCollection concealedServiceCollection)
    {
        concealedServiceCollection.AddSingleton<LeFileSystemService>();
        concealedServiceCollection.AddSingleton<LeBackgroundTasks>();
        concealedServiceCollection.AddSingleton<LeBackgroundTasks.FixedTimeEventFactory>();

        concealedServiceCollection.AddSingleton(typeof(IDbAccessFactory<AuthorizationDbAccess>), typeof(DbAccessFactory<AuthorizationDbAccess>));
        concealedServiceCollection.AddSingleton(typeof(IDbAccessFactory<StatisticsDbAccess>),    typeof(DbAccessFactory<StatisticsDbAccess>));

        concealedServiceCollection.AddSingleton<IStatisticsService, StatisticsService>();
        concealedServiceCollection.AddSingleton<IIrisOfficeService, IrisOfficeService>();
        
        concealedServiceCollection.AddSingleton(typeof(IDbAccessFactory<GenericPersistentTaskDbAccess>), typeof(DbAccessFactory<GenericPersistentTaskDbAccess>));
        concealedServiceCollection.AddSingleton<PersistentTasksResoluteEvent>();
        concealedServiceCollection.AddSingleton<PersistentTasksService>();
        concealedServiceCollection.AddSingleton<PersistentTasksService.Executor>();
        
        base.AddConcealedServices(concealedServiceCollection);
    }

    protected override void ExposeConcealedServices()
    {
        this.ExposeSingleton<LeFileSystemService>();
        this.ExposeSingleton<LeBackgroundTasks>();
        this.ExposeSingleton<LeBackgroundTasks.FixedTimeEventFactory>();
        
        this.ExposeSingleton<IDbAccessFactory<AuthorizationDbAccess>>();
        this.ExposeSingleton<IDbAccessFactory<StatisticsDbAccess>>();  
        
        this.ExposeSingleton<IStatisticsService>();
        this.ExposeSingleton<IIrisOfficeService>();
        
        this.ExposeSingleton<PersistentTasksService>();
        this.ExposeSingleton<PersistentTasksService.Executor>();
        
        base.ExposeConcealedServices();
    }
}