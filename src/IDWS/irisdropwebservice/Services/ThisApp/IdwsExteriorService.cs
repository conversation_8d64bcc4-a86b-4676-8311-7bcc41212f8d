using System.Diagnostics;
using System.Threading.Tasks;

using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.ExteriorServ;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.AppConfig;
using irisdropwebservice.Services.Chats;
using irisdropwebservice.Services.LinkService;
using irisdropwebservice.Services.PersistentTasks;
using irisdropwebservice.Services.Statistics;

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace irisdropwebservice.Services.ThisApp;

public class IdwsExteriorService : IExteriorService
{
    private readonly ILogger _logger = InvLog.Logger<IdwsExteriorService>();

    private readonly IServiceProvider _serviceProvider;
    private readonly IStatisticsService _statisticsService;
    private readonly Stopwatch _startupTimeStopwatch;
    
    public IdwsExteriorService(IServiceProvider serviceProvider, IIrisOfficeService irisOfficeService, IStatisticsService statisticsService)
    {
        _serviceProvider = serviceProvider;
        _statisticsService = statisticsService;
        _startupTimeStopwatch = Stopwatch.StartNew();
        
        var telegramChatBotWorker = _serviceProvider.GetService<ITelegramSendWorker>();

        telegramChatBotWorker?.Run(w => w.PostToTelegramFromStaffAccount("IDWS starting", CCAppConfig.TelegramIrisDrop.DevChat, null));
    }
    
    public Task Run()
    {
        _serviceProvider.GetRequiredService<IInvAppLifetime>().ApplicationStarting.Register(this.OnAppStarting);
        
        _serviceProvider.GetRequiredService<IHostApplicationLifetime>().ApplicationStopping.Register(this.OnStopping);
        
        return Task.CompletedTask;
    }

    // Wait for this during autotest.
    internal const string APP_HAS_FULLY_STARTED_TELEGRAM_MSG = "IDWS started";

    private void OnAppStarting()
    {
        if (_serviceProvider.GetRequiredService<IInvAppLifetime>().ApplicationStopping.IsCancellationRequested)
            return;
        
        var linkExteriorService = _serviceProvider.GetRequiredService<LinkExteriorService>();
        
        linkExteriorService.Run2().ContinueWith(_ => {
            _statisticsService.Run();
            
            var chatsExteriorService = _serviceProvider.GetRequiredService<ChatsExteriorService>();
            chatsExteriorService.Activate();

            var persistentTasksService = _serviceProvider.GetRequiredService<PersistentTasksService>();
            persistentTasksService.AllowExecution();

            _startupTimeStopwatch.Stop();

            _logger.Information($"App has fully started in {_startupTimeStopwatch.Elapsed}");

            var telegramChatBotWorker = _serviceProvider.GetRequiredService<ITelegramSendWorker>();

            telegramChatBotWorker.Run(w => w.PostToTelegramFromStaffAccount(APP_HAS_FULLY_STARTED_TELEGRAM_MSG, CCAppConfig.TelegramIrisDrop.DevChat, null));
        });
    }

    private void OnStopping()
    {
        var telegramChatBotWorker = _serviceProvider.GetService<ITelegramSendWorker>();

        telegramChatBotWorker?.Run(w => w.PostToTelegramFromStaffAccount("IDWS stopping", CCAppConfig.TelegramIrisDrop.DevChat, null));
    }
}