using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Misc;

using Microsoft.EntityFrameworkCore;

namespace irisdropwebservice.Services.Statistics.Ins
{
    public class StatisticsDbAccess : DbAccessBase
    {
        private readonly MyDbContext _dbContext;

        public StatisticsDbAccess(DbAccessOptions accessOptions, MyDbContext context)
            : base(accessOptions, context)
        {
            _dbContext = context;
        }

        public void InsertStatisticsItem(OperationType opType, StatSource statSource, object p1 = null, object p2 = null, object p3 = null)
        {
            if (statSource == StatSource.Invalid)
                throw new Exception("StatSource is invalid.");

            _dbContext.StatisticsItems.Add(new StatisticsItem()
                {
                    DateTime = ServerClock.GetCurrentUtcTime().DateTime,
                    OperationType = opType,
                    StatSource = statSource,
                    Parameter1 = p1 == null ? null : (p1 as string ?? p1.ToString()),
                    Parameter2 = p2 == null ? null : (p2 as string ?? p2.ToString()),
                    Parameter3 = p3 == null ? null : (p3 as string ?? p3.ToString())
                }
            );
        }

        public void InsertStatisticsWasFlushed(OperationType flushOperationType, DateTime fetchStatsTime, string argument)
        {
            _dbContext.StatisticsItems.Add(new StatisticsItem()
                {
                    DateTime = fetchStatsTime,
                    OperationType = flushOperationType,
                    Parameter1 = argument,
                    StatSource = StatSource.Application
                }
            );
        }

        public StatisticsItem GetLastFlush(OperationType flushOperationType)
        {
            StatisticsItem lastFlush = _dbContext.StatisticsItems.AsNoTracking()
                .OrderByDescending(x => x.Id)
                .FirstOrDefault(si => si.OperationType == flushOperationType);

            return lastFlush;
        }

        public DateTime GetLastFlushDateTimeUtc(OperationType flushOperationType)
        {
            StatisticsItem lastFlush = this.GetLastFlush(flushOperationType);

            return lastFlush?.DateTime ?? DateTime.MinValue;
        }

        public StatisticsItem[] GetStatisticsItemsSinceLastFlush(OperationType flushOperationType, DateTime currentTimeFixedUtc, OperationType[] limitToOperationTypes, out DateTime? dateFromUtc)
        {
            StatisticsItem lastFlush = _dbContext.StatisticsItems.AsNoTracking()
                .OrderByDescending(x => x.Id)
                .FirstOrDefault(si => si.OperationType == flushOperationType);

            dateFromUtc = lastFlush?.DateTime;

            DateTime fromUtc = lastFlush?.DateTime ?? DateTime.MinValue;
            DateTime toUtc = currentTimeFixedUtc;

            IQueryable<StatisticsItem> query = _dbContext.StatisticsItems.AsNoTracking()
                .Where(si => si.DateTime > fromUtc && si.DateTime < toUtc);

            if (limitToOperationTypes != null)
            {
                query = query.Where(si => limitToOperationTypes.Contains(si.OperationType));
            }

            StatisticsItem[] res = query.ToArray();

            return res;
        }

        public void ExecuteDeleteStatisticsItemsBeforeDate(DateTime dateTimeUtc)
        {
            _dbContext.StatisticsItems.AsNoTracking().Where(s => s.DateTime < dateTimeUtc).ExecuteDelete();
        }

        public class MyDbContext : DbContext
        {
            public virtual DbSet<StatisticsItem> StatisticsItems { get; set; }

            public MyDbContext(DbContextOptions<MyDbContext> options)
                : base(options)
            {
            }

            protected override void OnModelCreating(ModelBuilder modelBuilder)
            {
                base.OnModelCreating(modelBuilder);
            }
        }
    }
}