using System.ComponentModel.DataAnnotations.Schema;

namespace irisdropwebservice.Services.Statistics.Ins
{
    public enum OperationType : short
    {
        None = 0,
        NotUsed = 1,

        StatFlushStats_CustomerErrors = 10,
        StatFlushStats2 = 11,
        StatFlushStatsDevWarnings = 12,
        StatFlushStatsVkAvaliReport = 13,
        StatFlushStats1_1 = 14,

        LogWarning = 40,
        LogErrorOrHigher = 41,

        VkPhotoUpdated = 200,
        VkPhotoCreated = 201,
        VkPhotoDeleted = 202,
        VkScrapAndParse = 204,

        PrestaProductCreatedNew = 401,
        PrestaProductUpdated = 402,
        PrestaProductInactivatedDueToParsingError = 403,
        PrestaProductInactivatedDueToOtherReasons = 404,
        PrestaProductException = 406,
        PrestaVk2PrestaProductPush = 407
    }

    [Table("StatisticsLog")]
    public class StatisticsItem
    {
        public long Id { get; set; }

        public DateTime DateTime { get; set; }

        public StatSource StatSource { get; set; }
        public OperationType OperationType { get; set; }

        public string Parameter1 { get; set; }
        public string Parameter2 { get; set; }
        public string Parameter3 { get; set; }
    }
}