using System.Threading.Tasks;

using irisdropwebservice.AppConfig;
using irisdropwebservice.AppConfig.ClassConfigurations;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Misc;

using irisdropwebservice.Legacy;
using irisdropwebservice.Services.Chats;
using irisdropwebservice.Services.LinkService;
using irisdropwebservice.Services.Statistics.Ins;

namespace irisdropwebservice.Services.Statistics
{
    public interface IStatisticsService
    {
        IPublicStatisticsFlushContext PublicFlushContext { get; }

        void Run();

        void SignalLogWarningHappened(string text);
        void SignalLogErrorHappened(string text);
        void SignalVkPhotoWasUpdated(StatSource statSource, long photoId);
        void SignalVkPhotoWasCreated(StatSource statSource, long photoId);
        void SignalVkPhotoWasDeleted(StatSource statSource, long photoId);
        void SignalScrapAndParseHappened(StatSource statSource);
        void SignalPrestaVk2PrestaProductPushHappened(StatSource statSource);
        void SignalPrestaProductCreatedNew(StatSource statSource, long productId, string art, string vkSitePhotoUrl);
        void SignalPrestaSiteProductInactivatedDueToParsingError(StatSource statSource, long productId);
        void SignalPrestaProductInactivatedDueToOtherReasons(StatSource statSource, long productId);
        void PrestaSiteProductUpdated(StatSource statSource, long productId);
        void PrestaSiteProductComputationException(StatSource statSource, long productId);
    }

    public interface IPublicStatisticsFlushContext
    {
        void InsertStatisticsWasFlushed(OperationType flushStatType, DateTime fetchStatsTime, string argument);
        (DateTime, string) GetLastFlushTimeAndArgumentUtc(OperationType flushStatType);
    }

    public class StatisticsService : IStatisticsService
    {
        private readonly IDbAccessFactory<StatisticsDbAccess> _dbAccessFactory;
        private readonly ITelegramSendWorker _telegramSendWorker;
        private readonly InvTasks _threadedTasks;
        private readonly LeBackgroundTasks _backgroundTasks;
        private readonly GlobalUserErrorsManager _globalUserErrorsManager;
        private readonly TelegramChatBotPolling _telegramChatBotPolling;
        private readonly object _syncRoot = new();

        private readonly StatisticsCCInfo _configuration = CCAppConfig.Statistics;

        // ReSharper disable once NotAccessedField.Local
        private StatisticsFlushContext[] _flushContexts;

        public IPublicStatisticsFlushContext PublicFlushContext { get; }

        public StatisticsService(
            IDbAccessFactory<StatisticsDbAccess> dbAccessFactory,
            ITelegramSendWorker telegramSendWorker,
            InvTasks threadedTasks,
            LeBackgroundTasks backgroundTasks,
            GlobalUserErrorsManager globalUserErrorsManager,
            TelegramChatBotPolling telegramChatBotPolling
        )
        {
            _dbAccessFactory = dbAccessFactory;
            _telegramSendWorker = telegramSendWorker;
            _threadedTasks = threadedTasks;
            _backgroundTasks = backgroundTasks;
            _globalUserErrorsManager = globalUserErrorsManager;
            _telegramChatBotPolling = telegramChatBotPolling;

            PublicFlushContext = new PublicStatisticsFlushContext(_dbAccessFactory, _syncRoot);
        }

        public void Run()
        {
            _flushContexts = new StatisticsFlushContext[]
            {
                new StatisticsFlushCustomerErrors(_telegramSendWorker, _dbAccessFactory, _globalUserErrorsManager,_backgroundTasks, _syncRoot, _configuration),
                new StatisticsFlushContext1_1(_telegramSendWorker, _dbAccessFactory, _backgroundTasks, _syncRoot, _configuration),

                // new StatisticsFlushContext2(_telegramSendWorker, _dbAccessFactory, _threadedTasks, _syncRoot, _configuration),

                new StatisticsFlushContextDevWarnings(_telegramSendWorker, _dbAccessFactory, _backgroundTasks, _syncRoot, _configuration),

                // new StatisticsFlushContextDevPerf(_telegramSendWorker, _dbAccessFactory, _threadedTasks, _syncRoot)
            };

            foreach (StatisticsFlushContext statisticsFlushContext in _flushContexts)
            {
                if (statisticsFlushContext is ITelegramDialogueHandler dialogueHandler)
                {
                    _telegramChatBotPolling.AddDialogueHandler(dialogueHandler);
                }
                if (statisticsFlushContext is ITelegramGroupListener groupListener)
                {
                    _telegramChatBotPolling.AddGroupListener(groupListener);
                }
            }

            _backgroundTasks.AddPeriodicBackgroundTask(this,
                TimeSpan.FromMinutes(56),
                (_) => this.CleanupOldStatisticsOnBackgroundThread(),
                nameof(StatisticsService) + "::" + nameof(this.CleanupOldStatisticsOnBackgroundThread)
            );
        }

        // ...
        // General
        // ...

        private void InsertStatisticsItem(OperationType opType, StatSource statSource, object p1 = null, object p2 = null, object p3 = null)
        {
            lock (_syncRoot)
            {
                using StatisticsDbAccess dbAccess = _dbAccessFactory.CreateAccess();
                dbAccess.InsertStatisticsItem(opType, statSource, p1, p2, p3);
                dbAccess.SaveChanges();
            }
        }

        public void SignalLogWarningHappened(string text)
        {
            this.InsertStatisticsItem(OperationType.LogWarning, StatSource.Application, text);
        }

        public void SignalLogErrorHappened(string text)
        {
            this.InsertStatisticsItem(OperationType.LogErrorOrHigher, StatSource.Application, text);
        }

        // ...
        // Vk
        // ...

        public void SignalVkPhotoWasUpdated(StatSource statSource, long photoId)
        {
            this.InsertStatisticsItem(OperationType.VkPhotoUpdated, statSource, photoId);
        }

        public void SignalVkPhotoWasCreated(StatSource statSource, long photoId)
        {
            this.InsertStatisticsItem(OperationType.VkPhotoCreated, statSource, photoId);
        }

        public void SignalVkPhotoWasDeleted(StatSource statSource, long photoId)
        {
            this.InsertStatisticsItem(OperationType.VkPhotoDeleted, statSource, photoId);
        }

        public void SignalScrapAndParseHappened(StatSource statSource)
        {
            this.InsertStatisticsItem(OperationType.VkScrapAndParse, statSource);
        }

        // ...
        // Presta
        // ...

        public void SignalPrestaVk2PrestaProductPushHappened(StatSource statSource)
        {
            this.InsertStatisticsItem(OperationType.PrestaVk2PrestaProductPush, statSource);
        }

        public void SignalPrestaProductCreatedNew(StatSource statSource, long productId, string art, string vkSitePhotoUrl)
        {
            this.InsertStatisticsItem(OperationType.PrestaProductCreatedNew, statSource, productId, art, vkSitePhotoUrl);
        }

        public void SignalPrestaSiteProductInactivatedDueToParsingError(StatSource statSource, long productId)
        {
            this.InsertStatisticsItem(OperationType.PrestaProductInactivatedDueToParsingError, statSource, productId);
        }

        public void SignalPrestaProductInactivatedDueToOtherReasons(StatSource statSource, long productId)
        {
            this.InsertStatisticsItem(OperationType.PrestaProductInactivatedDueToOtherReasons, statSource, productId);
        }

        public void PrestaSiteProductUpdated(StatSource statSource, long productId)
        {
            this.InsertStatisticsItem(OperationType.PrestaProductUpdated, statSource, productId);
        }

        public void PrestaSiteProductComputationException(StatSource statSource, long productId)
        {
            this.InsertStatisticsItem(OperationType.PrestaProductException, statSource, productId);
        }

        private void CleanupOldStatisticsOnBackgroundThread()
        {
            DateTime cutoffUtc = ServerClock.GetCurrentUtcTime().AddDays(-_configuration.KeepStatisticsFor_Days).DateTime;

            using StatisticsDbAccess dbAccess = _dbAccessFactory.CreateAccess();
            dbAccess.ExecuteDeleteStatisticsItemsBeforeDate(cutoffUtc);
            dbAccess.SaveChanges();
        }
    }

    public abstract class StatisticsFlushContext
    {
        protected abstract ILogger Log { get; }

        private readonly OperationType _flushStatType;
        protected readonly object _syncRoot;
        protected readonly IDbAccessFactory<StatisticsDbAccess> _dbAccessFactory;

        protected StatisticsItem[] _currentFlushingItems;

        protected abstract Task DoReportImpl();

        protected bool _neverFlushOnStartup;

        protected StatisticsFlushContext(
            IDbAccessFactory<StatisticsDbAccess> dbAccessFactory,
            LeBackgroundTasks backgroundTasks,
            object syncRoot,
            OperationType flushStatType,
            TimeSpan[] timesOfDay
        )
        {
            _dbAccessFactory = dbAccessFactory;
            _flushStatType = flushStatType;
            _syncRoot = syncRoot;

            if (flushStatType == OperationType.None)
                return;

            using StatisticsDbAccess dbAccess = dbAccessFactory.CreateAccess();
            DateTime lastFlushUtc = dbAccess.GetLastFlushDateTimeUtc(flushStatType);

            if (this.GetType() == typeof(StatisticsFlushContextDevPerf))
            {
                Task.Delay(3 * 60 * 1000).ContinueWithShortThread(_ => {
                        this.DoReport().ContinueWithShortThread(_ => {
                                backgroundTasks.AddFixedTimeOfDayShortRunningForegroundTask(this.DoReportVoid, this.GetType().Name + "::" + nameof(this.DoReportVoid), timesOfDay);
                            }
                        );
                    }
                );
            } else
            {
                Task.Delay(2000).ContinueWithShortThread(_ => {
                        this.Initialize(backgroundTasks, timesOfDay, lastFlushUtc);
                    }
                );
            }

        }

        private void Initialize(LeBackgroundTasks threadedTasks, TimeSpan[] timesOfDay, DateTime lastFlushUtc)
        {
            bool flushOnStartup = !_neverFlushOnStartup && this.FlushOnStartup(timesOfDay, lastFlushUtc);

            if (flushOnStartup)
            {
                this.DoReport().ContinueWithShortThread(_ => {
                        threadedTasks.AddFixedTimeOfDayShortRunningForegroundTask(this.DoReportVoid, this.GetType().Name + "::" + nameof(this.DoReportVoid), timesOfDay);
                    }
                );
            } else
            {
                threadedTasks.AddFixedTimeOfDayShortRunningForegroundTask(this.DoReportVoid, this.GetType().Name + "::" + nameof(this.DoReportVoid), timesOfDay);
            }
        }

        private bool FlushOnStartup(TimeSpan[] timesOfDay, DateTime lastFlushUtc)
        {
            return lastFlushUtc + TimeSpan.FromDays(1) < DateTime.UtcNow;
        }

        private Task DoReport()
        {
            lock (_syncRoot)
            {
                if (_currentFlushingItems != null)
                    return Task.Delay(100); // Already flushing

                try
                {
                    Log.Information("Reporting...");

                    return this.DoReportImpl();
                }
                finally
                {
                    _currentFlushingItems = null;
                }
            }
        }

        private void DoReportVoid()
        {
            this.DoReport();
        }

        protected IEnumerable<StatisticsItem> CfByType(params OperationType[] opTypes)
        {
            return _currentFlushingItems.Where(si => opTypes.Contains(si.OperationType));
        }

        protected int CfByTypeUniqueByFirstParamCount(OperationType opType)
        {
            return _currentFlushingItems
                .Where(si => si.OperationType == opType)
                .DistinctBy(si => {
                        if (si.Parameter1 == null)
                            throw new Exception("Param1 should not be null.");

                        return si.Parameter1;
                    }
                )
                .Count();
        }

        protected int GetCfWarningsCount()
        {
            return this.CfByType(OperationType.LogWarning).Count();
        }

        protected int GetCfErrorsCount()
        {
            return this.CfByType(OperationType.LogErrorOrHigher).Count();
        }

        protected string[] GetCfWarningsAndErrors()
        {
            return this.CfByType(OperationType.LogWarning, OperationType.LogErrorOrHigher).Select(si => si.Parameter1).ToArray();
        }

        protected int GetCfVkUpdatedPhotoes()
        {
            return this.CfByTypeUniqueByFirstParamCount(OperationType.VkPhotoUpdated);
        }

        protected int GetCfVkCreatedPhotoes()
        {
            return this.CfByTypeUniqueByFirstParamCount(OperationType.VkPhotoCreated);
        }

        protected int GetCfVkDeletedPhotoes()
        {
            return this.CfByTypeUniqueByFirstParamCount(OperationType.VkPhotoDeleted);
        }

        protected int GetCfVkTotalScraps()
        {
            return this.CfByType(OperationType.VkScrapAndParse).Count();
        }

        protected int GetCfPrestaProductInactivatedDueToParsingError()
        {
            return this.CfByTypeUniqueByFirstParamCount(OperationType.PrestaProductInactivatedDueToParsingError);
        }

        protected int GetCfPrestaProductInactivatedDueToOtherReasons()
        {
            return this.CfByTypeUniqueByFirstParamCount(OperationType.PrestaProductInactivatedDueToOtherReasons);
        }

        protected int GetCfPrestaProductsUpdated()
        {
            return this.CfByTypeUniqueByFirstParamCount(OperationType.PrestaProductUpdated);
        }

        protected int GetCfPrestaSiteProductComputationException()
        {
            return this.CfByTypeUniqueByFirstParamCount(OperationType.PrestaProductException);
        }

        protected int GetCfPrestaVk2PrestaProductPushHappened()
        {
            return this.CfByType(OperationType.PrestaVk2PrestaProductPush).Count();
        }

        // ReSharper disable once NotAccessedPositionalProperty.Local
        protected readonly record struct SiteProductNew(long ProductId, long Art, string VkSitePhotoUrl);

        protected SiteProductNew[] GetCfPrestaProductCreatedNew()
        {
            return this.CfByType(OperationType.PrestaProductCreatedNew)
                .Select(si => new SiteProductNew(Convert.ToInt64(si.Parameter1), Convert.ToInt64(si.Parameter2), si.Parameter3))
                .ToArray();
        }

        protected void OnStatisticsReported(DateTime processingTimeUtc, bool fail)
        {
            _currentFlushingItems = null;

            if (!fail)
                this.InsertStatisticsWasFlushed(processingTimeUtc, null);
        }

        protected void OnStatisticsReportedWithArgument(DateTime processingTimeUtc, string argument)
        {
            _currentFlushingItems = null;

            this.InsertStatisticsWasFlushed(processingTimeUtc, argument);
        }

        protected void InsertStatisticsWasFlushed(DateTime fetchStatsTime, string argument)
        {
            this.InsertStatisticsWasFlushed(_flushStatType, fetchStatsTime, argument);
        }

        protected void InsertStatisticsWasFlushed(OperationType flushStatType, DateTime fetchStatsTime, string argument)
        {
            lock (_syncRoot)
            {
                using StatisticsDbAccess dbAccess = _dbAccessFactory.CreateAccess();
                dbAccess.InsertStatisticsWasFlushed(flushStatType, fetchStatsTime, argument);
                dbAccess.SaveChanges();
            }
        }

        protected (DateTime, string) GetLastFlushTimeAndArgumentUtc()
        {
            return this.GetLastFlushTimeAndArgumentUtc(_flushStatType);
        }

        protected (DateTime, string) GetLastFlushTimeAndArgumentUtc(OperationType flushStatType)
        {
            lock (_syncRoot)
            {
                using StatisticsDbAccess dbAccess = _dbAccessFactory.CreateAccess();

                StatisticsItem r = dbAccess.GetLastFlush(flushStatType);

                if (r == null)
                    return (DateTime.MinValue, null);

                return (r.DateTime, r.Parameter1);
            }
        }

        protected StatisticsItem[] GetStatisticsItemsSinceLastFlush(DateTime processingTimeUtc, OperationType[] operationTypesOfInterest, out DateTime? dateFromUtc)
        {
            using StatisticsDbAccess dbAccess = _dbAccessFactory.CreateAccess();

            return dbAccess.GetStatisticsItemsSinceLastFlush(_flushStatType, processingTimeUtc, operationTypesOfInterest, out dateFromUtc);
        }
    }

    public class PublicStatisticsFlushContext : StatisticsFlushContext, IPublicStatisticsFlushContext
    {
        protected override ILogger Log { get; } = null;

        public PublicStatisticsFlushContext(IDbAccessFactory<StatisticsDbAccess> dbAccessFactory, object syncRoot)
            : base(dbAccessFactory, null, syncRoot, OperationType.None, Array.Empty<TimeSpan>())
        {
        }

        public new void InsertStatisticsWasFlushed(OperationType flushStatType, DateTime fetchStatsTime, string argument)
        {
            base.InsertStatisticsWasFlushed(flushStatType, fetchStatsTime, argument);
        }

        public new (DateTime, string) GetLastFlushTimeAndArgumentUtc(OperationType flushStatType)
        {
            return base.GetLastFlushTimeAndArgumentUtc(flushStatType);
        }

        protected override Task DoReportImpl()
        {
            throw new NotImplementedException();
        }
    }
}