using System.Text;
using System.Threading.Tasks;

using Humanizer;

using irisdropwebservice.AppConfig;
using irisdropwebservice.AppConfig.ClassConfigurations;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Workers;
using Invictus.Nomenklatura.Misc;

using irisdropwebservice.Legacy;
using irisdropwebservice.Services.Chats;
using irisdropwebservice.Services.LinkService;
using irisdropwebservice.Services.Statistics.Ins;

using Telegram.Bot.Types;

namespace irisdropwebservice.Services.Statistics
{
    class StatisticsFlushContextDevPerf : StatisticsFlushContext
    {
        protected override ILogger Log { get; } = InvLog.Logger<StatisticsFlushContextDevPerf>();

        // private readonly bool _configurationPublishStatistics;
        private readonly ITelegramSendWorker _telegramSendWorker;

        public StatisticsFlushContextDevPerf(
            ITelegramSendWorker telegramSendWorker,
            IDbAccessFactory<StatisticsDbAccess> dbAccessFactory,
            LeBackgroundTasks threadedTasks,
            object syncRoot
            // StatisticsCCInfo configuration
        )
            : base(dbAccessFactory,
                threadedTasks,
                syncRoot,
                OperationType.NotUsed,
                new [] { TimeSpan.FromHours(11) }
            )
        {
            _telegramSendWorker = telegramSendWorker;
            // _configurationPublishStatistics = configuration.PublishStatistics;
        }

        protected override Task DoReportImpl()
        {
            //if (!_configurationPublishStatistics)
            //    return Task.Delay(100);

            var tcs = new TaskCompletionSource();

            DateTime processingTimeUtc = ServerClock.GetCurrentUtcTime().DateTime;

            Dictionary<string, TimelinePerformanceCounter.Report> stats = WorkersGod.GetPerformanceReportsAndFlush();

            string statText = this.GetStatText(stats);

            Task postToTelegramTask1 = _telegramSendWorker.Run(w => w.PostToTelegramFromStaffAccount(statText, CCAppConfig.TelegramIrisDrop.DevChat, null));

            postToTelegramTask1.ContinueWithShortThread(t => {
                if (t.IsCanceled || t.IsFaulted)
                {
                    this.OnStatisticsReported(processingTimeUtc, fail: true);
                    tcs.SetResult();

                    return;
                }

                this.OnStatisticsReported(processingTimeUtc, fail: false);
                tcs.SetResult();
            }
            );

            return tcs.Task;
        }

        private string GetStatText(Dictionary<string, TimelinePerformanceCounter.Report> stats)
        {
            var bdr = new StringBuilder();

            bdr.AppendLine("=== === PERFSTAT: ");

            bdr.AppendLine("=== MAINLINE: ");

            foreach (KeyValuePair<string, TimelinePerformanceCounter.Report> kv in stats)
            {
                bdr.Append(":: ");
                bdr.AppendLine(kv.Key);

                foreach ((string, float) valueTuple in kv.Value.InternalExecution)
                {
                    bdr.Append(" - ");
                    bdr.Append(valueTuple.Item1);
                    bdr.Append(": ");
                    bdr.Append($"{valueTuple.Item2:P}");
                    bdr.AppendLine("%");
                }
            }

            bdr.AppendLine("=== OTHER: ");

            foreach (KeyValuePair<string, TimelinePerformanceCounter.Report> kv in stats)
            {
                bdr.Append(":: ");
                bdr.AppendLine(kv.Key);

                foreach ((string, float) valueTuple in kv.Value.ExternalExecution)
                {
                    bdr.Append(" - ");
                    bdr.Append(valueTuple.Item1);
                    bdr.Append(": ");
                    bdr.Append($"{valueTuple.Item2:P}");
                    bdr.AppendLine("%");
                }
            }

            string str = bdr.ToString();

            return str;
        }
    }

    class StatisticsFlushContext2 : StatisticsFlushContext
    {
        protected override ILogger Log { get; } = InvLog.Logger<StatisticsFlushContext2>();

        private readonly bool _configurationPublishStatistics;
        private readonly ITelegramSendWorker _telegramSendWorker;

        public StatisticsFlushContext2(
            ITelegramSendWorker telegramSendWorker,
            IDbAccessFactory<StatisticsDbAccess> dbAccessFactory,
            LeBackgroundTasks threadedTasks,
            object syncRoot,
            StatisticsCCInfo configuration
        )
            : base(dbAccessFactory,
                threadedTasks,
                syncRoot,
                OperationType.StatFlushStats2,
                configuration.PublishStatistics2TimesUTC
            )
        {
            _telegramSendWorker = telegramSendWorker;
            _configurationPublishStatistics = configuration.PublishStatistics;
        }

        protected override Task DoReportImpl()
        {
            if (!_configurationPublishStatistics)
                return Task.Delay(100);

            var tcs = new TaskCompletionSource();

            DateTime processingTimeUtc = ServerClock.GetCurrentUtcTime().DateTime;

            DateTime? dateFromUtc;
            _currentFlushingItems = this.GetStatisticsItemsSinceLastFlush(processingTimeUtc, null, out dateFromUtc);

            string userStatText = this.GetStatSinceLastFlush_UserText(processingTimeUtc, dateFromUtc);

            if (userStatText == null)
            {
                // Skip the very first stat
                this.OnStatisticsReported(processingTimeUtc, fail: false);
                tcs.SetResult();

                return Task.Delay(100);
            }

            Task postToTelegramTask1 = _telegramSendWorker.Run(w => w.PostToTelegramFromStaffAccount(userStatText, CCAppConfig.TelegramIrisDrop.ShopAdminAttentionChat, null));

            postToTelegramTask1.ContinueWithShortThread(t => {
                    if (t.IsCanceled || t.IsFaulted)
                    {
                        this.OnStatisticsReported(processingTimeUtc, fail: true);
                        tcs.SetResult();

                        return;
                    }

                    this.OnStatisticsReported(processingTimeUtc, fail: false);
                    tcs.SetResult();
                }
            );

            return tcs.Task;
        }

        private string GetStatSinceLastFlush_UserText(DateTime processingTimeUtc, DateTime? dateFromUtc)
        {
            var bdr = new StringBuilder();

            if (!dateFromUtc.HasValue)
            {
                // Skip the very first stat
                return null;
            }
            else
            {
                bdr.AppendLine($"Немного статистики с {dateFromUtc.Value.UtcToUkraineTime().ToStringUADateAndTimeWithoutYear()} по сей момент.");
            }

            int created = this.GetCfVkCreatedPhotoes();
            bdr.AppendLine($"Новых: {created}");

            int deleted = this.GetCfVkDeletedPhotoes();
            bdr.AppendLine($"Продано: {deleted}");

            string str = bdr.ToString();

            return str;
        }
    }

    class StatisticsFlushCustomerErrors : StatisticsFlushContext, ITelegramDialogueHandler, ITelegramGroupListener
    {
        protected override ILogger Log { get; } = InvLog.Logger<StatisticsFlushCustomerErrors>();

        private readonly bool _configurationPublishStatistics;
        private readonly ITelegramSendWorker _telegramSendWorker;
        private readonly GlobalUserErrorsManager _globalUserErrorsManager;

        public StatisticsFlushCustomerErrors(
            ITelegramSendWorker telegramSendWorker,
            IDbAccessFactory<StatisticsDbAccess> dbAccessFactory,
            GlobalUserErrorsManager globalUserErrorsManager,
            LeBackgroundTasks threadedTasks,
            object syncRoot,
            StatisticsCCInfo configuration
        )
            : base(dbAccessFactory,
                threadedTasks,
                syncRoot,
                OperationType.StatFlushStats_CustomerErrors,
                configuration.PublishStatisticsTimesUTC
            )
        {
            _telegramSendWorker = telegramSendWorker;
            _globalUserErrorsManager = globalUserErrorsManager;
            _configurationPublishStatistics = configuration.PublishStatistics;
        }

        private string GetSourceUserText(GlobalUserErrorSource errorSource)
        {
            return errorSource switch
            {
                GlobalUserErrorSource.VkScrap          => "VK",
                GlobalUserErrorSource.KastaValidation  => "Kasta.UA",
                GlobalUserErrorSource.EpicentrValidation  => "EpicentrK",
                GlobalUserErrorSource.PrestaValidation => "Сайт",
                GlobalUserErrorSource.PromValidation => "Prom",
                GlobalUserErrorSource.RozetkaValidation => "Rozetka",
                _                                      => throw new EnumAbominationException(typeof(GlobalUserErrorSource))
            };
        }

        private (string text, bool containsParsingErrors) GetErrors()
        {
            GlobalUserErrorsStruct errors = _globalUserErrorsManager.GetErrors();

            bool noErrors = (!errors.Errors.Any() || errors.Errors.All(e => string.IsNullOrWhiteSpace(e.Value))) 
                            && 
                            (!errors.ProductErrors.Any() || errors.ProductErrors.All(e => string.IsNullOrWhiteSpace(e.Value)));

            if (noErrors)
                return ("😎😎😎", false);

            var bdr = new StringBuilder();

            foreach (GlobalUserErrorSource errorSource in Enum.GetValues(typeof(GlobalUserErrorSource)))
            {
                if (errorSource == GlobalUserErrorSource.RozetkaValidation || errorSource == GlobalUserErrorSource.EpicentrValidation)
                    continue;
                
                string globalSourceError;
                errors.Errors.TryGetValue(errorSource, out globalSourceError);

                KeyValuePair<GlobalUserProductErrorKey, string>[] productErrors = errors.ProductErrors
                    .Where(e => e.Key.Source == errorSource)
                    .ToArray();

                bool globalError = !string.IsNullOrWhiteSpace(globalSourceError);
                bool anyErrors = globalError || productErrors.Length != 0;

                if (errorSource == GlobalUserErrorSource.Invalid)
                {
                    if (anyErrors)
                        throw new Exception("ErrorSource 'Invalid' contains errors.");

                    continue;
                }

                bdr.Append("<b>");
                bdr.Append(this.GetSourceUserText(errorSource));
                bdr.Append("</b>");
                bdr.Append(": ");

                if (!anyErrors)
                {
                    bdr.AppendLine("🥝");
                    bdr.AppendLine();

                    continue;
                }

                bdr.AppendLine();

                if (globalError)
                {
                    bdr.Append(globalSourceError);

                    if (!globalSourceError.EndsWith("."))
                        bdr.AppendLine(".");
                    else
                        bdr.AppendLine();
                } 

                bdr.AppendLine();

                for (int i = 0; i < 4; i++)
                {
                    IEnumerable<KeyValuePair<GlobalUserProductErrorKey, string>> localProductErrors = productErrors.Skip(i * 10).Take(10);
                    
                    if (errorSource == GlobalUserErrorSource.PrestaValidation || errorSource == GlobalUserErrorSource.KastaValidation ||
                        errorSource == GlobalUserErrorSource.EpicentrValidation || errorSource == GlobalUserErrorSource.PromValidation)
                    {
                        bool any = false;

                        foreach (KeyValuePair<GlobalUserProductErrorKey, string> productError in localProductErrors)
                        {
                            if (!any && i > 0)
                                bdr.AppendLine();
                            
                            bdr.Append(productError.Key.PrestaArt);
                            bdr.Append("|");
                            any = true;
                        }

                        if (any)
                            bdr.AppendLine();
                    }

                    foreach (KeyValuePair<GlobalUserProductErrorKey, string> productError in localProductErrors)
                    {
                        if (LinkedNaming.IsPrestaSiteReferenceVkAuthorityArt(productError.Key.PrestaArt))
                        {
                            bdr.Append("Арт ");
                        }

                        bdr.Append("<b>");
                        bdr.Append(productError.Key.PrestaArt);
                        bdr.Append("</b>");
                        bdr.Append(" ");
                        bdr.Append(productError.Value);

                        if (!productError.Value.EndsWith("."))
                            bdr.AppendLine(".");
                        else
                            bdr.AppendLine();
                    }
                }
                
                int theRestOfErrors = productErrors.Skip(4 * 10).Count();

                if (theRestOfErrors > 0)
                {
                    bdr.AppendLine();
                    bdr.AppendLine($"Та ще {theRestOfErrors} помилок...");
                }

                bdr.AppendLine();
            }

            return (bdr.ToString(), true);
        }

        protected override Task DoReportImpl()
        {
            if (!_configurationPublishStatistics)
                return Task.Delay(100);

            var tcs = new TaskCompletionSource();

            DateTime processingTimeUtc = ServerClock.GetCurrentUtcTime().DateTime;

            _currentFlushingItems = this.GetStatisticsItemsSinceLastFlush(processingTimeUtc, null, out DateTime? _);

            (string userVisibleErrorsText, bool containsParsingErrors) = this.GetErrors();

            Task postToTelegramTask1 =
                string.IsNullOrWhiteSpace(userVisibleErrorsText)
                    ? Task.CompletedTask
                    : _telegramSendWorker.Run(w => w.PostToTelegramFromStaffAccount(userVisibleErrorsText, CCAppConfig.TelegramIrisDrop.ShopAdminAttentionChat, new PostToTelegramOptions() { Silent = !containsParsingErrors, DisableWebPagePreview = true, HtmlFormattingEnabled = true}));

            postToTelegramTask1.ContinueWithShortThread(t => {
                    if (t.IsCanceled || t.IsFaulted)
                    {
                        this.OnStatisticsReported(processingTimeUtc, fail: true);
                        tcs.SetResult();

                        return;
                    }

                    _globalUserErrorsManager.FlushErrors();
                    this.OnStatisticsReported(processingTimeUtc, fail: false);
                    tcs.SetResult();
                }
            );

            return tcs.Task;
        }

        public TelegramDialogueUpdateHandler CanHandleConversationInitiation(TelegramBotWho who, Update update)
        {
            if (who != TelegramBotWho.Staff)
                return null;

            if (update.Message == null || update.Message.Text == null)
                return null;

            if (update.Message.Chat.FirstName == null)
                return null;

            string messageText = update.Message.Text.Trim();

            if (messageText != "/errorreport" && messageText != "/showerrors" && messageText != "/lasterrors")
                return null;

            return this.BeginHandleUpdate;
        }

        private void BeginHandleUpdate(TelegramDialogue dialogue, Update update)
        {
            Task<string> task = Task.Run(() => this.GetErrors().text);

            dialogue.FinishAfterTask(task, new PostToTelegramOptions() { DisableWebPagePreview = true, Silent = false, HtmlFormattingEnabled = true });
        }

        public void IncomingMessage(TelegramBotWho who, Update update)
        {
            if (who != TelegramBotWho.Staff)
                return;

            if (update.Message == null || update.Message.Text == null)
                return;

            if (update.Message.Chat.Id != TelegramChatId.FromUnknownChat(CCAppConfig.TelegramIrisDrop.ShopAdminAttentionChat.DefinitionName).ChatId)
                return;

            string messageText = update.Message.Text.Trim();

            if (messageText != "/errorreport" && messageText != "/showerrors" && messageText != "/lasterrors")
                return;

            _telegramSendWorker.Do(w => w.PostToTelegramFromStaffAccount(this.GetErrors().text,
                CCAppConfig.TelegramIrisDrop.ShopAdminAttentionChat,
                new PostToTelegramOptions() { DisableWebPagePreview = true, HtmlFormattingEnabled = true, Silent = false }
            ));
        }
    }

    class StatisticsFlushContext1_1 : StatisticsFlushContext
    {
        protected override ILogger Log { get; } = InvLog.Logger<StatisticsFlushContext1_1>();

        private readonly bool _configurationPublishStatistics;
        private readonly ITelegramSendWorker _telegramSendWorker;

        public StatisticsFlushContext1_1(
            ITelegramSendWorker telegramSendWorker,
            IDbAccessFactory<StatisticsDbAccess> dbAccessFactory,
            LeBackgroundTasks threadedTasks,
            object syncRoot,
            StatisticsCCInfo configuration
        )
            : base(dbAccessFactory,
                threadedTasks,
                syncRoot,
                OperationType.StatFlushStats1_1,
                configuration.PublishStatisticsDevTimesUTC
            )
        {
            _telegramSendWorker = telegramSendWorker;
            _configurationPublishStatistics = configuration.PublishStatistics;
        }

        protected override Task DoReportImpl()
        {
            if (!_configurationPublishStatistics)
                return Task.Delay(100);

            var tcs = new TaskCompletionSource();

            DateTime processingTimeUtc = ServerClock.GetCurrentUtcTime().DateTime;

            DateTime? dateFromUtc;
            _currentFlushingItems = this.GetStatisticsItemsSinceLastFlush(processingTimeUtc, null, out dateFromUtc);

            string adminStatText = this.GetStatSinceLastFlush_AdminText(processingTimeUtc, dateFromUtc);

            Task postToTelegramTask2 = _telegramSendWorker.Run(w => w.PostToTelegramFromStaffAccount(adminStatText, CCAppConfig.TelegramIrisDrop.DevChat, null));

            postToTelegramTask2.ContinueWithShortThread(tt => {
                    if (tt.IsCanceled || tt.IsFaulted)
                    {
                        this.OnStatisticsReported(processingTimeUtc, fail: true);
                        tcs.SetResult();

                        return;
                    }

                    this.OnStatisticsReported(processingTimeUtc, fail: false);
                    tcs.SetResult();
                }
            );

            return tcs.Task;
        }

        private string GetStatSinceLastFlush_AdminText(DateTime processingTimeUtc, DateTime? dateFromUtc)
        {
            var bdr = new StringBuilder();

            //var timeInfo = TimeZoneInfo.FindSystemTimeZoneById(GetFromConfig.ManilaTimeZoneKey());

            if (!dateFromUtc.HasValue)
            {
                bdr.AppendLine("Статистика с самого начала.");
            }
            else
            {
                bdr.AppendLine($"Статистика с {dateFromUtc.Value.UtcToUkraineTime()} по сей момент.");
            }

            int totalWarnings = this.GetCfWarningsCount();
            bdr.AppendLine($"WARN: {totalWarnings}");

            int totalErrors = this.GetCfErrorsCount();
            bdr.AppendLine($"ERR: {totalErrors}");

            bdr.AppendLine("=== VK:");

            int timesScrapAndParseHappened = this.GetCfVkTotalScraps();
            bdr.AppendLine($"SCRAPS: {timesScrapAndParseHappened}");

            int vkPhotosWereUpdated = this.GetCfVkUpdatedPhotoes();
            bdr.AppendLine($"SCRAP NEW ITEM DATA: {vkPhotosWereUpdated}");

            int vkPhotosWereCreated = this.GetCfVkCreatedPhotoes();
            bdr.AppendLine($"NEW: {vkPhotosWereCreated}");

            int vkPhotoesDeleted = this.GetCfVkDeletedPhotoes();
            bdr.AppendLine($"DEL: {vkPhotoesDeleted}");

            bdr.AppendLine("");
            bdr.AppendLine("=== WEBSITE:");

            int prestaProductsUpdated = this.GetCfPrestaProductsUpdated();

            bdr.AppendLine($"VK2PRESTA PUSHES: {this.GetCfPrestaVk2PrestaProductPushHappened()}");
            bdr.AppendLine($"UPD: {prestaProductsUpdated}");

            SiteProductNew[] prestaProductsCreatedNew = this.GetCfPrestaProductCreatedNew();
            bdr.AppendLine($"NEW: {prestaProductsCreatedNew.Length}");

            if (prestaProductsCreatedNew.Length != 0)
            {
                foreach (SiteProductNew vk in prestaProductsCreatedNew)
                {
                    bdr.AppendLine($"ART: {vk.Art}, LINK: {vk.VkSitePhotoUrl}");
                }

                bdr.AppendLine("");
            }

            int prestaProductsInactivatedDueToParsingError = this.GetCfPrestaProductInactivatedDueToParsingError();
            bdr.AppendLine($"DEACC PARS: {prestaProductsInactivatedDueToParsingError}");

            int prestaProductsInactivatedDueToOtherReasons = this.GetCfPrestaProductInactivatedDueToOtherReasons();
            bdr.AppendLine($"DEACC OTHER: {prestaProductsInactivatedDueToOtherReasons}");

            int prestaProductsWithException = this.GetCfPrestaSiteProductComputationException();
            bdr.AppendLine($"ITEM PROC ERR: {prestaProductsWithException}");

            string str = bdr.ToString();

            return str;
        }
    }

    class StatisticsFlushContextDevWarnings : StatisticsFlushContext
    {
        protected override ILogger Log { get; } = InvLog.Logger<StatisticsFlushContextDevWarnings>();

        private readonly ITelegramSendWorker _telegramSendWorker;
        private readonly bool _configurationPublishStatistics;

        public StatisticsFlushContextDevWarnings(
            ITelegramSendWorker telegramSendWorker,
            IDbAccessFactory<StatisticsDbAccess> dbAccessFactory,
            LeBackgroundTasks threadedTasks,
            object syncRoot,
            StatisticsCCInfo configuration
        )
            : base(dbAccessFactory,
                threadedTasks,
                syncRoot,
                OperationType.StatFlushStatsDevWarnings,
                configuration.PublicDevErrorsTimesUTC
            )
        {
            _telegramSendWorker = telegramSendWorker;
            _configurationPublishStatistics = configuration.PublishStatistics;
        }

        protected override Task DoReportImpl()
        {
            if (!_configurationPublishStatistics)
                return Task.Delay(100);

            var tcs = new TaskCompletionSource();

            DateTime processingTimeUtc = ServerClock.GetCurrentUtcTime().DateTime;

            this.DoReportCore(processingTimeUtc, tcs);

            return tcs.Task;
        }

        private void DoReportCore(DateTime processingTimeUtc, TaskCompletionSource tcs)
        {
            TelegramChatConfiguration chatId = CCAppConfig.TelegramIrisDrop.DevChat;

            _currentFlushingItems = this.GetStatisticsItemsSinceLastFlush(
                processingTimeUtc,
                new[]
                {
                    OperationType.LogWarning, OperationType.LogErrorOrHigher
                },
                out DateTime? _
            );

            string[] warningsAndErrors = this.GetCfWarningsAndErrors();

            var wrnBdr = new StringBuilder();
            var errBdr = new StringBuilder();
            var ftlBdr = new StringBuilder();

            var postToTelegramTasks = new List<Task>();

            if (warningsAndErrors.Length == 0)
            {
                postToTelegramTasks.Add(_telegramSendWorker.Run(w => w.PostToTelegramFromStaffAccount("😎😎😎", chatId, null)));
            }
            else
            {
                postToTelegramTasks.Add(
                    _telegramSendWorker.Run(w => w.PostToTelegramFromStaffAccount("⚡⚡⚡ Daily error and warning report.", chatId, null))
                );

                foreach (string message in warningsAndErrors)
                {
                    StringBuilder bdr = message.Contains(InvLog.WARNING_TAG_DISPLAY_NAME)
                        ? wrnBdr : message.Contains(InvLog.ERROR_TAG_DISPLAY_NAME)
                            ? errBdr : ftlBdr;

                    bdr.AppendLine(message);
                    bdr.AppendLine();
                }
            }

            var documents = new List<(byte[], string)>();

            if (wrnBdr.Length != 0)
                documents.Add((Encoding.Unicode.GetBytes(wrnBdr.ToString()), "warnings.txt"));
            if (errBdr.Length != 0)
                documents.Add((Encoding.Unicode.GetBytes(errBdr.ToString()), "errors.txt"));
            if (ftlBdr.Length != 0)
                documents.Add((Encoding.Unicode.GetBytes(ftlBdr.ToString()), "fatal.txt"));

            foreach ((byte[], string) document in documents)
            {
                postToTelegramTasks.Add(_telegramSendWorker.Run(w => w.SendAttachFromStaffAccount(chatId, document.Item1, document.Item2, document.Item2)));
            }

            Task.WhenAll(postToTelegramTasks)
                .ContinueWithShortThread(t => {
                    if (t.IsCanceled || t.IsFaulted)
                    {
                        this.OnStatisticsReported(processingTimeUtc, fail: true);
                        tcs.SetResult();

                        return;
                    }

                    this.OnStatisticsReported(processingTimeUtc, fail: false);
                    tcs.SetResult();
                }
            );
        }
    }

}
