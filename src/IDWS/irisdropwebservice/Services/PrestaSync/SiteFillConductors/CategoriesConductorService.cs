using Bukimedia.PrestaSharp.Entities;

using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.AppConfig;
using irisdropwebservice.Libs.KastaSharp;
using irisdropwebservice.Libs.PrestaSharp;
using Invictus.Nomenklatura.Misc;

using irisdropwebservice.Legacy;
using irisdropwebservice.Services.KastaSync;
using irisdropwebservice.Services.PrestaSync.Ins;
using irisdropwebservice.Services.PrestaSync.Remote;
using irisdropwebservice.Services.PrestaSync.ServiceConductors;
using irisdropwebservice.Services.VkScrap;


using AuxProduct = Bukimedia.PrestaSharp.Entities.AuxEntities.product;
using AuxCategory = Bukimedia.PrestaSharp.Entities.AuxEntities.category;

namespace irisdropwebservice.Services.PrestaSync.SiteFillConductors
{
    // Purpose: Get ALL categories before DoPush()
    // Store them here.

    public record CategoryInfo(long Id, string Name, string LinkRewrite, bool IsVkManagedCategory);

    public enum CategoriesConductorServiceMode
    {
        Full,
        NoChecks
    }

    /// <summary>
    /// <see cref="PrestaProductSyncWorker"/> is responsible for this conductor. There is no this conductor without SiteFill, as it requires relevant vk data.
    /// </summary>
    public class CategoriesConductorService : PrestaSiteFillConductorBase
    {
        private readonly ILogger _logger = InvLog.Logger<CategoriesConductorService>();

        protected override ILogger Log => _logger;

        private readonly IPrestaSiteWebApiCallsWorkerNonUser _webApi;
        private readonly LangAndTemplateConductorService _langAndTemplateConductor;
        private readonly PrestaCommon _prestaCommon;

        private readonly CategoriesConductorServiceMode _mode;

        private List<category> _allCategories;

        public CategoriesConductorService(
            IPrestaSiteWebApiCallsWorkerNonUser webApi,
            LangAndTemplateConductorService langAndTemplateConductor, 
            PrestaCommon prestaCommon,
            CategoriesConductorServiceMode mode)
        {
            _webApi = webApi;
            _langAndTemplateConductor = langAndTemplateConductor;
            _prestaCommon = prestaCommon;
            _mode = mode;
        }

        private Dictionary<string, long> _categoryNameToSiteCategoryId;
        private List<CategoryInfo> _categoryInfos;
        private LinkedProduct[] _currentLinkedProducts;
        private long[] _otherActiveProducts;
        private long[] _currentActivePrestaProductsIds;

        public long GetCategoryId(string categoryName)
        {
            if (_categoryNameToSiteCategoryId == null || !_categoryNameToSiteCategoryId.ContainsKey(categoryName))
            {
                Log.Information("ReInitializing due to missing data.");

                this.ReInitialize(_currentLinkedProducts, _otherActiveProducts);

                if (!_categoryNameToSiteCategoryId.ContainsKey(categoryName))
                {
                    throw new Exception($"_categoryNameToSiteCategoryId has no key {categoryName} after re-initialization still.");
                }
            }

            return _categoryNameToSiteCategoryId[categoryName];
        }

        public CategoryInfo GetCategoryInfo(long categoryId)
        {
            foreach (CategoryInfo ci in _categoryInfos)
            {
                if (ci.Id == categoryId)
                    return ci;
            }

            return null;
        }

        public List<CategoryInfo> GetCategoryInfoAndDescedants(long categoryId)
        {
            var res = new List<CategoryInfo>();

            res.Add(this.GetCategoryInfo(categoryId));

            foreach (category category in _allCategories.Where(cat => cat.id_parent == categoryId))
            {
                res.Add(this.GetCategoryInfo(category.id.Val()));
                res.AddRange(this.GetCategoryInfoAndDescedants(category.id.Val()));
            }

            return res;
        }

        public List<CategoryInfo> GetCategoryInfoAndParents(long categoryId)
        {
            var res = new List<CategoryInfo>();

            category thisCat;

            do
            {
                thisCat = _allCategories.SingleOrDefault(cat => cat.id == categoryId);

                if (thisCat != null)
                {
                    CategoryInfo ci = this.GetCategoryInfo(thisCat.id.Val());
                    if (ci != null)
                        res.Add(ci);
                    categoryId = thisCat.id_parent.Val();
                }
            } while (thisCat != null);

            return res;
        }

        public bool IsVkManagedCategory(long category)
        {
            category vkRootCategory = _allCategories.Single(cat => cat.id.Val() == CCAppConfig.PrestaIrisDrop.AutoCreateVkSuperCategoryId);

            return this.IsVkManagedCategory(category, vkRootCategory);
        }

        private bool IsVkManagedCategory(long category, category root)
        {
            foreach (category associationsCategory in _allCategories.Where(cat => cat.id_parent == root.id))
            {
                if (associationsCategory.id == category)
                    return true;

                if (this.IsVkManagedCategory(category, associationsCategory))
                    return true;
            }

            return false;
        }
        
        public void ImportKastaCategories()
        {
            foreach (PrestaExteriorSuperCategory fileCategory in _prestaCommon.CategoriesFeaturesInfo.SuperCategories)
            {
                if (_allCategories.Any(cat => cat.name.Any(l => l.Value == fileCategory.NameUkr)))
                    continue;

                this.CreateCat(fileCategory.NameUkr, CCAppConfig.PrestaIrisDrop.RootCategoryId2);
            }

            _allCategories = _webApi.GetAllCategories();

            category[] nonVkCategories = _allCategories.Where(cat => !this.IsVkManagedCategory(cat.id.Val())).ToArray();

            foreach (PrestaExteriorCategory fileCategory in _prestaCommon.CategoriesFeaturesInfo.Categories)
            {
                if (nonVkCategories.Any(cat => cat.name.Any(l => l.Value == fileCategory.NamePluralUkr)))
                    continue;

                PrestaExteriorSuperCategory parentCat = _prestaCommon.CategoriesFeaturesInfo.SuperCategories.Single(cat => cat.KastaAffiliationId == fileCategory.AffiliationId);

                category newCategoryParent = _allCategories.Single(cat => cat.name.Any(l => l.Value == parentCat.NameUkr));

                this.CreateCat(fileCategory.NamePluralUkr, (int)newCategoryParent.id.Val());
            }

            _logger.Warning("CATS DONE");

            _allCategories = _webApi.GetAllCategories();
        }

        public void ReInitialize(LinkedProduct[] linkedProducts, long[] otherActiveProducts)
        {
            Log.Verbose("ReInitializing");
            if (CurrentParsedData == null)
                throw new Exception("Current parsed data should not be null");

            _currentLinkedProducts = linkedProducts;
            _otherActiveProducts = otherActiveProducts;

            bool fullMode = _mode switch
            {
                CategoriesConductorServiceMode.Full     => true,
                CategoriesConductorServiceMode.NoChecks => false,
                _                                       => throw TypeAbominationException.Enum(typeof(CategoriesConductorServiceMode), _mode)
            };

            _currentActivePrestaProductsIds = linkedProducts
                .Where(lp => lp.PrestaProduct != null)
                .Select(lp => lp.PrestaProduct.id.Val())
                .Concat(otherActiveProducts)
                .ToArray();

            string[] allVkCatNames = CurrentParsedData.Items
                .Where(vksi => vksi.ParsingResult == ParsingResult.Success)
                .Select(vksi => vksi.AlbumTitle)
                .Distinct()
                .ToArray();

            _allCategories = _webApi.GetAllCategories();

            // this.ImportKastaCategories();

            var catNameToId = new Dictionary<string, long>();
            _categoryInfos = new List<CategoryInfo>();

            foreach (category category in _allCategories)
            {
                if (category.id == null 
                    || category.id == CCAppConfig.PrestaIrisDrop.RootCategoryId1 
                    || category.id == CCAppConfig.PrestaIrisDrop.RootCategoryId2
                    || category.id == CCAppConfig.PrestaIrisDrop.AutoCreateVkSuperCategoryId)
                    continue;

                if (allVkCatNames.Contains(category.name[0].Value))
                {
                    _categoryInfos.Add(new CategoryInfo(
                            category.id.Value,
                            _langAndTemplateConductor.GetPrestaTextCategoryName(category),
                            _langAndTemplateConductor.GetPrestaTextCategoryLinkRewrite(category),
                            true
                        )
                    );

                    catNameToId.Add(category.name[0].Value, category.id.Value);
                } else
                {
                    _categoryInfos.Add(new CategoryInfo(
                            category.id.Value,
                            _langAndTemplateConductor.GetPrestaTextCategoryName(category),
                            _langAndTemplateConductor.GetPrestaTextCategoryLinkRewrite(category),
                            false
                        )
                    );
                }
            }

            foreach (string vkComCatName in allVkCatNames)
            {
                if (catNameToId.ContainsKey(vkComCatName))
                    continue;

                Log.Information($"Creating category {vkComCatName}.");

                category siteCat = this.CreateCat(vkComCatName, CCAppConfig.PrestaIrisDrop.AutoCreateVkSuperCategoryId);

                catNameToId.Add(vkComCatName, siteCat.id.Val());
            }

            _categoryNameToSiteCategoryId = catNameToId;
        }

        public void ManageCategoriesActivation()
        {
            bool fullMode = _mode switch
            {
                CategoriesConductorServiceMode.Full     => true,
                CategoriesConductorServiceMode.NoChecks => false,
                _                                       => throw new EnumAbominationException(typeof(CategoriesConductorServiceMode))
            };

            if (!fullMode)
                return;

            List<SourcedFromVkProductCacheInfo> activeCachedProductsCacheInfo = _webApi.MyApiProductsSourcedFromVk_GetAllActiveProductsCacheInfo();

            IEnumerable<long> filterByValues = activeCachedProductsCacheInfo
                .Select(pi => pi.ProductId);

            List<StockAvailableExtended> stockAvailables = PrestaSiteWebApiCallsExtensions.GetByOneKeyFilterBatched(
                _webApi.MyApiProducts_GetFullStockAvaliInfo,
                filterByValues,
                stopIfNothingMore: false,
                batchSize: 250,
                sleep: 50
            );

            foreach (category category in _allCategories)
            {
                if (category.id == null
                    || category.id == CCAppConfig.PrestaIrisDrop.RootCategoryId1
                    || category.id == CCAppConfig.PrestaIrisDrop.RootCategoryId2
                    || category.id == CCAppConfig.PrestaIrisDrop.AutoCreateVkSuperCategoryId)
                    continue;

                if (this.IsVkManagedCategory(category.id.Val())) // We don't care
                    continue;

                bool hasAnyProducts = this.HasAnyProductsWithQuantitiesRecursive(stockAvailables, category);

                if (!hasAnyProducts && category.active != 0)
                {
                    category.active = 0;

                    _logger.Information($"Deactivating category {category.name[0].Value} due to absence of active products.");

                    _webApi.UpdateCategory(category);
                }
                else if (hasAnyProducts && category.active == 0)
                {
                    category.active = 1;

                    _logger.Error($"Reactivating category {category.name[0].Value}. Please check website filters.");

                    _webApi.UpdateCategory(category);
                }
            }
        }

        private bool HasAnyProductsWithQuantitiesRecursive(List<StockAvailableExtended> stockAvailables, category category)
        {
            foreach (AuxProduct auxProduct in category.associations.products)
            {
                long productId = auxProduct.id;

                if (_currentActivePrestaProductsIds.Contains(productId))
                {
                    if (
                        stockAvailables
                            .Where(se => se.StockAvailable.id_product == productId)
                            .Any(se => se.ComputedAvailableQuantity != 0)
                    )
                    {
                        return true;
                    }
                }
            }

            foreach (category subCategory in category.associations.categories.Select(auxCat => _allCategories.FirstOrDefault(cat => cat.id == auxCat.id)))
            {
                if (subCategory == null)
                    throw new Exception($"subCategory of id of one of {category.id} children.. == null");

                if (this.HasAnyProductsWithQuantitiesRecursive(stockAvailables, subCategory))
                    return true;
            }

            return false;
        }

        private category CreateCat(string name, int parent)
        {
            var category = new category()
            {
                active = 1,
                id_shop_default = 1,
                name = _langAndTemplateConductor.CreatePrestaText(name, null),

                // description = _langAndTemplateConductor.CreatePrestaText("Це опис категорії, його треба змінити..."),
                id_parent = parent,
                is_root_category = 0
            };

            category.link_rewrite = _langAndTemplateConductor.CreateLinkRewrite(category.name);

            category = _webApi.AddCategory(category);

            Log.Information($"Created category {name}.");
            Log.Warning($"Category {name} was automatically created. Please check access rights to this category and add it to the supercategory.");

            category res = _webApi.GetCategory(category.id.Val());

            return res;
        }
    }
}