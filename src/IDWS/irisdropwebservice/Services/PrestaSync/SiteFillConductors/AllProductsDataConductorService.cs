using System.Diagnostics;
using System.Globalization;
using System.Text;
using System.Threading;

using Bukimedia.PrestaSharp.Entities;

using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.AppConfig;
using irisdropwebservice.AppConfig.ClassConfigurations;
using irisdropwebservice.Libs.PrestaSharp;
using Invictus.Nomenklatura.Misc;

using irisdropwebservice.Legacy;
using irisdropwebservice.Services.LinkService;
using irisdropwebservice.Services.PrestaSync.Ins;
using irisdropwebservice.Services.PrestaSync.Remote;
using irisdropwebservice.Services.PrestaSync.ServiceConductors;
using irisdropwebservice.Services.VkScrap;


using Aux = Bukimedia.PrestaSharp.Entities.AuxEntities;

namespace irisdropwebservice.Services.PrestaSync.SiteFillConductors
{
    // NOTE (!):
    // Some products may be in fact inactivated on presta site, but are still counted as active in cache
    // This should be possible only due to vk photo parsing errors.
    
    public class AllProductsDataConductorService : PrestaSiteFillConductorBase
    {
        protected override ILogger Log { get; } = InvLog.Logger<AllProductsDataConductorService>();

        private readonly ProductAttributesConductorService _productAttributesConductor;
        private readonly LangAndTemplateConductorService _langAndTemplateConductor;
        private readonly GlobalUserErrorsManager _globalUserErrorsManager;
        private readonly CategoriesConductorService _categoriesConductor;
        private readonly InvProductFeatureCentre _invProductFeatureCentre;

        private List<LinkedProduct> _matchedProducts;
        private List<long> _productIdsThatAreNotMatchedDueToPreCaching;
        private List<ProductOption> _vkUpdatingProductOptionsOnSite;
        private List<specific_price> _allSpecificPrices;

        private readonly PrestaShopCCInfo _configuration = CCAppConfig.PrestaIrisDrop;
        private readonly IPrestaSiteWebApiCallsWorkerNonUser _webApi;

        public AllProductsDataConductorService(
            IPrestaSiteWebApiCallsWorkerNonUser webApi, 
            ProductAttributesConductorService productAttributesConductor,
            LangAndTemplateConductorService langAndTemplateConductor,
            GlobalUserErrorsManager globalUserErrorsManager,
            CategoriesConductorService categoriesConductor,
            InvProductFeatureCentre invProductFeatureCentre)
        {
            _webApi = webApi;
            _productAttributesConductor = productAttributesConductor;
            _langAndTemplateConductor = langAndTemplateConductor;
            _globalUserErrorsManager = globalUserErrorsManager;
            _categoriesConductor = categoriesConductor;
            _invProductFeatureCentre = invProductFeatureCentre;
        }

        public IEnumerable<LinkedProduct> GetMatchedProducts()
        {
            return _matchedProducts;
        }

        public IEnumerable<long> GetProductIdsThatAreNotMatchedDueToPreCaching()
        {
            return _productIdsThatAreNotMatchedDueToPreCaching;
        }

        public IEnumerable<ProductOption> GetProductOptionsByProductId(List<ProductOption> dataSource, long productId)
        {
            return dataSource.Where(po => po.ProductId == productId);
        }

        public IEnumerable<ProductOption> GetProductOptionsByProductId(long productId)
        {
            return this.GetProductOptionsByProductId(_vkUpdatingProductOptionsOnSite, productId);
        }

        public void UpdateCombinations(List<ProductOption> combinations)
        {
            List<combination> engineCombinations = combinations.Select(this.ConvertProductOptionToEngineCombination).ToList();

            _webApi.UpdateCombinations(engineCombinations);
        }

        public List<combination> AddCombinations(List<ProductOption> combinations)
        {
            List<combination> engineCombinations = combinations.Select(this.ConvertProductOptionToEngineCombination).ToList();
            List<combination> newlyCreatedEngineCombinations = _webApi.AddCombinations(engineCombinations);

            return newlyCreatedEngineCombinations;
        }

        public void DeleteCombinations(List<ProductOption> combinations)
        {
            foreach (ProductOption productOption in combinations)
            {
                _webApi.DeleteCombination(productOption.Id);
            }
        }

        public void DeleteAllSpecificPrices(long productId)
        {
            specific_price[] productSpecificPrices = _allSpecificPrices.Where(p => p.id_product == productId).ToArray();

            foreach (specific_price specificPrice in productSpecificPrices)
            {
                _webApi.DeleteSpecificPrice(specificPrice.id.Val());
            }
        }

        public void AddSalePriceIfNeeded(int currentProductPrice, int specialPrice, long productId)
        {
            specific_price[] productSpecificPrices = _allSpecificPrices.Where(p => p.id_product == productId).ToArray();

            decimal reductionForDrops = Math.Max(
                _configuration.AbsoluteMinPriceUAH,
                currentProductPrice - specialPrice + _configuration.NonDropShippersPriceIncreaseUAH
            );

            decimal reductionForNonDrops = Math.Max(
                _configuration.AbsoluteMinPriceUAH,
                currentProductPrice - specialPrice
            );

            specific_price[] specificPricesToDelete = productSpecificPrices
                .Where(pr => pr.reduction != reductionForDrops && pr.reduction != reductionForNonDrops)
                .ToArray();

            foreach (specific_price specificPrice in specificPricesToDelete)
            {
                _webApi.DeleteSpecificPrice(specificPrice.id.Val());
            }

            // Discounts do not stack, so we should add Drop discount here. 

            if (!productSpecificPrices.Except(specificPricesToDelete).Any(pr => pr.reduction == reductionForDrops))
            {
                var specificPrice = new specific_price();
                specificPrice.reduction = reductionForDrops;
                specificPrice.id_product = productId;
                specificPrice.id_shop = 1;
                specificPrice.from_quantity = 1;
                specificPrice.id_cart = 0;
                specificPrice.id_country = 0;
                specificPrice.id_currency = 0;
                specificPrice.id_customer = 0;
                specificPrice.id_group = _configuration.DropShippersCustomerGroupId;
                specificPrice.id_product_attribute = 0;
                specificPrice.id_specific_price_rule = 0;
                specificPrice.reduction_type = "amount";
                specificPrice.price = -1;
                specificPrice.reduction_tax = 1;
                _webApi.AddSpecificPrice(specificPrice);
            }

            if (!productSpecificPrices.Except(specificPricesToDelete).Any(pr => pr.reduction == reductionForNonDrops))
            {
                var specificPrice = new specific_price();
                specificPrice.reduction = reductionForNonDrops;
                specificPrice.id_product = productId;
                specificPrice.id_shop = 1;
                specificPrice.from_quantity = 1;
                specificPrice.id_cart = 0;
                specificPrice.id_country = 0;
                specificPrice.id_currency = 0;
                specificPrice.id_customer = 0;
                specificPrice.id_group = 0;
                specificPrice.id_product_attribute = 0;
                specificPrice.id_specific_price_rule = 0;
                specificPrice.reduction_type = "amount";
                specificPrice.price = -1;
                specificPrice.reduction_tax = 1;
                _webApi.AddSpecificPrice(specificPrice);
            }
        }

        private class FullVkAndPrestaProductCacheInfo
        {
            public long ProductId { get; }
            public string ProductReference { get; }

            private readonly SourcedFromVkProductCacheInfo _cacheInfo;
            public ProductInfo Relevant { get; }

            public VkScrapItem VkScrapItem { get; set; }

            public long RelevantProductVer => Relevant.ProductVer;
            public string RelevantVkFullTextHash => VkScrapItem?.FullTextHash;
            public string RelevantVkFullPhotoId => VkScrapItem?.FullId;

            public long CachedProductVer => _cacheInfo.LastProductVer;
            public string CachedVkFullTextHash => _cacheInfo.VkDescriptionHash;
            public string CachedVkFullPhotoId => _cacheInfo.VkFullPhotoId;

            public FullVkAndPrestaProductCacheInfo(ProductInfo relevant, SourcedFromVkProductCacheInfo cacheInfo)
            {
                if (relevant.ProductId != cacheInfo.ProductId)
                    throw new Exception("...");

                ProductId = relevant.ProductId;
                ProductReference = relevant.Reference;

                _cacheInfo = cacheInfo;
                Relevant = relevant;
            }

            public SourcedFromVkProductCacheInfo GetCacheInfo() => _cacheInfo;
        }

        // Log.Verbose($"4: PreCache completed discarded {_productIdsThatAreNotMatchedDueToPreCaching.Count} out of about {CurrentParsedDataEligibleItems} items");

        public int PreCacheDiscardedItemsCount { get; private set; }

        private void ValidateProducts(IReadOnlyCollection<ProductInfo> productInfos)
        {
            foreach (ProductInfo productInfo in productInfos)
            {
                this.ValidateProduct(productInfo);
            }
        }

        private bool ValidateProduct(ProductInfo productInfo)
        {
            if (productInfo.DefaultCategory == _configuration.RootCategoryId2 || productInfo.DefaultCategory == _configuration.RootCategoryId1)
            {
                _globalUserErrorsManager.SetFlushableProductError(GlobalUserErrorSource.PrestaValidation, productInfo.Reference, "Невірна основна категорія товару");
                return false;
            }

            bool hasExportableCategory = _invProductFeatureCentre.GetMainCategory(productInfo.Categories).IsExportableCategory;
            
            if (hasExportableCategory)
            {
                bool defaultCategoryIsExportable = _invProductFeatureCentre.GetMainCategory(productInfo.DefaultCategory).IsExportableCategory;
                
                if (!defaultCategoryIsExportable)
                {
                    _globalUserErrorsManager.SetFlushableProductError(GlobalUserErrorSource.PrestaValidation,
                        productInfo.Reference,
                        "Невірна основна категорія товару"
                    );

                    return false;
                }
            } else
            {
                _globalUserErrorsManager.SetFlushableProductError(GlobalUserErrorSource.PrestaValidation,
                    productInfo.Reference,
                    "Товар повинен бути перенесений у Kasta категорію"
                );
                return false;
            }

            _globalUserErrorsManager.SetFlushableProductError(GlobalUserErrorSource.PrestaValidation, productInfo.Reference, null);

            return true;
        }

        public void PrepareForProductsProcessing(CancellationToken cancellationToken, bool isJustInitialization)
        {
            var sw = Stopwatch.StartNew();

            Log.Verbose("PrepareForProductsProcessing");

            /*
            List<ProductInfo> fe = PrestaSiteWebApiCallsExtensions.GetByOneKeyFilterBatched(
                range => _webApi.MyApiProducts_GetInfo(range, _langAndTemplateConductor.UkrainianLanguage.Id),
                Enumerable.Range(0, 20000).Select(i => (long)i).ToArray(),
                true,
                200
            );

            foreach (ProductInfo productInfo in fe)
            {
                _webApi.MyApiProducts_SetDefaultReservationSettings(productInfo.ProductId);
            }

            Log.Error("DONE###");
            */

            // Step 1: Get all product_ids and related cache product info for products that are active and are sourced from Vk (get cacheInfos).

            List<SourcedFromVkProductCacheInfo> activeCachedProductsCacheInfo = _webApi.MyApiProductsSourcedFromVk_GetAllActiveProductsCacheInfo();

            Log.Verbose("1: MyApiProductsSourcedFromVk_GetAllActiveProductsCacheInfo() done.");

            // Step 2: Get all products versions.
            
            List<ProductInfo> allProductsVersions = PrestaSiteWebApiCallsExtensions.GetByOneKeyFilterBatched(
                (arg0) => _webApi.MyApiProducts_GetInfo(arg0, _langAndTemplateConductor.UkrainianLanguage.Id),
                Enumerable.Range(0, int.MaxValue).Select(i => (long)i),
                stopIfNothingMore: true,
                batchSize: 400,
                cancellationToken: cancellationToken,
                50
            );

            Log.Verbose("2: MyApiProducts_GetInfo() done.");

            // Step 3: Match relevant products versions with cache and data from vk.

            // ReSharper disable once JoinDeclarationAndInitializer
            FullVkAndPrestaProductCacheInfo[] cachedAndRelevantJoin;

            cachedAndRelevantJoin = activeCachedProductsCacheInfo
                .FullOuterJoinSingle(
                    allProductsVersions,
                    cached => cached.ProductId,
                    relevant => relevant.ProductId,
                    (cached, relevant, _) => {
                        if (relevant.Reference == null || cached == null)
                            return null;
                        return new FullVkAndPrestaProductCacheInfo(relevant, cached);
                    }
                )
                .Where(item => item != null)
                .ToArray();
            
            if (!isJustInitialization)
            {
                this.ValidateProducts(cachedAndRelevantJoin.Select(item => item.Relevant).Where(b => b != null).ToArray());
            }

            cachedAndRelevantJoin = cachedAndRelevantJoin
                .FullOuterJoinSingle(
                    CurrentParsedDataParsingSuccessOnlyItems,
                    fullCacheInfo => fullCacheInfo.ProductReference,
                    vkScrapItem => LinkedNaming.PrestaSiteReferenceFromVkAuthorityArt(vkScrapItem.Art),
                    (fullCacheInfo, vkScrapItem, _) => {
                        if (fullCacheInfo == null)
                            return null;
                        fullCacheInfo.VkScrapItem = vkScrapItem;

                        return fullCacheInfo;
                    }
                )
                .Where(item => item != null)
                .ToArray();

            Log.Verbose("3: Matched Relevant <-> Cached.");

            // Step 4: Exclude from
            //  - SourcedFromVkProductCacheInfo activeCachedProductsCacheInfo 
            // and 
            //  - next VkScrapItems
            //
            // when we are 100% sure presta site product 100% does not need any kind of update.

            List<VkScrapItem> vkScrapItemsToProcessFurther = CurrentParsedDataEligibleItems.ToList();

            _productIdsThatAreNotMatchedDueToPreCaching = new List<long>();

            foreach (FullVkAndPrestaProductCacheInfo cachedAndRelevant in cachedAndRelevantJoin)
            {
                if (PrestaSyncExteriorService.Debug_AlwaysProcessProductIds.Contains(cachedAndRelevant.ProductId))
                    continue;

                if (cachedAndRelevant.CachedProductVer == cachedAndRelevant.RelevantProductVer &&
                    cachedAndRelevant.CachedVkFullPhotoId == cachedAndRelevant.RelevantVkFullPhotoId &&
                    cachedAndRelevant.CachedVkFullTextHash == cachedAndRelevant.RelevantVkFullTextHash)
                {
                    // Nothing has changed on both Presta and Vk sides.

                    int idx1 = activeCachedProductsCacheInfo.IndexOf(cachedAndRelevant.GetCacheInfo());
                    if (idx1 == -1)
                        throw new JustNoWayException();
                    activeCachedProductsCacheInfo[idx1] = null;

                    int idx2 = vkScrapItemsToProcessFurther.IndexOf(cachedAndRelevant.VkScrapItem);
                    if (idx2 == -1)
                        throw new JustNoWayException();
                    vkScrapItemsToProcessFurther[idx2] = null;

                    _productIdsThatAreNotMatchedDueToPreCaching.Add(cachedAndRelevant.ProductId);
                }
            }

            activeCachedProductsCacheInfo = activeCachedProductsCacheInfo.Where(item => item != null).ToList();
            vkScrapItemsToProcessFurther = vkScrapItemsToProcessFurther.Where(item => item != null).ToList();
            PreCacheDiscardedItemsCount = _productIdsThatAreNotMatchedDueToPreCaching.Count;

            Log.Verbose("4: PreCache completed.");

            // Step 5: Get all products with product_ids from prev. call.

            IEnumerable<string> filterByValues = activeCachedProductsCacheInfo
                .Select(pi => pi.ProductId.ToString());
            
            List<product> activeProducts = _webApi.GetByOneKeyFilterBatched(_webApi.GetProducts,
                "id",
                filterByValues,
                "id_ASC",
                stopIfNothingMore: false,
                batchSize: 150,
                cancellationToken: cancellationToken
            );

            Log.Verbose("5: ProductFactory.GetByFilter(active cached products) done.");

            // Step 6: Match cacheInfos to real products on site.

            List<LinkedProduct> linkedProducts = activeCachedProductsCacheInfo.FullOuterJoinSingle(
                b: activeProducts,
                selectKeyA: capsvk => capsvk.ProductId,
                selectKeyB: pr => pr.id.Val(),
                projection: (capsvk, pr, _) => new LinkedProduct() { PrestaCache = capsvk, PrestaProduct = pr },
                defaultA: null,
                defaultB: null
            ).ToList();

            // Case: product is present in cache but absent on site. Delete from cache.

            LinkedProduct[] abominationsLinkedProducts = linkedProducts.Where(lp => lp.PrestaProduct == null).ToArray();

            if (abominationsLinkedProducts.Length != 0)
            {
                Log.Warning("Some products are present in cache but absent on site. Clearing some parts of the cache.");

                foreach (LinkedProduct linkedProduct in abominationsLinkedProducts)
                {
                    Log.Warning($"Deleting product {linkedProduct.IdSourcedFromPresta} from cache.");

                    _webApi.MyApiProductsSourcedFromVk_SetDeactivated(linkedProduct.IdSourcedFromPresta.Val());

                    linkedProducts.Remove(linkedProduct);
                }
            }

            // Case: product is present on site but absent in cache. Throw an exception. This is a bug in code above.

            abominationsLinkedProducts = linkedProducts.Where(lp => lp.PrestaCache == null).ToArray();

            if (abominationsLinkedProducts.Length != 0)
            {
                var sb = new StringBuilder();
                sb.AppendLine("Some products were fetched after cache info but there is no cache info about these products. Products in case:");

                foreach (LinkedProduct linkedProduct in abominationsLinkedProducts)
                {
                    sb.Append(linkedProduct.IdSourcedFromPresta);
                    sb.Append(",");
                }

                Log.Error(sb.ToString());

                throw new Exception(sb.ToString());
            }

            Log.Verbose("6: Matched Presta products and Presta Cache.");

            // Step 7: Match linked products from site with Vk photos (except ones that are 100% unchanged).

            IEnumerable<EnumerableMatch.Result<string, LinkedProduct, VkScrapItem>> linkedProductsAndScrapItemsMatch = 
                linkedProducts.Match(
                    right: vkScrapItemsToProcessFurther,
                    selectKeyLeft: lp => lp.PrestaProduct.reference,
                    selectKeyRight: vksi => string.IsNullOrWhiteSpace(vksi.Art) ? null : LinkedNaming.PrestaSiteReferenceFromVkAuthorityArt(vksi.Art)
                );

            linkedProducts = linkedProductsAndScrapItemsMatch.Select(
                (EnumerableMatch.Result<string, LinkedProduct, VkScrapItem> r) => {
                    VkScrapItem rightFirstWithArt;

                    switch (r.Type)
                    {
                        case EnumerableMatch.ResultType.OnlyLeftSingle: 
                            return r.LeftSingle;

                        case EnumerableMatch.ResultType.OnlyRightSingle:
                            if (string.IsNullOrWhiteSpace(r.RightSingle.Art))
                                return null;

                            var lp = new LinkedProduct();
                            lp.Vk = r.RightSingle;

                            return lp;

                        case EnumerableMatch.ResultType.OneToOne:
                            if (string.IsNullOrWhiteSpace(r.RightSingle.Art))
                                return r.LeftSingle;

                            r.LeftSingle.Vk = r.RightSingle;

                            return r.LeftSingle;

                        // Options below are not the most optimal program behavior
                        case EnumerableMatch.ResultType.OnlyLeftMultiple:
                        case EnumerableMatch.ResultType.ManyToMany:
                        case EnumerableMatch.ResultType.LeftMultipleOneRight:
                            throw new Exception("The app cannot handle multiple presta products with the same reference.");

                        case EnumerableMatch.ResultType.OnlyRightMultiple:
                            if (!r.RightArray.All(vksi => vksi.ParsingResult == ParsingResult.ParsingError))
                                throw new Exception($"It is expected that same art products have parsing errors. Reference is {r.Key}");

                            rightFirstWithArt = r.RightArray.FirstOrDefault(vksi => !string.IsNullOrWhiteSpace(vksi.Art));

                            if (rightFirstWithArt == null)
                                return null;
                            return new LinkedProduct() { Vk = rightFirstWithArt };

                        case EnumerableMatch.ResultType.LeftOneRightMultiple:
                            if (!r.RightArray.All(vksi => vksi.ParsingResult == ParsingResult.ParsingError))
                                throw new Exception($"It is expected that same art products have parsing errors. Reference is {r.Key}");

                            rightFirstWithArt = r.RightArray.FirstOrDefault(vksi => !string.IsNullOrWhiteSpace(vksi.Art));
                            r.LeftSingle.Vk = rightFirstWithArt;
                            return r.LeftSingle;

                        default: throw new EnumAbominationException(typeof(EnumerableMatch.ResultType));
                    }
                }
            ).Where(lp => lp != null).ToList();

            foreach (LinkedProduct linkedProduct in linkedProducts)
            {
                ProductInfo ver = allProductsVersions.FirstOrDefault(ver => ver.ProductId == linkedProduct.IdSourcedFromPresta);

                if (ver?.Reference != null)
                {
                    linkedProduct.RelevantProductVersion = ver.ProductVer;
                }
            }

            Log.Verbose("7: Matched Presta and Vk.");

            // Step 8: In case of Vk photo that is not in presta site cache, we need to be sure that no duplicate product with such ref/art is created.
            // If the product exists on site (matched by art/ref) but is inactivated, we need to revive it here.

            if (!isJustInitialization)
            {
                List<LinkedProduct> linkedProductsThatMayNeedToBeRevived = linkedProducts.Where(lp => lp.PrestaProduct == null).ToList();

                foreach (LinkedProduct badLinkedProduct in linkedProductsThatMayNeedToBeRevived.Where(lp => lp.Vk == null).ToArray())
                {
                    Log.Error("LinkedProduct has both PrestaProduct and Vk Null.");
                    Log.Error($"Remaining info: RelevantVer: {badLinkedProduct.RelevantProductVersion} PrestaCache: {badLinkedProduct.PrestaCache}");

                    linkedProducts.Remove(badLinkedProduct);
                    linkedProductsThatMayNeedToBeRevived.Remove(badLinkedProduct);
                }

                if (linkedProductsThatMayNeedToBeRevived.Count != 0)
                {
                    filterByValues = linkedProductsThatMayNeedToBeRevived
                        .Select(pi => LinkedNaming.PrestaSiteReferenceFromVkAuthorityArt(pi.Vk.Art));

                    List<product> productsToBeRevived = _webApi.GetByOneKeyFilterBatched(_webApi.GetProducts,
                        "reference",
                        filterByValues,
                        "id_ASC",
                        stopIfNothingMore: false,
                        batchSize: 150,
                        cancellationToken: cancellationToken
                    );

                    if (productsToBeRevived.Count != 0)
                    {
                        Log.Information($"There are {productsToBeRevived.Count} presta site products that need to be revived. Recreating cache values.");

                        foreach (product product in productsToBeRevived)
                        {
                            LinkedProduct linkedProductRelatedToThisProduct =
                                linkedProducts.Single(lp => lp.Vk != null && LinkedNaming.PrestaSiteReferenceFromVkAuthorityArt(lp.Vk.Art) == product.reference);

                            linkedProductRelatedToThisProduct.PrestaCache = new SourcedFromVkProductCacheInfo(product.id.Val());
                            linkedProductRelatedToThisProduct.PrestaProduct = product;

                            _webApi.MyApiProductsSourcedFromVk_AddNew(product.id.Val());

                            Log.Information($"Reviving (re-added to cache) product {product.id.Val()}, ref={product.reference}");
                        }
                    }
                }
            }

            if (linkedProducts.Where(lp => lp.PrestaProduct != null).GroupBy(lp => lp.PrestaProduct.id).Any(g => g.Count() > 1))
            {
                IEnumerable<IGrouping<long?, LinkedProduct>> dupGroups = linkedProducts.Where(lp => lp.PrestaProduct != null).GroupBy(lp => lp.PrestaProduct.id)
                    .Where(g => g.Count() > 1);

                foreach (IGrouping<long?, LinkedProduct> group in dupGroups)
                {
                    Log.Error($"Group with product id {group.Key} has multiple res.");
                }
            }

            Log.Verbose("8: Revived lacking cache info.");

            // Step 9: choose desired action for each linked product
            // At this point LinkedProducts may contain next:
            foreach (LinkedProduct linkedProduct in linkedProducts)
            {
                if ((linkedProduct.PrestaCache == null) != (linkedProduct.PrestaProduct == null))
                    throw new Exception("LinkedProduct abomination (2)");

                bool vkIsNull = linkedProduct.Vk == null;
                bool existsOnSite = linkedProduct.PrestaProduct != null;

                if (vkIsNull)
                {
                    if (existsOnSite)
                    {
                        linkedProduct.DesiredAction = LinkedProductDesiredAction.InactivateItBecauseVkIsNull;

                        continue;
                    }

                    throw new Exception("LinkedProduct abomination (3)");
                }

                bool vkHasParsingError = linkedProduct.Vk.ParsingResult == ParsingResult.ParsingError;

                if (vkHasParsingError)
                {
                    if (existsOnSite)
                    {
                        linkedProduct.DesiredAction = LinkedProductDesiredAction.InactivateItBecauseVkHasParsingError;

                        continue;
                    } else
                    {
                        linkedProduct.DesiredAction = LinkedProductDesiredAction.DoNotCreateNewFromVkBecauseVkHasParsingError;

                        continue;
                    }
                }

                if (existsOnSite)
                {
                    linkedProduct.DesiredAction = LinkedProductDesiredAction.UpdateIt_IfOutdated;
                } else
                {
                    linkedProduct.DesiredAction = LinkedProductDesiredAction.NewFromVk;
                }
            }

            linkedProducts.Sort(LinkedProductComparer.Instance);

            _matchedProducts = linkedProducts;

            Log.Verbose("9: Fully matched vk and presta products.");

            _vkUpdatingProductOptionsOnSite = this.GetAllVkUpdatingSiteProductsOptions(cancellationToken);

            Log.Verbose("10: Fetched relevant products options.");

            this.FetchAllSpecificPrices(cancellationToken);

            Log.Verbose("11: Fetched relevant prices.");

            sw.Stop();

            Log.Information($"PrepareForProductsProcessing finished in {sw.Elapsed}");
        }

        private List<ProductOption> GetAllSiteProductOptions(long[] productIds, CancellationToken ct)
        {
            List<combination> cmb = _webApi.GetByOneKeyFilterBatched(_webApi.GetCombinations,
                "id_product",
                productIds.Select(i => Convert.ToString(i)),
                "id_ASC",
                stopIfNothingMore: true,
                batchSize: 125,
                cancellationToken: ct
            );

            return this.GetProductOptionsInner(cmb);
        }

        private List<ProductOption> GetAllVkUpdatingSiteProductsOptions(CancellationToken ct)
        {
            IEnumerable<string> filterByValues = this.GetListOfProductIdsAndZeroFromMatchedProducts();

            List<combination> cmb = _webApi.GetByOneKeyFilterBatched(_webApi.GetCombinations,
                "id_product",
                filterByValues,
                "id_ASC",
                stopIfNothingMore: false,
                batchSize: 125,
                cancellationToken: ct
            );

            return this.GetProductOptionsInner(cmb);
        }

        private List<ProductOption> GetProductOptionsInner(List<combination> cmb)
        {
            List<ProductOption> productOptions = cmb.Select(this.ConvertEngineCombinationToProductOption).ToList();

            foreach (ProductOption productOption in productOptions.ToList())
            {
                if (productOption.ProductId == 0 || productOption.AttributeOptions.Count == 0)
                    continue;

                ProductOption duplicate = productOptions.FirstOrDefault(po2 =>
                    po2.Id != productOption.Id
                    &&
                    po2.ProductId == productOption.ProductId
                    &&
                    po2.AttributeOptions.Select(ao => ao.AttributeId).SequenceEqual(productOption.AttributeOptions.Select(ao => ao.AttributeId))
                );

                if (duplicate != null)
                {
                    _webApi.DeleteCombination(duplicate.Id);

                    productOptions.Remove(duplicate);
                }
            }

            return productOptions;
        }

        private ProductOption ConvertEngineCombinationToProductOption(combination cm)
        {
            return new ProductOption()
            {
                Id = cm.id.Val(),
                ProductId = cm.id_product.Val(),
                Price = (int)cm.price,
                Quantity = cm.quantity,
                IsDefault = cm.default_on.HasValue && cm.default_on.Value > 0,
                AttributeOptions = cm.associations.product_option_values.Select(pov => _productAttributesConductor.GetProductOptionValueById(pov.id)).ToList()
            };
        }

        private void FetchAllSpecificPrices(CancellationToken cancellationToken)
        {
            IEnumerable<string> filterByValues = this.GetListOfProductIdsAndZeroFromMatchedProducts();

            _allSpecificPrices = _webApi.GetByOneKeyFilterBatched(
                _webApi.GetSpecificPrices,
                "id_product",
                filterByValues,
                "id_ASC",
                stopIfNothingMore: false,
                batchSize: 100,
                cancellationToken: cancellationToken
            );
        }

        private combination ConvertProductOptionToEngineCombination(ProductOption po)
        {
            var res = new combination()
            {
                id = po.Id,
                id_product = po.ProductId,
                quantity = po.Quantity,
                price = po.Price,
                minimal_quantity = 1,
                default_on = po.IsDefault ? 1 : 0
            };

            foreach (AttributeOption attributeOption in po.AttributeOptions)
                res.associations.product_option_values.Add(new Aux.product_option_value() { id = attributeOption.AttributeId });

            return res;
        }

        private class LinkedProductComparer : IComparer<LinkedProduct>
        {
            public static readonly LinkedProductComparer Instance = new();

            public int Compare(LinkedProduct a, LinkedProduct b)
            {
                DateTime creationDateA;
                DateTime creationDateB;

                if (a.PrestaProduct?.date_add != null)
                    creationDateA = DateTime.ParseExact(a.PrestaProduct.date_add, PrestaSharpConstants.PRODUCT_DATE_TIMES_FORMAT, CultureInfo.InvariantCulture);
                else if (a.Vk != null)
                    creationDateA = a.Vk.VkPhotoCreationDate;
                else
                    creationDateA = DateTime.MinValue;

                if (b.PrestaProduct?.date_add != null)
                    creationDateB = DateTime.ParseExact(b.PrestaProduct.date_add, PrestaSharpConstants.PRODUCT_DATE_TIMES_FORMAT, CultureInfo.InvariantCulture);
                else if (b.Vk != null)
                    creationDateB = b.Vk.VkPhotoCreationDate;
                else
                    creationDateB = DateTime.MinValue;

                return creationDateA.CompareTo(creationDateB);
            }
        }

        private IEnumerable<string> GetListOfProductIdsAndZeroFromMatchedProducts()
        {
            IEnumerable<string> productIds = _matchedProducts
                .Where(lp => lp.PrestaProduct != null)
                .Select(lp => Convert.ToString(lp.PrestaProduct.id.Val()))
                .Concat(new[] { "0" });

            return productIds;
        }

        public ExternalSyncData GetProductsForProm_OnCallerThread(CancellationToken ct)
        {
            List<SourcedFromVkProductCacheInfo> activeCachedProductsCacheInfo = _webApi.MyApiProductsSourcedFromVk_GetAllActiveProductsCacheInfo();

            long[] filterByValues = activeCachedProductsCacheInfo
                .Select(pi => pi.ProductId)
                .ToArray();

            List<ProductInfo> activeProducts = PrestaSiteWebApiCallsExtensions.GetByOneKeyFilterBatched(
                (arg0) => _webApi.MyApiProducts_GetInfo(arg0, _langAndTemplateConductor.UkrainianLanguage.Id),
                filterByValues,
                stopIfNothingMore: false,
                batchSize: 400,
                cancellationToken: ct,
                50
            );

            List <ProductOption> allProductOptionsOnSite = this.GetAllSiteProductOptions(filterByValues, ct);
            List<ExternalSyncProduct> allProducts = new();
            
            var res = new ExternalSyncData()
            {
                AllPrestaProducts = allProducts,
                ProductAttributesConductor = _productAttributesConductor
            };

            List<StockAvailableExtended> stockAvailables = PrestaSiteWebApiCallsExtensions.GetByOneKeyFilterBatched(
                _webApi.MyApiProducts_GetFullStockAvaliInfo,
                activeProducts.Select(p => p.ProductId),
                stopIfNothingMore: false,
                batchSize: 250,
                cancellationToken: ct,
                200
            );

            foreach (ProductInfo productInfo in activeProducts)
            {
                long productId = productInfo.ProductId;

                ProductOption[] productOptions = this.GetProductOptionsByProductId(allProductOptionsOnSite, productId).ToArray();

                foreach (ProductOption productOption in productOptions)
                {
                    StockAvailableExtended existingStockAvali = stockAvailables
                        .FirstOrDefault(se => se.StockAvailable.id_product == productId && se.StockAvailable.id_product_attribute == productOption.Id);

                    productOption.Quantity = existingStockAvali.MinBuglessQuantity;
                }

                allProducts.Add(new ExternalSyncProduct() { PrestaProduct = productInfo, ProductOptions = productOptions });
            }

            return res;
        }
    }
}