using System.Diagnostics.CodeAnalysis;
using Bukimedia.PrestaSharp;
using Bukimedia.PrestaSharp.Entities;
using declination = Bukimedia.PrestaSharp.Entities.FilterEntities.declination;

using irisdropwebservice.AppConfig;
using irisdropwebservice.AppConfig.ClassConfigurations;
using irisdropwebservice.Libs.PrestaSharp;
using Invictus.Nomenklatura.Misc;
using irisdropwebservice.Services.LinkService;
using irisdropwebservice.Services.PrestaSync.Ins;
using irisdropwebservice.Services.PrestaSync.Remote;
using irisdropwebservice.Services.PrestaSync.ServiceConductors;
using irisdropwebservice.Services.Statistics;
using irisdropwebservice.Services.VkScrap;


using Aux = Bukimedia.PrestaSharp.Entities.AuxEntities;
using System.IO;

using Invictus.Nomenklatura.Exceptions;

using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats.Jpeg;
using irisdropwebservice.Libs.KastaSharp;

namespace irisdropwebservice.Services.PrestaSync.SiteFillConductors
{
    class MatchedProductConductor : PrestaSiteFillConductorBase
    {
        protected override ILogger Log { get; }

        private readonly CategoriesConductorService _categoriesConductor;
        private readonly ProductAttributesConductorService _productAttributesConductor;
        private readonly LangAndTemplateConductorService _langAndTemplateConductor;
        private readonly PrestaCommon _prestaCommon;
        private readonly InvProductFeatureCentre _invProductFeatureCentre;
        private readonly AllProductsDataConductorService _allProductsDataConductor;
        private readonly IStatisticsService _statisticsService;
        private readonly IVkScrapFileDownload _vkScrapFileDownload;
        private readonly IPrestaSiteWebApiCallsWorkerNonUser _webApi;

        private readonly PrestaShopCCInfo _configuration = CCAppConfig.PrestaIrisDrop;

        public MatchedProductConductor(
            ILogger logger,
            IPrestaSiteWebApiCallsWorkerNonUser webApi,
            IStatisticsService statisticsService,
            CategoriesConductorService categoriesConductor,
            ProductAttributesConductorService productAttributesConductor,
            AllProductsDataConductorService allProductsDataConductor,
            IVkScrapFileDownload vkScrapFileDownload,
            LangAndTemplateConductorService langAndTemplateConductorService,
            PrestaCommon prestaCommon,
            InvProductFeatureCentre invProductFeatureCentre
        )
        {
            Log = logger;
            _webApi = webApi;
            _statisticsService = statisticsService;
            _categoriesConductor = categoriesConductor;
            _productAttributesConductor = productAttributesConductor;
            _allProductsDataConductor = allProductsDataConductor;
            _vkScrapFileDownload = vkScrapFileDownload;
            _langAndTemplateConductor = langAndTemplateConductorService;
            _prestaCommon = prestaCommon;
            _invProductFeatureCentre = invProductFeatureCentre;
        }

        public bool ProcessMatchedProduct2(LinkedProduct linkedProduct, bool forcePush, out bool wasError)
        {
            wasError = false;

            long productId = -2;

            try
            {
                return this.ProcessMatchedProduct2Inner(linkedProduct, forcePush, out productId);
            }
            catch (PrestaSharpException exc)
            {
                Log.Error($"Got exception while updating product {linkedProduct.Vk?.Art ?? linkedProduct.PrestaProduct?.reference ?? "(null)"}.");
                Log.Error(exc);
                wasError = true;
            }
            catch (Exception exc)
            {
                if (ExceptionUtil.HasTaskOrOperationCancelledException(exc) && !ExceptionUtil.HasTimeoutExceptionOrIOException(exc))
                    throw;
                Log.Error($"Got exception while updating product {linkedProduct.Vk?.Art ?? linkedProduct.PrestaProduct?.reference ?? "(null)"}.");
                Log.Error(exc);
                wasError = true;
            }

            if (wasError)
            {
                _statisticsService.PrestaSiteProductComputationException(_configuration.StatSourceName, productId);
            }

            return true;
        }

        public bool ProcessMatchedProduct2Inner(LinkedProduct linkedProduct, bool forcePush, out long productId)
        {
            bool isNew = false;

            switch (linkedProduct.DesiredAction)
            {
                case LinkedProductDesiredAction.InactivateItBecauseVkIsNull:

                    productId = linkedProduct.PrestaProduct.id.Val();

                    if (!this.DoInactivateSiteProduct(linkedProduct))
                    {
                        Log.Verbose($"Skipped inactivating product, ref={linkedProduct.PrestaProduct.reference}.");

                        return false;
                    } else
                    {
                        Log.Verbose($"Inactivated product due to other reasons, ref={linkedProduct.PrestaProduct.reference}.");

                        _statisticsService.SignalPrestaProductInactivatedDueToOtherReasons(_configuration.StatSourceName, productId);
                    }
                    
                    _webApi.MyApiProductsSourcedFromVk_SetDeactivated(productId);

                    return true;

                case LinkedProductDesiredAction.InactivateItBecauseVkHasParsingError:

                    productId = linkedProduct.PrestaProduct.id.Val();

                    if (!this.DoInactivateSiteProduct(linkedProduct))
                    {
                        Log.Verbose($"Skipped inactivating product, ref={linkedProduct.PrestaProduct.reference}.");

                        return false;
                    } else
                    {
                        Log.Verbose($"Inactivated product due to parsing error, ref={linkedProduct.PrestaProduct.reference}.");

                        _statisticsService.SignalPrestaSiteProductInactivatedDueToParsingError(_configuration.StatSourceName, productId);
                    }

                    // Keep it in cache as 'active' though.

                    return true;

                case LinkedProductDesiredAction.DoNotCreateNewFromVkBecauseVkHasParsingError:

                    productId = -1;

                    return false;

                case LinkedProductDesiredAction.NewFromVk:
                    Log.Debug($"Creating product, art={linkedProduct.Vk.Art}.");

                    linkedProduct.PrestaProduct = this.CreateAddBlankProduct(linkedProduct.Vk);

                    productId = linkedProduct.PrestaProduct.id.Val();

                    _webApi.MyApiProductsSourcedFromVk_AddNew(productId);

                    linkedProduct.PrestaCache = new SourcedFromVkProductCacheInfo(productId);
                    isNew = true;

                    goto _updateIt;

                case LinkedProductDesiredAction.UpdateIt_IfOutdated:
                _updateIt:

                    productId = linkedProduct.PrestaProduct.id.Val();

                    bool updated = false;
                    
                    bool needsUpdate = this.ProductsNeedsToBeUpdated(linkedProduct, forcePush);

                    if (needsUpdate)
                    {
                        this.UpdateProduct(linkedProduct);

                        Log.Verbose($"Updating vk full text hash, id={productId}, art={linkedProduct.Vk.Art}.");

                        // This is the moment. If somebody else updates this product before this call than it still counts as latest and won't be updated again.
                        _webApi.MyApiProductsSourcedFromVk_SetProductUpdatedPlusMostRecentProductVer(productId, linkedProduct.Vk.FullTextHash);

                        linkedProduct.PrestaCache.VkDescriptionHash = linkedProduct.Vk.FullTextHash;
                        linkedProduct.RelevantProductVersion = null; // It is unknown now

                        updated = true;
                        
                    }

                    bool exceptionHappened = false;
                    bool needsImageUpdate = this.ImageUpdateIsNeeded(linkedProduct);

                    if (needsImageUpdate)
                    {
                        updated |= this.UpdateImage(linkedProduct, out exceptionHappened);
                    }

                    if (exceptionHappened)
                    {
                        this.DoInactivateSiteProduct(linkedProduct);

                        Log.Verbose($"Inactivated product due to other reasons (Exception when uploading image), ref={linkedProduct.PrestaProduct.reference}.");

                        _statisticsService.SignalPrestaProductInactivatedDueToOtherReasons(_configuration.StatSourceName, productId);

                        _webApi.MyApiProductsSourcedFromVk_SetDeactivated(productId);

                        if (isNew)
                        {
                            _statisticsService.SignalPrestaProductCreatedNew(_configuration.StatSourceName,
                                productId,
                                linkedProduct.Vk.Art,
                                linkedProduct.Vk.VkComGroupPhotoUri
                            );
                        }
                    } else
                    {
                        if (updated)
                        {
                            if (isNew)
                            {
                                _statisticsService.SignalPrestaProductCreatedNew(_configuration.StatSourceName,
                                    productId,
                                    linkedProduct.Vk.Art,
                                    linkedProduct.Vk.VkComGroupPhotoUri
                                );
                            } else
                            {
                                _statisticsService.PrestaSiteProductUpdated(_configuration.StatSourceName, productId);

                                Log.Verbose($"Product was updated, ref={linkedProduct.PrestaProduct.reference}.");
                            }
                        } else
                        {
                            if (isNew)
                            {
                                throw new Exception("Abomination[5]!");
                            }

                            Log.Verbose($"Skipped updating product, ref={linkedProduct.PrestaProduct.reference}.");
                        }
                    }

                    return updated;
                case LinkedProductDesiredAction.None:
                default: throw TypeAbominationException.Enum(typeof(LinkedProductDesiredAction), linkedProduct.DesiredAction);
            }
        }

        private product CreateAddBlankProduct(VkScrapItem vksi)
        {
            var blankProduct = new product()
            {
                reference = LinkedNaming.PrestaSiteReferenceFromVkAuthorityArt(vksi.Art),

                name = _langAndTemplateConductor.CreateBlankProductName(),
                description = _langAndTemplateConductor.CreatePrestaText("Новий Товар. Будь ласка зачекайте поки опис поновиться.", null),
                description_short = _langAndTemplateConductor.CreatePrestaText("Новий Товар. Будь ласка зачекайте поки опис поновиться.", null),

                active = 0,
                visibility = "none",
                state = 1,

                price = 99999,

                minimal_quantity = 1,
                available_for_order = 1,
                show_price = 1,
                id_shop_default = 1,
                redirect_type = "404",
                pack_stock_type = 3,
                type = "combinations",
                additional_delivery_times = 1,

                available_now = _langAndTemplateConductor.CreatePrestaText("Цей розмір є у наявності", null),
                available_later = _langAndTemplateConductor.CreatePrestaText("Немає на складі але може бути доступним під предзамовлення", null)
            };

            this.SetProductCategories(blankProduct, vksi, true, out _);

            product product = _webApi.AddProduct(blankProduct);

            _webApi.MyApiProducts_SetDefaultReservationSettings(product.id.Val());

            return product;
        }

        private bool ProductsNeedsToBeUpdated(LinkedProduct linkedProduct, bool forcePush)
        {
            product product = linkedProduct.PrestaProduct;
            VkScrapItem vksi = linkedProduct.Vk;

            if (forcePush)
            {
                Log.Verbose($"Product ref={product.reference} needs to be updated because of forcePush.");

                return true;
            }

            if (this.ImageUpdateIsNeeded(linkedProduct))
            {
                // When vk album is changed but description remains - ImageUpdateIsNeeded will return true.
                // We need to update categories then.
                return true;
            }

            bool isInactivated = product.visibility != "both" || product.active == 0;

            // If product is inactivated at this point then we definitely need to update it
            if (isInactivated)
            {
                Log.Verbose($"Product ref={product.reference} needs to be updated because it is inactivated.");

                return true;
            }

            bool needsUpdateAfterCachedHashValues = linkedProduct.PrestaCache.VkDescriptionHash != vksi.FullTextHash;

            if (needsUpdateAfterCachedHashValues)
            {
                Log.Verbose($"Product ref={product.reference} needs to be updated because has absent or obsolete cached vk text value.");

                return true;
            }

            bool needsUpdateAfterProductWasChangedOnSite = 
                !linkedProduct.RelevantProductVersion.HasValue 
                ||
                linkedProduct.PrestaCache.LastProductVer != linkedProduct.RelevantProductVersion;

            if (needsUpdateAfterProductWasChangedOnSite)
            {
                Log.Verbose($"Product ref={product.reference} needs to be updated because presta site product version has changed.");

                return true;
            }

            // Do not update if admin has moved product out of vk managed category
            bool hasVkManagedCategory = this.HasVkManagedCategory(product);

            if (hasVkManagedCategory)
            {
                long productCategoryId = _categoriesConductor.GetCategoryId(vksi.AlbumTitle);

                if (!product.associations.categories.Any(ec => ec.id == productCategoryId))
                {
                    Log.Verbose($"Product ref={product.reference} needs to be updated because category needs to be changed.");

                    return true; // Album was changed
                }
            }

            return false;
        }

        private void SetProductCategories(product product, VkScrapItem vksi, bool initial, out bool categoryWasVkAndWasChanged)
        {
            categoryWasVkAndWasChanged = false;

            // 1: Remove vk categories
            foreach (Aux.category category in product.associations.categories.ToArray())
            {
                if (_categoriesConductor.IsVkManagedCategory(category.id))
                    product.associations.categories.Remove(category);
            }

            // 2: Add vk categories
            long vkProductCategoryId = _categoriesConductor.GetCategoryId(vksi.AlbumTitle);
            product.associations.categories.Add(new Aux.category(vkProductCategoryId));

            if (initial)
            {
                product.id_category_default = vkProductCategoryId;
            } else if (product.id_category_default.HasValue)
            {
                InvProductCategory mainCategory = _invProductFeatureCentre.GetMainCategory(product);
                
                if (mainCategory.IsExportableCategory)
                {
                    product.id_category_default = mainCategory.PrestaCategoryId;
                }
                // Category was changed in VK
                else if (_categoriesConductor.IsVkManagedCategory(product.id_category_default.Val()))
                {
                    categoryWasVkAndWasChanged = true;
                    product.id_category_default = vkProductCategoryId;
                }

            }
        }

        private bool HasVkManagedCategory(product product)
        {
            return product.associations.categories.Any(c => _categoriesConductor.IsVkManagedCategory(c.id));
        }

        private bool IsDefaultCategoryVkManagedCategory(product product)
        {
            return _categoriesConductor.IsVkManagedCategory(product.id_category_default ?? 0);
        }

        private void UpdateProduct(LinkedProduct linkedProduct)
        {
            product product = linkedProduct.PrestaProduct;
            VkScrapItem vksi = linkedProduct.Vk;
            
            long productId = product.id.Val();

            Log.Debug($"Updating product, vk product link: {vksi.VkComGroupPhotoUri}, art={vksi.Art}");

            // Category

            bool categoryWasVkAndWasChanged;
            this.SetProductCategories(product, vksi, false, out categoryWasVkAndWasChanged);

            // Reference
            product.reference = LinkedNaming.PrestaSiteReferenceFromVkAuthorityArt(vksi.Art);
            product.type = "combinations";

            // Ensure it is valid and visible
            this.EnsureSiteProductIsActive(product);

            // Texts
            _langAndTemplateConductor.SetSiteProductNameAndDescriptions(product, vksi, false);

            // Prices
            if (vksi.PriceOld != null && vksi.PriceNew != null)
            {
                product.price = vksi.PriceOld.Value + _configuration.NonDropShippersPriceIncreaseUAH;
                product.wholesale_price = vksi.PriceOld.Value * 0.909M;

                string albumTitleLowerCase = linkedProduct.Vk.AlbumTitle.ToLower(CultureHandler.UkrainianCulture);

                bool markAsOnSaleOnSite =
                    _configuration.SetAsOnSaleOnlyForEndCategoriesContainingText.Any(substr => albumTitleLowerCase.Contains(substr));

                if (markAsOnSaleOnSite)
                    product.on_sale = 1;

                int specialPrice = vksi.PriceNew.Value + _configuration.NonDropShippersPriceIncreaseUAH;

                // Apply price reduction
                _allProductsDataConductor.AddSalePriceIfNeeded((int)product.price, specialPrice, productId);
            } else if (vksi.PriceOld != null)
            {
                product.price = vksi.PriceOld.Value + _configuration.NonDropShippersPriceIncreaseUAH;
                product.wholesale_price = vksi.PriceOld.Value * 0.909M;
                product.on_sale = 0;

                _allProductsDataConductor.DeleteAllSpecificPrices(productId);
            } else if (vksi.PriceNew != null)
            {
                product.price = vksi.PriceNew.Value + _configuration.NonDropShippersPriceIncreaseUAH;
                product.wholesale_price = vksi.PriceNew.Value * 0.909M;
                product.on_sale = 0;

                _allProductsDataConductor.DeleteAllSpecificPrices(productId);
            } else
            {
                throw new Exception("No price.");
            }

            // Autoset Features
            InvProductCategory mainCategory = _invProductFeatureCentre.GetMainCategory(product);

            if (mainCategory.IsExportableCategory)
            {
                var setFeatures = new List<(InvProductFeature, string[])>();
                var setFeaturesOverride = new List<(InvProductFeature, string[])>();

                string[] getAutoValue(CategoryFeatureValue categoryFeatureValue)
                {
                    if (categoryFeatureValue.Value != null)
                        return new string[] { categoryFeatureValue.Value };

                    if (categoryFeatureValue.InferFrom == null)
                        throw new Exception("categoryFeatureValue.Value == null && categoryFeatureValue.InferFrom == null");

                    return _productAttributesConductor.InferFeatureValueFromOtherFeature(product, categoryFeatureValue.InferFrom, categoryFeatureValue.InferenceTable);
                }

                foreach (InvProductFeature invFeature in mainCategory.Features)
                {
                    if (invFeature.AutoSet != null)
                    {
                        if (invFeature.AutoSet.Override)
                        {
                            setFeaturesOverride.Add((invFeature, getAutoValue(invFeature.AutoSet)));
                        } else
                        {
                            setFeatures.Add((invFeature, getAutoValue(invFeature.AutoSet)));
                        }
                    }

                    if (invFeature.AutoSetSuper != null)
                    {
                        if (invFeature.AutoSetSuper.Override)
                        {
                            if (!setFeaturesOverride.Any(s => s.Item1 == invFeature))
                                setFeaturesOverride.Add((invFeature, getAutoValue(invFeature.AutoSetSuper)));
                        }
                        else
                        {
                            if (!setFeatures.Any(s => s.Item1 == invFeature))
                                setFeatures.Add((invFeature, getAutoValue(invFeature.AutoSetSuper)));
                        }
                    }
                }

                if (setFeatures.Count > 0)
                {
                    foreach ((InvProductFeature, string[]) keyValuePair in setFeatures)
                    {
                        if (keyValuePair.Item2 == null)
                            continue;
                        
                        try
                        {
                            _productAttributesConductor.ReplaceOrAddProductFeatureValue(product, keyValuePair.Item1, keyValuePair.Item2, false);
                        }
                        catch (Exception exc)
                        {
                            throw new Exception($"Exception while attempting to set feature value {keyValuePair.Item1} {keyValuePair.Item2}, override false", exc);
                        }
                    }
                }

                if (setFeaturesOverride.Count > 0)
                {
                    foreach ((InvProductFeature, string[]) keyValuePair in setFeaturesOverride)
                    {
                        if (keyValuePair.Item2 == null)
                            continue;
                        
                        try
                        {
                            _productAttributesConductor.ReplaceOrAddProductFeatureValue(product, keyValuePair.Item1, keyValuePair.Item2, true);
                        }
                        catch (Exception exc)
                        {
                            throw new Exception($"Exception while attempting to set feature value {keyValuePair.Item1} {keyValuePair.Item2}, override true", exc);
                        }
                    }
                }
            }

            // List<StockAvailableExtended> stockAvailables = _webApi.MyApiProducts_GetFullStockAvaliInfo(productId);

            // Sizes aka Combinations
            var qtyUnitToProductOption = new Dictionary<VkScrapItemQtyUnit, ProductOption>(
                VkScrapItemQtyUnitByRefEqualityComparer.Instance
            );

            var sizesAttributeOptionIds = new List<long>(4);
            var engineCombinationsToUpdate = new List<ProductOption>();
            var engineCombinationsToSetAssoc = new List<ProductOption>();
            var engineCombinationsToAdd = new List<ProductOption>();

            foreach (VkScrapItemQtyUnit qtyUnit in vksi.QtyObjects.OfType<VkScrapItemQtyUnit>())
            {
                sizesAttributeOptionIds.Clear();

                if (qtyUnit.Size_Age != null)
                {
                    long attributeOptionId = _productAttributesConductor.GetProductOptionValue(_productAttributesConductor.ProductOptionOfAgeId, qtyUnit.Size_Age);
                    sizesAttributeOptionIds.Add(attributeOptionId);
                }

                if (qtyUnit.Size_Height_Fixed != null)
                {
                    long attributeOptionId = _productAttributesConductor.GetProductOptionValue(_productAttributesConductor.ProductOptionOfHeightId, qtyUnit.Size_Height_Fixed);
                    sizesAttributeOptionIds.Add(attributeOptionId);
                }

                if (qtyUnit.Size_Size != null)
                {
                    long attributeOptionId = _productAttributesConductor.GetProductOptionValue(_productAttributesConductor.ProductOptionOfSizeId, qtyUnit.Size_Size);
                    sizesAttributeOptionIds.Add(attributeOptionId);
                }

                if (sizesAttributeOptionIds.Count == 0)
                {
                    throw new Exception("No size");
                }

                int optionPriceModifier = (qtyUnit.SpecialPriceReduction.HasValue && qtyUnit.SpecialPriceReduction != 0)
                    ? -qtyUnit.SpecialPriceReduction.Value
                    : 0;

                ProductOption existingOption = _allProductsDataConductor.GetProductOptionsByProductId(productId)
                    .SingleOrDefault(po =>
                        po.AttributeOptions.Select(ao => ao.AttributeId).Intersect(sizesAttributeOptionIds).Count()
                        ==
                        po.AttributeOptions.Count
                    );

                // TODO?
                // existingOption.Quantity is always zero
                // StockAvailableExtended stockAvailable = stockAvailables.FirstOrDefault(se => se.StockAvailable.id_product_attribute == (existingOption?.Id ?? 0));

                if (existingOption != null)
                {
                    /*if (stockAvailable.StockAvailable != null)
                    {
                        // for some reason presta always returns 0 in combination.quantity
                        existingOption.Quantity = stockAvailable.PhysicalQuantity;
                    }*/

                    // existingOption.Quantity is always zero
                    // if (existingOption.Quantity != qtyUnit.Quantity || existingOption.Price != optionPriceModifier)
                    // {
                        // It looks like that when stock availables are set, <-- this action does not do much.
                        // If we immediately Get() after this Set() is still returns stock_availables value which is 'wat???'
                        // Back office product page reports correct stock_availables quantities (minus all reservations).
                        // Let it be here though.
                        existingOption.Quantity = qtyUnit.Quantity; // ?
                        existingOption.Price = optionPriceModifier;
                        engineCombinationsToUpdate.Add(existingOption);
                    // } else
                    // {
                    //    engineCombinationsToSetAssoc.Add(existingOption);
                    // }

                    qtyUnitToProductOption.Add(qtyUnit, existingOption);

                    continue;
                }

                var newOption = new ProductOption()
                {
                    ProductId = productId,
                    Quantity = qtyUnit.Quantity,
                    Price = optionPriceModifier,

                    // Id: Assign later,
                    AttributeOptions = new List<AttributeOption>()
                };

                foreach (long attributeOptionId in sizesAttributeOptionIds)
                    newOption.AttributeOptions.Add(_productAttributesConductor.GetProductOptionValueById(attributeOptionId));

                engineCombinationsToAdd.Add(newOption);
                qtyUnitToProductOption.Add(qtyUnit, newOption);
            }


            using IDisposable ignoreCancellationRegion = _webApi.IgnoreCancellationRegion();

            ProductOption[] allProductOptions = engineCombinationsToUpdate.Concat(engineCombinationsToAdd).Concat(engineCombinationsToSetAssoc).ToArray();

            ProductOption defaultProductOption = allProductOptions
                .OrderBy(c => string.Join(',', c.AttributeOptions.Select(ao => ao.Name)))
                .First();

            foreach (ProductOption allProductOption in allProductOptions)
            {
                if (allProductOption != defaultProductOption)
                    allProductOption.IsDefault = false;
            }

            defaultProductOption.IsDefault = true;

            List<ProductOption> engineCombinationsToDelete = _allProductsDataConductor.GetProductOptionsByProductId(productId).ToList();

            foreach (ProductOption existingProductOption in engineCombinationsToDelete.ToArray())
            {
                ProductOption existingOptionWeAreAwareOf = allProductOptions
                    .SingleOrDefault(po =>
                        po.AttributeOptions.Select(ao => ao.AttributeId).Intersect(existingProductOption.AttributeOptions.Select(ao => ao.AttributeId)).Count()
                        ==
                        po.AttributeOptions.Count
                    );

                if (existingOptionWeAreAwareOf != null)
                    engineCombinationsToDelete.Remove(existingProductOption);
            }

            if (engineCombinationsToDelete.Count != 0)
            {
                _allProductsDataConductor.DeleteCombinations(engineCombinationsToDelete);
            }

            if (engineCombinationsToUpdate.Count != 0)
            {
                List<ProductOption> upd1 = engineCombinationsToUpdate.Where(c => !c.IsDefault).ToList();
                List<ProductOption> upd2 = engineCombinationsToUpdate.Where(c => c.IsDefault).ToList();

                if (upd1.Count != 0)
                    _allProductsDataConductor.UpdateCombinations(upd1); // These should go first or else 'BOOM'

                if (upd2.Count != 0)
                    _allProductsDataConductor.UpdateCombinations(upd2);
            }

            if (engineCombinationsToAdd.Count != 0)
            {
                List<combination> newlyCreatedEngineCombinations = _allProductsDataConductor.AddCombinations(engineCombinationsToAdd);

                // Assign back new ids, match by AttributeOptions aka product_option_values
                foreach (ProductOption combination in engineCombinationsToAdd)
                {
                    combination.Id = newlyCreatedEngineCombinations.Single(
                        s2 =>
                            s2.associations.product_option_values
                                .OrderBy(s => s.id)
                                .Select(s => s.id)
                                .SequenceEqual(
                                    combination.AttributeOptions
                                        .OrderBy(s => s.AttributeId)
                                        .Select(s => s.AttributeId)
                                )
                    ).id.Val();
                }
            }

            IEnumerable<(ProductOption productOption, Aux.combinations productAssocCombination)> productOptionsJoin =
                allProductOptions.FullOuterJoinSingle(
                    product.associations.combinations,
                    po => po.Id,
                    eCmb => eCmb.id,
                    (option, combination, _) => (productOption: option, productAssocCombination: combination)
                );

            foreach ((ProductOption productOption, Aux.combinations productAssocCombination) in productOptionsJoin)
            {
                if (productAssocCombination == null)
                {
                    if (productOption == null)
                        continue;

                    product.associations.combinations.Add(new Aux.combinations() { id = productOption.Id });
                    continue;
                }

                if (productOption == null)
                {
                    product.associations.combinations.RemoveAll(c => c.id == productAssocCombination.id);
                }
            }

            product.position_in_category = null;
            product.cache_default_attribute = defaultProductOption.Id;
            product = _webApi.UpdateProducts(new List<product> { product })[0];

            // !!!
            linkedProduct.PrestaProduct = product;

            List<StockAvailableExtended> stockAvailables = _webApi.MyApiProducts_GetFullStockAvaliInfo(new [] { productId });

            var stockAvailablesToUpdateInBulk = new List<stock_available>();

            int totalStockAvali = 0;

            foreach (VkScrapItemQtyUnit qtyUnit in vksi.QtyObjects.OfType<VkScrapItemQtyUnit>())
            {
                ProductOption existingOption = qtyUnitToProductOption[qtyUnit];

                StockAvailableExtended existingStockAvali = stockAvailables
                    .FirstOrDefault(se => se.StockAvailable.id_product == productId && se.StockAvailable.id_product_attribute == existingOption.Id);

                if (existingStockAvali.StockAvailable == null)
                {
                    throw new Exception("This should have been automatically created by PrestaShop.");
                } else
                {
                    // ps_stock_available::physical_quantity which is StockAvailable.quantity Presta WebService API field, must remain the same.

                    AttributeOption ae = existingOption.AttributeOptions.Single();
                    string productOptionValueName = _productAttributesConductor.GetProductOptionValueById(ae.AttributeId).Name;

                    if (existingStockAvali.VkTempQuantityAdj > existingStockAvali.PrestaReservedQuantity)
                    {
                        Log.Error($"[QTY] Product {productId}, ref {product.reference}, size {productOptionValueName} size id {existingOption.Id} " +
                                  $"has VkTempQuantityAdj {existingStockAvali.VkTempQuantityAdj} more than " +
                                  $"PrestaReservedQuantity {existingStockAvali.PrestaReservedQuantity} which should never happen.");
                    }

                    int desiredStockAvaliQuantity = qtyUnit.Quantity 
                        + existingStockAvali.VkTempQuantityAdj // Stock is already reduced at vk site but is still not finalized on presta site
                        - existingStockAvali.PrestaReservedQuantity // Here
                        - existingStockAvali.ReservationModuleReservedQuantity;

                    // TODO: -1 is possible but this is not disruptive
                    Log.Debug($"[QTY] {product.reference} {productOptionValueName} ({existingOption.Id}) Id={productId} " +
                              $"Vk={qtyUnit.Quantity} Presta={existingStockAvali.ToString()}");

                    if (desiredStockAvaliQuantity < 0)
                    {
                        Log.Error($"[QTY] Product {productId}, ref {product.reference}, size {productOptionValueName} size id {existingOption.Id} has {desiredStockAvaliQuantity} desired stock avali");
                    } else
                    {
                        Log.Debug($"[QTY] Product {productId}, ref {product.reference}, size {productOptionValueName} size id {existingOption.Id} has {desiredStockAvaliQuantity} desired stock avali");
                    }

                    totalStockAvali += desiredStockAvaliQuantity;

                    if (linkedProduct.PrestaCache.VkDescriptionHash == null || existingStockAvali.StockAvailable.quantity != desiredStockAvaliQuantity)
                    {
                        existingStockAvali.StockAvailable.quantity = desiredStockAvaliQuantity;

                        stockAvailablesToUpdateInBulk.Add(existingStockAvali.StockAvailable);
                    }
                }
            }

            if (stockAvailablesToUpdateInBulk.Count != 0)
            {
                StockAvailableExtended mainStockAvali = stockAvailables.Single(se => se.StockAvailable.id_product_attribute == 0);

                mainStockAvali.StockAvailable.quantity = totalStockAvali;

                stockAvailablesToUpdateInBulk.Add(mainStockAvali.StockAvailable);

                _webApi.UpdateStockAvailables(stockAvailablesToUpdateInBulk);
            }

            if (product.cache_default_attribute != defaultProductOption.Id)
            {
                product.cache_default_attribute = defaultProductOption.Id;
                product.position_in_category = null;
                product = _webApi.UpdateProducts(new List<product> { product })[0];

                // !!!
                linkedProduct.PrestaProduct = product;

                if (!product.cache_default_attribute.HasValue || product.cache_default_attribute <= 0)
                {
                    throw new Exception("Invalid product.cache_default_attribute");
                }
            }
        }

        private bool ImageUpdateIsNeeded(LinkedProduct linkedProduct)
        {
            if (linkedProduct.PrestaCache.VkFullPhotoId == linkedProduct.Vk.FullId)
                return false;

            return true;
        }

        private bool UpdateImage(LinkedProduct linkedProduct, out bool exceptionHappened)
        {
            exceptionHappened = false;

            long productId = linkedProduct.PrestaProduct.id.Val();
            VkScrapItem vksi = linkedProduct.Vk;

            List<declination> images = _webApi.GetProductImages(productId);

            foreach (declination image in images)
                _webApi.DeleteProductImage(productId, image.id);

            byte[] imageFileContents = _vkScrapFileDownload.DownloadFile(linkedProduct.Vk.FullId);
            bool fixedImageFile = false;

        _retry:
            try
            {
                _webApi.AddProductImage(productId, imageFileContents);
            }
            catch (AggregateException exc)
                when (!fixedImageFile && exc.Flatten().InnerExceptions.OfType<PrestaSharpException>().Any(pExc => pExc.Message.Contains("Please set an \"image\" parameter with image data for value")))
            {
                Image imageToFix = Image.Load(imageFileContents);

                var ms = new MemoryStream();

                imageToFix.Save(ms, new JpegEncoder() { Quality = 80 });

                ms.Position = 0;

                imageFileContents = new byte[ms.Length];

                ms.Read(imageFileContents, 0, (int)ms.Length);

                fixedImageFile = true;

                goto _retry;
            }
            catch (Exception exc)
            {
                Log.Warning("Exception during adding product image.");
                Log.Warning(exc);

                exceptionHappened = true;

                return false;
            }

            Log.Verbose($"Updating vk full photo id, product_id={productId}, art={vksi.Art}, photo_id={vksi.FullId}.");

            _webApi.MyApiProductsSourcedFromVk_SetImageUpdated(productId, vksi.FullId);

            linkedProduct.PrestaCache.VkFullPhotoId = vksi.FullId;

            return true;
        }

        private bool DoInactivateSiteProduct(LinkedProduct linkedProduct)
        {
            if (linkedProduct.PrestaProduct.visibility == "none")
                return false;

            linkedProduct.PrestaProduct.visibility = "none";

            ProductOption[] cmbs = _allProductsDataConductor.GetProductOptionsByProductId(linkedProduct.PrestaProduct.id.Val()).ToArray();
            var productOptionsToUpdateToNull = new List<ProductOption>();

            foreach (ProductOption cmb in cmbs)
            {
                if (cmb.Quantity != 0)
                {
                    cmb.Quantity = 0;
                    productOptionsToUpdateToNull.Add(cmb);
                }
            }

            if (productOptionsToUpdateToNull.Count != 0)
                _allProductsDataConductor.UpdateCombinations(productOptionsToUpdateToNull);

            List<StockAvailableExtended> stockAvailables = _webApi.MyApiProducts_GetFullStockAvaliInfo(new [] { linkedProduct.PrestaProduct.id.Val() });

            var stockAvailablesToUpdate = new List<stock_available>();

            foreach (StockAvailableExtended stockAvali in stockAvailables)
            {
                // TODO: what to do when we are deactivating product but there are reserved ones?
                int desiredStockAvaliQuantity = 0 - stockAvali.PrestaReservedQuantity;

                if (stockAvali.RealPhysicalQuantity != desiredStockAvaliQuantity)
                {
                    stockAvali.StockAvailable.quantity = desiredStockAvaliQuantity;

                    stockAvailablesToUpdate.Add(stockAvali.StockAvailable);
                }
            }

            if (stockAvailablesToUpdate.Count != 0)
                _webApi.UpdateStockAvailables(stockAvailablesToUpdate);

            VkScrapItem vksi = linkedProduct.Vk;

            if (vksi == null)
            {
                // linkedProduct.Site.Product.description_short = 
                linkedProduct.PrestaProduct.description = _langAndTemplateConductor.CreatePrestaText("Товар Видалено.", "Товар Удалён.");
            } else
            {
                // Texts
                _langAndTemplateConductor.SetSiteProductNameAndDescriptions(linkedProduct.PrestaProduct, vksi, true);
            }

            linkedProduct.PrestaProduct.position_in_category = null;
            _webApi.UpdateProduct(linkedProduct.PrestaProduct);

            return true;
        }

        private void EnsureSiteProductIsActive(product product)
        {
            product.visibility = "both";
            product.active = 1;
        }

        private class VkScrapItemQtyUnitByRefEqualityComparer : IEqualityComparer<VkScrapItemQtyUnit>
        {
            public static VkScrapItemQtyUnitByRefEqualityComparer Instance { get; } = new();

            private VkScrapItemQtyUnitByRefEqualityComparer()
            {
            }

            public bool Equals(VkScrapItemQtyUnit x, VkScrapItemQtyUnit y)
            {
                return ReferenceEquals(x, y);
            }

            public int GetHashCode([DisallowNull] VkScrapItemQtyUnit obj)
            {
                return obj.GetHashCode();
            }
        }
    }
}