using Bukimedia.PrestaSharp.Entities;

using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;

using irisdropwebservice.Libs.Epicentr;
using irisdropwebservice.Libs.KastaSharp;
using irisdropwebservice.Libs.PrestaSharp;
using irisdropwebservice.Libs.PromSharp;
using irisdropwebservice.Services.Epicentr;
using irisdropwebservice.Services.KastaSync;
using irisdropwebservice.Services.PrestaSync.Ins;
using irisdropwebservice.Services.PrestaSync.ServiceConductors;
using irisdropwebservice.Services.PrestaSync.SiteFillConductors;
using irisdropwebservice.Services.PromSync;

namespace irisdropwebservice.Services.PrestaSync;

// For now all categories are linked via Kasta affiliationId + kindId

public enum InvPlatform
{
    Vk,
    Presta,
    Prom,
    Kasta,
    Epicentr,
    Rozetka
}

public enum InvProductFeatureType
{
    Invalid,
    Select,
    Multiselect,
    FreeText
}

public class InvProductCategory
{
    public static InvProductCategory Default { get; } = new InvProductCategory();
    
    public KastaCategoryId? KastaCategoryId { get; init; } 
    public long PrestaCategoryId { get; set; }
    public string EpicentrCategoryName { get; init; }
    
    public string EpicentrCategoryNameSingular { get; init; }
    
    public int EpicentrCategoryCode { get; init; }
    
    public int? PromCategoryId { get; init; }
    
    public long? PromGroupId { get; init; }
    
    public string PromCategoryLink { get; set; }

    public string EpicentrExportCategoryName => EpicentrCategoryName.Replace("-", " ").FirstCharManipulation(char.ToUpper);
    
    public List<InvProductFeature> Features { get; } = new();
    
    public List<InvProductFeature> HiddenFeatures { get; } = new();

    public bool IsExportableCategory => KastaCategoryId.HasValue;
    
    // public bool SuperCategoryIsAdult { get; init; }
    
    public InvProductCategory()
    {
        
    }
}

public class InvProductFeature
{
    public InvProductFeatureType Type { get; init; }
    
    public string Name { get; init; }
    
    public InvPlatform Platform { get; init; }
    
    public bool IsMandatory { get; init; }
    
    public object PlatformData { get; init; }
    
    public CategoryFeatureValue AutoSet { get; init; }
    public CategoryFeatureValue AutoSetSuper { get; init; }
}

public class InvProductFeatureCentre
{
    public const string EPICENTR_NAME_FEATURE = "Epicentr Особливість + Бренд + Модель + Інші характеристики";

    private readonly ILogger _logger = InvLog.Logger<InvProductFeatureCentre>();
    
    private readonly KastaSyncCommon _kastaCommon;
    private readonly EpicentrCommon _epicentrCommon;
    private readonly PrestaCommon _prestaCommon;
    private readonly PromSyncCommon _promCommon;
    private readonly CategoriesConductorService _categoriesConductorService;

    private readonly List<InvProductCategory> _categories = new();

    public InvProductFeatureCentre(
        KastaSyncCommon kastaCommon,
        EpicentrCommon epicentrCommon,
        PrestaCommon prestaCommon,
        PromSyncCommon promCommon,
        CategoriesConductorService categoriesConductorService
    )
    {
        _kastaCommon = kastaCommon;
        _epicentrCommon = epicentrCommon;
        _prestaCommon = prestaCommon;
        _promCommon = promCommon;
        _categoriesConductorService = categoriesConductorService;

        this.Initialize();
    }

    private void Initialize()
    {
        long[] kastaInvAffiliationIds = _prestaCommon.CategoriesFeaturesInfo.SuperCategories
            .Select(cat => cat.KastaAffiliationId)
            .ToArray();

        string[] kastaInvAffiliationAndKindIds = _prestaCommon.CategoriesFeaturesInfo.Categories
            .Select(cat => cat.KastaAffiliationAndKindId)
            .ToArray();

        KastaMyCategorySchema[] kastaInvSchemas = _kastaCommon.CategorySchemas
            .Where(schema => kastaInvAffiliationIds.Contains(schema.Category.AffiliationId))
            .ToArray();

        KastaMyCategorySchemaKind[] kastaInvSchemaKinds = kastaInvSchemas
            .SelectMany(s => s.KindsWithSchemas)
            .Where(sc => kastaInvAffiliationAndKindIds.Contains(sc.CategoryKindItself.AffiliationId + "_" + sc.CategoryKindItself.KindId))
            .ToArray();
        
        foreach (PrestaExteriorCategory prestaExteriorCategory in  _prestaCommon.CategoriesFeaturesInfo.Categories)
        {
            InvProductCategory invCategory = this.InitializeCategory(prestaExteriorCategory, kastaInvSchemaKinds);
            
            _logger.Verbose($"Initializing category {prestaExteriorCategory.NamePluralUkr} {prestaExteriorCategory.AffiliationId}_{prestaExteriorCategory.KindId}");
            
            _categories.Add(invCategory);
        }
    }

    public string GetFullFeatureName(PrestaFeatureSpecificationsPlatform platformSpec, PrestaFeatureSpecificationsPlatformFeature featureSpec, string categoryName, string featureName)
    {
        return this.GetFullFeatureName(platformSpec.FeaturePrefix, (featureSpec.SpecifyCategory ? categoryName : null), featureName);
    }
    
    public string GetFullFeatureName(string platformFeaturePrefix, string categoryNameOrNull, string featureName)
    {
        return  platformFeaturePrefix + " " + (categoryNameOrNull != null ? ("(" + categoryNameOrNull + ") ") : "") + featureName;
    }

    private InvProductCategory InitializeCategory(PrestaExteriorCategory prestaExteriorCategory, KastaMyCategorySchemaKind[] kastaInvSchemaKinds)
    {
        PrestaExteriorSuperCategory prestaExteriorSuperCategory =
            _prestaCommon.CategoriesFeaturesInfo.SuperCategories.Single(c => c.KastaAffiliationId == prestaExteriorCategory.AffiliationId);
        
        bool getIsMandatory(string featureName, bool defaultValue)
        {
            bool isMandatory;

            if (prestaExteriorCategory.MandatoryFeatures?.Contains(featureName) ?? false)
                isMandatory = true;
            else if (prestaExteriorCategory.NotMandatoryFeatures?.Contains(featureName) ?? false)
                isMandatory = false;
            else if (prestaExteriorSuperCategory.MandatoryFeatures?.Contains(featureName) ?? false)
                isMandatory = true;
            else if (prestaExteriorSuperCategory.NotMandatoryFeatures?.Contains(featureName) ?? false)
                isMandatory = false;
            else
                isMandatory = defaultValue;

            return isMandatory;
        }
        
        var invCategory = new InvProductCategory()
        {
            KastaCategoryId = new KastaCategoryId(prestaExteriorCategory.AffiliationId, prestaExteriorCategory.KindId),
            EpicentrCategoryName = prestaExteriorCategory.EpicentrCategoryName,
            EpicentrCategoryNameSingular = prestaExteriorCategory.EpicentrCategoryNameSingular,
            EpicentrCategoryCode = prestaExteriorCategory.EpicentrCategoryCode,
            PromCategoryId = prestaExteriorCategory.PromCategoryId,
            PromGroupId = prestaExteriorCategory.PromGroupId
            // SuperCategoryIsAdult = prestaExteriorSuperCategory.NameUkr == "Жінкам"
            // InvCategory = prestaExteriorCategory,
            // InvSuperCategory = prestaExteriorSuperCategory
        };
        
        InvProductFeature invProductFeature;

        // Kasta
        KastaMyCategorySchemaKind thisCategoryKastaSchemaKind = 
            kastaInvSchemaKinds
                .Single(s =>
                    s.CategoryKindItself.KindId == prestaExteriorCategory.KindId && s.CategoryKindItself.AffiliationId == prestaExteriorCategory.AffiliationId
                );

        foreach (KastaCategorySchemaItem schemaItem in thisCategoryKastaSchemaKind.SchemaItems)
        {
            if (schemaItem.Type != "characteristic")
                continue;
                
            bool isMandatory = getIsMandatory(schemaItem.HumanName, schemaItem.IsDesirable);
            
            CategoryFeatureValue autoSet = prestaExteriorCategory.AutoSet.SingleOrDefault(cfv => cfv.Feature == schemaItem.HumanName);
            CategoryFeatureValue autoSetSuper = prestaExteriorSuperCategory.AutoSet.SingleOrDefault(cfv => cfv.Feature == schemaItem.HumanName);
            
            invProductFeature = new InvProductFeature()
            {
                Platform = InvPlatform.Kasta,
                Name = schemaItem.HumanName,
                Type = schemaItem.Requirements.IsMulti ? InvProductFeatureType.Multiselect : InvProductFeatureType.Select,
                PlatformData = schemaItem,
                IsMandatory = isMandatory,
                AutoSet = autoSet,
                AutoSetSuper = autoSetSuper
            };
            
            // _logger.Debug($"Adding Kasta category feature {invProductFeature.Name}");
            
            invCategory.Features.Add(invProductFeature);
        }
        
        // Prom

        if (prestaExteriorCategory.PromCategoryId.HasValue)
        {
            PromCategoryDef promCategory = _promCommon.GetCategoryById(prestaExteriorCategory.PromCategoryId.Value);
            
            PrestaFeatureSpecificationsPlatform promPlatformSpec = _prestaCommon.CategoriesFeaturesInfo.PlatformsSpecifications.Platforms.Single(p => p.PlatformName == "Prom");

            invCategory.PromCategoryLink = promCategory.CatLink;
            
            PromAttributeDef[] boolAttributes = promCategory.Attributes.Where(a => a.Type == "bool").ToArray();

            if (boolAttributes.Length != 0)
            {
                var compositeAttributeDef = new PromBoolArrayCompositeAttributeDef();
                
                List<PromAttributeDef> platformData = new();
                
                string featureName = this.GetFullFeatureName(promPlatformSpec.FeaturePrefix, promCategory.NameUK, "Склад2");
                
                bool isMandatory = getIsMandatory(featureName, true);
                
                invProductFeature = new InvProductFeature()
                {
                    Platform = InvPlatform.Prom,
                    Name = featureName,
                    Type = InvProductFeatureType.Multiselect,
                    PlatformData = compositeAttributeDef,
                    IsMandatory = isMandatory,
                    AutoSet = null,
                    AutoSetSuper = null
                };
                
                foreach (PromAttributeDef attributeDef in boolAttributes)
                {
                    platformData.Add(attributeDef);
                }

                compositeAttributeDef.NameUK = featureName;
                compositeAttributeDef.BoolAttributes = platformData.ToArray();
                
                invCategory.Features.Add(invProductFeature);
            }

            foreach (PromAttributeDef attributeDef in promCategory.Attributes.Where(a => a.Type != "bool" && a.Type != "real" && a.Type != "integer"))
            {
                PrestaFeatureSpecificationsPlatformFeature invFeatureSpec = promPlatformSpec.Features.SingleOrDefault(s => s.Name == attributeDef.NameUK);

                if (invFeatureSpec == null)
                    invFeatureSpec = new PrestaFeatureSpecificationsPlatformFeature();
                
                string featureName = this.GetFullFeatureName(promPlatformSpec, invFeatureSpec, promCategory.NameUK, attributeDef.NameUK);
                
                bool isMandatory = getIsMandatory(featureName, true);
                
                CategoryFeatureValue autoSet = prestaExteriorCategory.AutoSet.SingleOrDefault(cfv => cfv.Feature == featureName);
                CategoryFeatureValue autoSetSuper = prestaExteriorSuperCategory.AutoSet.SingleOrDefault(cfv => cfv.Feature == featureName);

                InvProductFeatureType promTypeToInvFeaType(string promType)
                {
                    switch (promType)
                    {
                        case "singleselect": return InvProductFeatureType.Select;
                        case "multiselect": return InvProductFeatureType.Multiselect;
                        case "free": return InvProductFeatureType.FreeText;
                        case "bool": throw new NotImplementedException("bool");
                        default: throw new Exception($"Unknown prom attribute type {promType}");
                    }
                }

                invProductFeature = new InvProductFeature()
                {
                    Platform = InvPlatform.Prom,
                    Name = featureName,
                    Type = promTypeToInvFeaType(attributeDef.Type),
                    PlatformData = attributeDef,
                    IsMandatory = isMandatory,
                    AutoSet = autoSet,
                    AutoSetSuper = autoSetSuper
                };
                
                if (invFeatureSpec.AddEmptyValue && (invProductFeature.Type == InvProductFeatureType.Select || invProductFeature.Type == InvProductFeatureType.Multiselect))
                {
                    attributeDef.AttributeValues.Add(new PromAttributeValueDef() { Id = -1, NameUK = "[пусто]", NameRU = "[пусто]" });
                }
                
                (invFeatureSpec.Ignore ? invCategory.HiddenFeatures : invCategory.Features).Add(invProductFeature);
            }
        }
            
        // Epicentr
        
        if (prestaExteriorCategory.EpicentrCategoryName != null)
        {
            EpicentrCategoryEntry epiCategory = _epicentrCommon.CategoriesRoot.CategoryEntries.Single(c => c.Name == prestaExteriorCategory.EpicentrCategoryName);

            PrestaFeatureSpecificationsPlatform epiPlatformSpec = _prestaCommon.CategoriesFeaturesInfo.PlatformsSpecifications.Platforms.Single(p => p.PlatformName == "Epicentr");

            foreach (EpicentrFeatureEntry epiFeature in epiCategory.Features)
            {
                PrestaFeatureSpecificationsPlatformFeature invFeatureSpec = epiPlatformSpec.Features.SingleOrDefault(s => s.Name == epiFeature.FeatureName);

                if (invFeatureSpec == null)
                    invFeatureSpec = new PrestaFeatureSpecificationsPlatformFeature();
                
                string featureName = this.GetFullFeatureName(epiPlatformSpec, invFeatureSpec, epiCategory.Name, epiFeature.FeatureName);
                
                bool isMandatory = getIsMandatory(featureName, true);

                CategoryFeatureValue autoSet = prestaExteriorCategory.AutoSet.SingleOrDefault(cfv => cfv.Feature == featureName);
                CategoryFeatureValue autoSetSuper = prestaExteriorSuperCategory.AutoSet.SingleOrDefault(cfv => cfv.Feature == featureName);
                
                invProductFeature = new InvProductFeature()
                {
                    Platform = InvPlatform.Epicentr,
                    Name = featureName,
                    Type = epiFeature.FeatureType == "select" ? InvProductFeatureType.Select : 
                        (epiFeature.FeatureType == "multiselect" ? InvProductFeatureType.Multiselect : InvProductFeatureType.FreeText),
                    PlatformData = epiFeature,
                    IsMandatory = isMandatory,
                    AutoSet = autoSet,
                    AutoSetSuper = autoSetSuper
                };
                
                // _logger.Debug($"Adding Epicentr category feature {invProductFeature.Name}");
                
                (invFeatureSpec.Ignore ? invCategory.HiddenFeatures : invCategory.Features).Add(invProductFeature);
            }
            
            invProductFeature = new InvProductFeature()
            {
                Platform = InvPlatform.Epicentr,
                Name = EPICENTR_NAME_FEATURE,
                Type = InvProductFeatureType.FreeText,
                PlatformData = new EpicentrFeatureEntry() { FeatureOptions = new List<EpicentrFeatureOption>() },
                IsMandatory = false
            };
            
            // _logger.Debug($"Adding Epicentr category feature {invProductFeature.Name}");
            
            invCategory.Features.Add(invProductFeature);
        }

        return invCategory;
    }

    public InvProductCategory[] GetAllMainCategories()
    {
        return _categories.ToArray();
    }
    
    public InvProductCategory GetMainCategory(KastaCategoryId kastaCategoryId)
    {
        InvProductCategory res = _categories.Single(c => c.KastaCategoryId == kastaCategoryId);

        return res;
    }

    public InvProductCategory GetMainCategory(product product)
    {
        long[] categoryIds = product.associations.categories.Select(c => c.id).ToArray();

        return this.GetMainCategory(categoryIds);
    }

    public InvProductCategory GetMainCategory(IList<long> prestaCategories)
    {
        (KastaCategoryId, long)? kastaCategoryId = this.GetKastaCategoryIdWithPrestaCategory(prestaCategories);
        
        if (!kastaCategoryId.HasValue)
            return InvProductCategory.Default;
        
        InvProductCategory res = _categories.Single(c => c.KastaCategoryId == kastaCategoryId.Value.Item1);

        res.PrestaCategoryId = kastaCategoryId.Value.Item2;

        return res;
    }
    
    public InvProductCategory GetMainCategory(long prestaCategory)
    {
        KastaCategoryId? kastaCategoryId = this.GetKastaCategoryId(prestaCategory);
        
        if (!kastaCategoryId.HasValue)
            return InvProductCategory.Default;
        
        InvProductCategory res = _categories.Single(c => c.KastaCategoryId == kastaCategoryId);

        res.PrestaCategoryId = prestaCategory;

        return res;
    }

#region Kasta Categories
    
    private KastaCategoryId? GetKastaCategoryId(IList<long> allProductCategories)
    {
        (KastaCategoryId, long)? res = this.GetKastaCategoryIdWithPrestaCategory(allProductCategories);

        return res?.Item1;
    }
    
    private (KastaCategoryId, long)? GetKastaCategoryIdWithPrestaCategory(IList<long> allProductCategories)
    {
        (KastaCategoryId, long)? res = null;

        foreach (long categoryId in allProductCategories)
        {
            KastaCategoryId? tryCategory = this.GetKastaCategoryId(categoryId);

            if (tryCategory != null && res != null)
                throw new ArgumentException("There are multiple presta categories that correspond to kasta category", nameof(allProductCategories));

            if (tryCategory != null)
                res = (tryCategory.Value, categoryId);
        }

        return res;
    }

    private KastaCategoryId? GetKastaCategoryId(long categoryId)
    {
        List<CategoryInfo> categoryInfos = _categoriesConductorService.GetCategoryInfoAndParents(categoryId);

        long affiliationId = 0;
        long kindId = 0;

        foreach (CategoryInfo info in categoryInfos)
        {
            PrestaExteriorSuperCategory prestaExteriorSuperCategory = _prestaCommon.CategoriesFeaturesInfo.SuperCategories.SingleOrDefault(c => c.NameUkr == info.Name);

            if (prestaExteriorSuperCategory != null)
                affiliationId = prestaExteriorSuperCategory.KastaAffiliationId;
        }

        if (affiliationId == 0)
            return null;

        foreach (CategoryInfo info in categoryInfos)
        {
            PrestaExteriorCategory prestaExteriorCategory = _prestaCommon.CategoriesFeaturesInfo.Categories.SingleOrDefault(c => c.NamePluralUkr == info.Name && c.AffiliationId == affiliationId);

            if (prestaExteriorCategory != null)
            {
                if (kindId != 0)
                    throw new Exception("There are multiple Kasta category ids for given presta category id.");

                kindId = prestaExteriorCategory.KindId;
            }
        }

        if (kindId == 0)
            return null;

        return new KastaCategoryId(affiliationId, kindId);
    }

#endregion
    
}