using System.Threading.Tasks;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.ExteriorServ;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.AppConfig;
using irisdropwebservice.Legacy;
using irisdropwebservice.Services.PrestaSync.Ins;
using irisdropwebservice.Services.PrestaSync.Remote;
using irisdropwebservice.Services.VkScrap;

namespace irisdropwebservice.Services.PrestaSync
{
    public class PrestaSyncExteriorService : IExteriorService
    {
        public const bool DEBUG_ALLOW_FILL = true;
        private const bool DEBUG_ALLOW_POLL = true;

        public static long[] Debug_AlwaysProcessProductIds { get; } = new[] { 0L };

        private readonly ILogger _logger = InvLog.Logger<PrestaSyncExteriorService>();

        private readonly TriggerPrestaPushFlowEvent _triggerPrestaPushFlowEvent;
        private readonly TriggerExternalFlowsEvent _triggerExternalFlowsEvent;
        private readonly TriggerPollingCycleResoluteEvent _triggerPollingCycleResoluteEvent;
        private readonly IPrestaProductSyncWorker _prestaProductSyncWorker;
        private readonly IPrestaSyncWorker _prestaSyncWorker;

        public PrestaSyncExteriorService(
            TriggerPrestaPushFlowEvent triggerPrestaPushFlowEvent,
            LeBackgroundTasks threadedTasks,
            IPrestaProductSyncWorker prestaProductSyncWorker,
            IPrestaSyncWorker prestaSyncWorker,
            IPrestaSiteWebApiCallsWorkerNonUser webApiWorkerImpl,
            PrestaWebApiCallsWorkerUser webApiWorkerUserImpl,
            TriggerPollingCycleResoluteEvent triggerPollingCycleResoluteEvent,
            PrestaTelegramBridge prestaTelegramBridge,
            TriggerExternalFlowsEvent triggerExternalFlowsEvent
        )
        {
            _triggerPrestaPushFlowEvent = triggerPrestaPushFlowEvent;
            _prestaProductSyncWorker = prestaProductSyncWorker;
            _prestaSyncWorker = prestaSyncWorker;
            _triggerPollingCycleResoluteEvent = triggerPollingCycleResoluteEvent;
            _triggerExternalFlowsEvent = triggerExternalFlowsEvent;

            GC.KeepAlive(webApiWorkerImpl);
            GC.KeepAlive(webApiWorkerUserImpl);
            GC.KeepAlive(prestaTelegramBridge);

            threadedTasks.AddFixedTimeOfDayBackgroundTask(this.QueueCleanupCreatedOrphanedBlankProducts,
                nameof(PrestaSyncExteriorService) + "::" + nameof(this.QueueCleanupCreatedOrphanedBlankProducts),
                TimeSpan.FromHours(3)
            );
        }

        public Task Run()
        {
            return Task.CompletedTask;
        }

        public Task Run2(VkScrapResult initializeWith)
        {
            if (!CCAppConfig.PrestaIrisDrop.DoWork || !(DEBUG_ALLOW_FILL || DEBUG_ALLOW_POLL))
            {
                _logger.Warning("Service is configured to do nothing.");
            }

            Task<Task> res = _prestaSyncWorker.Run(w => w.InitializeServiceConductors())
                .ContinueWithShortThread(_ => {

                    // Some services depend on this operation.
                    Task prestaInitTask = _prestaProductSyncWorker.Run(w => w.InitializeFillSomeConductorsWithPrevScrappedData(initializeWith));

                    if (CCAppConfig.PrestaIrisDrop.DoWork && DEBUG_ALLOW_POLL)
                    {
                        prestaInitTask = prestaInitTask.ContinueWithShortThread(
                            _ => {
                                _triggerPrestaPushFlowEvent.CanBeginFiring();
                                _triggerPollingCycleResoluteEvent.CanBeginFiring();
                                _logger.Information("Running2.");
                            }
                        );
                    }

                    return prestaInitTask;
                });

            _logger.Information("Running.");

            return res.Unwrap();
        }

        public void QueueTriggerPushFlow(VkScrapResult data)
        {
            _triggerPrestaPushFlowEvent.QueueTryTrigger(data);
        }

        private void QueueCleanupCreatedOrphanedBlankProducts()
        {
            _prestaProductSyncWorker.Run(w => w.CleanupCreatedOrphanedBlankProducts());
        }
    }
}