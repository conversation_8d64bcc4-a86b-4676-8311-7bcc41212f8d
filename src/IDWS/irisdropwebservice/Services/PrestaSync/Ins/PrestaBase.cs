using irisdropwebservice.Services.VkScrap;

namespace irisdropwebservice.Services.PrestaSync.Ins
{
    public abstract class PrestaSiteFillConductorBase
    {
        protected abstract ILogger Log { get; }

        protected IEnumerable<VkScrapItem> CurrentParsedDataEligibleItems => CurrentParsedData.Items
            .Where(vksi => vksi.ParsingResult == ParsingResult.ParsingError || vksi.ParsingResult == ParsingResult.Success)
            .ToList();

        protected IEnumerable<VkScrapItem> CurrentParsedDataParsingSuccessOnlyItems => CurrentParsedData.Items
            .Where(vksi => vksi.ParsingResult == ParsingResult.Success)
            .ToList();

        protected VkScrapResult CurrentParsedData { get; private set; }

        public void SetCurrentParsedData(VkScrapResult currentParsedData)
        {
            CurrentParsedData = currentParsedData;
        }
    }
}