using System.Threading.Tasks;

using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.Legacy;
using irisdropwebservice.Services.VkScrap;

using Microsoft.Extensions.Hosting;

namespace irisdropwebservice.Services.PrestaSync.Ins
{
    public class TriggerPrestaPushFlowEvent : ResoluteEventBase
    {
        protected override ILogger Logger => InvLog.Logger<TriggerPrestaPushFlowEvent>();

        private readonly IPrestaProductSyncWorker _prestaProductSyncWorker;
        private VkScrapResult _lastParseResult;

        public TriggerPrestaPushFlowEvent(IInvAppLifetime applicationLifetime, InvAppConfig appConfig, IPrestaProductSyncWorker prestaProductSyncWorker)
            : base(
                applicationLifetime, appConfig,
                TimeSpan.FromMinutes(1),
                TimeSpan.FromMinutes(30),
                ResoluteEventInitialState.CanFireAfterTold
            )
        {
            _prestaProductSyncWorker = prestaProductSyncWorker;
        }

        protected override object GetCurrentDefaultArg()
        {
            return _lastParseResult;
        }

        protected override Task OnTrigger(object arg)
        {
            if (arg != null)
                _lastParseResult = (VkScrapResult)arg;

            if (_lastParseResult == null)
                return Task.CompletedTask;

            return _prestaProductSyncWorker.Run(w => w.Push(_lastParseResult));
        }
    }
    
    public class TriggerExternalFlowsEvent : ResoluteEventBase
    {
        protected override ILogger Logger => InvLog.Logger<TriggerExternalFlowsEvent>();

        public TriggerExternalFlowsEvent(IInvAppLifetime applicationLifetime, InvAppConfig appConfig)
            : base(
                applicationLifetime, appConfig,
                TimeSpan.FromMinutes(45),
                TimeSpan.FromMinutes(120),
                ResoluteEventInitialState.CanFireAfterTold
            )
        {
        }

        protected override object GetCurrentDefaultArg()
        {
            return null;
        }

        protected override Task OnTrigger(object arg)
        {
            if (arg == null)
                return Task.CompletedTask;

            return ((Func<Task>)arg)();
        }
    }
}