using Microsoft.Extensions.Hosting;
using System.Threading.Tasks;

using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;

using irisdropwebservice.Legacy;

namespace irisdropwebservice.Services.PrestaSync.Ins
{
    public class TriggerPollingCycleResoluteEvent : ResoluteEventBase
    {
        protected override ILogger Logger => InvLog.Logger<TriggerPollingCycleResoluteEvent>();

        private readonly ILazyResolve<IPrestaSyncWorker> _prestaSyncWorkerLazy;

        public TriggerPollingCycleResoluteEvent(IInvAppLifetime applicationLifetime, InvAppConfig appConfig, ILazyResolve<IPrestaSyncWorker> prestaSyncWorker)
            : base(
                applicationLifetime, appConfig,
                TimeSpan.FromSeconds(20),
                TimeSpan.FromSeconds(115),
                ResoluteEventInitialState.CanFireAfterTold
            )
        {
            _prestaSyncWorkerLazy = prestaSyncWorker;
        }

        protected override object GetCurrentDefaultArg()
        {
            return null;
        }

        protected override Task OnTrigger(object arg)
        {
            return _prestaSyncWorkerLazy.Resolve().Run(w => w.DoPollingCycle());
        }
    }
}
