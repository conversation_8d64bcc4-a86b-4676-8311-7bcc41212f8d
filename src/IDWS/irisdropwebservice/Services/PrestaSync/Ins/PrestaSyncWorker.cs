using System.Diagnostics;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

using Bukimedia.PrestaSharp.Entities;

using Invictus.Nomenklatura.Logg;

using irisdropwebservice.AppConfig;

using Invictus.Nomenklatura.Workers;
using irisdropwebservice.Libs.PrestaSharp;
using Invictus.Nomenklatura.Misc;
using irisdropwebservice.Services.LinkService;
using irisdropwebservice.Services.PrestaSync.Processing;
using irisdropwebservice.Services.PrestaSync.Remote;
using irisdropwebservice.Services.PrestaSync.ServiceConductors;
using irisdropwebservice.Services.PrestaSync.SiteFillConductors;
using irisdropwebservice.Services.VkScrap;


using Microsoft.Extensions.Hosting;

using Serilog.Events;

namespace irisdropwebservice.Services.PrestaSync.Ins
{
    public interface IPrestaWorkerPublic : IWorker<IPrestaWorkerPublic>
    {
        [CanFail]
        string GetVkLinksFromReference(string reference);

        string GetMainVkPhotoIdFromProductId(long productId);
    }

    public interface IPrestaSyncWorkerCallbacks : IWorker<IPrestaSyncWorkerCallbacks>
    {
        void ExecuteCallback(Action callback);
    }

    public interface IPrestaSyncWorker : IWorker<IPrestaSyncWorker>
    {
        void InitializeServiceConductors();

        void DoPollingCycle();
    }

    // It does polling of presta site, also do does some other things in between polls.
    public class PrestaSyncWorker : 
        IWorkerImpl<IPrestaSyncWorker>, IPrestaSyncWorker,
        IWorkerImpl<IPrestaSyncWorkerCallbacks>, IPrestaSyncWorkerCallbacks,
        IWorkerImpl<IPrestaWorkerPublic>, IPrestaWorkerPublic
    {
        public WorkerConfiguration WorkerConfiguration { get; } = new(
            "PSSYNC",
            new WorkerConfiguration.Thread("PrestaSync", ThreadPriority.Normal, false),
            LogEventLevel.Information,
            AllowDirectCall: false
        );

        public ILogger Log { get; } = InvLog.Logger<PrestaSyncWorker>();

        public WorkerCore Core { get; set; }

        IPrestaSyncWorker IWorkerImpl<IPrestaSyncWorker>.PublicInterface { get; set; }
        IPrestaSyncWorkerCallbacks IWorkerImpl<IPrestaSyncWorkerCallbacks>.PublicInterface { get; set; }
        IPrestaWorkerPublic IWorkerImpl<IPrestaWorkerPublic>.PublicInterface { get; set; }

        private readonly CarriersConductorService _carriersConductor;
        private readonly CountryAndStateConductorService _countryAndStateConductor;
        private readonly CategoriesConductorService _categoriesConductor;
        private readonly ProductAttributesConductorService _productAttributesConductor;
        private readonly LangAndTemplateConductorService _langAndTemplateConductor;
        private readonly IVkScrapGetLast _vkScrapGetLast;
        private readonly IHostApplicationLifetime _applicationLifetime;
        private readonly IPrestaSiteWebApiCallsWorkerNonUser _nonUserWebApi;
        private readonly IPrestaSiteWebApiCallsWorkerUser _userWebApi;
        private readonly PsOrderStatusChangeProcessing _orderStatusChangeProcessing;
        private readonly PsReservationsProcessing _reservationsProcessing;
        private readonly PsMiscProcessing _miscProcessing;
        private readonly IServiceFactory<ProcessingContext> _processingContextFactory;
        private readonly TriggerPollingCycleResoluteEvent _triggerPollingCycleResoluteEvent;

        public PrestaSyncWorker(
            IHostApplicationLifetime applicationLifetime,
            IPrestaSiteWebApiCallsWorkerNonUser nonUserWebApi,
            IPrestaSiteWebApiCallsWorkerUser userWebApi,
            CarriersConductorService carriersConductor,
            CountryAndStateConductorService countryAndStateConductor,
            CategoriesConductorService categoriesConductor,
            IVkScrapGetLast vkScrapGetLast,
            ProductAttributesConductorService productAttributesConductorService,
            LangAndTemplateConductorService langAndTemplateConductor,
            PsOrderStatusChangeProcessing orderStatusChangeProcessing,
            PsReservationsProcessing reservationsProcessing,
            IServiceFactory<ProcessingContext> processingContextFactory,
            TriggerPollingCycleResoluteEvent triggerPollingCycleResoluteEvent,
            PsMiscProcessing miscProcessing
        )
        {
            _applicationLifetime = applicationLifetime;
            _nonUserWebApi = nonUserWebApi;
            _userWebApi = userWebApi;
            _carriersConductor = carriersConductor;
            _countryAndStateConductor = countryAndStateConductor;
            _categoriesConductor = categoriesConductor;
            _vkScrapGetLast = vkScrapGetLast;
            _productAttributesConductor = productAttributesConductorService;
            _langAndTemplateConductor = langAndTemplateConductor;
            _orderStatusChangeProcessing = orderStatusChangeProcessing;
            _reservationsProcessing = reservationsProcessing;
            _processingContextFactory = processingContextFactory;
            _triggerPollingCycleResoluteEvent = triggerPollingCycleResoluteEvent;
            _miscProcessing = miscProcessing;
        }

        public void InitializeServiceConductors()
        {
            if (!CCAppConfig.PrestaIrisDrop.DoWork)
                return;

            _langAndTemplateConductor.InitializeBeforehand();
            _carriersConductor.InitializeBeforehand();
            _countryAndStateConductor.InitializeBeforehand();
            _productAttributesConductor.InitializeBeforehand();

            _orderStatusChangeProcessing.Initialize();
        }

        public string GetMainVkPhotoIdFromProductId(long productId)
        {
            ICollection<HistoricalVkImgId> vkImgIdsOrdered = _nonUserWebApi.MyApiProductsSourcedFromVk_GetHistoricalVkImgIds(new[] { productId }, "0")
                .OrderByDescending(hvid => hvid.Id)
                .ToArray();

            return vkImgIdsOrdered.First(imgId => imgId.VkFullPhotoId != HistoricalVkImgId.VkFullPhotoId_Deleted).VkFullPhotoId;
        }

        public string GetVkLinksFromReference(string reference)
        {
            var sw = Stopwatch.StartNew();

            Log.Verbose("GetVkLinks start, begin get LastScrapResult." + sw.Elapsed);

            if (!LinkedNaming.IsPrestaSiteReferenceVkAuthorityArt(reference))
            {
                long longReference;

                if (!long.TryParse(reference, out longReference))
                    return "?";

                reference = LinkedNaming.PrestaSiteReferenceFromVkAuthorityArt(longReference.ToString());
            }

            Task<VkScrapResult> lastScrapResultTask = Task.Run(() => _vkScrapGetLast.GetAnyLastScrapResult(ScrapReadMode.AlbumInfo));

            List<product> productsAll = _userWebApi.GetProducts(
                PrestaSiteWebApiCallsExtensions.CreatePrestaFilterByOneField("reference", new[] { reference }),
                "id_ASC",
                null
            );

            Log.Verbose("GetVkLinks got product by ref " + sw.Elapsed);

            product[] products = productsAll.Where(p => p.reference == reference).ToArray();

            if (products.Length == 0)
                return "-";

            if (products.Length != 1)
                return "--";

            long[] productIds = products.Select(p => p.id.Val()).ToArray();

            if (productIds.Length != 1)
                return "---";

            return this.GetVkLinksFromProduct(products[0], reference,lastScrapResultTask, sw);
        }

        private string GetVkLinksFromProduct(product product, string reference, Task<VkScrapResult> lastScrapResultTask, Stopwatch sw)
        {
            Log.Verbose("GetVkLinks get scrap start " + sw.Elapsed);
            
            long productId = product.id.Val();

            ICollection<HistoricalVkImgId> vkImgIdsOrdered = _userWebApi.MyApiProductsSourcedFromVk_GetHistoricalVkImgIds(new[] { productId }, reference)
                .OrderByDescending(hvid => hvid.Id)
                .ToArray();

            Log.Verbose("GetVkLinks got hvids " + sw.Elapsed);

            var bdr = new StringBuilder();
            bdr.AppendLine($"Артикул {product.reference}: ");

            if (vkImgIdsOrdered.Count == 0)
            {
                bdr.AppendLine("Історичних даних не знайдено.");

                return bdr.ToString();
            }

            HistoricalVkImgId lastVkImgId = vkImgIdsOrdered.First(); // OrderedByDescending

            bdr.AppendLine();
            bdr.AppendLine("VK:");

            IGrouping<string, HistoricalVkImgId>[] groupImgIdsByPhotoUris = vkImgIdsOrdered.GroupBy(vkImdId =>
                    vkImdId.VkFullPhotoId == HistoricalVkImgId.VkFullPhotoId_Deleted
                        ? HistoricalVkImgId.VkFullPhotoId_Deleted
                        : VkNamesAndUrls.GetVkComGroupPhotoUri(vkImdId.VkFullPhotoId)
                )
                .ToArray();

            IGrouping<string, HistoricalVkImgId> deletedVkImgIdsGroup = groupImgIdsByPhotoUris.SingleOrDefault(grp => grp.Key == HistoricalVkImgId.VkFullPhotoId_Deleted);

            // If last record is not 'deleted' then it is not deleted.
            bool isDeletedCurrently = deletedVkImgIdsGroup != null && deletedVkImgIdsGroup.Contains(lastVkImgId);

            if (isDeletedCurrently)
            {
                bdr.AppendLine("💚💔 видалено або перенесено у ПРОДАНО.");
                bdr.AppendLine();
            }

            IGrouping<string, HistoricalVkImgId>[] groupImgIdsByPhotoUrisExceptDeleted =
                groupImgIdsByPhotoUris
                    .Except(new[] { deletedVkImgIdsGroup })
                    .ToArray();

            if (!groupImgIdsByPhotoUrisExceptDeleted.Any())
                return bdr.ToString();

            string orderInAlbumToStr(int orderInAlbum)
            {
                int zeroBasedOrderInAlbum = orderInAlbum - 1;

                int _3base = (int)Math.Floor(zeroBasedOrderInAlbum / 3.0) + 1;
                int _4base = (int)Math.Floor(zeroBasedOrderInAlbum / 4.0) + 1;

                return $"Позиція {orderInAlbum}, {_3base} або {_4base} рядок.";
            }

            Log.Verbose("GetVkLinks before scrap wait " + sw.Elapsed);

            VkScrapResult lastScrapResult = lastScrapResultTask.Result;

            Log.Verbose("GetVkLinks after scrap gotten " + sw.Elapsed);

            bool isFirstPhoto = true;

            foreach (IGrouping<string, HistoricalVkImgId> normalVkImgIdsGroup in groupImgIdsByPhotoUrisExceptDeleted)
            {
                bdr.Append("💚 ");
                bdr.AppendLine(normalVkImgIdsGroup.Key); // Photo URI

                // All past albums
                foreach (HistoricalVkImgId vkImgId in normalVkImgIdsGroup)
                {
                    string groupAlbumUri = VkNamesAndUrls.GetVkComGroupAlbumUri(vkImgId.VkFullPhotoId);

                    bdr.Append("💙 ");
                    bdr.AppendLine(groupAlbumUri);

                    if (isFirstPhoto)
                    {
                        VkScrapItem scrapItem = lastScrapResult.Items.FirstOrDefault(item => item.FullId == vkImgId.VkFullPhotoId);

                        if (!isDeletedCurrently)
                        {
                            if (scrapItem != null)
                            {
                                bdr.Append(scrapItem.AlbumTitle);
                                bdr.Append(" (");
                                bdr.Append(orderInAlbumToStr(scrapItem.OrderInAlbum));
                                bdr.AppendLine(")");
                            }
                            else
                            {
                                bdr.AppendLine(" (?)");
                            }
                        }

                        if (scrapItem != null)
                        {
                            if (scrapItem.ParsingResult == ParsingResult.ParsingError)
                            {
                                bdr.Insert(0, "⚠️⚠️⚠️ Дані можуть бути не актуальними через помилку в розпізнаванні тексту." + Environment.NewLine);
                            } else if (scrapItem.ParsingResult != ParsingResult.Success)
                            {
                                bdr.Insert(0, "⚠️⚠️⚠️ Дані можуть бути не актуальними." + Environment.NewLine);
                            }
                        } else if (deletedVkImgIdsGroup == null)
                        {
                            bdr.Insert(0, "⚠️⚠️⚠️ Дані можуть бути не актуальними." + Environment.NewLine);
                        }
                    }

                    isFirstPhoto = false;

                    bdr.AppendLine();
                }
            }

            if (CCAppConfig.PrestaIrisDrop.DoWork)
            {
                bdr.AppendLine("Сайт:");

                long[] categoryIds = product.associations.categories.Select(c => c.id).ToArray();

                if (categoryIds.Length < 1)
                {
                    bdr.AppendLine("Помилка: у товара не знайдено категорії, зверніться до адміністратора.");
                } else
                {
                    bool productShown = false;

                    foreach (long categoryId in categoryIds)
                    {
                        CategoryInfo categoryInfo = _categoriesConductor.GetCategoryInfo(categoryId);

                        if (categoryInfo != null)
                        {
                            ProductUrls productLinks = _userWebApi.MyApiProducts_GetProductAndCategoryLink(productId, categoryInfo.Id, categoryInfo.LinkRewrite);

                            if (!productShown)
                            {
                                bdr.Append("💛 ");
                                bdr.AppendLine(productLinks.ProductUrl);
                                productShown = true;
                            }

                            bdr.Append("🧡 ");
                            bdr.AppendLine(productLinks.CategoryUrl);

                            bdr.AppendLine(categoryInfo.Name);
                        }
                    }
                }
            }

            Log.Verbose("GetVkLinks formulated " + sw.Elapsed);
            Log.Verbose("GetVkLinks formulated at " + ServerClock.GetCurrentUtcTime().ToString("hh.mm.ss.ffffff"));

            return bdr.ToString();
        }

        public void DoPollingCycle()
        {
            if (!CCAppConfig.PrestaIrisDrop.DoWork)
                return;

            const int MAX_TAKE_ENTRIES_AT_ONCE = 30;

            DataForProcessing dataForProcessing = _nonUserWebApi.Shared_GetAllUpdatesForProcessing(MAX_TAKE_ENTRIES_AT_ONCE);

            ProcessingContext processingContext = _processingContextFactory.Create();

            bool hasSome = this.ReservationUpdatesPollingCycle(processingContext, dataForProcessing.ProductReservations);
            hasSome |= this.OrderUpdatesPollingCycle(processingContext, dataForProcessing.OrdersHistoryEntries);
            hasSome |= this.EmailUpdatesPollingCycle(dataForProcessing.SentEmails);
            hasSome |= this.CustomerMessagesPollingCycle(processingContext, dataForProcessing.CustomerMessages);
            hasSome |= this.OrderFilesPollingCycle(processingContext, dataForProcessing.OrderFiles);

            bool fireFaster = hasSome;

            if (fireFaster)
            {
                _triggerPollingCycleResoluteEvent.QueueTryTrigger();
            }
        }

        private bool OrderUpdatesPollingCycle(ProcessingContext processingContext, List<OrdersHistoryEntry> historyEntries)
        {
            // TODO: move all of this to processing

            // This is used for both: do not block thread for too long, do not use sophisticated flush caches for dictionaries below
            const int MAX_PROCESSED_ENTRIES_AT_ONCE = 8;
            
            // (!) NOTE: we should not use these dictionaries for too long or data may get out of date.

            int totalProcessed = 0;
            long lastProcessedForStaff = -1;
            long lastProcessedForCustomer = -1;
            
            bool updateStaffLastProcessed()
            {
                if (lastProcessedForStaff == -1)
                    return false;
                _nonUserWebApi.MyApiOrders_SetOrdersHistoryIsProcessed(lastProcessedForStaff, staff: true); // TODO: no-cancel region

                return true;
            }

            bool updateCustomersLastProcessed()
            {
                if (lastProcessedForCustomer == -1)
                    return false;
                _nonUserWebApi.MyApiOrders_SetOrdersHistoryIsProcessed(lastProcessedForCustomer, staff: false);

                return true;
            }

            foreach (OrdersHistoryEntry ordersHistoryEntry in historyEntries)
            {
                bool processedForStaff;

                try
                {
                    processedForStaff = _orderStatusChangeProcessing.ProcessHistoryEntrySystemAndStaff(processingContext, ordersHistoryEntry);
                }
                catch (Exception)
                {
                    Log.Warning("Could not ProcessHistoryEntrySystemAndStaff.");

                    updateStaffLastProcessed(); // Important
                    updateCustomersLastProcessed();

                    throw;
                }

                if (processedForStaff)
                {
                    lastProcessedForStaff = ordersHistoryEntry.OrderHistoryId;
                    totalProcessed++;
                }

                bool processedForCustomers;

                try
                {
                    processedForCustomers = _orderStatusChangeProcessing.ProcessHistoryEntryCustomer(processingContext, ordersHistoryEntry);
                }
                catch (Exception)
                {
                    Log.Warning("Could not ProcessHistoryEntryCustomer.");

                    updateCustomersLastProcessed(); // Important
                    updateStaffLastProcessed();

                    throw;
                }

                if (processedForCustomers)
                {
                    totalProcessed++;
                    lastProcessedForCustomer = ordersHistoryEntry.OrderHistoryId;
                }

                if (totalProcessed >= MAX_PROCESSED_ENTRIES_AT_ONCE)
                    break;
            }

            if (historyEntries.Count == 0)
                return totalProcessed != 0; // false

            if (!updateStaffLastProcessed())
            {
                OrdersHistoryEntry last = historyEntries.OrderByDescending(he => he.OrderHistoryId).First();
                lastProcessedForStaff = last.OrderHistoryId;

                if (!updateStaffLastProcessed())
                    throw new Exception("Something is wrong [staff handling].");
            }

            if (!updateCustomersLastProcessed())
            {
                OrdersHistoryEntry last = historyEntries.OrderByDescending(he => he.OrderHistoryId).First();
                lastProcessedForCustomer = last.OrderHistoryId;

                if (!updateCustomersLastProcessed())
                    throw new Exception("Something is wrong [customers handling].");
            }

            if (totalProcessed != 0)
            {
                Log.Information($"Processed {totalProcessed} order history entries.");

                return true;
            }

            return false;
        }

        private bool ReservationUpdatesPollingCycle(ProcessingContext processingContext, List<ReservationHistoryEntry> reservations)
        {
            return _reservationsProcessing.ProcessReservations(processingContext, reservations);
        }

        private bool EmailUpdatesPollingCycle(List<EmailSent> emails)
        {
            if (emails.Count == 0) 
                return false;

            _nonUserWebApi.Mails_SetLastProcessedEmail(emails.Max(m => m.SentMailId));

            return true;
        }

        private bool CustomerMessagesPollingCycle(ProcessingContext processingContext, List<CustomerMessage> messages)
        {
            foreach (CustomerMessage customerMessage in messages)
            {
                _miscProcessing.ProcessCustomerMessage(processingContext, customerMessage);

                _nonUserWebApi.Misc_SetLastProcessedCustomerMessage(customerMessage.MessageId);
            }

            return messages.Count != 0;
        }

        private bool OrderFilesPollingCycle(ProcessingContext processingContext, List<OrderFile> orderFiles)
        {
            foreach (OrderFile orderFile in orderFiles)
            {
                _miscProcessing.ProcessOrderFile(processingContext, orderFile);

                _nonUserWebApi.Misc_SetLastProcessedOrderFile(orderFile.OrderFileId);
            }

            return orderFiles.Count != 0;
        }

        public void ExecuteCallback(Action callback)
        {
            callback();
        }
    }
}