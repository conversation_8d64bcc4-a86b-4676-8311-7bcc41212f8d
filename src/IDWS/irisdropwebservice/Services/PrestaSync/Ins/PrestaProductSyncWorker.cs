using System.Globalization;
using System.IO;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;

using Bukimedia.PrestaSharp.Entities;

using CsvHelper;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.AppConfig;
using irisdropwebservice.AppConfig.ClassConfigurations;


using Invictus.Nomenklatura.Workers;
using Invictus.Nomenklatura.Misc;

using irisdropwebservice.Libs.PrestaSharp;
using irisdropwebservice.Services.LinkService;
using irisdropwebservice.Services.PrestaSync.Remote;
using irisdropwebservice.Services.PrestaSync.ServiceConductors;
using irisdropwebservice.Services.PrestaSync.SiteFillConductors;
using irisdropwebservice.Services.PromSync.Ins;
using irisdropwebservice.Services.Statistics;
using irisdropwebservice.Services.VkScrap;


using Microsoft.Extensions.Hosting;
using Serilog.Events;

namespace irisdropwebservice.Services.PrestaSync.Ins
{
    public class DidPushEventArgs : EventArgs
    {
        public DidPushEventArgs(ExternalSyncData syncData)
        {
            PrestaSyncData = syncData;
        }
        
        public ExternalSyncData PrestaSyncData { get; }
    }

    public interface IPrestaProductSyncPublic
    {
        bool IsSiteFillHappening { get; }

        event EventHandler<DidPushEventArgs> DidPush;
    }

    public interface IPrestaProductSyncWorker : IWorker<IPrestaProductSyncWorker>
    {
        Task InitializeFillSomeConductorsWithPrevScrappedData([DoNotLog] VkScrapResult intermediateParsedData);

        [CanFail(5, LogLevel = LogEventLevel.Error)]
        void Push([DoNotLog] VkScrapResult parsedData);

        Task PushFurther();

        [CanFail(3)]
        void CleanupCreatedOrphanedBlankProducts();
    }

    public class PrestaProductSyncWorker : IWorkerImpl<IPrestaProductSyncWorker>, IPrestaProductSyncWorker,
        IPrestaProductSyncPublic
    {
        public WorkerConfiguration WorkerConfiguration { get; } = new(
            "PSPSNC",
            new WorkerConfiguration.Thread("PrestaProductSync", ThreadPriority.Normal, false),
            LogEventLevel.Information,
            AllowDirectCall: false
        );

        public ILogger Log { get; } = InvLog.Logger<PrestaProductSyncWorker>();

        public WorkerCore Core { get; set; }

        public IPrestaProductSyncWorker PublicInterface { get; set; }

        private volatile bool _initializing;

        private readonly ProductAttributesConductorService _productAttributesConductor;
        private readonly CategoriesConductorService _categoriesConductor;

        private readonly IHostApplicationLifetime _applicationLifetime;
        private readonly InvTasks _threadedTasks;
        private readonly AllProductsDataConductorService _allProductsDataConductor;
        private readonly PrestaCommon _prestaCommon;
        private readonly GlobalUserErrorsManager _globalUserErrorsManager;
        private readonly InvProductFeatureCentre _invProductFeatureCentre;
        private readonly IStatisticsService _statisticsService;
        private readonly IVkScrapFileDownload _vkScrapFileDownload;
        private readonly IPrestaSiteWebApiCallsWorkerNonUser _webApi;
        private readonly LangAndTemplateConductorService _langAndTemplateConductor;
        private readonly VkScrapExteriorService _vkScrapExteriorService;
        private readonly TriggerExternalFlowsEvent _triggerExternalFlowsEvent;
        private readonly PostponedProductUpdateService _postponedProductUpdateService;

        private CancellationTokenSource _currentPushCancellationTokenSource;

        private readonly PrestaShopCCInfo _configuration = CCAppConfig.PrestaIrisDrop;

        public bool IsSiteFillHappening
        {
            get 
            {
                lock (this)
                {
                    return _currentPushCancellationTokenSource != null;
                }
            }
        }

        // This event is called from thread pool asynchronously.
        public event EventHandler<DidPushEventArgs> DidPush;

        public PrestaProductSyncWorker(
            IHostApplicationLifetime applicationLifetime,
            InvTasks threadedTasks,
            IPrestaSiteWebApiCallsWorkerNonUser webApi,
            IStatisticsService statisticsService,
            CategoriesConductorService categoriesConductor,
            IVkScrapFileDownload vkScrapFileDownload,
            ProductAttributesConductorService productAttributesConductor,
            LangAndTemplateConductorService langAndTemplateConductor,
            VkScrapExteriorService vkScrapExteriorService,
            AllProductsDataConductorService allProductsDataConductor,
            PrestaCommon prestaCommon,
            GlobalUserErrorsManager globalUserErrorsManager,
            InvProductFeatureCentre invProductFeatureCentre,
            TriggerExternalFlowsEvent triggerExternalFlowsEvent,
            PostponedProductUpdateService postponedProductUpdateService
        )
        {
            _applicationLifetime = applicationLifetime;
            _threadedTasks = threadedTasks;
            _categoriesConductor = categoriesConductor;
            _webApi = webApi;
            _statisticsService = statisticsService;
            _vkScrapFileDownload = vkScrapFileDownload;
            _productAttributesConductor = productAttributesConductor;
            _langAndTemplateConductor = langAndTemplateConductor;
            _vkScrapExteriorService = vkScrapExteriorService;
            _allProductsDataConductor = allProductsDataConductor;
            _prestaCommon = prestaCommon;
            _globalUserErrorsManager = globalUserErrorsManager;
            _invProductFeatureCentre = invProductFeatureCentre;
            _triggerExternalFlowsEvent = triggerExternalFlowsEvent;
            _postponedProductUpdateService = postponedProductUpdateService;

            _vkScrapExteriorService.OnScrapFetchedEvent += this.VkScrapExteriorServiceOnOnScrapFetchedEvent;
            
            postponedProductUpdateService.OnFire += this.PostponedProductUpdateServiceOnOnFire;
        }

        private void VkScrapExteriorServiceOnOnScrapFetchedEvent(VkScrapResult obj)
        {
            lock (this)
            {
                _currentPushCancellationTokenSource?.Cancel();
            }
        }

        public async Task InitializeFillSomeConductorsWithPrevScrappedData(VkScrapResult intermediateParsedData)
        {
            await Task.Delay(100);
            
            if (!CCAppConfig.PrestaIrisDrop.DoWork || !PrestaSyncExteriorService.DEBUG_ALLOW_FILL)
            {
                Log.Information("InitializeFillSomeConductorsWithPrevScrappedData discarded due to configuraiton.");
                return;
            }

            _initializing = true;

            _productAttributesConductor.FillWithDataFromVk(intermediateParsedData.Items);

            _allProductsDataConductor.SetCurrentParsedData(intermediateParsedData);
            _allProductsDataConductor.PrepareForProductsProcessing(_applicationLifetime.ApplicationStopping, true);

            _categoriesConductor.SetCurrentParsedData(intermediateParsedData);
            _categoriesConductor.ReInitialize(
                _allProductsDataConductor.GetMatchedProducts().ToArray(),
                _allProductsDataConductor.GetProductIdsThatAreNotMatchedDueToPreCaching().ToArray()
            );
        }

        private void ReInitialize(VkScrapResult parsedData)
        {
            ArgumentNullException.ThrowIfNull(parsedData);

            _productAttributesConductor.FillWithDataFromVk(parsedData.Items);

            _allProductsDataConductor.SetCurrentParsedData(parsedData);
            _allProductsDataConductor.PrepareForProductsProcessing(_currentPushCancellationTokenSource.Token, false);

            if (this.CheckIfCancelled())
                return;

            _categoriesConductor.SetCurrentParsedData(parsedData);
            _categoriesConductor.ReInitialize(
                _allProductsDataConductor.GetMatchedProducts().ToArray(),
                _allProductsDataConductor.GetProductIdsThatAreNotMatchedDueToPreCaching().ToArray()
            );
        }

        public void Push(VkScrapResult parsedData)
        {
            if (!CCAppConfig.PrestaIrisDrop.DoWork || !PrestaSyncExteriorService.DEBUG_ALLOW_FILL)
            {
                Log.Information("DoPush discarded due to configuration.");
                return;
            }

            ArgumentNullException.ThrowIfNull(parsedData);

            if (!_initializing)
            {
                /* return */ Task.Delay(500).ContinueWithShortThread(_ => PublicInterface.Run(w => w.Push(parsedData))).Unwrap(); // TODO: implement task prolongation
                return;
            }

            lock (this)
            {
                _currentPushCancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(_applicationLifetime.ApplicationStopping);
            }

            try
            {
                if (this.CheckIfCancelled())
                    return;

                this.ReInitialize(parsedData);
                this.DoPushInner(parsedData);

                _categoriesConductor.ManageCategoriesActivation();
                
                _triggerExternalFlowsEvent.CanBeginFiring();
                _triggerExternalFlowsEvent.QueueTryTrigger((Func<Task>)(() => {
                    return PublicInterface.Run(w => w.PushFurther());
                }));
            }
            finally
            {
                lock (this)
                {
                    CancellationTokenSource cts = _currentPushCancellationTokenSource;
                    _currentPushCancellationTokenSource = null;
                    cts.Dispose();
                }
            }
        }

        private void ImportSizesFromPromCsv(string csvFilePath)
        {
            using var sr = new StreamReader(File.OpenRead(csvFilePath));
            using var csr = new CsvReader(sr, CultureInfo.InvariantCulture);

            csr.Context.RegisterClassMap<PromSyncWorker.CsvClassMap>();

            //PromProductCsv[] promRecords = csr.GetRecords<PromProductCsv>().ToArray();
            
            //_productAttributesConductor.FillWithDataFromVk();
        }

        private bool CheckIfCancelled()
        {
            lock (this)
            {
                if (_currentPushCancellationTokenSource.IsCancellationRequested)
                {
                    Log.Information("Stopping current process due to cancellation request.");

                    return true;
                }
            }

            return false;
        }

        private void DoPushInner(VkScrapResult parsedData)
        {
            if (this.CheckIfCancelled())
                return;

            int productN = 0;
            int totalUpdated = 0;
            int errorsCount = 0;

            LinkedProduct[] matchedProducts = _allProductsDataConductor.GetMatchedProducts().ToArray();
            DateTime timeSinceLastLogEventUtc = ServerClock.GetCurrentUtcTime().DateTime;
            TimeSpan reportEvery = TimeSpan.FromMinutes(1);

            IGrouping<int, LinkedProduct>[] groupedMatchedProducts = matchedProducts
                .GroupBy(lp =>
                    lp.Vk == null ? -100 : IntStringUtil.GetStableHashCode(lp.Vk.AlbumTitle)
                )
                .ToArray();

            Log.Verbose($"Matched {matchedProducts.Length} products.");

            ILogger matchedProductConductorLogger = InvLog.Logger<MatchedProductConductor>();
            
            foreach (IGrouping<int, LinkedProduct> group in groupedMatchedProducts.ToArray())
            {
                LinkedProduct[] orderedGroup = group
                    .OrderByDescending(lp => lp.Vk?.OrderInAlbum ?? 99000)
                    .ToArray();

                Log.Verbose($"Processing {group.Key} group.");

                if (group.First().Vk != null)
                {
                    Log.Verbose($"It is vk album name {group.First().Vk.AlbumTitle} group.");
                }

                void processLinkedProduct(LinkedProduct linkedProduct)
                {
                    var matchedProductConductor = new MatchedProductConductor(
                        matchedProductConductorLogger,
                        _webApi,
                        _statisticsService,
                        _categoriesConductor,
                        _productAttributesConductor,
                        _allProductsDataConductor,
                        _vkScrapFileDownload,
                        _langAndTemplateConductor,
                        _prestaCommon,
                        _invProductFeatureCentre
                    );

                    productN++;

                    if (ServerClock.GetCurrentUtcTime() - timeSinceLastLogEventUtc >= reportEvery)
                    {
                        Log.Debug("Processing product number " + productN + " out of " + matchedProducts.Length);
                        timeSinceLastLogEventUtc = ServerClock.GetCurrentUtcTime().DateTime;
                    }

                    bool wasError;
                    bool forcePush = linkedProduct.PrestaProduct != null && PrestaSyncExteriorService.Debug_AlwaysProcessProductIds.Contains(linkedProduct.PrestaProduct.id.Val());

                    if (linkedProduct.PrestaProduct != null)
                        _postponedProductUpdateService.DeleteFromQueue(linkedProduct.PrestaProduct.reference);
                    
                    bool updated = matchedProductConductor.ProcessMatchedProduct2(linkedProduct, forcePush, out wasError);

                    if (updated)
                        totalUpdated++;

                    if (wasError)
                        errorsCount++;
                }

                // New ones go first or presta will throw position_in_category exception.

                foreach (LinkedProduct linkedProduct in orderedGroup.Where(lp => lp.DesiredAction == LinkedProductDesiredAction.NewFromVk))
                {
                    if (this.CheckIfCancelled())
                        return;

                    processLinkedProduct(linkedProduct);
                }

                foreach (LinkedProduct linkedProduct in orderedGroup.Where(lp => lp.DesiredAction != LinkedProductDesiredAction.NewFromVk))
                {
                    if (this.CheckIfCancelled())
                        return;

                    processLinkedProduct(linkedProduct);
                }
            }

            /*
            if (productsCountToReorder != 0)
            {
                Log.Debug($"needsToReorderWholeCategory true for this cat ({group.First().Vk.AlbumTitle})");

                List<product> productsToUpdate = orderedGroup
                    .Where(lp => lp.PrestaProduct != null)
                    .OrderBy(lp => lp.PrestaProduct.position_in_category)
                    .Select(lp => lp.PrestaProduct)
                    .ToList();

                // Batch to avoid server timeout.
                foreach (product[] batch in productsToUpdate.Chunk(25))
                {
                    _webApi.UpdateProducts(batch.ToList());
                }

                Log.Debug($"Reordered {productsToUpdate.Count} products. Hope for the best!");
            }
            */

            Log.Information(
                $"DoPush(). \r\nTotal: about {parsedData.Items.Length} \r\nPreCacheDiscarded: {_allProductsDataConductor.PreCacheDiscardedItemsCount} \r\nEligible for update: {productN} \r\nUpdated: {totalUpdated} \r\nErroneous: {errorsCount}"
            );

            _statisticsService.SignalPrestaVk2PrestaProductPushHappened(_configuration.StatSourceName);

            if (errorsCount != 0)
            {
                // Do not re-trigger this method as this will happen some time from now anyway.
                // And that action could cause delayed but an infinite loop.
                Log.Warning("There were exceptions during updating Presta products.");
            }
        }

        public Task PushFurther()
        {
            ExternalSyncData prestaSyncData = this.GetExternalSyncData(_applicationLifetime.ApplicationStopping);

            return _threadedTasks.RunShort(() => {
                    EventHandler<DidPushEventArgs> ev = this.DidPush;

                    if (ev != null)
                        ev(this, new DidPushEventArgs(prestaSyncData));
                }
            );
        }

        private ExternalSyncData GetExternalSyncData(CancellationToken ct)
        {
            ExternalSyncData res = _allProductsDataConductor.GetProductsForProm_OnCallerThread(ct);

            res.LangAndTemplateConductor = _langAndTemplateConductor;
            res.CategoriesConductor = _categoriesConductor;

            return res;
        }
        
        private void PostponedProductUpdateServiceOnOnFire(PostponedProductUpdateService.PersistentTaskData obj)
        {
            List<product> products;
            
            if (obj.Art == null)
            {
                products = _webApi.GetProducts(new Dictionary<string, string>(), null, null);
                
                Log.Warning("Reset cache for all");
            } else
            {
                products = _webApi.GetByOneKeyFilterBatched(_webApi.GetProducts,
                    "reference",
                    [obj.Art],
                    "id_ASC",
                    stopIfNothingMore: false,
                    batchSize: 150,
                    cancellationToken: CancellationToken.None
                );
                
                Log.Warning("Reset cache for " + obj.Art);
            }

            foreach (product product in products)
            {
                _webApi.MyApiProductsSourcedFromVk_SetProductUpdatedPlusMostRecentProductVer(product.id.Val(), "(refresh)");
            }
        }

        public void CleanupCreatedOrphanedBlankProducts()
        {
            List<product> products = _webApi.GetByOneKeyFilterBatched(
                _webApi.GetProducts,
                "id",
                Enumerable.Range(0, int.MaxValue).Select(i => Convert.ToString(i)),
                "id_ASC",
                stopIfNothingMore: true,
                batchSize: 150,
                cancellationToken: _applicationLifetime.ApplicationStopping,
                750
            );

            product[] productsToDelete = products.Where(p => _langAndTemplateConductor.IsBlankProductName(p.name)).ToArray();

            foreach (product product in productsToDelete)
                _webApi.DeleteProduct(product);
        }
    }
}