using Bukimedia.PrestaSharp.Entities;

using irisdropwebservice.Libs.PrestaSharp;
using irisdropwebservice.Services.PrestaSync.ServiceConductors;
using irisdropwebservice.Services.PrestaSync.SiteFillConductors;
using irisdropwebservice.Services.VkScrap;
using System.Text.Json.Serialization;

namespace irisdropwebservice.Services.PrestaSync.Ins
{
    public readonly record struct PrestaLanguage(short Id, string TwoLetterIsoCode)
    {
        public string Locale => throw new NotImplementedException();
    }

    public readonly record struct LangIdAndMailTemplate(short LangId, string MailTemplateName);

    public readonly record struct OrderStateChangeLangConfiguration(short LangId, string Name, string MailTemplateName);

    public record OrderStateChangeConfiguration(short OrderStateId, bool SendMail, OrderStateChangeLangConfiguration[] PerLanguage);

    public enum LinkedProductDesiredAction
    {
        None = 0,
        NewFromVk,
        UpdateIt_IfOutdated,
        InactivateItBecauseVkIsNull,
        InactivateItBecauseVkHasParsingError,
        DoNotCreateNewFromVkBecauseVkHasParsingError
    }

    public class LinkedProduct
    {
        public product PrestaProduct { get; set; }
        public SourcedFromVkProductCacheInfo PrestaCache { get; set; }
        public VkScrapItem Vk { get; set; }

        public long? IdSourcedFromPresta => PrestaProduct?.id ?? PrestaCache?.ProductId;
        
        public LinkedProductDesiredAction DesiredAction { get; set; }

        public long? RelevantProductVersion { get; set; }
    }

    public record AttributeOption(long AttributeId, string Name);

    public class ProductOption
    {
        public long Id { get; set; }
        public long ProductId { get; set; }
        public List<AttributeOption> AttributeOptions { get; set; }
        public int Quantity { get; set; }
        public int Price { get; set; }
        public bool IsDefault { get; set; }
    }

    public class ExternalSyncProduct
    {
        public ProductInfo PrestaProduct { get; set; }
        public ProductOption[] ProductOptions { get; set; }
    }

    public class ExternalSyncData
    {
        public IReadOnlyList<ExternalSyncProduct> AllPrestaProducts { get; init; }
        public ProductAttributesConductorService ProductAttributesConductor { get; init; }
        public CategoriesConductorService CategoriesConductor { get; set; }
        public LangAndTemplateConductorService LangAndTemplateConductor { get; set; }
    }

    [Serializable]
    public class PrestaExteriorCategoriesInfo
    {
        [JsonPropertyName("supercategories")]
        public List<PrestaExteriorSuperCategory> SuperCategories { get; set; }

        [JsonPropertyName("categories")]
        public List<PrestaExteriorCategory> Categories { get; set; }
        
        [JsonPropertyName("platforms_specifications")]
        public PrestaPlatformsSpecifications PlatformsSpecifications { get; set; }
    }

    [Serializable]
    public class PrestaPlatformsSpecifications
    {
        [JsonPropertyName("platforms")]
        public PrestaFeatureSpecificationsPlatform[] Platforms { get; set; }
    }
    
    [Serializable]
    public class PrestaFeatureSpecificationsPlatform
    {
        [JsonPropertyName("name")]
        public string PlatformName { get; set; }
        
        [JsonPropertyName("feature_prefix")]
        public string FeaturePrefix { get; set; }
        
        [JsonPropertyName("features")]
        public List<PrestaFeatureSpecificationsPlatformFeature> Features { get; set; }
    }

    [Serializable]
    public class PrestaFeatureSpecificationsPlatformFeature
    {
        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("ignore")]
        public bool Ignore { get; set;} = false;

        [JsonPropertyName("specify_category")]
        public bool SpecifyCategory { get; set; } = false;
        
        [JsonPropertyName("add_empty_value")]
        public bool AddEmptyValue { get; set;} = false;
    }

    [Serializable]
    public class PrestaExteriorBaseCategory
    {
        [JsonPropertyName("auto_set")]
        public List<CategoryFeatureValue> AutoSet { get; set; } = new();

        [JsonPropertyName("mandatory_features")]
        public List<string> MandatoryFeatures { get; set; }

        [JsonPropertyName("not_mandatory_features")]
        public List<string> NotMandatoryFeatures { get; set; }
    }

    [Serializable]
    public class PrestaExteriorSuperCategory : PrestaExteriorBaseCategory
    {
        [JsonPropertyName("kasta_affiliation_id")]
        public long KastaAffiliationId { get; set; }

        [JsonPropertyName("name_ukr")]
        public string NameUkr { get; set; }
    }

    [Serializable]
    public class PrestaExteriorCategory : PrestaExteriorBaseCategory
    {
        [JsonPropertyName("kasta_affiliation_and_kind_id")]
        public string KastaAffiliationAndKindId { get; set; }

        [JsonPropertyName("name_plural_ukr")]
        public string NamePluralUkr { get; set; }
        
        [JsonPropertyName("epicentr_category_name")]
        public string EpicentrCategoryName { get; set; }
        
        [JsonPropertyName("epicentr_category_name_singular")]
        public string EpicentrCategoryNameSingular { get; set; }
        
        [JsonPropertyName("prom_category_id")]
        public int? PromCategoryId { get; set; }
        
        [JsonPropertyName("prom_group_id")]
        public long? PromGroupId { get; set; }
        
        [JsonPropertyName("epicentr_category_code")]
        public int EpicentrCategoryCode { get; set; }

        public long AffiliationId => long.Parse(KastaAffiliationAndKindId.Split('_')[0]);
        public long KindId => long.Parse(KastaAffiliationAndKindId.Split('_')[1]);
    }

    [Serializable]
    public class CategoryFeatureValue
    {
        [JsonPropertyName("feature")]
        public string Feature { get; set; }

        [JsonPropertyName("value")]
        public string Value { get; set; }

        [JsonPropertyName("override")]
        public bool Override { get; set; } = true;
        
        [JsonPropertyName("infer_from")]
        public string InferFrom { get; set; }
        
        [JsonPropertyName("inference_table")]
        public Dictionary<string, string> InferenceTable { get; set; }
    }
}