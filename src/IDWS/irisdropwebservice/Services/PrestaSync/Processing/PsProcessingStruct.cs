using Invictus.Nomenklatura.Misc;

using irisdropwebservice.AppConfig.ClassConfigurations;

using irisdropwebservice.Libs.PrestaSharp;
using irisdropwebservice.Services.Chats.Ins;
using irisdropwebservice.Services.PersistentTasks;
using irisdropwebservice.Services.PrestaSync.ServiceConductors;


namespace irisdropwebservice.Services.PrestaSync.Processing
{
    public abstract class PsSpecificOrderStatusChangeBase
    {
        public short[] NewOrderStatuses { get; }

        protected PsSpecificOrderStatusChangeBase(short[] newOrderStatuses)
        {
            NewOrderStatuses = newOrderStatuses;
        }
    }

    public class PsSpecificOrderStatusChangeView : PsSpecificOrderStatusChangeBase
    {
        protected PsSpecificOrderStatusChangeView(short[] newOrderStatuses)
            : base(newOrderStatuses)
        {
        }

        public static PsSpecificOrderStatusChangeView CreateForCustomer(LangAndTemplateConductorService langAndTemplateConductorService, params string[] templateNames)
        {
            // TODO: merge these two methods into one
            short[] orderStatuses = templateNames
                .SelectMany(langAndTemplateConductorService.GetOrderStatesByTemplateName)
                .Distinct()
                .ToArray();

            if (orderStatuses.Length == 0)
                throw new Exception("Not order status found for given tempaltes");

            var view = new PsSpecificOrderStatusChangeView(orderStatuses);
            view.PrestaTemplateNames = orderStatuses.Select(langAndTemplateConductorService.GetTemplateNameForNewOrderState).Distinct().ToArray();

            if (view.PrestaTemplateNames.Length != templateNames.Length)
                throw new Exception("Something is wrong.");

            return view;
        }

        public string[] PrestaTemplateNames { get; protected set; }

        public string RazorViewPath { get; set; }
        public Func<OrdersHistoryEntry, OrderDetails, object> CreateModel { get; set; }
    }

    public class PsSpecificOrderStatusChangeViewStaff : PsSpecificOrderStatusChangeView
    {
        private PsSpecificOrderStatusChangeViewStaff(short[] newOrderStatuses)
            : base(newOrderStatuses)
        {
        }

        public static PsSpecificOrderStatusChangeViewStaff CreateForStaff(LangAndTemplateConductorService langAndTemplateConductorService, params string[] templateNames)
        {
            short[] orderStatuses = templateNames
                .SelectMany(langAndTemplateConductorService.GetOrderStatesByTemplateName)
                .Distinct()
                .ToArray();

            if (orderStatuses.Length == 0)
                throw new Exception("Not order status found for given tempaltes");

            var view = new PsSpecificOrderStatusChangeViewStaff(orderStatuses);
            view.PrestaTemplateNames = orderStatuses.Select(langAndTemplateConductorService.GetTemplateNameForNewOrderState).Distinct().ToArray();

            if (view.PrestaTemplateNames.Length != templateNames.Length)
                throw new Exception("Something is wrong.");

            return view;
        }

        public TelegramChatConfiguration ChatId { get; set; }
        public OrderMessageInfoType OrderMessageInfoType { get; set; }
    }

    public class PsSpecificStatusChangeAction : PsSpecificOrderStatusChangeBase
    {
        public PsSpecificStatusChangeAction(params short[] newOrderStatuses)
            : base(newOrderStatuses)
        {
        }

        public Action<OrdersHistoryEntry, OrderDetails> Process { get; set; }
    }

    public class ChangePackedOrdersStatusesToSentPersistentTaskData : IGenericPersistentTaskData
    {
        public string TelegramChatDefinitionName { get; set; }
        public long[] TelegramChatMessageIds { get; set; }

        public int GetStableHashCode()
        {
            return IntStringUtil.GetStableHashCode(TelegramChatMessageIds.Select(v => v.ToString()).Concat(new[] { TelegramChatDefinitionName }).Aggregate("", (a, b) => a + b));
        }
    }

    // TODO: make common
    public class ReduceAvaliConfirmedPersistentTaskData : IGenericPersistentTaskData
    {
        public long SourceMessageDbId { get; set; }
        public int ReplyMessageId { get; set; }

        public int GetStableHashCode()
        {
            return HashCode.Combine(SourceMessageDbId, ReplyMessageId);
        }
    }

    public class PsReservationConfirmationPersistentTaskData : IGenericPersistentTaskData
    {
        public long SourceDbMessageId { get; set; }
        public int ReplyMessageId { get; set; }
        public string ReplyMessageText { get; set; }

        public int GetStableHashCode()
        {
            return HashCode.Combine(SourceDbMessageId, ReplyMessageId, ReplyMessageText);
        }
    }
}
