using System.Globalization;
using System.Text;
using System.Threading.Tasks;

using Bukimedia.PrestaSharp.Entities;

using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.AppConfig;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.AppConfig;

using irisdropwebservice.Libs.PrestaSharp;
using Invictus.Nomenklatura.Misc;

using irisdropwebservice.Legacy;
using irisdropwebservice.Services.Chats;
using irisdropwebservice.Services.Chats.Ins;
using irisdropwebservice.Services.PersistentTasks;
using irisdropwebservice.Services.PrestaSync.Ins;
using irisdropwebservice.Services.PrestaSync.Remote;
using irisdropwebservice.Services.PrestaSync.ServiceConductors;
using irisdropwebservice.Services.VkScrap;
using irisdropwebservice.Views.PrestaShopForChat;
using irisdropwebservice.Views.PrestaShopForChat.Staff.Reservations;

using Telegram.Bot.Types;

namespace irisdropwebservice.Services.PrestaSync.Processing
{
    public class PsReservationsProcessing : ITelegramChannelListener //, IPersistentTaskHandler<PsReservationConfirmationPersistentTaskData>
    {
        private const string RESERVATION_CHAT_APPROVAL_PERSISTENT_TASK_NAME = "ReservedProductsChatApproval";

        private readonly ILogger _logger = InvLog.Logger<PsReservationsProcessing>();

        private readonly ITelegramSendWorker _telegramSendWorker;
        private readonly IPrestaSiteWebApiCallsWorkerNonUser _webApi;
        private readonly RazorTemplateRender _razorViewRender;
        private readonly ProductAttributesConductorService _productAttributesConductor;
        private readonly ITelegramDbHandler _telegramDbHandler;
        private readonly PersistentTasksService _persistentTasksService;
        private readonly ILazyResolve<IPrestaSyncWorkerCallbacks> _prestaSyncWorkerCallbacks;
        private readonly LangAndTemplateConductorService _langAndTemplateConductor;
        private readonly InvEnv _invEnv;

        public PsReservationsProcessing(
            ITelegramSendWorker telegramSendWorker,
            IPrestaSiteWebApiCallsWorkerNonUser webApi,
            RazorTemplateRender razorViewRender,
            ProductAttributesConductorService productAttributesConductor,
            ITelegramDbHandler telegramDbHandler,
            PersistentTasksService persistentTasksService,
            LeBackgroundTasks threadedTasks,
            ILazyResolve<IPrestaSyncWorkerCallbacks> prestaSyncWorkerCallbacks,
            TelegramChatBotPolling telegramChatBotPolling,
            LangAndTemplateConductorService langAndTemplateConductor,
            InvEnv invEnv
        )
        {
            _telegramSendWorker = telegramSendWorker;
            _webApi = webApi;
            _razorViewRender = razorViewRender;
            _productAttributesConductor = productAttributesConductor;
            _telegramDbHandler = telegramDbHandler;
            _persistentTasksService = persistentTasksService;
            _prestaSyncWorkerCallbacks = prestaSyncWorkerCallbacks;
            _langAndTemplateConductor = langAndTemplateConductor;
            _invEnv = invEnv;

            threadedTasks.AddPeriodicBackgroundTask(this,
                TimeSpan.FromHours(1),
                this.CheckUnansweredReservationRequests,
                nameof(PsReservationsProcessing) + "::" + nameof(this.CheckUnansweredReservationRequests)
            );

            // _persistentTasksService.RegisterHandler(RESERVATION_CHAT_APPROVAL_PERSISTENT_TASK_NAME, this);

            telegramChatBotPolling.AddChannelListener(this);
        }

        public bool ProcessReservations(ProcessingContext processingContext, List<ReservationHistoryEntry> reservations)
        {
            int processedCount = 0;
            
            IEnumerable<IGrouping<long, ReservationHistoryEntry>> reservationsByReservationId = reservations.GroupBy(r => r.ReservationId);

            foreach (IGrouping<long, ReservationHistoryEntry> reservationHistory in reservationsByReservationId)
            {
                ReservationHistoryEntry lastReservationHistoryEntry = reservationHistory.MaxBy(rhe => rhe.ReservationHistoryId);

                if (!lastReservationHistoryEntry.IsUnprocessed)
                    throw new Exception("Last reservation returned by api is processed, this should not happen.");

                ReservationHistoryEntry[] processedReservations = reservationHistory.Where(rhe => !rhe.IsUnprocessed).ToArray();

                ReservationHistoryEntry prevReservation = null;

                if (processedReservations.Length > 0)
                    prevReservation = processedReservations.MaxBy(rhe => rhe.ReservationHistoryId);

                // TODO: watch out for Quantity != ReservedQuantity
                if (lastReservationHistoryEntry.Quantity != lastReservationHistoryEntry.QuantityReserved
                    && lastReservationHistoryEntry.QuantityReserved != 0)
                {
                    _logger.Error($"ReservationHistoryEntry {lastReservationHistoryEntry.ReservationHistoryId}, reservationId={lastReservationHistoryEntry.ReservationId} Q != QR " +
                                  $"quantity={lastReservationHistoryEntry.Quantity} quantityReserved={lastReservationHistoryEntry.QuantityReserved}");
                }

                // TODO: watch out for updates that are not quantity
                if (prevReservation != null && prevReservation.QuantityReserved == lastReservationHistoryEntry.QuantityReserved)
                {
                    _logger.Error($"ReservationHistoryEntry {lastReservationHistoryEntry.ReservationHistoryId}, reservationId={lastReservationHistoryEntry.ReservationId} QR is the same " +
                                  $"quantity={lastReservationHistoryEntry.Quantity} quantityReserved={lastReservationHistoryEntry.QuantityReserved}");
                }

                bool handled = this.HandleReservation(processingContext, lastReservationHistoryEntry, prevReservation);

                if (handled)
                    processedCount++;
            }

            if (processedCount != 0)
            {
                _webApi.Reservations_SetLastProcessedHistoryEntry(reservations.Max(rhe => rhe.ReservationHistoryId));

                _logger.Information($"Processed {reservations.Count} reservation history entries total, real ones: {processedCount}");

                return true;
            }
           
            return false;
        }

        private bool HandleReservation(ProcessingContext processingContext, ReservationHistoryEntry reservation, ReservationHistoryEntry prevReservation)
        {
            long productId = reservation.ProductId;
            bool isGuest = reservation.CustomerId == 0;

            string customerInfoForStaff;

            if (isGuest)
            {
                guest guest = processingContext.GetGuest(reservation.GuestId);

                customerInfoForStaff = StaffFormatHelper.CustomerToString(guest);
            } else
            {
                CustomerExtended customer = processingContext.GetCustomer(reservation.CustomerId);
                CustomerInfo telegramUser = processingContext.GetCustomerInfo(customer.PhoneNumber);

                customerInfoForStaff = StaffFormatHelper.CustomerToString(customer, telegramUser?.TelegramUserHandleLink);
            }

            ICollection<HistoricalVkImgId> vkImgIdsOrdered = _webApi.MyApiProductsSourcedFromVk_GetHistoricalVkImgIds(new[] { productId }, "0")
                .OrderByDescending(hvid => hvid.Id)
                .ToArray();

            ProductInfo productInfo = _webApi
                .MyApiProducts_GetInfo(new long[] { productId }, _langAndTemplateConductor.UkrainianLanguage.Id)
                .Single();

            List<combination> productCombinations = _webApi.GetCombinations(
                PrestaSiteWebApiCallsExtensions.CreatePrestaFilterByOneField("id_product", new[] { productId.ToString() }),
                null,
                null
            );

            combination combination = productCombinations.Single(cmb => cmb.id == reservation.ProductAttributeId);

            long productOptionValueId = combination.associations.product_option_values.Single().id;

            string productSizeDescription = _productAttributesConductor.GetProductSizeDescriptionByProductOptionId(productOptionValueId);
            string productOptionValueName = _productAttributesConductor.GetProductOptionValueById(productOptionValueId).Name;

            string prestaProductLink = _webApi.MyApiProducts_GetProductAndCategoryLink(productId, -1, "").ProductUrl;

            string[] vkUrls = vkImgIdsOrdered
                .Where(hvid => hvid.ProductId == productId)
                .Select(hvid => hvid.VkFullPhotoId)
                .Select(imgId => imgId == "(deleted)" ? null : VkNamesAndUrls.GetVkComGroupPhotoUri(imgId))
                .ToArray();

            // Set m.UnreservedIsInCart
            Dictionary<string, string> cartsFilter = isGuest
                ? PrestaSiteWebApiCallsExtensions.CreatePrestaFilterByOneField("id_guest",    new[] { reservation.GuestId.ToString() })
                : PrestaSiteWebApiCallsExtensions.CreatePrestaFilterByOneField("id_customer", new[] { reservation.CustomerId.ToString() });

            List<cart> carts = _webApi.GetCarts(cartsFilter, null, null);

            bool movedToCart = false;

            if (carts.Count > 0)
            {
                cart lastCart = carts
                    .OrderByDescending(c => DateTime.ParseExact(c.date_upd, PrestaSharpConstants.PRODUCT_DATE_TIMES_FORMAT, CultureInfo.InvariantCulture))
                    .First();

                foreach (cart_row cartRow in lastCart.associations.cart_rows)
                {
                    if (cartRow.quantity > 0 && cartRow.id_product == productId && cartRow.id_product_attribute == combination.id)
                    {
                        movedToCart = true;
                        break;
                    }
                }
            }

            // Done
            var model = new NewOrChangedReservationModel()
            {
                CustomerInfoString = customerInfoForStaff,
                IsTestSite = _invEnv.EnvEnum != InvEnvEnum.ProdServ,
                ReservationDateUser = reservation.DateAdd.ToStringUADateAndTimeWithoutYear(),
                ReservationDateExpiryUser = reservation.DateExp.ToStringUADateAndTimeWithoutYear(),
                ProductReference = productInfo.Reference,
                ProductSizeDescription = productSizeDescription,
                ProductChosenSizeName = productOptionValueName,
                NewReservedQuantity = reservation.QuantityReserved,
                PrevReservedQuantity = prevReservation?.QuantityReserved,
                PrestaProductLink = prestaProductLink,
                VkUrls = vkUrls,
                UnreservedIsInCart = movedToCart
            };

            string renderResult = _razorViewRender.Render("PrestaShopForChat/Staff/Reservations/NewOrChangedReservation", model, new StringBuilder());

            Message[] messages = _telegramSendWorker.Do(w => w.PostToTelegramFromStaffAccount(
                renderResult,
                CCAppConfig.TelegramIrisDrop.ReservationsChat,
                new PostToTelegramOptions() { DisableWebPagePreview = true, Silent = false }
            ));

            if (messages.Length > 1)
                throw new NotImplementedException("messages.Length > 1");

            Message message = messages.Single();

            string messageData = "Handled_" + reservation.ReservationHistoryId + "_" + reservation.ReservationId;

            _telegramDbHandler.LinkMessageToData(message, messageData, OrderMessageInfoType.Reservation_StaffUnconfirmedReservation);

            return true;
        }

        void ITelegramChannelListener.IncomingPost(Update update)
        {
            if (!update.IsFromChatWithThisNameOrTheSameChatWithDifferentName(CCAppConfig.TelegramIrisDrop.ReservationsChat))
                return;

            Message channelPostOrMessage = update.ChannelPost ?? update.Message;

            if (channelPostOrMessage.ReplyToMessage != null)
            {
                // this.HandleReservationApprovalOrDisapproval_AddPersistentTask(channelPostOrMessage.ReplyToMessage.MessageId, channelPostOrMessage.MessageId, channelPostOrMessage.Text);

                return;
            }
        }

        /*
        private void HandleReservationApprovalOrDisapproval_AddPersistentTask(int sourceMessageId, int replyMessageId, string replyMessageText)
        {
            TelegramChatMessage message =
                _telegramDbHandler.GetDbMessageByChatIdAndMessageId(
                    CCAppConfig.TelegramIrisDrop.ReservationsChat,
                    sourceMessageId
                );

            // ITelegramDialogueHandler should not execute for too long, so we make a persistent task.
            var persistentTaskData = new PsReservationConfirmationPersistentTaskData()
            {
                SourceDbMessageId = message.Id,
                ReplyMessageId = replyMessageId,
                ReplyMessageText = replyMessageText
            };

            _logger.Debug("Will process stock change appr.");

            _persistentTasksService.QueueTask(RESERVATION_CHAT_APPROVAL_PERSISTENT_TASK_NAME, persistentTaskData);
        }

        Task IPersistentTaskHandler<PsReservationConfirmationPersistentTaskData>.BeginExecuteTask(PsReservationConfirmationPersistentTaskData taskData)
        {
            return this.RunOnWorkerThread(() => this.HandleReservationApprovalOrDisapproval_HandlePersistentTask(taskData));
        }

        public void HandleReservationApprovalOrDisapproval_HandlePersistentTask(PsReservationConfirmationPersistentTaskData taskData)
        {
            TelegramChatMessageAndLinkedInfo sourceMessageInfo = _telegramDbHandler.GetMessagesAndAssociatedDataByDbIds(new[] { taskData.SourceDbMessageId }).SingleOrDefault();

            if (sourceMessageInfo == null || sourceMessageInfo.ChatLinkedInfo == null)
                return;

            if (sourceMessageInfo.ChatLinkedInfo.InfoType != OrderMessageInfoType.Reservation_StaffUnconfirmedReservation)
                return;

            string[] messageDataSpl = sourceMessageInfo.ChatLinkedInfo.Data.Split('_');

            (string isHandled, long reservationHistoryId, long reservationId) = (messageDataSpl[0], long.Parse(messageDataSpl[1]), long.Parse(messageDataSpl[2]));

            if (isHandled == "Handled")
            {
                _telegramSendWorker.Do(w => w.PostToTelegramFromStaffAccount("Ви вже давали відповідь. Ви змінили думку? ",
                    CCAppConfig.TelegramIrisDrop.ReduceInStockChat,
                    new PostToTelegramOptions() { DisableWebPagePreview = true, Silent = true }
                ));

                return;
            }

            bool isDisapproval = taskData.ReplyMessageText == "-";
            bool isApproval = taskData.ReplyMessageText == "+";

            if (!isDisapproval)
            {
                if (!isApproval)
                {
                    _telegramSendWorker.Do(w => w.PostToTelegramFromStaffAccount("Невірна команда.",
                        CCAppConfig.TelegramIrisDrop.ReduceInStockChat,
                        new PostToTelegramOptions() { DisableWebPagePreview = true, Silent = false }
                    ));
                }
                else
                {
                    _telegramSendWorker.Do(w => w.PostToTelegramFromStaffAccount("Оброблено.",
                        CCAppConfig.TelegramIrisDrop.ReduceInStockChat,
                        new PostToTelegramOptions() { DisableWebPagePreview = true, Silent = true }
                    ));

                    sourceMessageInfo.ChatLinkedInfo.Data = "Handled_" + reservationHistoryId + "_" + reservationId;

                    _telegramDbHandler.UpdateLinkedData(sourceMessageInfo.ChatLinkedInfo);
                }

                return;
            }

            ReservationApprovalResult approvalResult = _webApi.Reservations_SetApproval(reservationHistoryId, reservationId, false);

            string responseMessage;
            bool markAsHandled;

            switch (approvalResult)
            {
                case ReservationApprovalResult.Success:
                    responseMessage = "Оброблено. Бронь на сайті обнулена.";
                    markAsHandled = true;
                    break;
                case ReservationApprovalResult.ReservationHistoryItemIsOutdated:
                    responseMessage = "Клієнт змінив кількість, будь ласта дайте відповідь на більш нове повідомлення щодо цього товару з цим розміром.";
                    markAsHandled = false;
                    break;
                case ReservationApprovalResult.ReservationNotFound:
                    responseMessage = "Бронь не знайдена. Мабудь вона вже застара. Перевірте список броней клієнта на сайті.";
                    markAsHandled = true;
                    break;
                case ReservationApprovalResult.ReservationReservedQuantityAlreadyZero:
                    responseMessage = "К-ть забронованих для цоього товару+розміру+клієнта вже була нульовою.";
                    markAsHandled = true;
                    break;
                case ReservationApprovalResult.None:
                default:
                    throw new EnumAbominationException(typeof(ReservationApprovalResult));
            }

            _telegramSendWorker.Do(w => w.PostToTelegramFromStaffAccount(responseMessage,
                CCAppConfig.TelegramIrisDrop.ReduceInStockChat,
                new PostToTelegramOptions() { ReplyToMessageId = taskData.ReplyMessageId, Silent = false }
            ));

            if (markAsHandled)
            {
                sourceMessageInfo.ChatLinkedInfo.Data = "Handled_" + reservationHistoryId + "_" + reservationId;

                _telegramDbHandler.UpdateLinkedData(sourceMessageInfo.ChatLinkedInfo);
            }
        }
        */
        
        private void CheckUnansweredReservationRequests(object obj)
        {
            /*TelegramChatMessageAndLinkedInfo[] linedInfos = _telegramDbHandler.GetMessagesAndAssociatedDataByInfoType(OrderMessageInfoType.Reservation_StaffUnconfirmedReservation);

            foreach (TelegramChatMessageAndLinkedInfo linkedInfo in linedInfos)
            {
                string[] messageDataSpl = linkedInfo.ChatLinkedInfo.Data.Split('_');

                (string isHandled, _, _) = (messageDataSpl[0], long.Parse(messageDataSpl[1]), long.Parse(messageDataSpl[2]));

                if (isHandled == "Handled")
                    continue;

                if (linkedInfo.Message.DateTimeUtc > ServerClock.GetCurrentUtcTime() - TimeSpan.FromHours(10))
                    continue;

                string text = "⚠️⚠️⚠️ Тут є запроси на бронювання без відповідей вже більш як 10 годин. Якщо таких немає - зверніться до адміністратора.";

                _telegramSendWorker.Do(w => w.PostToTelegramFromStaffAccount(text,
                    CCAppConfig.TelegramIrisDrop.ReduceInStockChat,
                    new PostToTelegramOptions() { Silent = false }
                ));

                break;
            }*/
        }
        

        private Task RunOnWorkerThread(Action action)
        {
            return _prestaSyncWorkerCallbacks.Resolve().Run(w => w.ExecuteCallback(action));
        }
    }
}
