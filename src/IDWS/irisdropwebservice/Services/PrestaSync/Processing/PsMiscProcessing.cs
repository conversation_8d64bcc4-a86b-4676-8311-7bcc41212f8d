using System.Text;

using irisdropwebservice.AppConfig;
using irisdropwebservice.AppConfig.ClassConfigurations;
using irisdropwebservice.Libs.PrestaSharp;
using Invictus.Nomenklatura.Misc;
using irisdropwebservice.Services.Chats;
using irisdropwebservice.Views.PrestaShopForChat.Staff;

namespace irisdropwebservice.Services.PrestaSync.Processing
{
    public class PsMiscProcessing
    {
        private readonly ITelegramSendWorker _telegramSendWorker;
        private readonly RazorTemplateRender _razorViewRender;

        public PsMiscProcessing(ITelegramSendWorker telegramSendWorker, RazorTemplateRender razorViewRender)
        {
            _telegramSendWorker = telegramSendWorker;
            _razorViewRender = razorViewRender;
        }

        public void ProcessCustomerMessage(ProcessingContext processingContext, CustomerMessage message)
        {
            TelegramChatConfiguration chatId = CCAppConfig.TelegramIrisDrop.NewOrdersChat;

            string razorViewPath = "PrestaShopForChat/Staff/NewCustomerMessage";
            var model = new NewCustomerMessageModel();

            string renderResult = _razorViewRender.Render(razorViewPath, model, new StringBuilder());

            _telegramSendWorker.Do(w => w.PostToTelegramFromStaffAccount(renderResult, chatId, new PostToTelegramOptions() { DisableWebPagePreview = true, Silent = false }));
        }

        public void ProcessOrderFile(ProcessingContext processingContext, OrderFile orderFile)
        {
            TelegramChatConfiguration chatId = CCAppConfig.TelegramIrisDrop.NewOrdersChat;

            string razorViewPath = "PrestaShopForChat/Staff/NewOrderAttachedFile";
            var model = new NewOrderAttachedFileModel();

            string renderResult = _razorViewRender.Render(razorViewPath, model, new StringBuilder());

            _telegramSendWorker.Do(w => w.PostToTelegramFromStaffAccount(renderResult, chatId, new PostToTelegramOptions() { DisableWebPagePreview = true, Silent = false }));
        }
    }
}
