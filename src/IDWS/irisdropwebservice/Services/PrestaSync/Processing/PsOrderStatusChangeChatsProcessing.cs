using System.Threading.Tasks;

using Bukimedia.PrestaSharp.Entities;

using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.AppConfig;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.AppConfig;
using irisdropwebservice.AppConfig.ClassConfigurations;

using irisdropwebservice.Libs.PrestaSharp;
using Invictus.Nomenklatura.Misc;
using irisdropwebservice.Services.Chats;
using irisdropwebservice.Services.Chats.Ins;
using irisdropwebservice.Services.PersistentTasks;
using irisdropwebservice.Services.PrestaSync.Ins;
using irisdropwebservice.Services.PrestaSync.Remote;
using irisdropwebservice.Services.PrestaSync.ServiceConductors;
using irisdropwebservice.Services.VkScrap;
using irisdropwebservice.Views.PrestaShopForChat;
using irisdropwebservice.Views.PrestaShopForChat.Customers;
using irisdropwebservice.Views.PrestaShopForChat.Partials;
using irisdropwebservice.Views.PrestaShopForChat.Staff;

using Telegram.Bot.Types;

namespace irisdropwebservice.Services.PrestaSync.Processing
{
    public class PsOrderStatusChangeChatsProcessing : ITelegramChannelListener, 
        IPersistentTaskHandler<ChangePackedOrdersStatusesToSentPersistentTaskData>, 
        IPersistentTaskHandler<ReduceAvaliConfirmedPersistentTaskData>
    {
        private const string PAID_ORDER_CHAT_SET_STATUS_TO_SENT_PERSISTENT_TASK_NAME = "PaidOrdersChatSetStatusToSent";
        private const string REDUCE_AVALI_CHAT_CONFIRMED_AVALI_REDUCED_TASK_NAME = "ReduceAvaliChatConfirmedAvaliReduced";

        private static readonly PrestaShopCCInfo _Configuration = CCAppConfig.PrestaIrisDrop;

        private readonly ILogger _logger = InvLog.Logger<PsOrderStatusChangeChatsProcessing>();

        private readonly IPrestaSiteWebApiCallsWorkerNonUser _nonUserWebApi;
        private readonly CarriersConductorService _carriersConductor;
        private readonly CountryAndStateConductorService _countryAndStateConductor;
        private readonly ITelegramDbContacts _telegramDbContacts;
        private readonly ITelegramDbHandler _telegramDbHandler;
        private readonly PersistentTasksService _persistentTasksService;
        private readonly ILazyResolve<IPrestaSyncWorkerCallbacks> _prestaSyncWorkerCallbacks;
        private readonly TelegramChatBotPolling _telegramChatBotPolling;
        private readonly LangAndTemplateConductorService _langAndTemplateConductor;
        private readonly ITelegramSendWorker _telegramSendWorker;
        private readonly TriggerPrestaPushFlowEvent _triggerPrestaPushFlowEvent;
        private readonly InvEnv _invEnv;

        private PsSpecificOrderStatusChangeViewStaff[] _orderChangeActionsStaff;
        private PsSpecificOrderStatusChangeView[] _orderChangeActionsCustomers;

        public PsOrderStatusChangeChatsProcessing(
            IPrestaSiteWebApiCallsWorkerNonUser nonUserWebApi,
            CarriersConductorService carriersConductor,
            CountryAndStateConductorService countryAndStateConductor,
            ITelegramDbContacts telegramDbContacts,
            ITelegramDbHandler telegramDbHandler,
            PersistentTasksService persistentTasksService,
            ILazyResolve<IPrestaSyncWorkerCallbacks> prestaSyncWorkerCallbacks,
            TelegramChatBotPolling telegramChatBotPolling,
            LangAndTemplateConductorService langAndTemplateConductor,
            ITelegramSendWorker telegramSendWorker,
            TriggerPrestaPushFlowEvent triggerPrestaPushFlowEvent,
            InvEnv invEnv
        )
        {
            _nonUserWebApi = nonUserWebApi;
            _carriersConductor = carriersConductor;
            _countryAndStateConductor = countryAndStateConductor;
            _telegramDbContacts = telegramDbContacts;
            _telegramDbHandler = telegramDbHandler;
            _persistentTasksService = persistentTasksService;
            _prestaSyncWorkerCallbacks = prestaSyncWorkerCallbacks;
            _telegramChatBotPolling = telegramChatBotPolling;
            _langAndTemplateConductor = langAndTemplateConductor;
            _telegramSendWorker = telegramSendWorker;
            _triggerPrestaPushFlowEvent = triggerPrestaPushFlowEvent;
            _invEnv = invEnv;
        }

        public void Initialize()
        {
            _orderChangeActionsStaff = this.CreateOrderChangeActionsStaff();
            _orderChangeActionsCustomers = this.CreateOrderChangeActionsCustomers();

            _persistentTasksService.RegisterHandler<ChangePackedOrdersStatusesToSentPersistentTaskData>(PAID_ORDER_CHAT_SET_STATUS_TO_SENT_PERSISTENT_TASK_NAME, this);
            _persistentTasksService.RegisterHandler<ReduceAvaliConfirmedPersistentTaskData>(REDUCE_AVALI_CHAT_CONFIRMED_AVALI_REDUCED_TASK_NAME, this);

            _telegramChatBotPolling.AddChannelListener(this);
        }

        public PsSpecificOrderStatusChangeViewStaff[] GetOrderChangeStaffActions(short newStatusId)
        {
            PsSpecificOrderStatusChangeViewStaff[] concreteStatusChanges = _orderChangeActionsStaff.Where(h => h.NewOrderStatuses.Contains(newStatusId)).ToArray();

            return concreteStatusChanges;
        }

        public PsSpecificOrderStatusChangeView[] GetOrderChangeCustomerActions(short newStatusId)
        {
            PsSpecificOrderStatusChangeView[] concreteStatusChanges = _orderChangeActionsCustomers.Where(h => h.NewOrderStatuses.Contains(newStatusId)).ToArray();

            return concreteStatusChanges;
        }

        private PsSpecificOrderStatusChangeViewStaff[] CreateOrderChangeActionsStaff()
        {
            var res = new List<PsSpecificOrderStatusChangeViewStaff>();

            var item = PsSpecificOrderStatusChangeViewStaff.CreateForStaff(_langAndTemplateConductor, "bankwire");
            item.RazorViewPath = "PrestaShopForChat/Staff/NewOrder";
            item.CreateModel = this.CreateNewOrderModel;
            item.ChatId = CCAppConfig.TelegramIrisDrop.NewOrdersChat;
            item.OrderMessageInfoType = OrderMessageInfoType.Order_StaffNewOrder;
            res.Add(item);

            item = PsSpecificOrderStatusChangeViewStaff.CreateForStaff(_langAndTemplateConductor, "payment", "payment_partial");
            item.RazorViewPath = "PrestaShopForChat/Staff/SendOrder";
            item.CreateModel = this.CreatePaidOrPaidPartially_OrderModel;
            item.ChatId = CCAppConfig.TelegramIrisDrop.ToPackAndSendOrdersChat;
            item.OrderMessageInfoType = OrderMessageInfoType.Order_StaffPackAndSendOrder;
            res.Add(item);

            item = PsSpecificOrderStatusChangeViewStaff.CreateForStaff(_langAndTemplateConductor, "payment", "payment_partial");
            item.RazorViewPath = "PrestaShopForChat/Staff/VkReduceInStock";
            item.CreateModel = this.CreateProcessOrderPaidOrPaidPartially_VkReduceInStockOrderModel;
            item.ChatId = CCAppConfig.TelegramIrisDrop.ReduceInStockChat;
            item.OrderMessageInfoType = OrderMessageInfoType.Order_StaffReduceVkAvaliPaidOrder;
            res.Add(item);

            return res.ToArray();
        }

        private PsSpecificOrderStatusChangeView[] CreateOrderChangeActionsCustomers()
        {
            var res = new List<PsSpecificOrderStatusChangeView>();

            var item = PsSpecificOrderStatusChangeView.CreateForCustomer(_langAndTemplateConductor, "bankwire");
            item.RazorViewPath = "PrestaShopForChat/Customers/NewOrder";
            item.CreateModel = this.CreateGenericModelForCustomer;
            res.Add(item);

            item = PsSpecificOrderStatusChangeView.CreateForCustomer(_langAndTemplateConductor, "payment_waiting_local");
            item.RazorViewPath = "PrestaShopForChat/Customers/NewOrderLocal";
            item.CreateModel = this.CreateGenericModelForCustomer;
            res.Add(item);

            item = PsSpecificOrderStatusChangeView.CreateForCustomer(_langAndTemplateConductor, "payment", "payment_partial");
            item.RazorViewPath = "PrestaShopForChat/Customers/PaidOrder";
            item.CreateModel = this.CreateGenericModelForCustomer;
            res.Add(item);

            item = PsSpecificOrderStatusChangeView.CreateForCustomer(_langAndTemplateConductor, "shipped");
            item.RazorViewPath = "PrestaShopForChat/Customers/ShippedOrder";
            item.CreateModel = this.CreateGenericModelForCustomer;
            res.Add(item);

            item = PsSpecificOrderStatusChangeView.CreateForCustomer(_langAndTemplateConductor, "order_canceled");
            item.RazorViewPath = "PrestaShopForChat/Customers/CancelledOrder";
            item.CreateModel = this.CreateGenericModelForCustomer;
            res.Add(item);

            item = PsSpecificOrderStatusChangeView.CreateForCustomer(_langAndTemplateConductor, "refund");
            item.RazorViewPath = "PrestaShopForChat/Customers/RefundOrder";
            item.CreateModel = this.CreateGenericModelForCustomer;
            res.Add(item);

            item = PsSpecificOrderStatusChangeView.CreateForCustomer(_langAndTemplateConductor, "payment_error");
            item.RazorViewPath = "PrestaShopForChat/Customers/PaymentErrorOrder";
            item.CreateModel = this.CreateGenericModelForCustomer;
            res.Add(item);

            item = PsSpecificOrderStatusChangeView.CreateForCustomer(_langAndTemplateConductor, "outofstock");
            item.RazorViewPath = "PrestaShopForChat/Customers/OutOfStockOrder";
            item.CreateModel = this.CreateGenericModelForCustomer;
            res.Add(item);

            return res.ToArray();
        }

        #region Create models for staff

        private object CreateNewOrderModel(OrdersHistoryEntry historyEntry, OrderDetails orderDetails)
        {
            List<OrderSameProductIdsDetailsModel> productsWithSameIdsModels = this.GetProductsWithSameIdsModels(orderDetails);

            var model = new NewOrderModel()
            {
                IsTestSite = _invEnv.EnvEnum != InvEnvEnum.ProdServ,
                OrderReference = orderDetails.OrderReference,
                CarrierName = _carriersConductor.GetCarrierNameById(orderDetails.CarrierId),
                OrderCreationDateTimeUser = orderDetails.OrderCreationDateTimeLocal.ToStringUADateAndTimeWithoutYear(),
                Messages = orderDetails.Messages,
                Products = productsWithSameIdsModels
            };

            return model;
        }

        private object CreatePaidOrPaidPartially_OrderModel(OrdersHistoryEntry historyEntry, OrderDetails orderDetails)
        {
            bool isPaidPartially = historyEntry.OrderStateId == _Configuration.PartiallyPaidOrderStatus;

            List<OrderSameProductIdsDetailsModel> productsWithSameIdsModels = this.GetProductsWithSameIdsModels(orderDetails);

            List<OrderDetailsAddressModel> modelAddresses = orderDetails.Addresses.Select(
                addr =>
                    new OrderDetailsAddressModel()
                    {
                        Address1 = addr.Address1,
                        Address2 = addr.Address2,
                        City = addr.City,
                        CountryName = _countryAndStateConductor.GetCountryNameById(addr.CountryId),
                        LastName = addr.LastName,
                        FirstName = addr.FirstName,
                        MiddleName = addr.MiddleName,
                        OblastName = _countryAndStateConductor.TryGetStateNameById(addr.StateId),
                        PhoneMobile = addr.PhoneMobile
                    }
            ).ToList();

            var model = new PaidOrderModel()
            {
                IsTestSite = _invEnv.EnvEnum != InvEnvEnum.ProdServ,
                IsPaidPartially = isPaidPartially,
                AdminOrderNote = orderDetails.AdminOrderNote,
                OrderReference = orderDetails.OrderReference,
                CarrierName = _carriersConductor.GetCarrierNameById(orderDetails.CarrierId),
                OrderCreationDateTimeUser = orderDetails.OrderCreationDateTimeLocal.ToStringUADateAndTimeWithoutYear(),
                Messages = orderDetails.Messages,
                Products = productsWithSameIdsModels,
                Addresses = modelAddresses
            };

            return model;
        }

        private object CreateProcessOrderPaidOrPaidPartially_VkReduceInStockOrderModel(OrdersHistoryEntry historyEntry, OrderDetails orderDetails)
        {
            var innerModel = (PaidOrderModel)this.CreatePaidOrPaidPartially_OrderModel(historyEntry, orderDetails);

            List<long> keepThisProductIds = _nonUserWebApi.MyApiProductsSourcedFromVk_AreTheseProductsSourcedFromVk(
                orderDetails.Products.Select(p => p.ProductId).Distinct().ToArray()
            );

            foreach (OrderSameProductIdsDetailsModel orderSameProductIdsDetailsModel in innerModel.Products.ToArray())
            {
                OrderDetailProduct orderDetailProduct = orderDetails.Products.First(p => p.Reference == orderSameProductIdsDetailsModel.ProductReference);

                if (!keepThisProductIds.Contains(orderDetailProduct.ProductId))
                {
                    innerModel.Products.Remove(orderSameProductIdsDetailsModel);

                    // We don't have these yet.
                    _logger.Error($"Product {orderDetailProduct.ProductId}, ref {orderDetailProduct.Reference} is reported to be not from vk.");
                }
            }

            CustomerExtended customer = _nonUserWebApi.Customers_GetCustomer(orderDetails.CustomerId);

            CustomerInfo customerInfo = _telegramDbContacts.GetCustomerInfoByPhone(customer.PhoneNumber);

            return new VkReduceInStockModel()
            {
                M = innerModel,
                CustomerInfoString = StaffFormatHelper.CustomerToString(customer, customerInfo?.TelegramUserHandleLink)
            };
        }

        private List<OrderSameProductIdsDetailsModel> GetProductsWithSameIdsModels(OrderDetails orderDetails)
        {
            var productsWithSameIdsModels = new List<OrderSameProductIdsDetailsModel>();

            long[] productIds = orderDetails.Products
                .Select(p => p.ProductId)
                .ToArray();

            ICollection<HistoricalVkImgId> vkImgIdsOrdered = _nonUserWebApi.MyApiProductsSourcedFromVk_GetHistoricalVkImgIds(productIds, "0")
                .OrderByDescending(hvid => hvid.Id)
                .ToArray();

            IEnumerable<IGrouping<long, OrderDetailProduct>> groupOrderProductsByProductId = orderDetails.Products.GroupBy(p => p.ProductId);

            int productSizeNumber = 1;

            foreach (IGrouping<long, OrderDetailProduct> orderProductsGroup in groupOrderProductsByProductId)
            {
                long productId = orderProductsGroup.Key;
                string productReference = orderProductsGroup.First().Reference;

                string prestaProductLink = _nonUserWebApi.MyApiProducts_GetProductAndCategoryLink(productId, -1, "").ProductUrl;

                string[] vkUrls = vkImgIdsOrdered
                    .Where(hvid => hvid.ProductId == productId)
                    .Select(hvid => hvid.VkFullPhotoId)
                    .Select(imgId => imgId == "(deleted)" ? null : VkNamesAndUrls.GetVkComGroupPhotoUri(imgId))
                    .ToArray();

                var productsWithSameIdsModel = new OrderSameProductIdsDetailsModel()
                {
                    ProductSizeNumberInOrder = productSizeNumber,
                    ProductReference = productReference,
                    OrderProductsDetails = orderProductsGroup.ToArray(),
                    PrestaProductLink = prestaProductLink,
                    VkUrls = vkUrls
                };

                productSizeNumber += productsWithSameIdsModel.OrderProductsDetails.Length;

                productsWithSameIdsModels.Add(productsWithSameIdsModel);
            }

            return productsWithSameIdsModels;
        }

        #endregion

        #region Create models for customers

        private object CreateGenericModelForCustomer(OrdersHistoryEntry historyEntry, OrderDetails orderDetails)
        {
            var res = new CustomersGenericOrderModel()
            {
                OrderReference = orderDetails.OrderReference,
                IsFullPayment = historyEntry.OrderStateId == _Configuration.PaidOrderStatus 
                    ? true 
                    : (historyEntry.OrderStateId == _Configuration.PartiallyPaidOrderStatus ? false : null)
            };

            return res;
        }

        #endregion

        void ITelegramChannelListener.IncomingPost(Update update)
        {
            if (!update.IsFromChatWithThisNameOrTheSameChatWithDifferentName(CCAppConfig.TelegramIrisDrop.ToPackAndSendOrdersChat))
                return;

            Message channelPostOrMessage = update.ChannelPost ?? update.Message;

            if (channelPostOrMessage.ReplyToMessage != null && channelPostOrMessage.Text == "+")
            {
                this.HandleStockChangeApprovalOrDisapproval_AddPersistentTask(channelPostOrMessage.ReplyToMessage.MessageId, channelPostOrMessage.MessageId);

                return;
            }

            if (update.ChannelPost?.Sticker == null)
                return;

            if (update.ChannelPost.Sticker.Emoji == "😞" && update.ChannelPost.Sticker.SetName == "zhdun_vk")
            {
                this.SetAllPaidOrdersChatOrderStatusesToShippedFromZhdunToZhdun_AddPersistentTask();
            }
        }

        #region Trigger from chat: vk stock change acknowledgement

        private void HandleStockChangeApprovalOrDisapproval_AddPersistentTask(int sourceMessageId, int replyMessageId)
        {
            TelegramChatMessage message = _telegramDbHandler.GetDbMessageByChatIdAndMessageId(CCAppConfig.TelegramIrisDrop.ReduceInStockChat, sourceMessageId);

            // ITelegramDialogueHandler should not execute for too long, so we make a persistent task.
            var persistentTaskData = new ReduceAvaliConfirmedPersistentTaskData()
            {
                SourceMessageDbId = message.Id,
                ReplyMessageId = replyMessageId,
            };

            _logger.Debug("Will process stock change appr.");

            _persistentTasksService.QueueTask(REDUCE_AVALI_CHAT_CONFIRMED_AVALI_REDUCED_TASK_NAME, persistentTaskData, null);
        }

        Task IPersistentTaskHandler<ReduceAvaliConfirmedPersistentTaskData>.BeginExecuteTask(ReduceAvaliConfirmedPersistentTaskData taskData)
        {
            return this.RunOnWorkerThread(() => this.HandleStockChangeApprovalOrDisapproval_HandlePersistentTask(taskData));
        }

        public void HandleStockChangeApprovalOrDisapproval_HandlePersistentTask(ReduceAvaliConfirmedPersistentTaskData taskData)
        {
            TelegramChatMessageAndLinkedInfo messageInfo = _telegramDbHandler.GetMessagesAndAssociatedDataByDbIds(new[] { taskData.SourceMessageDbId }).SingleOrDefault();

            if (messageInfo == null)
                return;

            if (messageInfo.ChatLinkedInfo.InfoType != OrderMessageInfoType.Order_StaffReduceVkAvaliPaidOrder)
                return;

            long orderId = long.Parse(messageInfo.ChatLinkedInfo.Data);

            OrderDetails orderDetails = _nonUserWebApi.MyApiOrders_GetOrderDetails(orderId, _langAndTemplateConductor.UkrainianLanguage.Id);

            if (orderDetails == null)
                return;
            if (orderDetails.CurrentOrderState != _Configuration.PaidOrderStatus && orderDetails.CurrentOrderState != _Configuration.PartiallyPaidOrderStatus)
                return;

            List<long> productIdsFromVk = 
                _nonUserWebApi.MyApiProductsSourcedFromVk_AreTheseProductsSourcedFromVk(orderDetails.Products.Select(p => p.ProductId).Distinct().ToArray());

            foreach (OrderDetailProduct orderDetailsProduct in orderDetails.Products)
            {
                if (!productIdsFromVk.Contains(orderDetailsProduct.ProductId))
                    continue;

                // Deletion of this is done in presta module
                _nonUserWebApi.MyApiProductsSourcedFromVk_SetTemporaryQuantityAdjustment(orderId,
                    orderDetailsProduct.ProductId,
                    orderDetailsProduct.ProductAttributeId,
                    orderDetailsProduct.Quantity
                );
            }

            _triggerPrestaPushFlowEvent.QueueTryTrigger();

            _telegramSendWorker.Do(w => w.PostToTelegramFromStaffAccount("Успішно оброблено",
                CCAppConfig.TelegramIrisDrop.ReduceInStockChat,
                new PostToTelegramOptions() { Silent = true, ReplyToMessageId = taskData.ReplyMessageId }
            ));
        }

        #endregion

        #region Trigger from chat: order autoset to 'sent'

        private void SetAllPaidOrdersChatOrderStatusesToShippedFromZhdunToZhdun_AddPersistentTask()
        {
            TelegramChatId chatId = CCAppConfig.TelegramIrisDrop.ToPackAndSendOrdersChat;

            TelegramChatMessage[] messages = _telegramDbHandler.GetLast7DaysRecordedMessages(chatId);

            string anyZhdunStr = ChatsUtil.GetTechnicalStickerText("zhdun_vk", null);
            string ourZhdunStr = ChatsUtil.GetTechnicalStickerText("zhdun_vk", "😞");

            bool searchPrevZhdun = false;
            bool foundPrevZhdun = false;

            var messagesWithPaidOrders = new List<TelegramChatMessage>();

            foreach (TelegramChatMessage telegramChatMessage in messages.OrderByDescending(m => m.DateTimeUtc))
            {
                if (!searchPrevZhdun)
                {
                    if (telegramChatMessage.MessageText.EndsWith(ourZhdunStr))
                    {
                        searchPrevZhdun = true;
                    }
                    continue;
                }

                if (telegramChatMessage.MessageText.Contains(anyZhdunStr))
                {
                    foundPrevZhdun = true;
                    break;
                }

                messagesWithPaidOrders.Add(telegramChatMessage);
            }

            if (!searchPrevZhdun || !foundPrevZhdun || messagesWithPaidOrders.Count == 0)
                return;

            // ITelegramDialogueHandler should not execute for too long, so we make a persistent task.
            var persistentTaskData = new ChangePackedOrdersStatusesToSentPersistentTaskData()
            {
                TelegramChatDefinitionName = chatId.ChatDefinitionName,
                TelegramChatMessageIds = messagesWithPaidOrders.Select(m => m.Id).ToArray()
            };

            _logger.Debug($"Will process {messagesWithPaidOrders.Count} messages between zhduns");

            _persistentTasksService.QueueTask(PAID_ORDER_CHAT_SET_STATUS_TO_SENT_PERSISTENT_TASK_NAME, persistentTaskData, null);
        }

        Task IPersistentTaskHandler<ChangePackedOrdersStatusesToSentPersistentTaskData>.BeginExecuteTask(ChangePackedOrdersStatusesToSentPersistentTaskData taskData)
        {
            return this.RunOnWorkerThread(() => this.SetAllPaidOrdersChatOrderStatusesToShippedFromZhdunToZhdun_HandlePersistentTask(taskData));
        }

        public void SetAllPaidOrdersChatOrderStatusesToShippedFromZhdunToZhdun_HandlePersistentTask(ChangePackedOrdersStatusesToSentPersistentTaskData taskData)
        {
            TelegramChatConfiguration garbageChatId = CCAppConfig.TelegramIrisDrop.IrisGarbageChat;

            // Get all messages from db.
            TelegramChatMessageAndLinkedInfo[] msgs = _telegramDbHandler.GetMessagesAndAssociatedDataByDbIds(taskData.TelegramChatMessageIds);

            // Take only PaidOrder message.
            msgs = msgs
                .Where(msg => msg.ChatLinkedInfo != null && msg.ChatLinkedInfo.InfoType == OrderMessageInfoType.Order_StaffPackAndSendOrder)
                .ToArray();

            // Exclude messages that are deleted in telegram chat.
            var forwardTasks = new List<Task<TelegramChatMessageAndLinkedInfo>>();

            foreach (TelegramChatMessageAndLinkedInfo v in msgs)
            {
                int telegramMessageId = v.Message.MessageId;
                TelegramChatMessageAndLinkedInfo vCl = v;

                Task<Message> telegramTask = _telegramSendWorker.Run(w => w.ForwardMessageFromStaffAccount(
                    TelegramChatId.FromUnknownChat(taskData.TelegramChatDefinitionName),
                    garbageChatId,
                    telegramMessageId
                ));

                forwardTasks.Add(
                    telegramTask.ContinueWithShortThread(tt => tt.Result != null ? vCl : null)
                );
            }

            Task.WaitAll(forwardTasks.Cast<Task>().ToArray());

            msgs = forwardTasks
                .Select(t => t.Result)
                .Where(m => m != null)
                .ToArray();

            // Get site orders
            IEnumerable<string> orderIdsToGet = msgs.Select(msg => msg.ChatLinkedInfo.Data);

            List<order> orders = _nonUserWebApi.GetByOneKeyFilterBatched(_nonUserWebApi.GetOrders, "id", orderIdsToGet, null, false);

            List<order> ordersToUpdate = orders
                .Where(ord => ord.current_state == _Configuration.PaidOrderStatus || ord.current_state == _Configuration.PartiallyPaidOrderStatus)
                .ToList();

            _logger.Debug($"Will update {ordersToUpdate.Count} orders to 'sent' status.");

            // Update site orders
            foreach (order order in ordersToUpdate)
            {
                // TODO: check e-mails and order history
                order.current_state = _Configuration.SentOrderStatus;
            }

            _nonUserWebApi.UpdateOrders(ordersToUpdate);

            _telegramSendWorker.Do(w => w.PostToTelegramFromStaffAccount(
                    new Sticker() { FileUniqueId = "CAACAgIAAx0Ca9g8OAACAXtmq2jPe61vMCsq2LAAAVusI1TZO4kAApElAAKezgsAAdZAGeA0_XeRNQQ" },
                    TelegramChatId.FromUnknownChat(taskData.TelegramChatDefinitionName),
                    new PostToTelegramOptions() { Silent = true }
                )
            );
        }

        #endregion

        private Task RunOnWorkerThread(Action action)
        {
            return _prestaSyncWorkerCallbacks.Resolve().Run(w => w.ExecuteCallback(action));
        }
    }
}
