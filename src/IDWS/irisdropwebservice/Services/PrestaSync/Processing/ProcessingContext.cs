using Bukimedia.PrestaSharp.Entities;

using irisdropwebservice.Libs.PrestaSharp;
using irisdropwebservice.Services.Chats;
using irisdropwebservice.Services.Chats.Ins;
using irisdropwebservice.Services.PrestaSync.Remote;
using irisdropwebservice.Services.PrestaSync.ServiceConductors;

namespace irisdropwebservice.Services.PrestaSync.Processing
{
    public class ProcessingContext
    {
        private readonly IPrestaSiteWebApiCallsWorkerNonUser _nonUserWebApi;
        private readonly ChatsExteriorService _chatsExteriorService;
        private readonly LangAndTemplateConductorService _langAndTemplateConductor;

        private readonly Dictionary<long, CustomerExtended> _customersInfoMap = new ();
        private readonly Dictionary<long, guest> _guestsInfoMap = new();
        private readonly Dictionary<long, OrderDetails> _orderDetailsMap = new ();

        public ProcessingContext(IPrestaSiteWebApiCallsWorkerNonUser nonUserWebApi, ChatsExteriorService chatsExteriorService, LangAndTemplateConductorService langAndTemplateConductor)
        {
            _nonUserWebApi = nonUserWebApi;
            _chatsExteriorService = chatsExteriorService;
            _langAndTemplateConductor = langAndTemplateConductor;
        }

        public CustomerExtended GetCustomer(long customerId)
        {
            CustomerExtended res;

            if (_customersInfoMap.TryGetValue(customerId, out res))
                return res;

            res = _nonUserWebApi.Customers_GetCustomer(customerId);

            _customersInfoMap.Add(customerId, res);
            return res;
        }

        public CustomerInfo GetCustomerInfo(string customerPhoneNumber)
        {
            CustomerInfo telegramUser = _chatsExteriorService.GetSubscribedCustomerTelegramUserAndChatIdByPhone(customerPhoneNumber);

            return telegramUser;
        }

        public guest GetGuest(long guestId)
        {
            guest res;

            if (_guestsInfoMap.TryGetValue(guestId, out res))
                return res;

            res = _nonUserWebApi.GetGuest(guestId);

            _guestsInfoMap.Add(guestId, res);
            return res;
        }

        public OrderDetails GetOrderDetails(long orderId)
        {
            OrderDetails res;

            if (_orderDetailsMap.TryGetValue(orderId, out res))
                return res;

            res = _nonUserWebApi.MyApiOrders_GetOrderDetails(orderId, _langAndTemplateConductor.UkrainianLanguage.Id);

            _orderDetailsMap.Add(orderId, res);

            return res;
        }
    }
}
