using System.Text;

using Invictus.Nomenklatura.Logg;

using irisdropwebservice.AppConfig;
using irisdropwebservice.AppConfig.ClassConfigurations;
using irisdropwebservice.Libs.PrestaSharp;
using Invictus.Nomenklatura.Misc;
using irisdropwebservice.Services.Chats;
using irisdropwebservice.Services.Chats.Ins;
using irisdropwebservice.Services.PrestaSync.Ins;
using irisdropwebservice.Services.PrestaSync.Remote;
using irisdropwebservice.Views.PrestaShopForChat;

using Telegram.Bot.Types;

namespace irisdropwebservice.Services.PrestaSync.Processing
{
    public class PsOrderStatusChangeProcessing
    {
        private static readonly PrestaShopCCInfo _Configuration = CCAppConfig.PrestaIrisDrop;

        private readonly ILogger _logger = InvLog.Logger<PsOrderStatusChangeProcessing>();

        private readonly ITelegramSendWorker _telegramSendWorker;
        private readonly RazorTemplateRender _razorViewRender;
        private readonly ITelegramDbHandler _telegramDbHandler;
        private readonly TriggerPrestaPushFlowEvent _triggerPrestaPushFlowEvent;
        private readonly IPrestaSiteWebApiCallsWorkerNonUser _webApi;

        private readonly StringBuilder _reusableStringBuilder = new(1024);

        public PsOrderStatusChangeChatsProcessing OrderStatusChangeChatsProcessing { get; }

        private readonly PsSpecificStatusChangeAction[] _specificStatusChangeActions;

        public PsOrderStatusChangeProcessing(
            ITelegramSendWorker telegramSendWorker,
            RazorTemplateRender razorViewRender,
            ITelegramDbHandler telegramDbHandler,
            PsOrderStatusChangeChatsProcessing orderStatusChangeChatsProcessing,
            TriggerPrestaPushFlowEvent triggerPrestaPushFlowEvent,
            IPrestaSiteWebApiCallsWorkerNonUser webApi
        )
        {
            _telegramSendWorker = telegramSendWorker;
            _razorViewRender = razorViewRender;
            _telegramDbHandler = telegramDbHandler;
            _triggerPrestaPushFlowEvent = triggerPrestaPushFlowEvent;
            _webApi = webApi;

            OrderStatusChangeChatsProcessing = orderStatusChangeChatsProcessing;

            _specificStatusChangeActions = this.CreateOrderChangeActions();
        }

        public void Initialize()
        {
            OrderStatusChangeChatsProcessing.Initialize();
        }

        private PsSpecificStatusChangeAction[] CreateOrderChangeActions()
        {
            var res = new List<PsSpecificStatusChangeAction>();

            var item = new PsSpecificStatusChangeAction(_Configuration.FinalOrderStatuses);
            item.Process = this.SystemProcessFinalOrderStatus;
            res.Add(item);

            item = new PsSpecificStatusChangeAction(Enumerable.Range(0, 100).Select(i => (short)i).ToArray());
            item.Process = this.SystemProcessAnyNewOrderStatus;
            res.Add(item);

            return res.ToArray();
        }

        private void SystemProcessAnyNewOrderStatus(OrdersHistoryEntry ordersHistoryEntry, OrderDetails orderDetails)
        {
            List<StockAvailableExtended> stockAvali = _webApi.MyApiProducts_GetFullStockAvaliInfo(orderDetails.Products.Select(p => p.ProductId).ToArray());

            foreach (OrderDetailProduct orderDetailsProduct in orderDetails.Products)
            {
                StockAvailableExtended stock = stockAvali
                    .Single(sa =>
                        sa.StockAvailable.id_product == orderDetailsProduct.ProductId && sa.StockAvailable.id_product_attribute == orderDetailsProduct.ProductAttributeId
                    );

                _logger.Debug($"[QTY] {orderDetailsProduct.Reference} {orderDetailsProduct.ChosenSizeName} " +
                              $"({orderDetailsProduct.ProductAttributeId}) Id={orderDetailsProduct.ProductId} " +
                              $"Presta={stock.ToString()}"
                );

                _webApi.MyApiProductsSourcedFromVk_SetProductUpdatedPlusMostRecentProductVer(orderDetailsProduct.ProductId, "(OUTDATED)");
            }

            _triggerPrestaPushFlowEvent.QueueTryTrigger();
        }

        private void SystemProcessFinalOrderStatus(OrdersHistoryEntry ordersHistoryEntry, OrderDetails orderDetails)
        {

        }

        public bool ProcessHistoryEntrySystemAndStaff(ProcessingContext processingContext, OrdersHistoryEntry historyEntry)
        {
            if (!historyEntry.ProcessForStaff)
                return false;

            // 1: Retrieve order details
            OrderDetails orderDetails = processingContext.GetOrderDetails(historyEntry.OrderId);

            bool processedSome = false;

            // 2: Execute system actions
            PsSpecificStatusChangeAction[] systemActions = _specificStatusChangeActions.Where(act => act.NewOrderStatuses.Contains(historyEntry.OrderStateId)).ToArray();

            foreach (PsSpecificStatusChangeAction concreteStatusChangeAction in systemActions)
            {
                concreteStatusChangeAction.Process(historyEntry, orderDetails);
                processedSome = true;
            }

            // 3: Execute chat actions
            PsSpecificOrderStatusChangeViewStaff[] concreteStatusChanges = OrderStatusChangeChatsProcessing.GetOrderChangeStaffActions(historyEntry.OrderStateId);

            if (concreteStatusChanges.Length == 0)
                return processedSome;

            foreach (IGrouping<TelegramChatConfiguration, PsSpecificOrderStatusChangeViewStaff> groupByChatId in concreteStatusChanges.GroupBy(psosc => psosc.ChatId))
            {
                _telegramSendWorker.Do(w => w.SendTypingActionFromStaffAccount(groupByChatId.Key));
            }

            foreach (PsSpecificOrderStatusChangeViewStaff concreteStatusChangeAction in concreteStatusChanges)
            {
                if (concreteStatusChangeAction.ChatId.DefinitionName == null)
                    throw new Exception("PsSpecificOrderStatusChange should have ChatId for staff.");

                processedSome = true;

                if (!this.ShouldProcessCustomer(orderDetails.CustomerId))
                    continue;

                string linkedMessageData = orderDetails.OrderId.ToString();

                // TODO?: delete, it was updated
                // TelegramChatMessageAndLinkedInfo[] prevMessagesAboutThis = _telegramDbHandler.GetMessagesByInfoTypeAndData(concreteStatusChangeAction.OrderMessageInfoType, linkedMessageData);

                TelegramChatConfiguration chatId = concreteStatusChangeAction.ChatId;

                string razorViewPath = concreteStatusChangeAction.RazorViewPath;
                object model = concreteStatusChangeAction.CreateModel(historyEntry, orderDetails);

                string renderResult = _razorViewRender.Render(razorViewPath, model, _reusableStringBuilder);

                Message[] messages =
                    _telegramSendWorker.Do(w => w.PostToTelegramFromStaffAccount(renderResult, chatId, new PostToTelegramOptions() { DisableWebPagePreview = true, Silent = false }));

                foreach (Message message in messages)
                {
                    _telegramDbHandler.LinkMessageToData(message, linkedMessageData, concreteStatusChangeAction.OrderMessageInfoType);
                }
            }

            return processedSome;
        }

        public bool ProcessHistoryEntryCustomer(ProcessingContext processingContext, OrdersHistoryEntry historyEntry)
        {
            if (!historyEntry.ProcessForCustomers)
                return false;

            // if (!_langAndTemplateConductor.CustomerNotificationIsOnForThisOrderStateChange(historyEntry.OrderStateId))
            //     return false;

            var orderDetailsLazy = new Lazy<OrderDetails>(() => processingContext.GetOrderDetails(historyEntry.OrderId));

            PsSpecificOrderStatusChangeView[] concreteStatusChangeActions = OrderStatusChangeChatsProcessing.GetOrderChangeCustomerActions(historyEntry.OrderStateId);

            if (concreteStatusChangeActions.Length == 0)
                return false;

            foreach (PsSpecificOrderStatusChangeView concreteStatusChangeAction in concreteStatusChangeActions)
            {
                OrderDetails orderDetails = orderDetailsLazy.Value;

                if (!this.ShouldProcessCustomer(orderDetails.CustomerId))
                    continue;

                string razorViewPath = concreteStatusChangeAction.RazorViewPath;
                object model = concreteStatusChangeAction.CreateModel(historyEntry, orderDetails);

                string renderResult = _razorViewRender.Render(razorViewPath, model, _reusableStringBuilder);

                CustomerExtended customer = processingContext.GetCustomer(orderDetails.CustomerId);
                CustomerInfo telegramUser = processingContext.GetCustomerInfo(customer.PhoneNumber);

                string customerInfoForStaff = StaffFormatHelper.CustomerToString(customer, telegramUser?.TelegramUserHandleLink);

                // First, send to staff modified version.

                TelegramChatConfiguration devChatId = CCAppConfig.TelegramIrisDrop.DuplicateCustomerMessagesChat;

                string devText = telegramUser != null
                    ? "↪↪↪Повідомлення клієнту " + customerInfoForStaff + Environment.NewLine + "TELEGRAM + E-MAIL ?\r\n" + Environment.NewLine + Environment.NewLine + renderResult
                    : "↪↪↪Повідомлення клієнту " + customerInfoForStaff + Environment.NewLine + "TELEGRAM - E-MAIL ?\r\n" + Environment.NewLine + Environment.NewLine + renderResult;

                _telegramSendWorker.Do(w => w.PostToTelegramFromStaffAccount(devText,
                    devChatId,
                    new PostToTelegramOptions() { DisableWebPagePreview = true, Silent = false }
                ));

                /*
                if (telegramUser != null)
                {
                    string customerText = renderResult;

                    Message[] messages = _telegramSendWorker.Do(w => w.PostToTelegramFromCustomersAccount(customerText,
                        TelegramChatId.FromUnknownChatNameOrUser(telegramUser.TelegramUserId),
                        new PostToTelegramOptions() { DisableWebPagePreview = true, Silent = false }
                    ));

                    foreach (Message message in messages)
                    {
                        _telegramDbHandler.LinkMessageToData(message, "NO_ADDITIONAL_INFO", OrderMessageInfoType.Customer_OrderProcessing);
                    }
                }
                */
            }

            return true;
        }

        private bool ShouldProcessCustomer(long customerId)
        {
            if (_Configuration.ProcessTheseCustomerIds == null || _Configuration.ProcessTheseCustomerIds.Length == 0)
                return true;

            bool isThisCustomer = _Configuration.ProcessTheseCustomerIds.Contains(customerId);

            return isThisCustomer ? !_Configuration.ExcludeTheseCustomerIds : _Configuration.ExcludeTheseCustomerIds;
        }
    }
}
