using System.Diagnostics;
using System.Globalization;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using System.Text;

using Bukimedia.PrestaSharp;
using Bukimedia.PrestaSharp.Entities;
using Bukimedia.PrestaSharp.Factories;
using declination = Bukimedia.PrestaSharp.Entities.FilterEntities.declination;

using Castle.DynamicProxy;

using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.AppConfig;
using irisdropwebservice.AppConfig.RemoteResources;
using Invictus.Nomenklatura.Workers;
using irisdropwebservice.Libs.PrestaSharp;
using Invictus.Nomenklatura.Misc;
using Invictus.Nomenklatura.Web;

using irisdropwebservice.Legacy;

using Serilog.Events;

using WebRequestWithRetryResult = irisdropwebservice.Legacy.WebRequestWithRetryResult;

namespace irisdropwebservice.Services.PrestaSync.Remote
{
    [CanFail(LogLevel = LogEventLevel.Debug)]
    public interface IPrestaSiteWebApiCallsWorkerUser : IPrestaSiteWebApiCallsWorkerBase, IWorker<IPrestaSiteWebApiCallsWorkerUser>
    {
    }

    [CanFail(LogLevel = LogEventLevel.Debug)]
    public interface IPrestaSiteWebApiCallsWorkerNonUser : IPrestaSiteWebApiCallsWorkerBase, IWorker<IPrestaSiteWebApiCallsWorkerNonUser>
    {
    }

    [CanFail(LogLevel = LogEventLevel.Debug)]
    public interface IPrestaSiteWebApiCallsWorkerTest : IPrestaSiteWebApiCallsWorkerBase, IWorker<IPrestaSiteWebApiCallsWorkerTest>
    {
        void UpdateCart(cart cart);
        cart AddCart(cart cart);
        order MakeOrder(order order);
        List<address> GetAddresses(Dictionary<string, string> filter, string sort, string limit);
        address AddAddress(address address);
    }

    [CanFail(LogLevel = LogEventLevel.Debug)]
    public interface IPrestaSiteWebApiCallsWorkerBase
    {
        List<customer> GetAllCustomers();
        customer GetCustomer(long customerId);
        guest GetGuest(long guestId);
        List<country> GetAllCountries();
        List<carrier> GetAllCarriers();
        List<currency> GetAllCurrencies();
        List<state> GetAllStates();
        void DeleteProduct(product p);
        void UpdateProduct(product p);
        List<product> UpdateProducts(List<product> p);
        product AddProduct(product p);
        List<product> GetProducts(Dictionary<string, string> filter, string sort, string limit);
        List<product_option_value> GetAllProductOptionValues();
        product_option_value AddProductOptionValue(product_option_value p);
        List<product_option> GetAllProductOptions();
        List<product_feature> GetAllProductFeatures();
        product_feature AddProductFeature(product_feature f);
        List<product_feature_value> GetAllProductFeatureValues();
        List<product_feature_value> AddProductFeatureValues(List<product_feature_value> f);
        List<stock_available> UpdateStockAvailables(List<stock_available> s);
        List<combination> GetCombinations(Dictionary<string, string> filter, string sort, string limit);
        List<combination> UpdateCombinations(List<combination> c);
        List<combination> AddCombinations(List<combination> c);
        void DeleteCombination(long id);
        List<category> GetAllCategories();
        void UpdateCategory(category p);
        category GetCategory(long id);
        category AddCategory(category c);
        List<declination> GetProductImages(long productId);
        void DeleteProductImage(long productId, long imageId);
        void AddProductImage(long productId, byte[] fileContents);
        void DeleteSpecificPrice(long id);
        specific_price AddSpecificPrice(specific_price sp);
        List<specific_price> GetSpecificPrices(Dictionary<string, string> filter, string sort, string limit);
        List<order> GetOrders(Dictionary<string, string> filter, string sort, string limit);
        List<order> UpdateOrders(List<order> orders);

        List<cart> GetCarts(Dictionary<string, string> filter, string sort, string limit);

        List<language> GetAllLanguages();
        List<order_state> GetAllOrderStates();

        DataForProcessing Shared_GetAllUpdatesForProcessing(int maxEach);

        void Misc_SetCmsPageContent(long cmsId, long langId, long shopId, string content);
        string Misc_GetCmsPageContent(long cmsId, long langId, long shopId);
        void Mails_SetLastProcessedEmail(long lastProcessedEmailId);
        void Misc_SetLastProcessedCustomerMessage(long lastId);
        void Misc_SetLastProcessedOrderFile(long lastId);

        CustomerExtended Customers_GetCustomer(long customerId);

        ReservationApprovalResult Reservations_SetApproval(long reservationHistoryId, long reservedProductId, bool approved);
        void Reservations_SetLastProcessedHistoryEntry(long reservationHistoryId);

        OrderDetails MyApiOrders_GetOrderDetails(long orderId, long langId);
        void MyApiOrders_SetOrdersHistoryIsProcessed(long lastHistoryEntryId, bool staff);

        ProductUrls MyApiProducts_GetProductAndCategoryLink(long productId, long categoryId, string categoryLinkRewrite);
        List<ProductInfo> MyApiProducts_GetInfo(long[] productIds, short idLang);
        void MyApiProducts_SetDefaultReservationSettings(long productId);
        List<StockAvailableExtended> MyApiProducts_GetFullStockAvaliInfo(long[] productIds);

        List<SourcedFromVkProductCacheInfo> MyApiProductsSourcedFromVk_GetAllActiveProductsCacheInfo();
        void MyApiProductsSourcedFromVk_SetProductUpdatedPlusMostRecentProductVer(long productId, string vkDescriptionHash);
        void MyApiProductsSourcedFromVk_SetImageUpdated(long productId, string vkFullPhotoId);
        void MyApiProductsSourcedFromVk_SetDeactivated(long productId);
        void MyApiProductsSourcedFromVk_AddNew(long productId);
        List<HistoricalVkImgId> MyApiProductsSourcedFromVk_GetHistoricalVkImgIds(long[] productIds, string reference);
        List<long> MyApiProductsSourcedFromVk_AreTheseProductsSourcedFromVk(long[] productIds);
        void MyApiProductsSourcedFromVk_SetTemporaryQuantityAdjustment(long orderId, long productId, long productAttributeId, int quantity);

        IDisposable IgnoreCancellationRegion();
    }

    public static class PrestaSiteWebApiCallsExtensions
    {
        // Preventing 'too long request body' and 'response timeout' here.
        // This is done upon the layer outside of wrapper to lot others sneak their API calls in between these calls.
        public static List<T> GetByOneKeyFilterBatched<T>(
            this IPrestaSiteWebApiCallsWorkerBase webApiCalls,
            Func<Dictionary<string, string>, string, string, List<T>> getByFilterMethod,
            string key,
            IEnumerable<string> values,
            string sort,
            bool stopIfNothingMore,
            int batchSize = 150,
            CancellationToken cancellationToken = default,
            int sleep = 20
        )
            where T : PrestaShopEntity, IPrestaShopFactoryEntity, new()
        {
            // TODO: check that that is a decorator generated class
            if (!typeof(IPrestaSiteWebApiCallsWorkerBase).IsAssignableFrom(getByFilterMethod.GetMethodInfo().DeclaringType))
                throw new Exception("This is applicable only to " + nameof(IPrestaSiteWebApiCallsWorkerBase));

            var orderedResult = new List<(int index, List<T> res)>();

            Parallel.ForEach(values.Chunk(batchSize),
                new ParallelOptions() { MaxDegreeOfParallelism = 2 },
                (batch, parallelState, index) => {
                    Dictionary<string, string> filter = CreatePrestaFilterByOneField(key, batch);

                    cancellationToken.ThrowIfCancellationRequested();

                    List<T> intermediateRes = getByFilterMethod(filter, sort, null);

                    if (stopIfNothingMore && intermediateRes.Count == 0)
                    {
                        parallelState.Stop();
                    }

                    lock (orderedResult)
                    {
                        orderedResult.Add(((int)index, intermediateRes));
                    }

                    Thread.Sleep(sleep);
                }
            );

            return orderedResult
                .OrderBy(or => or.index)
                .SelectMany(or => or.res)
                .ToList();
        }

        public static List<TResult> GetByOneKeyFilterBatched<TArg, TResult>(
            Func<TArg[], List<TResult>> getByFilterMethod,
            IEnumerable<TArg> values,
            bool stopIfNothingMore,
            int batchSize = 150,
            CancellationToken cancellationToken = default,
            int sleep = 20
        )
        {
            var orderedResult = new List<(int index, List<TResult> res)>();

            Parallel.ForEach(values.Chunk(batchSize),
                new ParallelOptions() { MaxDegreeOfParallelism = 2 },
                (batch, parallelState, index) => {
                    cancellationToken.ThrowIfCancellationRequested();

                    List<TResult> intermediateRes = getByFilterMethod(batch);

                    if (stopIfNothingMore && intermediateRes.Count == 0)
                    {
                        parallelState.Stop();
                    }

                    lock (orderedResult)
                    {
                        orderedResult.Add(((int)index, intermediateRes));
                    }

                    Thread.Sleep(sleep);
                }
            );

            return orderedResult
                .OrderBy(or => or.index)
                .SelectMany(or => or.res)
                .ToList();
        }

        public static Dictionary<string, string> CreatePrestaFilterByOneField(string key, IEnumerable<string> values)
        {
            string filterByIdValues = CreateFilterValues(values);

            var dtn = new Dictionary<string, string>
            {
                { key, filterByIdValues }
            };

            return dtn;
        }

        private static string CreateFilterValues(IEnumerable<string> values)
        {
            var filterOneKeyValues = new StringBuilder();
            filterOneKeyValues.Append("[");

            foreach (string v in values)
            {
                filterOneKeyValues.Append(v);
                filterOneKeyValues.Append("|");
            }

            filterOneKeyValues.Append("0");
            filterOneKeyValues.Append("]");

            return filterOneKeyValues.ToString();
        }
    }

    public class PrestaWebApiCallsWorkerUser : PrestaWebApiCallsWorkerBase, 
        IWorkerImpl<IPrestaSiteWebApiCallsWorkerUser>, IPrestaSiteWebApiCallsWorkerUser
    {
        public override WorkerConfiguration WorkerConfiguration { get; } = new WorkerConfiguration(
            "PSAPIU",
            new WorkerConfiguration.TaskScheduler("PsWebApiCallsUser",1),
            LogEventLevel.Verbose,
            AllowDirectCall: true,
            GetCustomInterceptor: _ => new Interceptor()
        );

        public override ILogger Log { get; } = InvLog.Logger<PrestaWebApiCallsWorkerUser>();

        public IPrestaSiteWebApiCallsWorkerUser PublicInterface { get; set; }
    }

    public class PrestaWebApiCallsWorkerNonUser : PrestaWebApiCallsWorkerBase, 
        IWorkerImpl<IPrestaSiteWebApiCallsWorkerNonUser>, IPrestaSiteWebApiCallsWorkerNonUser,
        IWorkerImpl<IPrestaSiteWebApiCallsWorkerTest>, IPrestaSiteWebApiCallsWorkerTest
    {
        public override WorkerConfiguration WorkerConfiguration { get; } = new WorkerConfiguration(
            "PSAPIN",
            new WorkerConfiguration.TaskScheduler("PsWebApiCalls", 1),
            LogEventLevel.Verbose,
            AllowDirectCall: true,
            GetCustomInterceptor: _ => new Interceptor()
        );

        public override ILogger Log { get; } = InvLog.Logger<PrestaWebApiCallsWorkerNonUser>();

        IPrestaSiteWebApiCallsWorkerNonUser IWorkerImpl<IPrestaSiteWebApiCallsWorkerNonUser>.PublicInterface { get; set; }
        IPrestaSiteWebApiCallsWorkerTest IWorkerImpl<IPrestaSiteWebApiCallsWorkerTest>.PublicInterface { get; set; }
    } 

    public abstract class PrestaWebApiCallsWorkerBase : IWorkerImpl
    {
        public WorkerCore Core { get; set; }
        public abstract ILogger Log { get; }
        public abstract WorkerConfiguration WorkerConfiguration { get; }

        private static bool _CalledCreatePrestaFactoryOnce;

        private static T CreatePrestaFactory<T>()
        {
            PrestaShopRRInfo appCfg = RRAppConfig.PrestaShopIrisDrop;

            if (!_CalledCreatePrestaFactoryOnce)
            {
                _CalledCreatePrestaFactoryOnce = true;

                if (appCfg.BaseUrl.Contains("127.0.0.1"))
                {
                    throw new NotImplementedException("PrestaShop API connection to 127.0.0.1 is not implemented.");
                }
            }

            Type tType = typeof(T);
            ConstructorInfo constructorInfo = tType.GetConstructor(new Type[] { typeof(string), typeof(string), typeof(string) });

            object res = constructorInfo.Invoke(
                BindingFlags.CreateInstance,
                null,
                new object[] { appCfg.BaseUrl, appCfg.ServiceKey, appCfg.Password },
                CultureInfo.CurrentCulture
            );

            return (T)res;
        }

        public class Interceptor : IInterceptor
        {
            private static readonly string[] _AllowedLongCallMethods = new[]
            {
                nameof(AddProductImage),
                nameof(GetAllProductFeatureValues)
            };

            protected ILogger Log { get; } = InvLog.Logger<Interceptor>();

            public void Intercept(IInvocation invocation)
            {
                WebRequestWithRetryOld.WebCallWithRetry(Log, () => this.ApiCallWrapperWebCallWithRetry(invocation));
            }

            private WebRequestWithRetryResult ApiCallWrapperWebCallWithRetry(IInvocation invocation)
            {
                try
                {
                    var sw = Stopwatch.StartNew();

                    invocation.Proceed();

                    sw.Stop();

                    if (sw.Elapsed.TotalSeconds > 10 && !_AllowedLongCallMethods.Contains(invocation.Method.Name))
                    { 
                        Log.Warning($"Call {invocation.Method.Name} took too long ("+(sw.Elapsed.TotalSeconds)+") seconds.");
                    }

                    return WebRequestWithRetryResult.Success(null);
                }
                catch (Exception exc)
                {
                    if (exc.Any(e => e is PrestaSharpException prestaSharpException
                                     &&
                                     (
                                         prestaSharpException.Message.Contains("<code><![CDATA[135]]></code>")
                                         ||
                                         prestaSharpException.Message.Contains("Please set an \"image\" parameter with image data for value")
                                     )
                        ))
                    {
                        throw;
                    }

                    Log.Information("PrestaSiteWebApiCallsDecorator exception.");

                    RetryAdvice advice = WebRequestRetryExceptionBeHandler.GetWebRequestRetryAdvice(Log, exc);

                    return WebRequestWithRetryResult.FromExceptionAdvice(exc, advice);
                }
            }
        }

        public IDisposable IgnoreCancellationRegion()
        {
            return Core.IgnoreCancellationRegion();
        }

        //
        private readonly CartFactory _cartFactory = CreatePrestaFactory<CartFactory>();
        
        public List<cart> GetCarts(Dictionary<string, string> filter, string sort, string limit)
        {
            return _cartFactory.GetByFilter(filter, sort, limit);
        }

        public void UpdateCart(cart cart)
        {
            _cartFactory.Update(cart);
        }

        public cart AddCart(cart cart)
        {
            return _cartFactory.Add(cart);
        }

        //
        private readonly CustomerFactory _customerFactory = CreatePrestaFactory<CustomerFactory>();

        public customer GetCustomer(long customerId)
        {
            return _customerFactory.Get(customerId);
        }

        public List<customer> GetAllCustomers()
        {
            return _customerFactory.GetAll();
        }

        //
        private readonly GuestFactory _guestFactory = CreatePrestaFactory<GuestFactory>();

        public guest GetGuest(long guestId)
        {
            return _guestFactory.Get(guestId);
        }

        //
        private readonly AddressFactory _addressFactory = CreatePrestaFactory<AddressFactory>();

        public List<address> GetAddresses(Dictionary<string, string> filter, string sort, string limit)
        {
            return _addressFactory.GetByFilter(filter, sort, limit);
        }

        public address AddAddress(address address)
        {
            return _addressFactory.Add(address);
        }

        //
        private readonly CountryFactory _countryFactory = CreatePrestaFactory<CountryFactory>();

        public List<country> GetAllCountries()
        {
            return _countryFactory.GetAll();
        }

        //
        private readonly CarrierFactory _carrierFactory = CreatePrestaFactory<CarrierFactory>();

        public List<carrier> GetAllCarriers()
        {
            return _carrierFactory.GetAll();
        }

        //
        private readonly CurrencyFactory _currencyFactory = CreatePrestaFactory<CurrencyFactory>();

        public List<currency> GetAllCurrencies()
        {
            return _currencyFactory.GetAll();
        }

        //
        private readonly StateFactory _stateFactory = CreatePrestaFactory<StateFactory>();

        public List<state> GetAllStates()
        {
            return _stateFactory.GetAll();
        }

        //
        private readonly ProductFactory _productFactory = CreatePrestaFactory<ProductFactory>();

        public void DeleteProduct(product p)
        {
            _productFactory.Delete(p);
        }

        public void UpdateProduct(product p)
        {
            /*if (!p.cache_default_attribute.HasValue || p.cache_default_attribute <= 0)
            {
                throw new Exception("pr.cache_default_attribute <= 0 NOT ALLOWED");
            }*/

            _productFactory.Update(p);
        }

        public List<product> UpdateProducts(List<product> p)
        {
            /*foreach (product pr in p)
            {
                if (!pr.cache_default_attribute.HasValue || pr.cache_default_attribute <= 0)
                {
                    throw new Exception("pr.cache_default_attribute <= 0 NOT ALLOWED");
                }
            }*/
        
            List<product> res = _productFactory.UpdateList(p);

            /*foreach (product pr in res)
            {
                if (!pr.cache_default_attribute.HasValue || pr.cache_default_attribute <= 0)
                {
                    throw new Exception("pr.cache_default_attribute <= 0 NOT ALLOWED");
                }
            }*/

            return res;
        }

        public product AddProduct(product p)
        {
            return _productFactory.Add(p);
        }

        public List<product> GetProducts(Dictionary<string, string> filter, string sort, string limit)
        {
            List<product> res = _productFactory.GetByFilter(filter, sort, limit);

            /*foreach (product pr in res)
            {
                if (!pr.cache_default_attribute.HasValue || pr.cache_default_attribute <= 0)
                {
                    throw new Exception("pr.cache_default_attribute <= 0 NOT ALLOWED");
                }
            }*/

            return res;
        }

        //
        private readonly ProductOptionValueFactory _productOptionValueFactory = CreatePrestaFactory<ProductOptionValueFactory>();

        public List<product_option_value> GetAllProductOptionValues()
        {
            return _productOptionValueFactory.GetAll();
        }

        public product_option_value AddProductOptionValue(product_option_value p)
        {
            return _productOptionValueFactory.Add(p);
        }

        //
        private readonly ProductOptionFactory _productOptionFactory = CreatePrestaFactory<ProductOptionFactory>();

        public List<product_option> GetAllProductOptions()
        {
            return _productOptionFactory.GetAll();
        }

        //
        private readonly ProductFeatureFactory _productFeatureFactory = CreatePrestaFactory<ProductFeatureFactory>();

        public List<product_feature> GetAllProductFeatures()
        {
            return _productFeatureFactory.GetAll();
        }

        public product_feature AddProductFeature(product_feature f)
        {
            return _productFeatureFactory.Add(f);
        }

        //
        private readonly ProductFeatureValueFactory _productFeatureValueFactory = CreatePrestaFactory<ProductFeatureValueFactory>();

        public List<product_feature_value> GetAllProductFeatureValues()
        {
            return _productFeatureValueFactory.GetAll();
        }

        public List<product_feature_value> AddProductFeatureValues(List<product_feature_value> f)
        {
            return _productFeatureValueFactory.AddList(f);
        }

        //
        private readonly StockAvailableFactory _stockAvailableFactory = CreatePrestaFactory<StockAvailableFactory>();

        public List<stock_available> UpdateStockAvailables(List<stock_available> s)
        {
            return _stockAvailableFactory.UpdateList(s);
        }

        //
        private readonly CombinationFactory _combinationFactory = CreatePrestaFactory<CombinationFactory>();

        public List<combination> GetCombinations(Dictionary<string, string> filter, string sort, string limit)
        {
            return _combinationFactory.GetByFilter(filter, sort, limit);
        }

        public List<combination> UpdateCombinations(List<combination> c)
        {
            return _combinationFactory.UpdateList(c);
        }

        public List<combination> AddCombinations(List<combination> c)
        {
            return _combinationFactory.AddList(c);
        }

        public void DeleteCombination(long id)
        {
            _combinationFactory.Delete(id);
        }

        //
        private readonly CategoryFactory _categoryFactory = CreatePrestaFactory<CategoryFactory>();

        public List<category> GetAllCategories()
        {
            return _categoryFactory.GetAll();
        }

        public void UpdateCategory(category p)
        {
            _categoryFactory.Update(p);
        }

        public category GetCategory(long id)
        {
            return _categoryFactory.Get(id);
        }

        public category AddCategory(category c)
        {
            return _categoryFactory.Add(c);
        }

        // 
        private readonly ImageFactoryExtended _imageFactory = CreatePrestaFactory<ImageFactoryExtended>();

        public List<declination> GetProductImages(long productId)
        {
            return _imageFactory.GetProductImagesWithoutException(productId);
        }

        public void DeleteProductImage(long productId, long imageId)
        {
            _imageFactory.DeleteProductImage(productId, imageId);
        }

        public void AddProductImage(long productId, byte[] fileContents)
        {
            _imageFactory.AddProductImageNoMatterWhatNoId(productId, fileContents);
        }

        // 
        private readonly SpecificPriceFactory _specificPriceFactory = CreatePrestaFactory<SpecificPriceFactory>();

        public void DeleteSpecificPrice(long id)
        {
            _specificPriceFactory.Delete(id);
        }

        public specific_price AddSpecificPrice(specific_price sp)
        {
            return _specificPriceFactory.Add(sp);
        }

        public List<specific_price> GetSpecificPrices(Dictionary<string, string> filter, string sort, string limit)
        {
            return _specificPriceFactory.GetByFilter(filter, sort, limit);
        }

        //
        private readonly OrderFactory _orderFactory = CreatePrestaFactory<OrderFactory>();

        public List<order> GetOrders(Dictionary<string, string> filter, string sort, string limit)
        {
            return _orderFactory.GetByFilter(filter, sort, limit);
        }

        public order MakeOrder(order order)
        {
            return _orderFactory.Add(order);
        }

        public List<order> UpdateOrders(List<order> orders)
        {
            return _orderFactory.UpdateList(orders);
        }

        //
        private readonly LanguageFactory _languageFactory = CreatePrestaFactory<LanguageFactory>();

        public List<language> GetAllLanguages()
        {
            return _languageFactory.GetAll();
        }

        //
        private readonly OrderStateFactory _orderStateFactory = CreatePrestaFactory<OrderStateFactory>();

        public List<order_state> GetAllOrderStates()
        {
            return _orderStateFactory.GetAll();
        }

        // 
        private readonly MyIrisDropModulePseudoFactory _myApi = CreatePrestaFactory<MyIrisDropModulePseudoFactory>();

        public DataForProcessing Shared_GetAllUpdatesForProcessing(int maxEach)
        {
            return _myApi.Shared_GetAllUpdatesForProcessing(maxEach);
        }

        public void Misc_SetCmsPageContent(long cmsId, long langId, long shopId, string content)
        {
            _myApi.Other_SetCmsPageContent(cmsId, langId, shopId, content);
        }

        public string Misc_GetCmsPageContent(long cmsId, long langId, long shopId)
        {
            return _myApi.Other_GetCmsPageContent(cmsId, langId, shopId);
        }

        public void Mails_SetLastProcessedEmail(long lastProcessedEmailId)
        {
            _myApi.Mails_SetLastProcessedEmail(lastProcessedEmailId);
        }

        public void Misc_SetLastProcessedCustomerMessage(long lastId)
        {
            _myApi.Misc_SetLastProcessedCustomerMessage(lastId);
        }

        public void Misc_SetLastProcessedOrderFile(long lastId)
        {
            _myApi.Misc_SetLastProcessedOrderFile(lastId);
        }

        public CustomerExtended Customers_GetCustomer(long customerId)
        {
            return _myApi.Customers_GetCustomer(customerId);
        }

        public ReservationApprovalResult Reservations_SetApproval(long reservationHistoryId, long reservedProductId, bool approved)
        {
            return _myApi.Reservations_SetApproval(reservationHistoryId, reservedProductId, approved);
        }

        public void Reservations_SetLastProcessedHistoryEntry(long reservationHistoryId)
        {
            _myApi.Reservations_SetLastProcessedHistoryEntry(reservationHistoryId);
        }

        public void MyApiOrders_SetOrdersHistoryIsProcessed(long lastHistoryEntryId, bool staff)
        {
            _myApi.Orders_SetOrdersHistoryIsProcessed(lastHistoryEntryId, staff);
        }

        public OrderDetails MyApiOrders_GetOrderDetails(long orderId, long langId)
        {
            return _myApi.Orders_GetOrderDetails(orderId, langId);
        }

        public ProductUrls MyApiProducts_GetProductAndCategoryLink(long productId, long categoryId, string categoryLinkRewrite)
        {
            return _myApi.Products_GetProductAndCategoryLink(productId, categoryId, categoryLinkRewrite);
        }

        public List<ProductInfo> MyApiProducts_GetInfo(long[] productIds, short idLang)
        {
            return _myApi.Products_GetInfo(productIds, idLang);
        }

        public void MyApiProducts_SetDefaultReservationSettings(long productId)
        {
            _myApi.Products_SetDefaultReservationSettings(productId);
        }

        public List<StockAvailableExtended> MyApiProducts_GetFullStockAvaliInfo(long[] productIds)
        {
            return _myApi.Products_GetFullStockAvaliInfo(productIds);
        }

        public List<SourcedFromVkProductCacheInfo> MyApiProductsSourcedFromVk_GetAllActiveProductsCacheInfo()
        {
            return _myApi.ProductsSourcedFromVk_GetAllActiveProductsCacheInfo();
        }

        public void MyApiProductsSourcedFromVk_SetProductUpdatedPlusMostRecentProductVer(long productId, string vkDescriptionHash)
        {
            _myApi.ProductsSourcedFromVk_SetProductUpdatedPlusMostRecentProductVer(productId, vkDescriptionHash);
        }

        public void MyApiProductsSourcedFromVk_SetImageUpdated(long productId, string vkFullPhotoId)
        {
            _myApi.ProductsSourcedFromVk_SetImageUpdated(productId, vkFullPhotoId);
        }

        public void MyApiProductsSourcedFromVk_SetDeactivated(long productId)
        {
            _myApi.ProductsSourcedFromVk_SetDeactivated(productId);
        }

        public void MyApiProductsSourcedFromVk_AddNew(long productId)
        {
            _myApi.ProductsSourcedFromVk_AddNew(productId);
        }

        public List<HistoricalVkImgId> MyApiProductsSourcedFromVk_GetHistoricalVkImgIds(long[] productIds, string reference)
        {
            return _myApi.ProductsSourcedFromVk_GetHistoricalVkImgIds(productIds, reference);
        }

        public List<long> MyApiProductsSourcedFromVk_AreTheseProductsSourcedFromVk(long[] productIds)
        {
            return _myApi.ProductsSourcedFromVk_AreTheseProductsSourcedFromVk(productIds);
        }

        public void MyApiProductsSourcedFromVk_SetTemporaryQuantityAdjustment(long orderId, long productId, long productAttributeId, int quantity)
        {
            _myApi.ProductsSourcedFromVk_SetTemporaryQuantityAdjustment(orderId, productId, productAttributeId, quantity);
        }
    }
}
