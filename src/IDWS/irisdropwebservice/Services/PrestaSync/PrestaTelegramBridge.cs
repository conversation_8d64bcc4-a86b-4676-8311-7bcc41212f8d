using System.Text;

using irisdropwebservice.AppConfig;
using irisdropwebservice.Services.Chats;
using irisdropwebservice.Services.Chats.Ins;
using irisdropwebservice.Services.PrestaSync.Remote;
using irisdropwebservice.Services.PrestaSync.ServiceConductors;

using Telegram.Bot.Types;

namespace irisdropwebservice.Services.PrestaSync
{
    public class PrestaTelegramBridge : ITelegramChannelListener
    {
        private readonly IPrestaSiteWebApiCallsWorkerNonUser _prestaApi;
        private readonly LangAndTemplateConductorService _langAndTemplateConductor;

        public PrestaTelegramBridge(TelegramChatBotPolling telegramChatBotPolling, IPrestaSiteWebApiCallsWorkerNonUser prestaApi, LangAndTemplateConductorService langAndTemplateConductor)
        {
            _prestaApi = prestaApi;
            _langAndTemplateConductor = langAndTemplateConductor;
            telegramChatBotPolling.AddChannelListener(this);
        }

        public void IncomingPost(Update update)
        {
            if (!update.IsFromChatWithThisNameOrTheSameChatWithDifferentName(CCAppConfig.TelegramIrisDrop.TTNSourceChannel))
                return;
            if (update.ChannelPost == null || string.IsNullOrWhiteSpace(update.ChannelPost.Text))
                return;

            string prevContent = _prestaApi.Misc_GetCmsPageContent(6, _langAndTemplateConductor.UkrainianLanguage.Id, 1);

            string[] spl = update.ChannelPost.Text.Split("\n");
            StringBuilder bdr = new();
            foreach (string s in spl)
            {
                bdr.Append("<p style=\"line-height: 16px; padding-bottom: 0.5em;\">");
                bdr.Append(s);
                bdr.AppendLine("</p>");
            }

            string addContent = bdr.ToString();

            string newContent = addContent + "<br/>-------------<br/>" + prevContent;

            _prestaApi.Misc_SetCmsPageContent(6, _langAndTemplateConductor.UkrainianLanguage.Id, 1, newContent);
        }
    }
}
