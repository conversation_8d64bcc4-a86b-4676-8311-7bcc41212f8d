using System.Text;

using Bukimedia.PrestaSharp.Entities;

using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;

using irisdropwebservice.Libs.Epicentr;
using irisdropwebservice.Libs.KastaSharp;
using irisdropwebservice.Libs.PromSharp;
using irisdropwebservice.Services.Epicentr;
using irisdropwebservice.Services.KastaSync;
using irisdropwebservice.Services.PrestaSync.Ins;
using irisdropwebservice.Services.PrestaSync.Remote;
using irisdropwebservice.Services.PromSync;
using irisdropwebservice.Services.VkScrap;


using AuxProductFeature = Bukimedia.PrestaSharp.Entities.AuxEntities.product_feature;

namespace irisdropwebservice.Services.PrestaSync.ServiceConductors
{
    // Purpose: Get ALL attributes and attribute options on startup
    // Store them here.

    public class ProductAttributesConductorService
    {
        private ILogger Log { get; } = InvLog.Logger<ProductAttributesConductorService>();

        private readonly IPrestaSiteWebApiCallsWorkerNonUser _webApi;
        private readonly LangAndTemplateConductorService _langAndTemplateConductor;
        private readonly KastaSyncCommon _kastaCommon;
        private readonly EpicentrCommon _epicentrCommon;
        private readonly PromSyncCommon _promCommon;
        private readonly InvProductFeatureCentre _invProductFeatureCentre;
        private readonly PrestaCommon _prestaCommon;

        public long ProductOptionOfAgeId { get; private set; }
        public long ProductOptionOfSizeId { get; private set; }
        public long ProductOptionOfHeightId { get; private set; }

        private readonly record struct AttributeOptionWithValues(product_option Option, List<AttributeOption> Values);

        private readonly List<AttributeOptionWithValues> _allSiteAttributeOptions = new();
        private List<product_feature> _allSiteProductFeatures;
        private List<product_feature_value> _allSiteProductFeatureValues;

        public ProductAttributesConductorService(
            IPrestaSiteWebApiCallsWorkerNonUser webApi,
            LangAndTemplateConductorService langAndTemplateConductor,
            KastaSyncCommon kastaCommon,
            EpicentrCommon epicentrCommon,
            PrestaCommon prestaCommon,
            PromSyncCommon promCommon,
            InvProductFeatureCentre invProductFeatureCentre
        )
        {
            _webApi = webApi;
            _langAndTemplateConductor = langAndTemplateConductor;
            _kastaCommon = kastaCommon;
            _epicentrCommon = epicentrCommon;
            _prestaCommon = prestaCommon;
            _promCommon = promCommon;
            _invProductFeatureCentre = invProductFeatureCentre;
        }

        public void InitializeBeforehand()
        {
            this.Initialize();
        }

        public string GetProductSizeDescriptionByProductOptionId(long productOptionId)
        {
            return _langAndTemplateConductor.GetPrestaTextProductOptionName(_allSiteAttributeOptions.Single(po => po.Values.Any(ao => ao.AttributeId == productOptionId)).Option);
        }

        public long GetProductOptionValue(long optionId, string optionName)
        {
            foreach (AttributeOptionWithValues attributeOptionWithValues in _allSiteAttributeOptions)
            {
                if (attributeOptionWithValues.Option.id != optionId)
                    continue;

                return attributeOptionWithValues.Values.SingleOrDefault(ao => ao.Name == optionName).AttributeId;
            }

            throw new Exception($"AttributeValue {optionName} not found in product option with id {optionId}");
        }

        public AttributeOption GetProductOptionValueById(long id)
        {
            foreach (AttributeOptionWithValues attributeOptionWithValues in _allSiteAttributeOptions)
            {
                AttributeOption attributeOption = attributeOptionWithValues.Values.SingleOrDefault(pov => pov.AttributeId == id);

                if (attributeOption != null)
                    return attributeOption;
            }

            return null;
        }

        public string[] InferFeatureValueFromOtherFeature(product product, string inferFeatureName, Dictionary<string, string> inference)
        {
            product_feature inferFeature = _allSiteProductFeatures.Single(pf => _langAndTemplateConductor.GetPrestaTextProductFeatureName(pf) == inferFeatureName);

            product_feature_value[] inferFeatureValues = product.associations.product_features
                .Where(pf => pf.id == inferFeature.id)
                .Select(productFeature => _allSiteProductFeatureValues.SingleOrDefault(fv => fv.id == productFeature.id_feature_value))
                .Where(pf => pf != null)
                .ToArray();

            if (inferFeatureValues.Length == 0)
                return null;

            string inferStr = null;

            if (inference == null || !inference.Any())
            {
                inferStr = string.Join(";", inferFeatureValues.Select(fv => _langAndTemplateConductor.GetPrestaTextProductFeatureValueValue(fv)).ToArray());
            } else
            {
                foreach (KeyValuePair<string, string> kv in inference)
                {
                    if (inferFeatureValues.Any(fv => _langAndTemplateConductor.GetPrestaTextProductFeatureValueValue(fv) == kv.Key))
                    {
                        // TODO: this infers from first value
                        inferStr = kv.Value;

                        break;
                    }
                }
            }

            if (inferStr == null)
                throw new Exception($"Cannot infer from {inferFeatureName} [1]");

            if (string.IsNullOrWhiteSpace(inferStr))
                return null;

            return inferStr.Split(";");
        }

        public void ReplaceOrAddProductFeatureValue(product product, InvProductFeature invFeature, string[] featureValueTexts, bool overrideIfAlreadySet)
        {
            product_feature feature = _allSiteProductFeatures.Single(pf => _langAndTemplateConductor.GetPrestaTextProductFeatureName(pf) == invFeature.Name);

            product_feature_value[] featureValues = _allSiteProductFeatureValues
                .Where(pfv => pfv.id_feature == feature.id && featureValueTexts.Contains(_langAndTemplateConductor.GetPrestaTextProductFeatureValueValue(pfv)))
                .ToArray();

            AuxProductFeature[] thisProductFeatures = product.associations.product_features.Where(pf => pf.id == feature.id).ToArray();

            if (thisProductFeatures.Length != 0 && !overrideIfAlreadySet)
                return;

            product.associations.product_features.RemoveAll(f => f.id == feature.id.Val());

            if (featureValues.Length == 0 && invFeature.Type == InvProductFeatureType.FreeText)
            {
                List<product_feature_value> commitProductFeatureValues = new();
                
                foreach (string featureValueText in featureValueTexts)
                {
                    var newFeatureValue = new product_feature_value()
                    {
                        custom = 1,
                        id_feature = feature.id,
                        value = _langAndTemplateConductor.CreatePrestaText(featureValueText, null)
                    };
                    
                    commitProductFeatureValues.Add(newFeatureValue);
                }

                if (commitProductFeatureValues.Count != 0)
                {
                    commitProductFeatureValues = _webApi.AddProductFeatureValues(commitProductFeatureValues);
                    
                    _allSiteProductFeatureValues.AddRange(commitProductFeatureValues);
                }

                featureValues = commitProductFeatureValues.ToArray();
            }
            
            foreach (product_feature_value featureValue in featureValues)
            {
                product.associations.product_features.Add(new AuxProductFeature()
                    {
                        id = feature.id.Val(),
                        id_feature_value = featureValue.id.Val()
                    }
                );
            }
        }

        public product_feature GetProductFeature(string featureText)
        {
            product_feature productFeature = _allSiteProductFeatures
                .Where(pf => _langAndTemplateConductor.GetPrestaTextProductFeatureName(pf) == featureText)
                .Single2<product_feature, Exception>($"Feature named {featureText} not found in presta", $"Multiple {featureText} features found");

            return productFeature;
        }
        
        public string GetFeatureText(long featureId)
        {
            return _langAndTemplateConductor.GetPrestaTextProductFeatureName(_allSiteProductFeatures.SingleOrDefault(pf => pf.id == featureId));
        }
        
        public string GetFeatureValueText(long featureValueId)
        {
            return _langAndTemplateConductor.GetPrestaTextProductFeatureValueValue(_allSiteProductFeatureValues.SingleOrDefault(pf => pf.id == featureValueId));
        }

        public void ImportPromCharacteristics()
        {
            PromCategoryDefinitions promCategories = _promCommon.PromCategoryDefinitions;

            PrestaFeatureSpecificationsPlatform platformSpec = _prestaCommon.CategoriesFeaturesInfo.PlatformsSpecifications.Platforms.Single(p => p.PlatformName == "Prom");
            
            List<PrestaFeatureSpecificationsPlatformFeature> customPlatformFeatureSpecs = platformSpec.Features;
            
            var defaultCustomSpec = new PrestaFeatureSpecificationsPlatformFeature();
            
            List<product_feature> allProductFeatures = _webApi.GetAllProductFeatures();
            List<product_feature_value> allProductFeatureValues = _webApi.GetAllProductFeatureValues();
            List<product_feature_value> commitProductFeatureValues = new();

            product_feature productFeature;

            foreach (PromCategoryDef promCategoryDef in promCategories.CategoryList)
            {
                if (promCategoryDef.Attributes.Count == 0)
                    continue;

                PromAttributeDef[] boolAttributes = promCategoryDef.Attributes.Where(a => a.Type == "bool").ToArray();

                if (boolAttributes.Length != 0)
                {
                    string featureName = _invProductFeatureCentre.GetFullFeatureName(platformSpec.FeaturePrefix, promCategoryDef.NameUK, "Склад2");

                    productFeature = allProductFeatures
                        .SingleOrDefault(pf => _langAndTemplateConductor.GetPrestaTextProductFeatureName(pf) == featureName);

                    if (productFeature == null)
                    {
                        productFeature = new product_feature()
                        {
                            name = _langAndTemplateConductor.CreatePrestaText(featureName, null)
                        };

                        productFeature = _webApi.AddProductFeature(productFeature);

                        allProductFeatures.Add(productFeature);
                    }

                    foreach (PromAttributeDef promAttribute in boolAttributes)
                    {
                        if (promAttribute.NameUK == "")
                            continue;
                        
                        product_feature_value[] allFeatureValues = allProductFeatureValues.Where(pfv => pfv.id_feature == productFeature.id).ToArray();
                        
                        bool alreadyHasValue = allFeatureValues.Any(pfv => _langAndTemplateConductor.GetPrestaTextProductFeatureValueValue(pfv) == promAttribute.NameUK);

                        if (alreadyHasValue)
                            continue;

                        var newFeatureValue = new product_feature_value()
                        {
                            custom = 0,
                            id_feature = productFeature.id,
                            value = _langAndTemplateConductor.CreatePrestaText(promAttribute.NameUK, null)
                        };

                        allProductFeatureValues.Add(newFeatureValue);
                        commitProductFeatureValues.Add(newFeatureValue);
                    }
                }

                foreach (PromAttributeDef promAttribute in promCategoryDef.Attributes.Where(a => a.Type != "bool"))
                {
                    if (promAttribute.NameUK == "")
                        continue;
                    
                    PrestaFeatureSpecificationsPlatformFeature customSpec = customPlatformFeatureSpecs.SingleOrDefault(s => s.Name == promAttribute.NameUK);

                    if (customSpec == null)
                        customSpec = defaultCustomSpec;
                    
                    if (customSpec.Ignore)
                        continue;

                    string featureName = _invProductFeatureCentre.GetFullFeatureName(platformSpec, customSpec, promCategoryDef.NameUK, promAttribute.NameUK);

                    productFeature = allProductFeatures
                        .SingleOrDefault(pf => _langAndTemplateConductor.GetPrestaTextProductFeatureName(pf) == featureName);

                    if (productFeature == null)
                    {
                        productFeature = new product_feature()
                        {
                            name = _langAndTemplateConductor.CreatePrestaText(featureName, null)
                        };

                        productFeature = _webApi.AddProductFeature(productFeature);

                        allProductFeatures.Add(productFeature);
                    }

                    if (promAttribute.Type == "singleselect" || promAttribute.Type == "multiselect")
                    {
                        product_feature_value[] allFeatureValues = allProductFeatureValues.Where(pfv => pfv.id_feature == productFeature.id).ToArray();

                        foreach (PromAttributeValueDef featureOption in promAttribute.AttributeValues)
                        {
                            if (featureOption.NameUK == "")
                                continue;
                            
                            bool alreadyHasValue = allFeatureValues.Any(pfv => _langAndTemplateConductor.GetPrestaTextProductFeatureValueValue(pfv) == featureOption.NameUK);

                            if (alreadyHasValue)
                                continue;

                            var newFeatureValue = new product_feature_value()
                            {
                                custom = 0,
                                id_feature = productFeature.id,
                                value = _langAndTemplateConductor.CreatePrestaText(featureOption.NameUK, null)
                            };

                            allProductFeatureValues.Add(newFeatureValue);
                            commitProductFeatureValues.Add(newFeatureValue);
                        }
                    }
                }
            }
            
            if (commitProductFeatureValues.Count > 0)
            {
                foreach (product_feature_value[] chunk in commitProductFeatureValues.Chunk(100))
                {
                    _webApi.AddProductFeatureValues(chunk.ToList());
                }
            }

            Log.Warning("CHARS DONE");
        }
        
        public void ImportEpicentrCharacteristics()
        {
            List<EpicentrCategoryEntry> epiCategories = _epicentrCommon.CategoriesRoot.CategoryEntries;
            
            epiCategories = epiCategories.Where(cat => cat.Name == "піжами-дитячі" || cat.Name == "піжами" || cat.Name == "лосини-дитячі" || cat.Name == "куртки-дитячі" || cat.Name == "пальта-дитячі").ToList();

            PrestaFeatureSpecificationsPlatform platformSpec = _prestaCommon.CategoriesFeaturesInfo.PlatformsSpecifications.Platforms.Single(p => p.PlatformName == "Epicentr");
            
            List<PrestaFeatureSpecificationsPlatformFeature> customPlatformFeatureSpecs = platformSpec.Features;
            
            var defaultCustomSpec = new PrestaFeatureSpecificationsPlatformFeature();
            
            List<product_feature> allProductFeatures = _webApi.GetAllProductFeatures();
            List<product_feature_value> allProductFeatureValues = _webApi.GetAllProductFeatureValues();
            List<product_feature_value> commitProductFeatureValues = new();

            product_feature productFeature;

            foreach (EpicentrCategoryEntry epiCategory in epiCategories)
            {
                foreach (EpicentrFeatureEntry epiFeature in epiCategory.Features)
                {
                    PrestaFeatureSpecificationsPlatformFeature customSpec = customPlatformFeatureSpecs.SingleOrDefault(s => s.Name == epiFeature.FeatureName);

                    if (customSpec == null)
                        customSpec = defaultCustomSpec;
                    
                    if (customSpec.Ignore)
                        continue;

                    string featureName = _invProductFeatureCentre.GetFullFeatureName(platformSpec, customSpec, epiCategory.Name, epiFeature.FeatureName);
                    
                    productFeature = allProductFeatures
                        .SingleOrDefault(pf => _langAndTemplateConductor.GetPrestaTextProductFeatureName(pf) == featureName);

                    if (productFeature == null)
                    {
                        productFeature = new product_feature()
                        {
                            name = _langAndTemplateConductor.CreatePrestaText(featureName, null)
                        };

                        productFeature = _webApi.AddProductFeature(productFeature);

                        allProductFeatures.Add(productFeature);
                    }

                    if (epiFeature.FeatureType == "select" || epiFeature.FeatureType == "multiselect")
                    {
                        product_feature_value[] allFeatureValues = allProductFeatureValues.Where(pfv => pfv.id_feature == productFeature.id).ToArray();

                        foreach (EpicentrFeatureOption featureOption in epiFeature.FeatureOptions)
                        {
                            bool alreadyHasValue = allFeatureValues.Any(pfv => _langAndTemplateConductor.GetPrestaTextProductFeatureValueValue(pfv) == featureOption.OptionName);

                            if (alreadyHasValue)
                                continue;

                            var newFeatureValue = new product_feature_value()
                            {
                                custom = 0,
                                id_feature = productFeature.id,
                                value = _langAndTemplateConductor.CreatePrestaText(featureOption.OptionName, null)
                            };

                            allProductFeatureValues.Add(newFeatureValue);
                            commitProductFeatureValues.Add(newFeatureValue);
                        }
                    }
                }
            }
            
            productFeature = allProductFeatures
                .SingleOrDefault(pf => _langAndTemplateConductor.GetPrestaTextProductFeatureName(pf) == InvProductFeatureCentre.EPICENTR_NAME_FEATURE);

            if (productFeature == null)
            {
                productFeature = new product_feature()
                {
                    name = _langAndTemplateConductor.CreatePrestaText(InvProductFeatureCentre.EPICENTR_NAME_FEATURE, null)
                };

                productFeature = _webApi.AddProductFeature(productFeature);

                allProductFeatures.Add(productFeature);
            }
            
            if (commitProductFeatureValues.Count > 0)
            {
                foreach (product_feature_value[] chunk in commitProductFeatureValues.Chunk(100))
                {
                    _webApi.AddProductFeatureValues(chunk.ToList());
                }
            }

            Log.Warning("CHARS DONE");
        }

        public void ImportKastaCharacteristics()
        {
            long[] allAffiliationIds = _prestaCommon.CategoriesFeaturesInfo.SuperCategories
                .Select(cat => cat.KastaAffiliationId)
                .ToArray();

            string[] allAffiliationKindIds = _prestaCommon.CategoriesFeaturesInfo.Categories
                .Select(cat => cat.KastaAffiliationAndKindId)
                .ToArray();

            KastaMyCategorySchema[] kastaSchemas = _kastaCommon.CategorySchemas
                .Where(schema => allAffiliationIds.Contains(schema.Category.AffiliationId))
                .ToArray();

            KastaMyCategorySchemaKind[] kastaSchemaKinds = kastaSchemas
                .SelectMany(s => s.KindsWithSchemas)
                .Where(sc => allAffiliationKindIds.Contains(sc.CategoryKindItself.AffiliationId + "_" + sc.CategoryKindItself.KindId))
                .ToArray();

            List<product_feature> allProductFeatures = _webApi.GetAllProductFeatures();
            List<product_feature_value> allProductFeatureValues = _webApi.GetAllProductFeatureValues();
            List<product_feature_value> commitProductFeatureValues = new();

            foreach (string affiliationAndKindId in allAffiliationKindIds)
            {
                KastaMyCategorySchemaKind schemaKind = kastaSchemaKinds
                    .Single(sc => sc.CategoryKindItself.AffiliationId + "_" + sc.CategoryKindItself.KindId == affiliationAndKindId);

                foreach (KastaCategorySchemaItem schemaItem in schemaKind.SchemaItems)
                {
                    if (schemaItem.Type != "characteristic")
                        continue;
                    
                    product_feature productFeature = allProductFeatures
                        .SingleOrDefault(pf => _langAndTemplateConductor.GetPrestaTextProductFeatureName(pf) == schemaItem.HumanName);

                    if (productFeature == null)
                    {
                        productFeature = new product_feature()
                        {
                            name = _langAndTemplateConductor.CreatePrestaText(schemaItem.HumanName, null)
                        };

                        productFeature = _webApi.AddProductFeature(productFeature);

                        allProductFeatures.Add(productFeature);
                    }

                    product_feature_value[] allFeatureValues = allProductFeatureValues.Where(pfv => pfv.id_feature == productFeature.id).ToArray();

                    foreach (KastaCategorySize kastaCategorySize in schemaItem.ValueIds)
                    {
                        bool alreadyHasValue = allFeatureValues.Any(pfv => _langAndTemplateConductor.GetPrestaTextProductFeatureValueValue(pfv) == kastaCategorySize.Value);

                        if (alreadyHasValue)
                            continue;

                        var newFeatureValue = new product_feature_value()
                        {
                            custom = 0,
                            id_feature = productFeature.id,
                            value = _langAndTemplateConductor.CreatePrestaText(kastaCategorySize.Value, null)
                        };

                        allProductFeatureValues.Add(newFeatureValue);
                        commitProductFeatureValues.Add(newFeatureValue);
                    }
                }
            }

            if (commitProductFeatureValues.Count > 0)
            {
                _webApi.AddProductFeatureValues(commitProductFeatureValues);
            }

            Log.Warning("CHARS DONE");
        }

        private void Initialize()
        {
            // this.ImportPromCharacteristics();
            // this.ImportKastaCharacteristics();
            // this.ImportEpicentrCharacteristics();

            // Free some memory
            _epicentrCommon.CategoriesRoot = null;
            // _kastaCommon.CategorySchemas = null;
            
            GC.Collect();

            Log.Verbose("Initializing");

            _allSiteProductFeatures = _webApi.GetAllProductFeatures();
            _allSiteProductFeatureValues = _webApi.GetAllProductFeatureValues();

            List<product_option> allProductOptionCategories = _webApi.GetAllProductOptions();

            product_option siteAgeProductOption = allProductOptionCategories.Single(po => po.name.Any(l => l.Value == "Вік"));
            product_option siteSizeProductOption = allProductOptionCategories.Single(po => po.name.Any(l => l.Value == "Розмір"));
            product_option siteHeightProductOption = allProductOptionCategories.Single(po => po.name.Any(l => l.Value == "Зріст"));

            ProductOptionOfAgeId = siteAgeProductOption.id.Val();
            ProductOptionOfSizeId = siteSizeProductOption.id.Val();
            ProductOptionOfHeightId = siteHeightProductOption.id.Val();

            var catsDict = new Dictionary<product_option, List<AttributeOption>>();

            foreach (product_option productOptionCategory in allProductOptionCategories)
            {
                catsDict[productOptionCategory] = new List<AttributeOption>();
            }

            List<product_option_value> allProductOptionValues = _webApi.GetAllProductOptionValues();

            _allSiteAttributeOptions.Clear();

            foreach (product_option_value productOptionValue in allProductOptionValues)
            {
                product_option option = allProductOptionCategories.Single(cat => cat.id == productOptionValue.id_attribute_group);

                string sizeName = _langAndTemplateConductor.GetPrestaTextProductOptionValueName(productOptionValue);

                catsDict[option].Add(new AttributeOption(productOptionValue.id.Val(), sizeName));
            }

            foreach (KeyValuePair<product_option, List<AttributeOption>> kv in catsDict)
            {
                _allSiteAttributeOptions.Add(new AttributeOptionWithValues(kv.Key, kv.Value));
            }
        }

        public void FillWithDataFromVk(VkScrapItem[] vkScrapItems)
        {
            Log.Verbose("FillWithDataFromVk");

            VkScrapItem[] goodScrapItems = vkScrapItems.Where(vksi => vksi.ParsingResult == ParsingResult.Success).ToArray();

            VkScrapItemQtyUnit[] allQtyUnits = goodScrapItems.SelectMany(vksi => vksi.QtyObjects.OfType<VkScrapItemQtyUnit>()).ToArray();

            string[] allVkComAgeSizesDistinct = allQtyUnits.Select(qtyUnit => qtyUnit.Size_Age)
                .Distinct()
                .Where(s => s != null)
                .ToArray();

            string[] allVkComSizeSizesDistinct = allQtyUnits.Select(qtyUnit => qtyUnit.Size_Size)
                .Distinct()
                .Where(s => s != null)
                .ToArray();

            string[] allVkComHeightSizesDistinct = allQtyUnits.Select(qtyUnit => qtyUnit.Size_Height_Fixed)
                .Distinct()
                .Where(s => s != null)
                .ToArray();

            this.AddNewValuesIfNeeded(allVkComAgeSizesDistinct, _allSiteAttributeOptions.Single(ao => ao.Option.id == ProductOptionOfAgeId));
            this.AddNewValuesIfNeeded(allVkComSizeSizesDistinct, _allSiteAttributeOptions.Single(ao => ao.Option.id == ProductOptionOfSizeId));
            this.AddNewValuesIfNeeded(allVkComHeightSizesDistinct, _allSiteAttributeOptions.Single(ao => ao.Option.id == ProductOptionOfHeightId));
        }

        private void AddNewValuesIfNeeded(string[] allVkComSizesDistinct, AttributeOptionWithValues attributeOptionWithValues)
        {
            foreach (string size in allVkComSizesDistinct)
            {
                AttributeOption existingAttributeOption = attributeOptionWithValues.Values.FirstOrDefault(pov => pov.Name == size);

                if (existingAttributeOption != null)
                    continue;

                Log.Information($"Creating product attribute {attributeOptionWithValues.Option.name.First().Value} :: {size}.");

                product_option_value newOptionValue = _webApi.AddProductOptionValue(
                    new product_option_value() { id_attribute_group = attributeOptionWithValues.Option.id, name = _langAndTemplateConductor.CreatePrestaText(size, null) }
                );

                attributeOptionWithValues.Values.Add(new AttributeOption(newOptionValue.id.Val(), size));
            }
        }
    }
}