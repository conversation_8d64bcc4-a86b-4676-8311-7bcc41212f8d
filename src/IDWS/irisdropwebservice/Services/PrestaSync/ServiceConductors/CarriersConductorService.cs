using Bukimedia.PrestaSharp.Entities;

using Invictus.Nomenklatura.Misc;

using irisdropwebservice.Services.PrestaSync.Remote;


namespace irisdropwebservice.Services.PrestaSync.ServiceConductors
{
    public class CarriersConductorService
    {
        private readonly Dictionary<long, string> _carrierIdToNameDictionary = new();

        private readonly IPrestaSiteWebApiCallsWorkerNonUser _apiCalls;

        public CarriersConductorService(IPrestaSiteWebApiCallsWorkerNonUser apiCalls)
        {
            _apiCalls = apiCalls;
        }

        public void InitializeBeforehand()
        {
            this.Recreate();
        }

        public string GetCarrierNameById(long id)
        {
            if (id == 0)
                return "Самовивіз";

            if (!_carrierIdToNameDictionary.ContainsKey(id))
            {
                this.Recreate();

                if (!_carrierIdToNameDictionary.ContainsKey(id))
                    throw new Exception($"Carrier id {id} was not found.");
            }

            return _carrierIdToNameDictionary[id];
        }

        private void Recreate()
        {
            List<carrier> allCarriers = _apiCalls.GetAllCarriers();

            _carrierIdToNameDictionary.Clear();
            foreach (carrier carrier in allCarriers)
                _carrierIdToNameDictionary[carrier.id.Val()] = carrier.name;
        }
    }
}