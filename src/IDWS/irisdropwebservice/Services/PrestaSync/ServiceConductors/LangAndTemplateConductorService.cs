using System.Text;
using System.Text.RegularExpressions;

using Bukimedia.PrestaSharp.Entities;

using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Misc;

using irisdropwebservice.Services.LinkService;
using irisdropwebservice.Services.PrestaSync.Ins;
using irisdropwebservice.Services.PrestaSync.Remote;
using irisdropwebservice.Services.VkScrap;


using auxLanguage = Bukimedia.PrestaSharp.Entities.AuxEntities.language;

namespace irisdropwebservice.Services.PrestaSync.ServiceConductors
{
    public class LangAndTemplateConductorService
    {
        private const string BLANK_PRODUCT_NAME = "tovar";

        private readonly IPrestaSiteWebApiCallsWorkerNonUser _apiCalls;
        private readonly LanguageService _languageService;

        private readonly List<PrestaLanguage> _allLanguages = new ();
        private readonly Dictionary<short, OrderStateChangeConfiguration> _orderStateIdToOrderStateChangeConfiguration = new();
        private readonly Dictionary<LangIdAndMailTemplate, List<(short, OrderStateChangeLangConfiguration)>> _templateNameToOrderStateChangeConfigurations = new();

        public PrestaLanguage UkrainianLanguage => _allLanguages.Single(l => l.TwoLetterIsoCode == "uk");
        public PrestaLanguage RussianLanguage => _allLanguages.Single(l => l.TwoLetterIsoCode == "ru");

        public LangAndTemplateConductorService(IPrestaSiteWebApiCallsWorkerNonUser apiCalls, LanguageService languageService)
        {
            _apiCalls = apiCalls;
            _languageService = languageService;
        }

        public List<auxLanguage> CreatePrestaText(string uaText, string ruText)
        {
            ruText ??= uaText;
            
            return new List<auxLanguage>()
            {
                new auxLanguage() { id = UkrainianLanguage.Id, Value = IntStringUtil.ConvertNewlinesToHtmlBr(uaText) },
                new auxLanguage() { id = RussianLanguage.Id, Value = IntStringUtil.ConvertNewlinesToHtmlBr(ruText) }
            };
        }

        public string GetPrestaTextCategoryName(category category)
        {
            return this.GetPrestaText(category.name);
        }
        
        public string GetPrestaTextCategoryLinkRewrite(category category)
        {
            return this.GetPrestaText(category.link_rewrite);
        }

        public string GetPrestaTextProductOptionName(product_option productOption)
        {
            return this.GetPrestaText(productOption.name);
        }
        
        public string GetPrestaTextProductOptionValueName(product_option_value productOptionValue)
        {
            return this.GetPrestaText(productOptionValue.name);
        }
        
        public string GetPrestaTextProductFeatureValueValue(product_feature_value productFeatureValue)
        {
            return this.GetPrestaText(productFeatureValue.value);
        }
        
        public string GetPrestaTextProductFeatureName(product_feature productFeature)
        {
            return this.GetPrestaText(productFeature.name);
        }
        
        public string GetPrestaTextCountry(country country)
        {
            return this.GetPrestaText(country.name);
        }

        private string GetPrestaText(List<auxLanguage> l)
        {
            auxLanguage existingText = l.SingleOrDefault(ll => ll.id == UkrainianLanguage.Id);

            if (existingText == null)
                return null;

            return existingText.Value;
        }

        public bool IsBlankProductName(List<auxLanguage> l)
        {
            if (l.Count == 0)
                return false;

            return l.All(ll => ll.Value == BLANK_PRODUCT_NAME);
        }

        public List<auxLanguage> CreateBlankProductName()
        {
            return _allLanguages.Select(l => new auxLanguage(l.Id, BLANK_PRODUCT_NAME)).ToList();
        }

        public List<auxLanguage> CreateLinkRewrite(List<auxLanguage> names)
        {
            return names.Select(l => new auxLanguage(l.id, GetUrlFriendlyText(l.Value))).ToList();
        }

        public bool CustomerNotificationIsOnForThisOrderStateChange(short newOrderState)
        {
            return _orderStateIdToOrderStateChangeConfiguration[newOrderState].SendMail;
        }

        public string GetTemplateNameForNewOrderState(short newOrderState)
        {
            OrderStateChangeConfiguration cfg = _orderStateIdToOrderStateChangeConfiguration[newOrderState];

            string mailTemplateName = cfg.PerLanguage.Single().MailTemplateName;

            return mailTemplateName;
        }

        public IEnumerable<short> GetOrderStatesByTemplateName(string templateName)
        {
            PrestaLanguage singleLanguage = UkrainianLanguage;

            List<(short, OrderStateChangeLangConfiguration)> langCfg = _templateNameToOrderStateChangeConfigurations[new LangIdAndMailTemplate(singleLanguage.Id, templateName)];

            if (langCfg.Count == 0)
                return Array.Empty<short>();

            return langCfg.Select(lcfg => lcfg.Item1);
        }

        public void InitializeBeforehand()
        {
            this.RecreateLangs();
            this.RecreateOrderStates();
        }

        private void RecreateLangs()
        {
            List<language> allLanguages = _apiCalls.GetAllLanguages();

            _allLanguages.Clear();

            foreach (language language in allLanguages)
            {
                var prestaLang = new PrestaLanguage((short)language.id.Val(), language.iso_code);

                _allLanguages.Add(prestaLang);
            }

            if (_allLanguages.Count > 2)
            {
                throw new NotImplementedException("PrestaShop contains multiple languages, multiple languages are not supported by this App.");
            }
        }

        private void RecreateOrderStates()
        {
            List<order_state> allOrderStates = _apiCalls.GetAllOrderStates();

            _orderStateIdToOrderStateChangeConfiguration.Clear();
            _templateNameToOrderStateChangeConfigurations.Clear();

            foreach (order_state orderState in allOrderStates)
            {
                IEnumerable<OrderStateChangeLangConfiguration> perLanguage = orderState.name.FullOuterJoinSingle(
                    orderState.template,
                    l => l.id,
                    l => l.id,
                    (name, template, key) => {
                        if (name == null || template == null || name.id != key || name.id != template.id)
                            throw new Exception("order_state and order_state_lang are a bit corrupted.");

                        return new OrderStateChangeLangConfiguration((short)key, name.Value, template.Value);
                    }
                );

                short orderStateId = (short)orderState.id.Val();

                var myOrderStateChangeConfiguration = new OrderStateChangeConfiguration(
                    orderStateId,
                    orderState.send_email != 0,
                    perLanguage.ToArray()
                );

                foreach (OrderStateChangeLangConfiguration orderStateChangeLangConfiguration in myOrderStateChangeConfiguration.PerLanguage)
                {
                    var key = new LangIdAndMailTemplate(orderStateChangeLangConfiguration.LangId, orderStateChangeLangConfiguration.MailTemplateName);

                    if (!_templateNameToOrderStateChangeConfigurations.ContainsKey(key))
                        _templateNameToOrderStateChangeConfigurations.Add(key, new List<(short, OrderStateChangeLangConfiguration)>());

                    List<(short, OrderStateChangeLangConfiguration)> val = _templateNameToOrderStateChangeConfigurations[key];

                    val.Add((orderStateId, orderStateChangeLangConfiguration));
                }

                _orderStateIdToOrderStateChangeConfiguration[(short)orderState.id.Val()] = myOrderStateChangeConfiguration;
            }
        }

        private static string GetUrlFriendlyText(string title)
        {
            if (string.IsNullOrEmpty(title))
                return "";

            title = UnidecodeSharpCore.Unidecoder.Unidecode(title);

            // remove entities
            title = Regex.Replace(title, @"&\w+;", "");

            // remove anything that is not letters, numbers, dash, or space
            title = Regex.Replace(title, @"[^A-Za-z0-9\-\s]", "");

            // remove any leading or trailing spaces left over
            title = title.Trim();

            // replace spaces with single dash
            title = Regex.Replace(title, @"\s+", "-");

            // if we end up with multiple dashes, collapse to single dash            
            title = Regex.Replace(title, @"\-{2,}", "-");

            // make it all lower case
            title = title.ToLower();

            // if it's too long, clip it
            if (title.Length > 80)
                title = title.Substring(0, 79);

            // remove trailing dash, if there is one
            if (title.EndsWith("-"))
                title = title.Substring(0, title.Length - 1);

            return title;
        }

        public string GetVkSourcedDescriptionWithoutQuantity(string description)
        {
            string[] spl = description.Split('\n', StringSplitOptions.TrimEntries);

            for (int i = 0; i < spl.Length; i++)
            {
                if (spl[i].IndexOf("шт.", StringComparison.InvariantCultureIgnoreCase) == -1)
                    continue;

                int index = spl[i].LastIndexOf("- ", StringComparison.InvariantCultureIgnoreCase);

                if (index < 1)
                    continue;

                spl[i] = spl[i][..index].Trim();
            }

            return string.Join('\n', spl);
        }

        public void SetSiteProductNameAndDescriptions(product product, VkScrapItem vksi, bool zeroSizes)
        {
            bool parsingError = vksi.ParsingResult != ParsingResult.Success;

            if (!parsingError) // These all are nulls otherwise, let's just not touch that.
            {
                var sizesBdr = new StringBuilder();

                foreach (object sizeDesc in vksi.QtyObjects)
                {
                    if (sizeDesc is VkScrapItemQtyStr sizeAsStr)
                    {
                        sizesBdr.AppendLine(sizeAsStr.Value);
                    }
                    else if (sizeDesc is VkScrapItemQtyUnit sizeAsQtyUnit)
                    {
                        string qtyUnitStr = this.QtyUnitToString(sizeAsQtyUnit, zeroSizes);
                        sizesBdr.AppendLine(qtyUnitStr);
                    }
                    else
                    {
                        throw TypeAbominationException.Variant("QtyObject", sizeDesc);
                    }
                }

                string desc = IntStringUtil.CapitalizeEachNewLine(vksi.PhotoTitle + Environment.NewLine + vksi.Description);
                
                product.description = this.CreatePrestaText(sizesBdr.ToString(), _languageService.TryTranslateToRussianRelatedToProduct(product.reference, "desc", sizesBdr.ToString()));
                product.description_short = this.CreatePrestaText(desc, _languageService.TryTranslateToRussianRelatedToProduct(product.reference, "desc_short", desc));
                product.name = this.CreatePrestaText(vksi.PhotoTitle, _languageService.TryTranslateToRussianRelatedToProduct(product.reference, "name", vksi.PhotoTitle));
                product.link_rewrite = this.CreateLinkRewrite(product.name);
            }
        }

        public string QtyUnitToString(VkScrapItemQtyUnit qtyUnit, bool qtyZero = false)
        {
            var bdr = new StringBuilder();

            if (qtyUnit.Size_Height_Fixed != null)
            {
                bdr.Append(qtyUnit.Size_Height_Fixed);
            }

            if (qtyUnit.Size_Size != null)
            {
                bdr.Append(qtyUnit.Size_Size);
            }

            if (qtyUnit.Size_Age != null)
            {
                bdr.Append(qtyUnit.Size_Age);

                if (qtyUnit.Size_Size != null)
                {
                    bdr.Append("/");
                    bdr.Append(qtyUnit.Size_Size);
                }
            }

            if (qtyUnit.Size_TheRestOfString != null)
            {
                bdr.Append("/");
                bdr.Append(qtyUnit.Size_TheRestOfString);
            }

            bdr.Append(" - ");
            bdr.Append(qtyZero ? 0 : qtyUnit.Quantity);
            bdr.Append("шт.");

            if (qtyUnit.SpecialPriceReduction != null && qtyUnit.SpecialPriceReduction != 0)
            {
                bdr.Append(" Спец. знижка: ");
                bdr.Append(qtyUnit.SpecialPriceReduction);
                bdr.Append(" грн.");
            }

            return bdr.ToString();
        }
    }
}
