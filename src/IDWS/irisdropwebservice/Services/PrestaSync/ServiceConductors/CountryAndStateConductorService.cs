using Bukimedia.PrestaSharp.Entities;

using Invictus.Nomenklatura.Misc;

using irisdropwebservice.Services.PrestaSync.Remote;


namespace irisdropwebservice.Services.PrestaSync.ServiceConductors
{
    public class CountryAndStateConductorService
    {
        private readonly Dictionary<long, string> _countryIdToNameDictionary = new();
        private readonly Dictionary<long, string> _stateIdToNameDictionary = new();

        private readonly IPrestaSiteWebApiCallsWorkerNonUser _apiCalls;
        private readonly LangAndTemplateConductorService _langAndTemplateConductor;

        public CountryAndStateConductorService(IPrestaSiteWebApiCallsWorkerNonUser apiCalls, LangAndTemplateConductorService langAndTemplateConductor)
        {
            _apiCalls = apiCalls;
            _langAndTemplateConductor = langAndTemplateConductor;
        }

        public void InitializeBeforehand()
        {
            this.RecreateCounties();
            this.RecreateStates();
        }

        public long GetFirstCountryId()
        {
            return _countryIdToNameDictionary.First().Key;
        }

        public string GetCountryNameById(long id)
        {
            if (!_countryIdToNameDictionary.ContainsKey(id))
            {
                this.RecreateCounties();

                if (!_countryIdToNameDictionary.ContainsKey(id))
                    throw new Exception($"Country id {id} was not found.");
            }

            return _countryIdToNameDictionary[id];
        }

        private void RecreateCounties()
        {
            country[] allCountries = _apiCalls.GetAllCountries().Where(c => c.active > 0).ToArray();

            _countryIdToNameDictionary.Clear();
            foreach (country country in allCountries)
                _countryIdToNameDictionary[country.id.Val()] = _langAndTemplateConductor.GetPrestaTextCountry(country);
        }

        public string TryGetStateNameById(long id)
        {
            if (!_stateIdToNameDictionary.ContainsKey(id))
            {
                this.RecreateStates();

                if (!_stateIdToNameDictionary.ContainsKey(id))
                    return null;
            }

            return _stateIdToNameDictionary[id];
        }

        private void RecreateStates()
        {
            List<state> allStates = _apiCalls.GetAllStates();

            _stateIdToNameDictionary.Clear();
            foreach (state state in allStates)
                _stateIdToNameDictionary[state.id.Val()] = state.name;
        }
    }
}