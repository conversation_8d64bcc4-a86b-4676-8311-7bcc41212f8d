using Invictus.Nomenklatura.Misc;
using System.IO;
using System.Reflection;

using irisdropwebservice.Services.PrestaSync.Ins;

namespace irisdropwebservice.Services.PrestaSync
{
    public class PrestaCommon
    {
        public PrestaExteriorCategoriesInfo CategoriesFeaturesInfo { get; }

        public PrestaCommon()
        {
            const string FEATURES_PATH = @"irisdropwebservice.Services.PrestaSync.Resources.cats_and_features.json";

            using Stream stream = Assembly.GetExecutingAssembly().GetManifestResourceStream(FEATURES_PATH);
            using var reader = new StreamReader(stream);

            CategoriesFeaturesInfo = new InvJsonSerializer().DeserializeForInternals<PrestaExteriorCategoriesInfo>(reader.ReadToEnd());
        }
    }
}
