using System.Diagnostics.CodeAnalysis;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.ExteriorServ;

using irisdropwebservice.Services.PrestaSync.Ins;
using irisdropwebservice.Services.PrestaSync.Processing;
using irisdropwebservice.Services.PrestaSync.Remote;
using irisdropwebservice.Services.PrestaSync.ServiceConductors;
using irisdropwebservice.Services.PrestaSync.SiteFillConductors;

using JetBrains.Annotations;

using Microsoft.Extensions.DependencyInjection;

namespace irisdropwebservice.Services.PrestaSync
{
    [UsedImplicitly]
    public class PrestaSyncExteriorServiceBuilder : ExteriorServiceBuilderBase<PrestaSyncExteriorService>
    {
        protected override void AddConcealedServices(ServiceCollection concealedServiceCollection)
        {
            concealedServiceCollection.AddWorkerSingleton<PrestaProductSyncWorker>();
            concealedServiceCollection.AddWorkerSingleton<PrestaSyncWorker>();

            concealedServiceCollection.AddWorkerSingleton<PrestaWebApiCallsWorkerNonUser>();
            concealedServiceCollection.AddWorkerSingleton<PrestaWebApiCallsWorkerUser>();
            
            concealedServiceCollection.AddSingleton<TriggerPrestaPushFlowEvent>();
            concealedServiceCollection.AddSingleton<TriggerExternalFlowsEvent>();

            concealedServiceCollection.AddSingleton<TriggerPollingCycleResoluteEvent>();

            concealedServiceCollection.AddSingleton<LangAndTemplateConductorService>();
            concealedServiceCollection.AddFactory<CategoriesConductorService>(_ => new object[] { CategoriesConductorServiceMode.Full });
            concealedServiceCollection.AddSingletonFromFactory<CategoriesConductorService>();
            concealedServiceCollection.AddSingleton<CarriersConductorService>();
            concealedServiceCollection.AddSingleton<CountryAndStateConductorService>();
            concealedServiceCollection.AddSingleton<ProductAttributesConductorService>();
            concealedServiceCollection.AddSingleton<AllProductsDataConductorService>();

            concealedServiceCollection.AddSingleton<PsOrderStatusChangeProcessing>();
            concealedServiceCollection.AddSingleton<PsOrderStatusChangeChatsProcessing>();
            concealedServiceCollection.AddSingleton<PsReservationsProcessing>();
            concealedServiceCollection.AddSingleton<PsMiscProcessing>();
            concealedServiceCollection.AddFactory<ProcessingContext>(_ => Array.Empty<object>());

            concealedServiceCollection.AddSingleton<PrestaTelegramBridge>();
            concealedServiceCollection.AddSingleton<PrestaCommon>();
            concealedServiceCollection.AddSingleton<InvProductFeatureCentre>();

            this.RegisterDatabaseTypes(concealedServiceCollection);

            base.AddConcealedServices(concealedServiceCollection);
        }

        protected override void ExposeConcealedServices()
        {
            this.ExposeSingleton<IPrestaWorkerPublic>();
            this.ExposeSingleton<IPrestaProductSyncPublic, PrestaProductSyncWorker>();

            this.ExposeSingleton<InvProductFeatureCentre>();
            
            base.ExposeConcealedServices();
        }
    }
}