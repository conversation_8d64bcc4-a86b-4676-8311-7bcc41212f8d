namespace irisdropwebservice.Services.VkScrap
{
    public static class VkNamesAndUrls
    {
        public static string GetVkComGroupPhotoUri(string fullId)
        {
            (long vkGroupId, _, long vkPhotoId) = ParseFullId(fullId);

            return $"https://vk.com/photo-{Math.Abs(vkGroupId)}_{vkPhotoId}";
        }

        public static string GetVkComGroupAlbumUri(string fullId)
        {
            (long vkGroupId, long vkAlbumId, _) = ParseFullId(fullId);

            return $"https://vk.com/album-{Math.Abs(vkGroupId)}_{vkAlbumId}";
        }

        public static string CreateFullId(long groupId, long albumId, long photoId)
        {
            return groupId + "_" + albumId + "_" + photoId;
        }

        public static (long vkGroupId, long vkAlbumId, long vkPhotoId) ParseFullId(string fullId)
        {
            string[] spl = fullId.Split("_");

            return (long.Parse(spl[0]), long.Parse(spl[1]), long.Parse(spl[2]));
        }
    }
}