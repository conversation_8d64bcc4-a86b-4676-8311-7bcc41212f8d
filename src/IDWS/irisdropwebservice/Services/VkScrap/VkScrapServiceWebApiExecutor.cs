using System.IO;

using irisdropwebservice.AppConfig;
using irisdropwebservice.AppConfig.ClassConfigurations;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;

using irisdropwebservice.Legacy;
using irisdropwebservice.Services.VkScrap.Ins;

namespace irisdropwebservice.Services.VkScrap
{
    public class VkScrapServiceWebApiExecutor
    {
        private readonly IDbAccessFactory<VkScrapDbAccess> _databaseAccessFactory;
        private readonly LeFileSystemService _fileSystemService;

        private readonly VkScrapCCInfo _configuration = CCAppConfig.VkScrapIrisDrop;

        public VkScrapServiceWebApiExecutor(IDbAccessFactory<VkScrapDbAccess> databaseAccessFactory, LeFileSystemService fileSystemService)
        {
            _databaseAccessFactory = databaseAccessFactory;
            _fileSystemService = fileSystemService;
        }

        public long GetLastCompleteScrapGeneration()
        {
            using VkScrapDbAccess dbAccess = _databaseAccessFactory.CreateAccess();

            VkScrapGeneration lastGen = dbAccess.GetLastCompleteScrapGeneration();

            if (lastGen == null)
                return -1;

            return lastGen.Id;
        }

        public VkScrapItemDto[] GetScrappedItemsFromGeneration(long scrapGenerationId, ScrapReadMode readMode)
        {
            VkScrapItem[] resWrapped =
                ReadScrappedDataFromSpecificGenerationAndMarkAsRead(_databaseAccessFactory, scrapGenerationId, readMode);

            VkScrapItemDto[] resDto = resWrapped
                .Select(vksi => (VkScrapItemDto)((IFriendGetDto)vksi).Friend_GetDto())
                .ToArray();

            return resDto;
        }

        public byte[] GetScrapItemImgBytes(string vkFullPhotoId)
        {
            string filePath = _configuration.GetPhotoFilePath(vkFullPhotoId);

            if (!_fileSystemService.FileExistsAndIsOk(filePath))
                throw new Exception($"Requested file by full vk id {vkFullPhotoId} is either not found or corrupted.");

            byte[] res = _fileSystemService.ReadWithFileStream(filePath,
                fs => {
                    using var binaryReader = new BinaryReader(fs);

                    return binaryReader.ReadBytes((int)fs.Length);
                }
            );

            return res;
        }

        public VkScrapArtLogDto GetVkProductChangelog(string art)
        {
            using VkScrapDbAccess dbAccess = _databaseAccessFactory.CreateAccess();

            VkScrapArtLogItem[] allScrapLogItems = dbAccess.GetAllScrapLogItems();

            VkScrapArtLogItem res = allScrapLogItems.FirstOrDefault(logItem => logItem.Art == art);

            return res == null ? new VkScrapArtLogDto() : (VkScrapArtLogDto)((IFriendGetDto)res).Friend_GetDto();
        }

        private readonly ILogger _logger = InvLog.Logger<VkScrapServiceWebApiExecutor>();

        public VkScrapArtLogDto[] GetAllVkProductsChangelog()
        {
            using VkScrapDbAccess dbAccess = _databaseAccessFactory.CreateAccess();

            _logger.Debug("Logging during GetAllVkProductsChangelog(). Last 10 scrap generations: ");

            foreach (VkScrapGeneration vkScrapGeneration in dbAccess.GetAllScrapGenerations().TakeLast(10))
            {
                _logger.Debug($"vkScrapGeneration: {vkScrapGeneration.Id} {vkScrapGeneration.ScrapStartDateTime} {vkScrapGeneration.IsComplete} {vkScrapGeneration.LastFetch} {vkScrapGeneration.ScrapInfoFetchingDurationMs}");   
            }

            return dbAccess.GetAllScrapLogItems().Select(logItem => (VkScrapArtLogDto)((IFriendGetDto)logItem).Friend_GetDto()).ToArray();
        }

        public static VkScrapItem[] ReadScrappedDataFromSpecificGenerationAndMarkAsRead(IDbAccessFactory<VkScrapDbAccess> dbAccessFactory, long scrapGenerationId, ScrapReadMode readMode)
        {
            using VkScrapDbAccess dbAccess = dbAccessFactory.CreateAccess();

            VkScrapGeneration scrapGeneration = dbAccess.GetScrapGenerationById(scrapGenerationId);

            if (scrapGeneration == null)
                throw new Exception($"Scrap Generation {scrapGenerationId} was not found in database.");

            if (!scrapGeneration.IsComplete)
                throw new Exception($"Requested Generation {scrapGenerationId} is incomplete, cannot use it.");

            scrapGeneration.LastFetch = ServerClock.GetCurrentUtcTime().DateTime;

            dbAccess.UpdateScrapGeneration(scrapGeneration);
            dbAccess.SaveChanges();

            VkScrapItem[] lastScrappedItems = dbAccess.GetScrapItemsFromGeneration(scrapGeneration.Id, readMode);

            return lastScrappedItems;
        }
    }
}