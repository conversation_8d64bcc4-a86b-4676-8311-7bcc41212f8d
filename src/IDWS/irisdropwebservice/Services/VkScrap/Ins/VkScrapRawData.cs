using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace irisdropwebservice.Services.VkScrap.Ins
{
    [Table("VkScrapGeneration")]
    public class VkScrapGenerationDto
    {
        public long Id { get; set; }

        public DateTime ScrapStartDateTime { get; set; }
        public int? ScrapInfoFetchingDurationMs { get; set; }
        public DateTime? LastFetch { get; set; }

        public bool IsComplete { get; set; }

        // public virtual ICollection<VkScrapItem> ScrapItems { get; set; }
    }

    [JsonDerivedType(typeof(VkScrapItemQtyStrXml), "Str")]
    [JsonDerivedType(typeof(VkScrapItemQtyUnitXml), "Unit")]
    public interface IScrapItemQtyXml
    {
    }

    public class VkScrapItemQtyStrXml : IScrapItemQtyXml
    {
        public string NonSizeArbitraryText { get; set; }
    }

    public class VkScrapItemQtyUnitXml : IScrapItemQtyXml
    {
        // Either variants are valid on a product, not both:
        //  - Height
        //  - Age and Size are 1:1 options
        //  - Size where only 'XLMS' is allowed
        public string Size_Size { get; set; }
        public string Size_Age { get; set; }
        public string Size_Height { get; set; }
        public string Size_TheRestOfString { get; set; }

        public short Quantity { get; set; }
        public int? SpecialPrice { get; set; }
    }

    [Table("VkScrapItem")]
    public class VkScrapItemDto
    {
        public long Id { get; set; }

        [JsonIgnore]
        public long ScrapGeneration { get; set; }

        // Scrap
        public string MaxPhotoSizeUri { get; set; }
        public string Text { get; set; }

        public long VkGroupId { get; set; }
        public long VkAlbumId { get; set; }
        public long VkPhotoId { get; set; }

        public DateTime VkPhotoCreationDate { get; set; }
        public int OrderInAlbum { get; set; }

        // Parsing
        public ParsingResult ParsingResult { get; set; }

        public string AlbumTitle { get; set; }
        public string PhotoTitle { get; set; }

        [MaxLength(16)]
        public string Art { get; set; }
        public string Description { get; set; }
        public int? PriceNew { get; set; }
        public int? PriceOld { get; set; }

        [MaxLength(50)]
        public string FullTextHash { get; set; }

        public string ParsingError { get; set; }

        [JsonIgnore]
        public byte[] QtysXmlBlock { get; set; }
        public IScrapItemQtyXml[] QtyObjects { get; set; }
    }

    [DataContract(Name = "VSALAT")] // LMAO
    public enum VkScrapArtLogActionType : byte
    {
        [EnumMember(Value = "-")]
        None = 0,

        [EnumMember(Value = "N")]
        New = 10,

        [EnumMember(Value = "C")]
        AvailabilityChange = 30,

        [EnumMember(Value = "D")]
        DeletionOrDifPhoto = 40,

        [EnumMember(Value = "R")]
        ParsingError = 50,

        [EnumMember(Value = "L")]
        AlbumChange = 60,

        [EnumMember(Value = "O")]
        NoArt = 70,

        [EnumMember(Value = "G")]
        NewNoArt = 80
    }

    [DataContract(Name = "VSALDA")]
    public struct VkScrapArtLogDataAction
    {
        [DataMember(Name = "AT")]
        public VkScrapArtLogActionType ActionType { get; set; }

        [DataMember(Name = "K")]
        public string Key { get; set; }

        [DataMember(Name = "OV")]
        public string OldValue { get; set; }

        [DataMember(Name = "NW")]
        public string NewValue { get; set; }

        public static VkScrapArtLogDataAction DeletionOrDifPhoto()
        {
            return new VkScrapArtLogDataAction() { ActionType = VkScrapArtLogActionType.DeletionOrDifPhoto };
        }

        public static VkScrapArtLogDataAction NewNoArt()
        {
            return new VkScrapArtLogDataAction() { ActionType = VkScrapArtLogActionType.NewNoArt };
        }

        public static VkScrapArtLogDataAction NoArt()
        {
            return new VkScrapArtLogDataAction() { ActionType = VkScrapArtLogActionType.NoArt };
        }

        public static VkScrapArtLogDataAction ParsingError()
        {
            return new VkScrapArtLogDataAction() { ActionType = VkScrapArtLogActionType.ParsingError };
        }

        public static VkScrapArtLogDataAction AlbumChange()
        {
            return new VkScrapArtLogDataAction() { ActionType = VkScrapArtLogActionType.AlbumChange };
        }

        public static VkScrapArtLogDataAction New(string key, string newValue)
        {
            if (key == null || newValue == null)
                throw new ArgumentNullException();

            return new VkScrapArtLogDataAction()
            {
                ActionType = VkScrapArtLogActionType.New,
                Key = key,
                NewValue = newValue
            };
        }

        public static VkScrapArtLogDataAction AvailabilityChange(string key, string oldValue, string newValue)
        {
            if (key == null || oldValue == null || newValue == null)
                throw new ArgumentNullException();

            return new VkScrapArtLogDataAction()
            {
                ActionType = VkScrapArtLogActionType.AvailabilityChange,
                Key = key,
                OldValue = oldValue,
                NewValue = newValue
            };
        }
    }

    [DataContract(Name = "VSALDAG")]
    public class VkScrapArtLogDataActionGroup
    {
        [DataMember(Name = "ATRFU")]
        public DateTime ActionSetTimeRangeFromUtc { get; set; }

        [DataMember(Name = "ASTRTU")]
        public DateTime ActionSetTimeRangeToUtc { get; set; }

        // NOTE (!) this is only for informational purposes. This generation may be long deleted.
        [DataMember(Name = "GI")]
        public long GenerationId { get; set; }

        [DataMember(Name = "A")]
        public List<VkScrapArtLogDataAction> Actions { get; set; }

        [DataMember(Name = "IRM")]
        public bool IsAResultOfMerger { get; set; }
    }

    [DataContract(Name = "VSALD")]
    public class VkScrapArtLogData
    {
        [DataMember(Name = "AG")]
        public List<VkScrapArtLogDataActionGroup> ActionsGroups { get; set; }
    }

    [Table("VkScrapArtLog")]
    public class VkScrapArtLogDto
    {
        public const int DATA_XML_BLOCK_MAX_LENGTH = 7000;

        [MaxLength(64)]
        public string Art { get; set; }
        public bool IsArtless { get; set; }

        [JsonIgnore]
        public byte[] DataXmlBlock { get; set; }
        [JsonIgnore]
        public byte[] DataXmlBlock2 { get; set; }
        
        public VkScrapArtLogData Data { get; set; }
    }

    [KnownType(typeof(VkScrapItemQtyStrXml))]
    [KnownType(typeof(VkScrapItemQtyUnitXml))]
    public class QtyObjectsSer
    {
        public IScrapItemQtyXml[] QtyObjects { get; set; }
    }
}