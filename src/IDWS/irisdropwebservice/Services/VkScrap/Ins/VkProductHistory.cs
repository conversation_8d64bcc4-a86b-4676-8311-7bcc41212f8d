using Invictus.Nomenklatura.Misc;
using irisdropwebservice.Services.Chats;


using System.Text;
using System.Threading.Tasks;

using Invictus.Nomenklatura.App;

using irisdropwebservice.AppConfig.ClassConfigurations;
using irisdropwebservice.AppConfig;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.AppConfig;
using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.Legacy;
using irisdropwebservice.Services.LinkService;
using irisdropwebservice.Services.Statistics;
using irisdropwebservice.Services.Statistics.Ins;
using irisdropwebservice.Views.Statistics;

using OneOf;

using Telegram.Bot.Types;

namespace irisdropwebservice.Services.VkScrap.Ins
{
    public interface IVkProductHistoryPublic
    {
        string[] GetMostSoldArts(int n);
    }

    public interface IVkProductHistory : IVkProductHistoryPublic
    {
        void Initialize();
    }

    public class VkProductHistory : ITelegramDialogue<PERSON><PERSON><PERSON>, ITelegramGroupListener, IVkProductHistory
    {
        private readonly ILogger _logger = InvLog.Logger<VkProductHistory>();

        private readonly LeBackgroundTasks _threadedTasks;
        private readonly IVkScrapServiceWebApiCallerInternal _webApiCaller;
        private readonly IVkScrapAndParseWorkerHelper _scrapAndParseWorkerHelper;
        private readonly IDbAccessFactory<VkScrapDbAccess> _vkScrapDbAccessFactory;
        private readonly IStatisticsService _statisticsService;
        private readonly RazorTemplateRender _razorTemplateRender;
        private readonly ITelegramSendWorker _telegramSendWorker;
        private readonly InvEnvEnum _envEnum;

        private readonly VkScrapCCInfo _configuration = CCAppConfig.VkScrapIrisDrop;

        public VkProductHistory(
            LeBackgroundTasks threadedTasks,
            IVkScrapServiceWebApiCallerInternal webApiCaller,
            TelegramChatBotPolling telegramChatBotPolling,
            IDbAccessFactory<VkScrapDbAccess> vkScrapDbAccessFactory,
            IStatisticsService statisticsService,
            RazorTemplateRender razorTemplateRender,
            ITelegramSendWorker telegramSendWorker,
            IVkScrapAndParseWorkerHelper scrapAndParseWorkerHelper,
            InvEnv invEnv
        )
        {
            _threadedTasks = threadedTasks;
            _webApiCaller = webApiCaller;
            _vkScrapDbAccessFactory = vkScrapDbAccessFactory;
            _statisticsService = statisticsService;
            _razorTemplateRender = razorTemplateRender;
            _telegramSendWorker = telegramSendWorker;
            _scrapAndParseWorkerHelper = scrapAndParseWorkerHelper;
            _envEnum = invEnv.EnvEnum;

            if (_configuration.IsScrapItemsProducer)
            {
                threadedTasks.AddFixedTimeOfDayBackgroundTask(
                    this.MergeOldScrapItemLogEntriesOnBackgroundThread,
                    nameof(VkScrapExteriorService) + "::" + nameof(this.MergeOldScrapItemLogEntriesOnBackgroundThread),
                    TimeSpan.FromHours(2.66)
                );
            }

            telegramChatBotPolling.AddDialogueHandler(this);
            telegramChatBotPolling.AddGroupListener(this);
        }

        public void Initialize()
        {
        }

        public record ScrapSessionInfo(VkScrapItem[] PreviousScrapData, VkScrapGeneration PreviousScrapGeneration, DateTime ScrapStartTimeUtc, long CurrentScrappingGeneration);

        public class ScrapSession
        {
            private readonly record struct QtyUnitMatch(string SizeKey, short? OldValue, short? NewValue);

            private readonly VkScrapCCInfo _configuration = CCAppConfig.VkScrapIrisDrop;

            private readonly Dictionary<string, VkScrapArtLogItem> _logItemsDictionary;
            private readonly List<VkScrapArtLogItem> _dbLogItems;
            private readonly List<VkScrapArtLogItem> _newLogItems;
            private readonly List<VkScrapArtLogItem> _deleteLogItems;

            // Since VkScrapItems can be changed during general parsing errors detection, we postpone all log operations to after general parsing errors detection.
            private readonly List<VkScrapItem> _toLogNewPhotoesLater = new();
            private readonly List<VkScrapItem> _toLogDeletedPhotoesLater = new();
            private readonly List<VkScrapItem> _toLogAlbumsChangedLater = new();
            private readonly List<VkScrapItem> _toLogMaybeChangedPhotoesLater = new();

            private readonly StringBuilder _reusableStringBuilder = new();

            private readonly IDbAccessFactory<VkScrapDbAccess> _databaseAccessFactory;
            private readonly ScrapSessionInfo _scrapSessionInfo;

            public ScrapSession(IDbAccessFactory<VkScrapDbAccess> databaseAccessFactory, ScrapSessionInfo scrapSessionInfo)
            {
                _databaseAccessFactory = databaseAccessFactory;
                _scrapSessionInfo = scrapSessionInfo;

                _dbLogItems = this.GetPreviousLogItems().ToList();

                if (_dbLogItems.DistinctBy(logItem => logItem.Art).Count() != _dbLogItems.Count)
                    throw new Exception("There are duplicate log items.");

                _logItemsDictionary = _dbLogItems.ToDictionary(logItem => logItem.Art);
                _newLogItems = new List<VkScrapArtLogItem>();
                _deleteLogItems = new List<VkScrapArtLogItem>();
            }

            private VkScrapArtLogItem[] GetPreviousLogItems()
            {
                using VkScrapDbAccess dbAccess = _databaseAccessFactory.CreateAccess();

                return dbAccess.GetAllScrapLogItems();
            }

            public void CommitLogItemsAndSaveToDb(VkScrapDbAccess dbAccess)
            {
                foreach (VkScrapItem vkScrapItem in _toLogNewPhotoesLater)
                    this.LogAddNewPhoto(vkScrapItem);

                foreach (VkScrapItem vkScrapItem in _toLogDeletedPhotoesLater)
                    this.LogDeleted(vkScrapItem);

                foreach (VkScrapItem vkScrapItem in _toLogAlbumsChangedLater)
                    this.LogAlbumChanged(vkScrapItem);

                foreach (VkScrapItem vkScrapItem in _toLogMaybeChangedPhotoesLater)
                    this.LogChangedPhotoDescription(vkScrapItem);

                foreach (VkScrapArtLogItem vkScrapArtLogItem in _dbLogItems)
                    dbAccess.UpdateScrapLogItem(vkScrapArtLogItem);

                foreach (VkScrapArtLogItem scrapArtLogItem in _newLogItems)
                    dbAccess.AddScrapLogItem(scrapArtLogItem);

                foreach (VkScrapArtLogItem scrapArtLogItem in _deleteLogItems)
                    dbAccess.DeleteScrapLogItem(scrapArtLogItem);
            }

            public void LogLaterAddNewPhoto(VkScrapItem scrapItem)
            {
                _toLogNewPhotoesLater.Add(scrapItem);
            }

            private void TryAttachPreviousArtlessActionItems(VkScrapItem scrapItem)
            {
                VkScrapArtLogItem logItem = this.GetFromDbOrAddNoDbLogItem(scrapItem.Art);

                foreach (VkScrapArtLogItem vkScrapArtLogItemArtless in _dbLogItems.Where(logItem => logItem.IsArtless))
                {
                    if (vkScrapArtLogItemArtless.Art == scrapItem.FullId)
                    {
                        logItem.Data.ActionsGroups.AddRange(vkScrapArtLogItemArtless.Data.ActionsGroups);
                        _deleteLogItems.Add(vkScrapArtLogItemArtless);
                    }
                }
            }

            private void LogAddNewPhoto(VkScrapItem scrapItem)
            {
                if (scrapItem.ParsingResult == ParsingResult.NotApplicable_LikelyNotAProduct)
                    return;

                List<VkScrapArtLogDataAction> logActionGroupList;

                if (string.IsNullOrWhiteSpace(scrapItem.Art))
                {
                    logActionGroupList = this.GetOrCreateArtlessActionGroupActionsList(scrapItem);

                    logActionGroupList.Add(VkScrapArtLogDataAction.NewNoArt());

                    return;
                }

                this.TryAttachPreviousArtlessActionItems(scrapItem);

                logActionGroupList = this.GetOrCreateActionGroupActionsList(scrapItem);

                if (scrapItem.ParsingResult != ParsingResult.Success)
                {
                    logActionGroupList.Add(VkScrapArtLogDataAction.ParsingError());

                    return;
                }

                this.LogAddNewPhotoCore(scrapItem, logActionGroupList);
            }

            private void LogAddNewPhotoCore(VkScrapItem scrapItem, List<VkScrapArtLogDataAction> logActionGroupList)
            {
                foreach (VkScrapItemQtyUnit qtyUnit in scrapItem.QtyObjects.OfType<VkScrapItemQtyUnit>())
                {
                    _reusableStringBuilder.Clear();

                    qtyUnit.GetSizeAllIdentifierLocalized(_reusableStringBuilder);

                    string sizeKey = _reusableStringBuilder.ToString();

                    logActionGroupList.Add(VkScrapArtLogDataAction.New(sizeKey, qtyUnit.Quantity.ToString(CultureHandler.UkrainianCulture)));
                }
            }

            public void LogLaterDeleted(VkScrapItem scrapItem)
            {
                _toLogDeletedPhotoesLater.Add(scrapItem);
            }

            private void LogDeleted(VkScrapItem scrapItem)
            {
                if (scrapItem.ParsingResult == ParsingResult.NotApplicable_LikelyNotAProduct)
                    return;

                List<VkScrapArtLogDataAction> logActionGroupList;

                if (string.IsNullOrWhiteSpace(scrapItem.Art))
                {
                    logActionGroupList = this.GetOrCreateArtlessActionGroupActionsList(scrapItem);

                    logActionGroupList.Add(VkScrapArtLogDataAction.NoArt());
                    logActionGroupList.Add(VkScrapArtLogDataAction.DeletionOrDifPhoto());

                    return;
                }

                this.TryAttachPreviousArtlessActionItems(scrapItem);

                logActionGroupList = this.GetOrCreateActionGroupActionsList(scrapItem);
                logActionGroupList.Add(VkScrapArtLogDataAction.DeletionOrDifPhoto());
            }

            public void LogLaterAlbumChanged(VkScrapItem scrapItem)
            {
                _toLogAlbumsChangedLater.Add(scrapItem);
            }

            private void LogAlbumChanged(VkScrapItem scrapItem)
            {
                if (scrapItem.ParsingResult == ParsingResult.NotApplicable_LikelyNotAProduct)
                    return;

                List<VkScrapArtLogDataAction> logActionGroupList;

                if (string.IsNullOrWhiteSpace(scrapItem.Art))
                {
                    logActionGroupList = this.GetOrCreateArtlessActionGroupActionsList(scrapItem);

                    logActionGroupList.Add(VkScrapArtLogDataAction.NoArt());
                    logActionGroupList.Add(VkScrapArtLogDataAction.AlbumChange());

                    return;
                }

                this.TryAttachPreviousArtlessActionItems(scrapItem);

                logActionGroupList = this.GetOrCreateActionGroupActionsList(scrapItem);
                logActionGroupList.Add(VkScrapArtLogDataAction.AlbumChange());
            }

            public void LogLaterChangedPhotoDescription(VkScrapItem scrapItem)
            {
                _toLogMaybeChangedPhotoesLater.Add(scrapItem);
            }

            private void LogChangedPhotoDescription(VkScrapItem scrapItem)
            {
                if (scrapItem.ParsingResult == ParsingResult.NotApplicable_LikelyNotAProduct)
                    return;

                if (string.IsNullOrWhiteSpace(scrapItem.Art))
                {
                    List<VkScrapArtLogDataAction> logActionGroupList = this.GetOrCreateArtlessActionGroupActionsList(scrapItem);

                    logActionGroupList.Add(VkScrapArtLogDataAction.NoArt());

                    return;
                }

                this.TryAttachPreviousArtlessActionItems(scrapItem);

                Lazy<List<VkScrapArtLogDataAction>> logActionGroupListLazy = this.GetOrCreateActionGroupActionsListLazy(scrapItem);

                if (scrapItem.ParsingResult != ParsingResult.Success)
                {
                    logActionGroupListLazy.Value.Add(VkScrapArtLogDataAction.ParsingError());

                    return;
                }

                VkScrapItem[] prevScrapItemsWithTheSameArt = _scrapSessionInfo.PreviousScrapData.Where(vksi => vksi.Art == scrapItem.Art).ToArray();

                VkScrapArtLogItem logItem = this.GetFromDbOrAddNoDbLogItem(scrapItem.Art);

                bool logItemContainsNewLogDataAction = logItem.Data.ActionsGroups
                    .SelectMany(g => g.Actions)
                    .Any(logAction => logAction.ActionType == VkScrapArtLogActionType.New);

                bool noPrevScrapItemOrTheyHaveParsingErrors =
                    prevScrapItemsWithTheSameArt.Length == 0
                    ||
                    prevScrapItemsWithTheSameArt.Any(vksi => vksi.ParsingResult != ParsingResult.Success);

                bool logAddNewPhotoCoreNext = false;

                if (noPrevScrapItemOrTheyHaveParsingErrors || !logItemContainsNewLogDataAction)
                {
                    if (noPrevScrapItemOrTheyHaveParsingErrors)
                    {
                        this.LogAddNewPhotoCore(scrapItem, logActionGroupListLazy.Value);

                        return;
                    }

                    // Do not return if this is just an old item that was not in logs system yet - show quantity change
                    logAddNewPhotoCoreNext = true;
                }

                if (prevScrapItemsWithTheSameArt.Length != 1)
                    throw new Exception("prevScrapItemsWithTheSameArt.Length != 1");

                VkScrapItem prevScrapItem = prevScrapItemsWithTheSameArt[0];

                IEnumerable<QtyUnitMatch> qtyMatches = prevScrapItem.QtyObjects.OfType<VkScrapItemQtyUnit>()
                    .FullOuterJoinSingle(
                        scrapItem.QtyObjects.OfType<VkScrapItemQtyUnit>(),
                        qtyUnit => {
                            _reusableStringBuilder.Clear();

                            qtyUnit.GetSizeAllIdentifierLocalized(_reusableStringBuilder);

                            return _reusableStringBuilder.ToString();
                        },
                        qtyUnit => {
                            _reusableStringBuilder.Clear();

                            qtyUnit.GetSizeAllIdentifierLocalized(_reusableStringBuilder);

                            return _reusableStringBuilder.ToString();
                        },
                        (prev, next, key) => new QtyUnitMatch(key, prev?.Quantity, next?.Quantity)
                    );

                foreach (QtyUnitMatch qtyUnitMatch in qtyMatches)
                {
                    if (qtyUnitMatch.OldValue == qtyUnitMatch.NewValue)
                        continue;

                    logActionGroupListLazy.Value.Add(VkScrapArtLogDataAction.AvailabilityChange(qtyUnitMatch.SizeKey,
                            qtyUnitMatch.OldValue?.ToString(CultureHandler.UkrainianCulture) ?? "?",
                            qtyUnitMatch.NewValue?.ToString(CultureHandler.UkrainianCulture) ?? "?"
                        )
                    );
                }

                if (logAddNewPhotoCoreNext)
                {
                    this.LogAddNewPhotoCore(scrapItem, logActionGroupListLazy.Value);
                } else
                {
                    this.AddTotalQuantitiesIfNeeded(scrapItem, logItem);
                }
            }

            private void AddTotalQuantitiesIfNeeded(VkScrapItem scrapItem, VkScrapArtLogItem logItem)
            {
                DateTime cutoffDate = ServerClock.GetCurrentUtcTime().AddDays(-_configuration.AddTotalQuantitiesToScrapLogAfter_Days).DateTime;

                bool logAddNewPhoto = logItem.Data.ActionsGroups
                    .SelectMany(ag => ag.Actions.Select(action => (action, ag)))
                    .Where(couple => couple.action.ActionType == VkScrapArtLogActionType.New)
                    .All(couple => couple.ag.ActionSetTimeRangeToUtc < cutoffDate);

                if (!logAddNewPhoto)
                {
                    VkScrapArtLogDataActionGroup prevScrapGroup = this.GetPreviousActionGroup(logItem);

                    if (prevScrapGroup != null && prevScrapGroup.Actions.Any(action => action.ActionType == VkScrapArtLogActionType.DeletionOrDifPhoto))
                        logAddNewPhoto = true;
                }

                if (!logAddNewPhoto)
                    return;

                this.LogAddNewPhotoCore(scrapItem, this.GetOrCreateActionGroup(logItem).Actions);
            }

            private VkScrapArtLogDataActionGroup GetPreviousActionGroup(VkScrapArtLogItem logItem)
            {
                return logItem.Data.ActionsGroups.LastOrDefault(group => group.GenerationId != _scrapSessionInfo.CurrentScrappingGeneration);
            }

            private VkScrapArtLogDataActionGroup GetOrCreateActionGroup(VkScrapArtLogItem logItem)
            {
                VkScrapArtLogDataActionGroup logActionGroup = logItem.Data.ActionsGroups.SingleOrDefault(group => group.GenerationId == _scrapSessionInfo.CurrentScrappingGeneration);

                if (logActionGroup == null)
                {
                    logActionGroup = new VkScrapArtLogDataActionGroup()
                    {
                        GenerationId = _scrapSessionInfo.CurrentScrappingGeneration,
                        Actions = new List<VkScrapArtLogDataAction>(1),
                        ActionSetTimeRangeFromUtc = _scrapSessionInfo.PreviousScrapGeneration.ScrapStartDateTime + (_scrapSessionInfo.PreviousScrapGeneration.ScrapInfoFetchingDurationMs ?? TimeSpan.FromMinutes(0)),
                        ActionSetTimeRangeToUtc = _scrapSessionInfo.ScrapStartTimeUtc
                    };

                    logItem.Data.ActionsGroups.Add(logActionGroup);
                }

                return logActionGroup;
            }

            private VkScrapArtLogItem GetFromDbOrAddNoDbLogItem(string artKey)
            {
                if (string.IsNullOrWhiteSpace(artKey))
                    throw new ArgumentException("art");

                if (!_logItemsDictionary.TryGetValue(artKey, out VkScrapArtLogItem res))
                {
                    var newLogItem = new VkScrapArtLogItem();
                    newLogItem.Art = artKey;
                    _newLogItems.Add(newLogItem);
                    _logItemsDictionary[artKey] = newLogItem;
                    res = newLogItem;
                }

                return res;
            }

            private List<VkScrapArtLogDataAction> GetOrCreateActionGroupActionsList(VkScrapItem scrapItem)
            {
                VkScrapArtLogItem logItem = this.GetFromDbOrAddNoDbLogItem(scrapItem.Art);
                VkScrapArtLogDataActionGroup actionGroup = this.GetOrCreateActionGroup(logItem);

                return actionGroup.Actions;
            }

            private List<VkScrapArtLogDataAction> GetOrCreateArtlessActionGroupActionsList(VkScrapItem scrapItem)
            {
                VkScrapArtLogItem logItem = this.GetFromDbOrAddNoDbLogItem(scrapItem.FullId);
                VkScrapArtLogDataActionGroup actionGroup = this.GetOrCreateActionGroup(logItem);

                return actionGroup.Actions;
            }

            private Lazy<List<VkScrapArtLogDataAction>> GetOrCreateActionGroupActionsListLazy(VkScrapItem scrapItem)
            {
                return new Lazy<List<VkScrapArtLogDataAction>>(() => this.GetOrCreateActionGroupActionsList(scrapItem));
            }
        }

        void ITelegramGroupListener.IncomingMessage(TelegramBotWho who, Update update)
        {
            if (who != TelegramBotWho.Staff)
                return;

            if (update.Message == null || update.Message.Text == null)
                return;

            switch (_envEnum)
            {
                case InvEnvEnum.Dev:
                    if (update.Message.Chat.Id != ((TelegramChatId)CCAppConfig.TelegramIrisDrop.DevChat).ChatId)
                        return;
                    break;
                case InvEnvEnum.ProdServ:
                    if (update.Message.Chat.Id != ((TelegramChatId)CCAppConfig.TelegramIrisDrop.ShopAdminAttentionChat).ChatId)
                        return;
                    break;
            }

            string messageText = update.Message.Text.Trim();

            if (messageText != "/hist" && !messageText.StartsWith("/hist@"))
                return;

            DateTime processingTimeUtc = ServerClock.GetCurrentUtcTime().DateTime;

            (DateTime lastFlushTime, string lastFlushArg) = _statisticsService.PublicFlushContext.GetLastFlushTimeAndArgumentUtc(OperationType.StatFlushStatsVkAvaliReport);
            long lastGenerationId;

            string avaliReportText = this.GetVkAvaliReportText(lastFlushTime, lastFlushArg, out lastGenerationId);

            if (avaliReportText == null)
                return;

            Task postToTelegramTask1 = _telegramSendWorker.Run(w => w.PostToTelegramFromStaffAccount(avaliReportText, TelegramChatId.FromUnknownChatNameOrUser(update.Message.Chat.Id), null));

            postToTelegramTask1.ContinueWithShortThread(_ => {
                    _statisticsService.PublicFlushContext.InsertStatisticsWasFlushed(OperationType.StatFlushStatsVkAvaliReport, processingTimeUtc, lastGenerationId.ToString());
                },
                TaskContinuationOptions.NotOnCanceled & TaskContinuationOptions.NotOnFaulted
            );
        }

        private string GetVkAvaliReportText(DateTime lastFlushTime, string lastFlushArg, out long lastGenerationId)
        {
            lastGenerationId = _webApiCaller.GetRemoteLastCompleteScrapGeneration();

            if (lastGenerationId < 20)
                return null;

            VkScrapArtLogItem[] logItems = _webApiCaller.GetAllVkProductsChangelog().Select(dto => new VkScrapArtLogItem(dto)).ToArray();
            VkScrapItem[] lastScrapItems = _webApiCaller.GetRemoteScrapResult(lastGenerationId, ScrapReadMode.Full)?.Items;

            if (lastScrapItems == null)
                return null;

            long lastFlushedGenerationId;

            if (lastFlushTime == DateTime.MinValue || !long.TryParse(lastFlushArg, out lastFlushedGenerationId))
            {
                return "Звіт наявності: почекайте кілька годин.";
            }

            if (lastFlushedGenerationId == lastGenerationId)
            {
                return "Звіт наявності: успішних сканувань не відбувалось у цей проміжок часу.";
            }

            (VkScrapArtLogItem logItem, VkScrapArtLogDataActionGroup group)[] selectManyUnfiltered =
                logItems
                    .Where(logItem => !logItem.IsArtless)
                    .SelectMany(logItem => logItem.Data.ActionsGroups.Select(group => (logItem, group)))
                    .ToArray();

            long lastGenerationIdLocal = lastGenerationId;

            (VkScrapArtLogItem logItem, List<VkScrapArtLogDataActionGroup> relevantGroups)[] logItemsActionGroupsInRange =
                selectManyUnfiltered
                    .Where(couple => couple.group.GenerationId > lastFlushedGenerationId && couple.group.GenerationId <= lastGenerationIdLocal) // Pick only next generations.
                    .GroupBy(couple => couple.logItem)
                    .Select(group => (group.Key, group.Select(grp => grp.group).ToList()))
                    .ToArray();

            if (logItemsActionGroupsInRange.Length == 0)
            {
                return "Звіт наявності: змін у наявності не помічено.";
            }

            var actionGroupsInRangeMerged = new List<VkScrapHistoryItem>();

            var bdr = new StringBuilder();

            foreach ((VkScrapArtLogItem logItem, List<VkScrapArtLogDataActionGroup> relevantGroups) in logItemsActionGroupsInRange)
            {
                // Merge all groups into one group.
                VkScrapArtLogDataActionGroup mergedActionGroup = MergeActionGroupsIntoOne(relevantGroups, false);

                // Remove all 'New' if there are any.
                mergedActionGroup.Actions = mergedActionGroup.Actions.Where(action => action.ActionType != VkScrapArtLogActionType.New).ToList();

                // Get all 'AvaliChange'
                VkScrapArtLogDataAction[] nonAvaliChangeActions = mergedActionGroup.Actions.Where(action => action.ActionType != VkScrapArtLogActionType.AvailabilityChange).ToArray();
                VkScrapArtLogDataAction[] avaliChangeActions = mergedActionGroup.Actions.Where(action => action.ActionType == VkScrapArtLogActionType.AvailabilityChange).ToArray();

                if (avaliChangeActions.Length == 0 && nonAvaliChangeActions.Length == 0)
                    continue;

                // Re-add 'New's if needed
                VkScrapItem scrapItem = lastScrapItems.FirstOrDefault(vksi => vksi.Art == logItem.Art);

                if (scrapItem != null)
                {
                    foreach (VkScrapItemQtyUnit qtyUnit in scrapItem.QtyObjects.OfType<VkScrapItemQtyUnit>())
                    {
                        bdr.Clear();

                        qtyUnit.GetSizeAllIdentifierLocalized(bdr);

                        string sizeKey = bdr.ToString();

                        VkScrapArtLogDataAction thisSizeAvaliChangeAction = avaliChangeActions.FirstOrDefault(action => action.Key == sizeKey);

                        if (thisSizeAvaliChangeAction.ActionType != VkScrapArtLogActionType.None)
                        {
                            if (thisSizeAvaliChangeAction.NewValue != qtyUnit.Quantity.ToString())
                            {
                                // Shit happens. But why?
                                mergedActionGroup.Actions.Remove(thisSizeAvaliChangeAction);

                                var newAction = VkScrapArtLogDataAction.New(sizeKey, "?");
                                mergedActionGroup.Actions.Add(newAction);
                            }
                        }
                        else
                        {
                            var newAction = VkScrapArtLogDataAction.New(sizeKey, qtyUnit.Quantity.ToString());
                            mergedActionGroup.Actions.Add(newAction);
                        }
                    }
                }

                // Re-sort
                mergedActionGroup.Actions = mergedActionGroup.Actions
                    .OrderBy(
                        action =>
                            action.ActionType switch
                            {
                                VkScrapArtLogActionType.NoArt => "_" + 1,
                                VkScrapArtLogActionType.NewNoArt => "_" + 2,
                                VkScrapArtLogActionType.ParsingError => "_" + 3,
                                VkScrapArtLogActionType.DeletionOrDifPhoto => "_" + 4,
                                VkScrapArtLogActionType.AlbumChange => "_" + 5,
                                VkScrapArtLogActionType.New => action.Key,
                                VkScrapArtLogActionType.AvailabilityChange => action.Key,

                                VkScrapArtLogActionType.None => throw TypeAbominationException.Enum(typeof(VkScrapArtLogActionType), action.ActionType),
                                _ => throw TypeAbominationException.Enum(typeof(VkScrapArtLogActionType), action.ActionType)
                            }
                    ).ToList();

                bdr.Clear();
                var historyPrinter = new HistoryPrinter(bdr, false);

                historyPrinter.GetProductHistoryTextForOneDayOfActionGroups(
                    new[] { mergedActionGroup }.GroupBy(group => group.ActionSetTimeRangeFromUtc.Date).First(),
                    null
                );

                var modelHistoryItem = new VkScrapHistoryItem()
                {
                    LogItem = logItem,
                    HistoryString = bdr.ToString()
                };

                if (scrapItem != null)
                    modelHistoryItem.VkLink = VkNamesAndUrls.GetVkComGroupPhotoUri(scrapItem.FullId);

                actionGroupsInRangeMerged.Add(modelHistoryItem);
            }

            DateTime beforeRelevantGenerationTimeToUtc =
                selectManyUnfiltered
                    .Where(couple => couple.group.GenerationId <= lastFlushedGenerationId)
                    .Max(couple => couple.group.ActionSetTimeRangeToUtc)
                + TimeSpan.FromMinutes(1);

            var model = new VkScrapAvaliReportModel()
            {
                FromLocal = beforeRelevantGenerationTimeToUtc.UtcToUkraineTime(),
                ToLocal = logItemsActionGroupsInRange.Max(couple => couple.relevantGroups.Max(gg => gg.ActionSetTimeRangeToUtc)).UtcToUkraineTime(),
                LogItems = actionGroupsInRangeMerged.ToArray()
            };

            return _razorTemplateRender.Render("Statistics/VkScrapAvaliReport", model, bdr);
        }

        TelegramDialogueUpdateHandler ITelegramDialogueHandler.CanHandleConversationInitiation(TelegramBotWho whichBot, Update request)
        {
            if (whichBot != TelegramBotWho.Staff)
                return null;

            if (request.Message == null || request.Message.Text == null)
                return null;

            string text = request.Message.Text.Trim();

            int commandType = 0;
            if (text.StartsWithEither(StringComparison.InvariantCultureIgnoreCase, "артлог", "artlog"))
                commandType = 1;
            else if (text.StartsWithEither(StringComparison.InvariantCultureIgnoreCase, "старарт", "oldart"))
                commandType = 2;
            else if (text.StartsWithEither(StringComparison.InvariantCultureIgnoreCase, "mostsold", "mostpopular"))
                commandType = 3;
            else
                return null;

            return (dialogue, update) =>
            {
                Task<string> task = null;

                switch (commandType)
                {
                    case 1:
                        task = Task.Run(() => this.GetProductHistoryText(update.Message.Text));
                        break;
                    case 2:
                        task = Task.Run(() => this.GetOldArtsList());
                        break;
                    case 3:
                        task = Task.Run(() => this.GetMostSoldProductsLastWeek());
                        break;
                }

                dialogue.FinishAfterTask(task, new PostToTelegramOptions() { DisableWebPagePreview = true, Silent = false });
            };
        }

        private class VkScrapArtLogDtoByDateComparer : IComparer<VkScrapArtLogDto>
        {
            public static readonly VkScrapArtLogDtoByDateComparer Instance = new();

            public int Compare(VkScrapArtLogDto x, VkScrapArtLogDto y)
            {
                if (ReferenceEquals(x, y))
                    return 0;

                if (ReferenceEquals(null, y))
                    return 1;

                if (ReferenceEquals(null, x))
                    return -1;

                return DateTime.Compare(
                    (
                        x.Data.ActionsGroups
                            .Where(grp => grp.Actions.Any(a => a.ActionType != VkScrapArtLogActionType.New))
                            .MaxBy(grp => grp.ActionSetTimeRangeToUtc)
                            ?.ActionSetTimeRangeToUtc ?? DateTime.MaxValue
                    )
                    .Date,
                    (
                        y.Data.ActionsGroups
                            .Where(grp => grp.Actions.Any(a => a.ActionType != VkScrapArtLogActionType.New))
                            .MaxBy(grp => grp.ActionSetTimeRangeToUtc)
                            ?.ActionSetTimeRangeToUtc ?? DateTime.MaxValue
                    )
                    .Date
                );
            }
        }

        private string GetOldArtsList()
        {
            const int MAX = 9000;

            VkScrapItem[] lastScrapItems = _webApiCaller.GetRemoteScrapResult(_webApiCaller.GetRemoteLastCompleteScrapGeneration(), ScrapReadMode.Full).Items;

            bool isInStock(string art)
            {
                VkScrapItem scrapItem = lastScrapItems.FirstOrDefault(item => item.Art == art);

                return scrapItem != null && scrapItem.QtyObjects.OfType<VkScrapItemQtyUnit>().Any(qty => qty.Quantity > 0);
            }

            VkScrapArtLogDto[] allLogItems = _webApiCaller.GetAllVkProductsChangelog();
            var res = new List<OneOf<VkScrapArtLogDto, int>>();

            for (int i = 1; i < 100; i++)
            {
                bool artIsAbsent = allLogItems.FirstOrDefault(logItem => LinkedNaming.GetVkComComparableArt(logItem.Art) == i) == null;

                if (artIsAbsent)
                {
                    res.Add(i);

                    if (res.Count >= MAX)
                        break;
                }
            }

            if (res.Count < MAX)
            {
                VkScrapArtLogDto[] oldestArts = allLogItems
                    .OrderBy(logItem => logItem, VkScrapArtLogDtoByDateComparer.Instance)
                    // .ThenBy(logItem => LinkedNaming.GetVkComComparableArt(logItem.Art))
                    .ToArray();

                foreach (VkScrapArtLogDto vkScrapArtLogItem in oldestArts)
                {
                    if (isInStock(vkScrapArtLogItem.Art))
                        continue;

                    res.Add(vkScrapArtLogItem);

                    if (res.Count >= MAX)
                        break;
                }
            }

            const string ART_ADD = ".1";

            var bdr = new StringBuilder();

            foreach (OneOf<VkScrapArtLogDto, int> oneOf in res)
            {
                if (oneOf.IsT0)
                {
                    VkScrapArtLogDto vkScrapArtLogItem = oneOf.AsT0;

                    bdr.Append(vkScrapArtLogItem.Art);
                    bdr.Append(ART_ADD);
                    bdr.Append(" @ ");

                    DateTime maxTime = vkScrapArtLogItem.Data.ActionsGroups
                        .Where(grp => grp.Actions.Any(a => a.ActionType != VkScrapArtLogActionType.New))
                        .MaxBy(grp => grp.ActionSetTimeRangeToUtc).ActionSetTimeRangeFromUtc;

                    bdr.AppendLine(maxTime.UtcToUkraineTime().ToStringUADate());

                    continue;
                }

                bdr.Append(oneOf.AsT1);
                bdr.Append(ART_ADD);
                bdr.AppendLine(" - ");
            }

            return bdr.ToString();
        }

        public string[] GetMostSoldArts(int n)
        {
            Dictionary<VkScrapArtLogDto, int> mostSold = this.GetThisWeekMostSoldScrapItems();

            var res = new List<string>();

            foreach (KeyValuePair<VkScrapArtLogDto, int> kv in mostSold.OrderByDescending(d => d.Value).Take(n))
            {
                res.Add(kv.Key.Art);
            }

            return res.ToArray();
        }

        private string GetMostSoldProductsLastWeek()
        {
            Dictionary<VkScrapArtLogDto, int> mostSold = this.GetThisWeekMostSoldScrapItems();

            var bdr = new StringBuilder();

            foreach (KeyValuePair<VkScrapArtLogDto, int> kv in mostSold.OrderByDescending(d => d.Value).Take(10))
            {
                bdr.AppendLine(kv.Key.Art + " (" + kv.Value + ")");
            }

            return bdr.ToString();
        }

        private Dictionary<VkScrapArtLogDto, int> GetThisWeekMostSoldScrapItems()
        {
            VkScrapArtLogDto[] allLogItems = _webApiCaller.GetAllVkProductsChangelog();

            var mostSold = new Dictionary<VkScrapArtLogDto, int>();
            DateTime cutoffUtc = DateTime.UtcNow.AddDays(-7);

            foreach (VkScrapArtLogDto vkScrapArtLogDto in allLogItems)
            {
                mostSold[vkScrapArtLogDto] = 0;

                VkScrapArtLogDataActionGroup[] relevantActionGroups = vkScrapArtLogDto.Data.ActionsGroups
                    .Where(ag => ag.ActionSetTimeRangeFromUtc > cutoffUtc)
                    .ToArray();

                foreach (VkScrapArtLogDataAction vkScrapArtLogDataAction in relevantActionGroups
                             .SelectMany(ag => ag.Actions)
                             .Where(action => action.ActionType == VkScrapArtLogActionType.AvailabilityChange))
                {
                    int oldValue;

                    if (!int.TryParse(vkScrapArtLogDataAction.OldValue, out oldValue))
                        continue;

                    int newValue;

                    if (!int.TryParse(vkScrapArtLogDataAction.NewValue, out newValue))
                        continue;

                    if (newValue > oldValue)
                        continue;

                    mostSold[vkScrapArtLogDto] += oldValue - newValue;
                }
            }

            return mostSold;
        }

        private string GetProductHistoryText(string commandText)
        {
            string[] spl = IntStringUtil.SplitCommand(commandText);

            string reference = spl[1];
            string size = spl.Length > 2 ? spl[2] : null;

            return this.GetProductHistoryText(reference, size);
        }

        private string GetProductHistoryText(string reference, string size)
        {
            Task<VkScrapArtLogDto> logItemTask = Task.Run(() => _webApiCaller.GetVkProductChangelog(reference));

            string availabilityChangeFilterByKey = null;

            var bdr = new StringBuilder();

            if (size != null)
            {
                VkScrapResult scrapResult = _webApiCaller.GetAnyLastScrapResult(ScrapReadMode.Full);

                bool sizeExists = scrapResult.Items
                    .SelectMany(scrapItem => scrapItem.QtyObjects.OfType<VkScrapItemQtyUnit>())
                    .Any(qtyObject => qtyObject.Size_All.Contains(size));

                if (!sizeExists)
                {
                    return $"Розмір {size} мабудь не існує.";
                }

                VkScrapItem scrapItemToFilterSizeBy = scrapResult.Items.FirstOrDefault(vksi => vksi.Art == reference);

                if (scrapItemToFilterSizeBy == null)
                {
                    return "Даних немає. Спробуйте не вказувати розмір.";
                }

                foreach (VkScrapItemQtyUnit qtyUnit in scrapItemToFilterSizeBy.QtyObjects.OfType<VkScrapItemQtyUnit>())
                {
                    if (!qtyUnit.Size_All.Contains(size))
                        continue;

                    qtyUnit.GetSizeAllIdentifierLocalized(bdr);
                    availabilityChangeFilterByKey = bdr.ToString();

                    bdr.Clear();

                    break;
                }

                if (availabilityChangeFilterByKey == null)
                {
                    return "Даних немає. Спробуйте не вказувати розмір.";
                }
            }

            VkScrapArtLogDto logItem = logItemTask.Result;

            if (logItem.Data == null || logItem.Art == null)
            {
                return "Даних немає.";
            }

            IEnumerable<IGrouping<DateTime, VkScrapArtLogDataActionGroup>> logItemActionGroupsGroupedByDate = logItem.Data.ActionsGroups
                .OrderByDescending(group => group.ActionSetTimeRangeFromUtc)
                .GroupBy(group => group.ActionSetTimeRangeFromUtc.Date);

            var historyPrinter = new HistoryPrinter(bdr, true);

            foreach (IGrouping<DateTime, VkScrapArtLogDataActionGroup> oneDayActionGroups in logItemActionGroupsGroupedByDate)
            {
                historyPrinter.GetProductHistoryTextForOneDayOfActionGroups(oneDayActionGroups, availabilityChangeFilterByKey);
            }

            if (bdr.Length < 3)
            {
                bdr.AppendLine("Інформації не знайдено. Спробуйте не вказувати розмір.");
            }

            return bdr.ToString();
        }

        public class HistoryPrinter
        {
            private readonly StringBuilder _bdr;
            private readonly bool _artlogStyle;
            private bool _printedDate;
            private bool _printedTime;
            private DateTime _dateTimeLocal;
            private VkScrapArtLogDataActionGroup _currentActionGroup;

            public HistoryPrinter(StringBuilder bdr, bool artlogStyle)
            {
                _bdr = bdr;
                _artlogStyle = artlogStyle;
            }

            private void PrintDateIfNot()
            {
                if (_printedDate)
                    return;

                _bdr.AppendLine("______________");
                _bdr.AppendLine(_dateTimeLocal.ToStringUADate());
                _bdr.AppendLine(" ");

                _printedDate = true;
            }

            private void PrintTimeIfNot()
            {
                if (_printedTime)
                    return;

                _bdr.Append("⟹ ");
                _bdr.Append(_currentActionGroup.ActionSetTimeRangeFromUtc.UtcToUkraineTime().AddMinutes(1).ToStringUADateAndTimeShort());
                _bdr.Append(" — ");
                _bdr.AppendLine(_currentActionGroup.ActionSetTimeRangeToUtc.UtcToUkraineTime().ToStringUADateAndTimeShort());

                _printedTime = true;
            }

            public void GetProductHistoryTextForOneDayOfActionGroups(IGrouping<DateTime, VkScrapArtLogDataActionGroup> oneDayActionGroups, string availabilityChangeFilterByKey)
            {
                _printedDate = !_artlogStyle;
                _printedTime = !_artlogStyle;

                _dateTimeLocal = oneDayActionGroups.Key.UtcToUkraineTime();

                foreach (VkScrapArtLogDataActionGroup actionGroup in oneDayActionGroups)
                {
                    _currentActionGroup = actionGroup;

                    IEnumerable<IGrouping<VkScrapArtLogActionType, VkScrapArtLogDataAction>> actionGroupActionsSeqByActionTypeGrps
                        = actionGroup.Actions.GroupBySequence(action => action.ActionType);

                    foreach (IGrouping<VkScrapArtLogActionType, VkScrapArtLogDataAction> actionGroupActionsSeqByActionTypeGrp in actionGroupActionsSeqByActionTypeGrps)
                    {
                        switch (actionGroupActionsSeqByActionTypeGrp.Key)
                        {
                            case VkScrapArtLogActionType.DeletionOrDifPhoto:
                                this.PrintDateIfNot();
                                this.PrintTimeIfNot();
                                _bdr.AppendLine(" - Фото видалено, перенесено у продано, або замінено на інше.");

                                goto _out;
                            case VkScrapArtLogActionType.NewNoArt:
                                this.PrintDateIfNot();
                                this.PrintTimeIfNot();
                                _bdr.AppendLine(" - Фото створено (немає артикула).");

                                goto _out;
                            case VkScrapArtLogActionType.NoArt:
                                this.PrintDateIfNot();
                                this.PrintTimeIfNot();
                                _bdr.AppendLine(" - Немає артикула.");

                                goto _out;
                            case VkScrapArtLogActionType.ParsingError:
                                this.PrintDateIfNot();
                                this.PrintTimeIfNot();
                                _bdr.AppendLine(" - Помилка розпізнавання тексту.");

                                goto _out;
                            case VkScrapArtLogActionType.AlbumChange:
                                this.PrintDateIfNot();
                                this.PrintTimeIfNot();
                                _bdr.AppendLine(" - Зміна альбому.");

                                goto _out;
                            case VkScrapArtLogActionType.New:
                                if (availabilityChangeFilterByKey == null || actionGroupActionsSeqByActionTypeGrp.Any(act => availabilityChangeFilterByKey == act.Key))
                                {
                                    this.PrintDateIfNot();
                                    this.PrintTimeIfNot();
                                    if (_artlogStyle)
                                        _bdr.AppendLine(" - Наявність:");
                                }
                                else
                                    goto _out;

                                break;
                            case VkScrapArtLogActionType.AvailabilityChange:
                                if (availabilityChangeFilterByKey == null || actionGroupActionsSeqByActionTypeGrp.Any(act => availabilityChangeFilterByKey == act.Key))
                                {
                                    this.PrintDateIfNot();
                                    this.PrintTimeIfNot();
                                }
                                else
                                    goto _out;

                                break;
                            default: throw new EnumAbominationException(typeof(VkScrapArtLogActionType));
                        }

                        foreach (VkScrapArtLogDataAction action in actionGroupActionsSeqByActionTypeGrp)
                        {
                            switch (action.ActionType)
                            {
                                case VkScrapArtLogActionType.New:
                                    if (action.NewValue == null)
                                        break;

                                    if (availabilityChangeFilterByKey == null || availabilityChangeFilterByKey == action.Key)
                                    {
                                        _bdr.Append(action.Key);
                                        _bdr.Append("  ⚌  ");
                                        _bdr.AppendLine(_artlogStyle ? IntStringUtil.ReplaceIntWithUnicodeFancyChars(action.NewValue) : action.NewValue);
                                    }

                                    break;
                                case VkScrapArtLogActionType.AvailabilityChange:
                                    if (availabilityChangeFilterByKey == null || availabilityChangeFilterByKey == action.Key)
                                    {
                                        _bdr.Append(action.Key);
                                        _bdr.Append(" ✎  ");
                                        _bdr.Append(_artlogStyle ? IntStringUtil.ReplaceIntWithUnicodeFancyChars(action.OldValue) : action.OldValue);
                                        _bdr.Append("   ➡   ");
                                        _bdr.AppendLine(_artlogStyle ? IntStringUtil.ReplaceIntWithUnicodeFancyChars(action.NewValue) : action.NewValue);
                                    }

                                    break;
                                default: throw new EnumAbominationException(typeof(VkScrapArtLogActionType));
                            }
                        }

                        if (_artlogStyle)
                        {
                            _bdr.AppendLine(" ");
                            _printedTime = false;
                        }

                    _out:
                        continue;
                    }
                }
            }
        }

        private void MergeOldScrapItemLogEntriesOnBackgroundThread()
        {
            if (_scrapAndParseWorkerHelper.RequeueBackgroundTaskIfScrapIsHappening(_logger, this.MergeOldScrapItemLogEntriesOnBackgroundThread,
                    nameof(this.MergeOldScrapItemLogEntriesOnBackgroundThread)
                ))
                return;

            // int mergeWithDataBlockBiggerThan = _configuration.MergeOldScrapLogEntriesAfter_DataBlockBytes;

            using VkScrapDbAccess access = _vkScrapDbAccessFactory.CreateAccess();

            VkScrapArtLogItem[] allLogItems = access.GetAllScrapLogItems();

            int totalMerged = 0;

            foreach (VkScrapArtLogItem logItem in allLogItems)
            {
                int toMergeIndexFrom = -1;
                int toMergeIndexTo = -1;

                var toMerge = new List<VkScrapArtLogDataActionGroup>();

                int definitelyMergeFirstActionGroups = 0;

                /*
                if (logItem.DataBlockLength > mergeWithDataBlockBiggerThan)
                {
                    // TODO: this is bugged
                    throw new NotImplementedException();
                    definitelyMergeFirstActionGroups = 30;
                }
                */

                if (definitelyMergeFirstActionGroups > 0)
                {
                    toMergeIndexFrom = 0;
                    toMergeIndexTo = Math.Min(definitelyMergeFirstActionGroups, logItem.Data.ActionsGroups.Count);

                    toMerge.AddRange(logItem.Data.ActionsGroups.GetRange(toMergeIndexFrom, toMergeIndexTo - toMergeIndexFrom));
                }

                if (toMergeIndexFrom == -1 || toMergeIndexTo < 4)
                    continue;

                if (toMergeIndexFrom != 0)
                    throw new Exception($"VkScrapArtLogItem {logItem.Art} does not have first action group as the oldest. This might indicate that database is corrupted.");

                if (toMergeIndexTo - toMergeIndexFrom < 3)
                    continue;

                //_logger.Error("Some scrap log items are about to be merged! Art=" + logItem.Art);
                //_logger.Error("Artlog before: \r\n" + this.GetProductHistoryText(logItem.Art, null));

                totalMerged += toMerge.Count;

                foreach (VkScrapArtLogDataActionGroup vkScrapArtLogDataActionGroup in toMerge)
                {
                    logItem.Data.ActionsGroups.Remove(vkScrapArtLogDataActionGroup);
                }
                
                VkScrapArtLogDataActionGroup mergedGroup = MergeActionGroupsIntoOne(toMerge, true);

                logItem.Data.ActionsGroups.Insert(toMergeIndexFrom, mergedGroup);

                access.UpdateScrapLogItem(logItem);

                if (totalMerged > 50)
                    break;
            }

            if (totalMerged != 0)
            {
                _logger.Information($"Merged {totalMerged} scrap item log events.");

                access.SaveChanges();

                if (totalMerged > 50)
                {
                    throw new NotImplementedException();
                    /*_threadedTasks.EnqueueTaskInBackgroundThread(() => {
                            Thread.Sleep(20 * 1000);
                            this.MergeOldScrapItemLogEntriesOnBackgroundThread();
                        }, "TEMP"
                    );*/
                }
            }
        }

        public static VkScrapArtLogDataActionGroup MergeActionGroupsIntoOne(List<VkScrapArtLogDataActionGroup> toMerge, bool inSequence)
        {
            var mergedGroup = new VkScrapArtLogDataActionGroup();
            mergedGroup.IsAResultOfMerger = true;
            mergedGroup.Actions = new List<VkScrapArtLogDataAction>();
            mergedGroup.ActionSetTimeRangeFromUtc = toMerge.Min(actionGroup => actionGroup.ActionSetTimeRangeFromUtc);
            mergedGroup.ActionSetTimeRangeToUtc = toMerge.Max(actionGroup => actionGroup.ActionSetTimeRangeToUtc);

            IEnumerable<VkScrapArtLogDataAction> allActions = toMerge
                .OrderBy(actionGroup => actionGroup.ActionSetTimeRangeFromUtc)
                .SelectMany(actionGroup => actionGroup.Actions);

            IEnumerable<IGrouping<VkScrapArtLogActionType, VkScrapArtLogDataAction>> groupByAction;

            if (inSequence)
            {
                groupByAction = allActions.GroupBySequence(action => action.ActionType);
            } else
            {
                groupByAction = allActions.GroupBy(action => action.ActionType);
            }

            foreach (IGrouping<VkScrapArtLogActionType, VkScrapArtLogDataAction> grp in groupByAction)
            {
                MergeActionGroupsIntoOne_HandleGroupByActionType(grp, mergedGroup);
            }

            return mergedGroup;
        }

        private static void MergeActionGroupsIntoOne_HandleGroupByActionType(IGrouping<VkScrapArtLogActionType, VkScrapArtLogDataAction> grp, VkScrapArtLogDataActionGroup mergedGroup)
        {
            switch (grp.Key)
            {
                case VkScrapArtLogActionType.AvailabilityChange:
                    foreach (IGrouping<string, VkScrapArtLogDataAction> groupAvaliChangeByKey in grp.GroupBy(gr => gr.Key))
                    {
                        string key = groupAvaliChangeByKey.Key;

                        if (key == null)
                            continue;

                        string oldValue = groupAvaliChangeByKey.First().OldValue;
                        string newValue = groupAvaliChangeByKey.Last().NewValue;

                        mergedGroup.Actions.Add(VkScrapArtLogDataAction.AvailabilityChange(key, oldValue, newValue));
                    }

                    break;
                case VkScrapArtLogActionType.New:
                    foreach (IGrouping<string, VkScrapArtLogDataAction> groupNewByKey in grp.GroupBy(gr => gr.Key))
                    {
                        string key = groupNewByKey.Key;

                        if (key == null)
                            continue;

                        string newValue = groupNewByKey.Last().NewValue;

                        mergedGroup.Actions.Add(VkScrapArtLogDataAction.New(key, newValue));
                    }

                    break;
                case VkScrapArtLogActionType.AlbumChange:
                    if (!mergedGroup.Actions.Any(i => i.ActionType == VkScrapArtLogActionType.AlbumChange))
                        mergedGroup.Actions.Add(VkScrapArtLogDataAction.AlbumChange());

                    break;
                case VkScrapArtLogActionType.DeletionOrDifPhoto:
                    if (!mergedGroup.Actions.Any(i => i.ActionType == VkScrapArtLogActionType.DeletionOrDifPhoto))
                        mergedGroup.Actions.Add(VkScrapArtLogDataAction.DeletionOrDifPhoto());

                    break;
                case VkScrapArtLogActionType.ParsingError:
                    if (!mergedGroup.Actions.Any(i => i.ActionType == VkScrapArtLogActionType.ParsingError))
                        mergedGroup.Actions.Add(VkScrapArtLogDataAction.ParsingError());

                    break;
                case VkScrapArtLogActionType.NewNoArt:
                    if (!mergedGroup.Actions.Any(i => i.ActionType == VkScrapArtLogActionType.NewNoArt))
                        mergedGroup.Actions.Add(VkScrapArtLogDataAction.NewNoArt());

                    break;
                case VkScrapArtLogActionType.NoArt:
                    if (!mergedGroup.Actions.Any(i => i.ActionType == VkScrapArtLogActionType.NoArt))
                        mergedGroup.Actions.Add(VkScrapArtLogDataAction.NoArt());

                    break;

                case VkScrapArtLogActionType.None:
                default: throw new EnumAbominationException($"This code is not ready for {Enum.GetName(grp.Key)}");
            }
        }
    }
}
