
using System.Text;

using irisdropwebservice.AppConfig;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Misc;


using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace irisdropwebservice.Services.VkScrap.Ins
{
    public class VkScrapDbAccess : DbAccessBase
    {
        private readonly MyDbContext _dbContext;

        public VkScrapDbAccess(DbAccessOptions accessOptions, MyDbContext context)
            : base(accessOptions, context)
        {
            _dbContext = context;

            if (!CCAppConfig.VkScrapIrisDrop.IsScrapItemsProducer)
                throw new Exception("Application is not configured as IsScrapItemsProducer = false. VkScrapDbAccess is denied.");
        }

        public IDbContextTransaction BeginTransaction()
        {
            return _dbContext.Database.BeginTransaction();
        }

        public void AddScrapItem(VkScrapItem scrapItem)
        {
            VkScrapDataComplement.SerializeDtoQtyObjectsToQtysXmlBlock(scrapItem);

            _dbContext.ScrapItems.Add((VkScrapItemDto)((IFriendGetDto)scrapItem).Friend_GetDto());
        }

        public void AddScrapGeneration(VkScrapGeneration scrapGeneration)
        {
            _dbContext.ScrapGenerations.Add((VkScrapGenerationDto)((IFriendGetDto)scrapGeneration).Friend_GetDto());
        }

        public void UpdateScrapGeneration(VkScrapGeneration scrapGeneration)
        {
            _dbContext.ScrapGenerations.Update((VkScrapGenerationDto)((IFriendGetDto)scrapGeneration).Friend_GetDto());
        }

        public VkScrapGeneration GetScrapGenerationById(long id)
        {
            VkScrapGenerationDto genStruct = _dbContext.ScrapGenerations
                .AsNoTracking()
                .First(x => x.Id == id);

            if (genStruct == null)
                return null;

            return new VkScrapGeneration(genStruct);
        }

        public VkScrapGeneration GetLastCompleteScrapGeneration()
        {
            VkScrapGeneration[] lastCompleteGenerations1 = this.GetLastCompleteScrapGenerations(1);

            if (lastCompleteGenerations1.Length == 0)
                return null;

            return lastCompleteGenerations1[0];
        }

        public VkScrapGeneration[] GetLastCompleteScrapGenerations(short generationsCount)
        {
            VkScrapGenerationDto[] lastCompleteGenerationsDto = this.GetLastCompleteScrapGenerationsQueryable(generationsCount)
                .ToArray();

            VkScrapGeneration[] res = lastCompleteGenerationsDto
                .Select(x => new VkScrapGeneration(x))
                .ToArray();

            return res;
        }

        public VkScrapItem[] GetScrapItemsFromGeneration(long scrapGeneration, ScrapReadMode readMode)
        {
            VkScrapItem[] lastScrappedItems;

            switch (readMode)
            {
                case ScrapReadMode.Full:
                    lastScrappedItems = this.GetScrapItemDtosFromGenerationQueryable(scrapGeneration)
                        .Select(x => new VkScrapItem(x))
                        .ToArray();

                    VkScrapGenerationDto scrapGenerationDtoWithQtyUnits = _dbContext.ScrapGenerations
                        .Where(x => x.Id == scrapGeneration)
                        .AsSingleQuery()
                        .First();

                    this.AccomplishVkScrapItemsForUse(lastScrappedItems, scrapGenerationDtoWithQtyUnits);

                    return lastScrappedItems;
                case ScrapReadMode.AlbumInfo:
                    lastScrappedItems = this.GetScrapItemDtosFromGenerationQueryable(scrapGeneration)
                        .Select(x => new { x.VkAlbumId, x.VkGroupId, x.VkPhotoId, x.AlbumTitle, x.OrderInAlbum, x.ParsingResult })
                        .ToArray()
                        .Select(x => new VkScrapItem(new VkScrapItemDto()
                                { VkAlbumId = x.VkAlbumId, VkGroupId = x.VkGroupId, VkPhotoId = x.VkPhotoId, AlbumTitle = x.AlbumTitle, OrderInAlbum = x.OrderInAlbum, ParsingResult = x.ParsingResult }
                            )
                        )
                        .ToArray();

                    return lastScrappedItems;
                default: throw TypeAbominationException.Enum(typeof(ScrapReadMode), readMode);
            }
        }

        public VkScrapGeneration[] GetAllScrapGenerations()
        {
            VkScrapGenerationDto[] pastGenerations = _dbContext.ScrapGenerations
                .AsNoTracking()
                .OrderBy(x => x.Id)
                .ToArray();

            VkScrapGeneration[] res = pastGenerations
                .Select(x => new VkScrapGeneration(x))
                .ToArray();

            return res;
        }

        public string GetTotalScrapItems()
        {
            var bdr = new StringBuilder();

            (long ScrapGeneration, long Id, DateTime ScrapStartDateTime)[] sel = _dbContext.ScrapItems
                .Select(si => new
                    {
                        si.ScrapGeneration,
                        si.Id
                    }
                )
                .ToArray()
                .Select(si => (si.ScrapGeneration, si.Id))
                .FullOuterJoin(
                    _dbContext.ScrapGenerations
                        .Select(sg => new { sg.Id, sg.ScrapStartDateTime })
                        .ToArray()
                        .Select(sg => (sg.Id, sg.ScrapStartDateTime))
                        .ToArray(),
                    si => si.ScrapGeneration,
                    sg => sg.Id,
                    (si, sg, key) => (si.ScrapGeneration, si.Id, sg.ScrapStartDateTime)
                    )
                .ToArray();

            foreach ((long ScrapGeneration, long Id, DateTime ScrapStartDateTime) tuple in sel)
            {
                bdr.AppendLine("( " + tuple.ScrapGeneration + ", " + tuple.ScrapStartDateTime + ",, " + tuple.Id + "); ");
            }

            return bdr.ToString();
        }

        public void DeleteScrapGeneration(long scrapGenerationId)
        {
            _dbContext.ScrapGenerations.Remove(_dbContext.ScrapGenerations.First(sg => sg.Id == scrapGenerationId));
        }

        public void ExecuteDeleteScrapGenerationRelatedRecords(long scrapGenerationId)
        {
            _dbContext.ScrapItems.Where(x => x.ScrapGeneration == scrapGenerationId).ExecuteDelete();
        }

        public ScrapItemShort[] GetAllGenerationsScrapItemsShort()
        {
            ScrapItemShort[] res = _dbContext.ScrapItems
                .Select(vksi => new { vksi.VkGroupId, vksi.VkAlbumId, vksi.VkPhotoId, vksi.MaxPhotoSizeUri })
                .Distinct()
                .ToArray()
                .Select(a => new ScrapItemShort(a.MaxPhotoSizeUri, a.VkGroupId, a.VkAlbumId, a.VkPhotoId))
                .ToArray();

            return res;
        }

        public void AddScrapLogItem(VkScrapArtLogItem logItem)
        {
            VkScrapDataComplement.SerializeScrapArtLogItemData(logItem);

            var dto = (VkScrapArtLogDto)((IFriendGetDto)logItem).Friend_GetDto();

            if (dto.Data == null)
                throw new Exception("dto.Data == null");

            _dbContext.ScrapItemsLog.Add(dto);
        }

        public void UpdateScrapLogItem(VkScrapArtLogItem logItem)
        {
            VkScrapDataComplement.SerializeScrapArtLogItemData(logItem);

            var dto = (VkScrapArtLogDto)((IFriendGetDto)logItem).Friend_GetDto();

            if (dto.Data == null)
                throw new Exception("dto.Data == null");

            _dbContext.ScrapItemsLog.Update(dto);
        }

        public void DeleteScrapLogItem(VkScrapArtLogItem logItem)
        {
            var dto = (VkScrapArtLogDto)((IFriendGetDto)logItem).Friend_GetDto();

            _dbContext.ScrapItemsLog.Remove(dto);
        }

        public VkScrapArtLogItem[] GetAllScrapLogItems()
        {
            VkScrapArtLogItem[] res = _dbContext.ScrapItemsLog
                .AsNoTracking()
                .ToArray()
                .Select(dto => new VkScrapArtLogItem(dto))
                .ToArray();

            this.AccomplishVkScrapLogItemsForUse(res);

            return res;
        }

        // Private
        private IQueryable<VkScrapGenerationDto> GetLastCompleteScrapGenerationsQueryable(short generationsCount)
        {
            IQueryable<VkScrapGenerationDto> lastCompleteGenerationsDto = _dbContext.ScrapGenerations
                .AsNoTracking()
                .OrderByDescending(x => x.Id)
                .Where(x => x.IsComplete)
                .Take(generationsCount);

            return lastCompleteGenerationsDto;
        }

        private IQueryable<VkScrapItemDto> GetScrapItemDtosFromGenerationQueryable(long scrapGeneration)
        {
            IQueryable<VkScrapItemDto> res = _dbContext.ScrapItems
                .AsNoTracking()
                .Where(x => x.ScrapGeneration == scrapGeneration);

            return res;
        }

        private void AccomplishVkScrapItemsForUse(VkScrapItem[] items, VkScrapGenerationDto scrapGenerationDtoWithQtyUnits)
        {
            foreach (VkScrapItem scrapItem in items)
            {
                VkScrapDataComplement.DeserializeQtysXmlBlockToQtyObjects(scrapItem, scrapGenerationDtoWithQtyUnits);
            }
        }

        private void AccomplishVkScrapLogItemsForUse(VkScrapArtLogItem[] items)
        {
            foreach (VkScrapArtLogItem scrapLogItem in items)
            {
                VkScrapDataComplement.DeserializeScrapArtLogItemData(scrapLogItem);

                if (scrapLogItem.Data == null)
                    throw new Exception("scrapLogItem.Data == null");
            }
        }

        public class VkScrapDataComplement
        {
            public static void SerializeDtoQtyObjectsToQtysXmlBlock(VkScrapItem scrapItem)
            {
                scrapItem.WriteQtyObjectsToDto();

                if (!scrapItem.IsNew)
                    throw new Exception("This metohd sould be called for new items.");

                var scrapItemDto = (VkScrapItemDto)(scrapItem as IFriendGetDto).Friend_GetDto();

                var ser = new QtyObjectsSer();
                ser.QtyObjects = scrapItemDto.QtyObjects;

                scrapItemDto.QtysXmlBlock = SerializationUtil<QtyObjectsSer>.SerializeToBinaryCompressed(ser);
            }

            public static void DeserializeQtysXmlBlockToQtyObjects(VkScrapItem scrapItem, VkScrapGenerationDto scrapGeneration)
            {
                if (scrapItem.IsNew)
                    throw new Exception("This metohd sould not be called for new items.");

                var scrapItemDto = (VkScrapItemDto)((IFriendGetDto)scrapItem).Friend_GetDto();

                QtyObjectsSer ser = SerializationUtil<QtyObjectsSer>.DeserializeFromBinaryCompressed(scrapItemDto.QtysXmlBlock);

                scrapItemDto.QtyObjects = ser.QtyObjects;
            }

            public static void SerializeScrapArtLogItemData(VkScrapArtLogItem logItem)
            {
                logItem.WriteDataToDto();

                var logItemDto = (VkScrapArtLogDto)((IFriendGetDto)logItem).Friend_GetDto();

                byte[] totalBlock = SerializationUtil<VkScrapArtLogData>.SerializeToBinaryCompressed(logItemDto.Data);

                if (totalBlock.Length > VkScrapArtLogDto.DATA_XML_BLOCK_MAX_LENGTH)
                {
                    byte[] firstBlock = totalBlock.Take(VkScrapArtLogDto.DATA_XML_BLOCK_MAX_LENGTH).ToArray();
                    byte[] secondBlock =  totalBlock.Skip(VkScrapArtLogDto.DATA_XML_BLOCK_MAX_LENGTH).ToArray();
                    
                    logItemDto.DataXmlBlock = firstBlock;
                    logItemDto.DataXmlBlock2 = secondBlock;
                    
                    if (logItemDto.DataXmlBlock2.Length > VkScrapArtLogDto.DATA_XML_BLOCK_MAX_LENGTH)
                    {
                        throw new Exception($"logItemDto.DataXmlBlock2.Length ({logItemDto.DataXmlBlock2.Length}) > VkScrapArtLogDto.DATA_XML_BLOCK_MAX_LENGTH, art=" + logItemDto.Art);
                    }
                } else
                {
                    logItemDto.DataXmlBlock = totalBlock;
                }
            }

            public static void DeserializeScrapArtLogItemData(VkScrapArtLogItem logItem)
            {
                if (logItem.IsNew)
                    throw new Exception("This metohd sould not be called for new items.");

                var logItemDto = (VkScrapArtLogDto)((IFriendGetDto)logItem).Friend_GetDto();

                byte[] totalBlock = new byte[logItemDto.DataXmlBlock.Length + (logItemDto.DataXmlBlock2?.Length ?? 0)];
                
                Array.Copy(logItemDto.DataXmlBlock, totalBlock, logItemDto.DataXmlBlock.Length);

                if (logItemDto.DataXmlBlock2 != null && logItemDto.DataXmlBlock2.Length != 0)
                {
                    Array.Copy(logItemDto.DataXmlBlock2, 0, totalBlock, logItemDto.DataXmlBlock.Length, logItemDto.DataXmlBlock2.Length);
                }
                
                VkScrapArtLogData ser = SerializationUtil<VkScrapArtLogData>.DeserializeFromBinaryCompressed(totalBlock);

                logItemDto.Data = ser;
            }
        }

        public class MyDbContext : DbContext
        {
            public virtual DbSet<VkScrapItemDto> ScrapItems { get; set; }
            public virtual DbSet<VkScrapGenerationDto> ScrapGenerations { get; set; }
            public virtual DbSet<VkScrapArtLogDto> ScrapItemsLog { get; set; }

            public MyDbContext(DbContextOptions<MyDbContext> options)
                : base(options)
            {
            }

            protected override void OnModelCreating(ModelBuilder modelBuilder)
            {
                // Keys
                modelBuilder.Entity<VkScrapArtLogDto>()
                    .HasKey(x => x.Art);

                // Props
                modelBuilder.Entity<VkScrapItemDto>()
                    .Ignore(x => x.QtyObjects);

                modelBuilder.Entity<VkScrapArtLogDto>()
                    .Ignore(x => x.Data);

                // Relations


                // Column lengths
                /*
                IEntityType scrapItemEntityType = Model.FindEntityType(typeof(VkScrapItemDto));

                string tableName = scrapItemEntityType.GetTableName();
                string tableSchema = scrapItemEntityType.GetSchema();

                foreach (IProperty property in scrapItemEntityType.GetProperties())
                {
                    string columnName = property.GetColumnName();
                    string columnType = property.GetColumnType();

                    GC.KeepAlive(columnType);
                    GC.KeepAlive(columnName);
                };
                */
                base.OnModelCreating(modelBuilder);
            }
        }
    }
}