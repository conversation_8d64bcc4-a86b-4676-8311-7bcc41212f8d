using System.Threading;
using System.Threading.Tasks;

using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Workers;
using Microsoft.Extensions.Hosting;
using Serilog.Events;

namespace irisdropwebservice.Services.VkScrap.Ins
{
    public interface IVkRemoteScrapWatcherWorker : IWorker<IVkRemoteScrapWatcherWorker>
    {
        void DoPollingCycles();

        event Action<VkScrapResult> OnScrapFetchedEvent;
    }

    public class VkRemoteScrapWatcherWorker : IWorkerImpl<IVkRemoteScrapWatcherWorker>, IVkRemoteScrapWatcherWorker
    {
        public WorkerConfiguration WorkerConfiguration { get; } = new WorkerConfiguration(
            "VKSWCH",
            new WorkerConfiguration.Thread("VkRemoteScrapWatcher", ThreadPriority.AboveNormal, IsBackground: true),
            LogEventLevel.Information,
            AllowDirectCall: false
        );

        public ILogger Log { get; } = InvLog.Logger<VkRemoteScrapWatcherWorker>();

        public WorkerCore Core { get; set; }

        public IVkRemoteScrapWatcherWorker PublicInterface { get; set; }

        private readonly IHostApplicationLifetime _applicationLifetime;
        private readonly IVkScrapServiceWebApiCallerInternal _vkScrapServiceWebApiCaller;

        public event Action<VkScrapResult> OnScrapFetchedEvent;

        public VkRemoteScrapWatcherWorker(
            IHostApplicationLifetime applicationLifetime,
            IVkScrapServiceWebApiCallerInternal vkScrapServiceWebApiCaller
        )
        {
            _applicationLifetime = applicationLifetime;
            _vkScrapServiceWebApiCaller = vkScrapServiceWebApiCaller;
        }

        public void DoPollingCycles()
        {
            long lastGen = -1;

            while (true)
            {
                if (_applicationLifetime.ApplicationStopping.IsCancellationRequested)
                    return;

                long thisGen = _vkScrapServiceWebApiCaller.GetRemoteLastCompleteScrapGeneration();

                if (thisGen < 0 || thisGen <= lastGen)
                {
                    try
                    {
                        Task.Delay(TimeSpan.FromSeconds(20), _applicationLifetime.ApplicationStopping).Wait(_applicationLifetime.ApplicationStopping);
                    }
                    catch (TaskCanceledException)
                    {
                        return;
                    }
                    catch (OperationCanceledException)
                    {
                        return;
                    }

                    continue;
                }

                VkScrapResult scrapResult = _vkScrapServiceWebApiCaller.GetRemoteScrapResult(thisGen, ScrapReadMode.Full);

                this.OnScrapFetchedEvent(scrapResult);

                lastGen = thisGen;

                try
                {
                    Task.Delay(TimeSpan.FromSeconds(30), _applicationLifetime.ApplicationStopping).Wait(_applicationLifetime.ApplicationStopping);
                }
                catch (TaskCanceledException)
                {
                    return;
                }
                catch (OperationCanceledException)
                {
                    return;
                }
            }
        }
    }
}