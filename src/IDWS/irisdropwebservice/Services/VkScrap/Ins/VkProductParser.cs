using System.Text;

using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;

using irisdropwebservice.Services.LinkService;


namespace irisdropwebservice.Services.VkScrap.Ins
{
    class VkProductParser
    {
        private readonly ILogger _logger = InvLog.Logger<VkProductParser>();
        
        private readonly VkScrapItem _scrapItem;
        private readonly IEnumerable<LocalAlbum> _localAlbums;

        private int _currentLineNumber;
        private string[] _textSpl;
        private string CurrentLine => _textSpl[_currentLineNumber];

        private readonly char[] _commaAndSpaceLikeCharacters = new char[]
        {
            ',', '،', '⸲', '⸴', '⹁', ' ', '	'
        };

        public VkProductParser(IEnumerable<LocalAlbum> localAlbums, VkScrapItem scrapItem)
        {
            _localAlbums = localAlbums;
            _scrapItem = scrapItem;
        }

        public void Perform()
        {
            _scrapItem.ParsingResult = ParsingResult.Unassigned;

            _currentLineNumber = 0;

            // Autofix some text
            string autofixedText = _scrapItem.Text.Replace("Цiна", "Ціна").Replace("цiна", "ціна");

            var bdr = new StringBuilder();

            using (var reader = new System.IO.StringReader(autofixedText))
            {
                string line;

                while ((line = reader.ReadLine()) != null)
                {
                    if (!line.StartsWith("//"))
                        bdr.AppendLine(line);
                }
            }

            autofixedText = bdr.ToString();

            string getTextHashFrom = autofixedText + " ___ " + "V4";
            _scrapItem.FullTextHash = IntStringUtil.GetStableHashCode(getTextHashFrom).ToString();

            _textSpl = autofixedText.Split("\r\n");

            // 0: Set Album name
            if (!this.Parse0_SetAlbumName())
                return;

            // 1: Reference number or ПРОДАНО
            bool isSoldMark;

            if (!this.Parse1_ReferenceAndSold(out isSoldMark))
                return;

            // 2: Product name
            if (!this.Parse2_ProductName())
                return;

            // 3: Description
            if (!this.Parse3_DescriptionBeforePrice())
                return;

            // 4: Prices, special prices
            List<(string, int)> specialPrices;

            if (!this.Parse4_Prices(out specialPrices))
                return;

            const string EXTRA_TEXT = "*знижка на останній розмір не діє";
            if (CurrentLine.Contains(EXTRA_TEXT))
            {
                _scrapItem.Description = _scrapItem.Description + Environment.NewLine + CurrentLine;
                _currentLineNumber++;

                if (string.IsNullOrWhiteSpace(CurrentLine))
                    _currentLineNumber++;
            }

            // 5: Sizes
            if (!this.Parse5_SizesBlock(isSoldMark))
                return;

            // 6: Apply special prices to sizes
            if (!this.Parse6_ApplySpecialPricesToSizes(specialPrices))
                return;

            _logger.Verbose("That was for art " + _scrapItem.Art);

            _scrapItem.ParsingResult = ParsingResult.Success;
        }

        private bool Parse0_SetAlbumName()
        {
            string albumName = VkScrapImmediateRemoteResult.GetAlbumTitle(_localAlbums, _scrapItem);

            if (albumName == null)
                throw new Exception("AlbumName is null");

            _scrapItem.AlbumTitle = albumName;

            return true;
        }

        private bool Parse1_ReferenceAndSold(out bool isSoldMark)
        {
            isSoldMark = false;

            string[] artSpl = CurrentLine.Trim().Split(" ", StringSplitOptions.RemoveEmptyEntries);

            if (artSpl.Length != 2)
            {
                if (artSpl[0].StartsWith("арт", StringComparison.CurrentCultureIgnoreCase) && artSpl.Length == 1)
                {
                    this.SetErrorForCurrentLine("Нет артикула, товар не может быть перенесен на сайт.");

                    return false;
                }

                this.SetErrorForCurrentLine("В первой строке должно быть два слова типа: 'Арт 123'");

                return false;
            }

            if (!artSpl[0].StartsWith("арт", StringComparison.CurrentCultureIgnoreCase))
            {
                this.SetErrorForCurrentLine("Артикул не обнаружен, он должен быть в первой строчке, ПРОДАНО во второй.");

                return false;
            }

            long art;

            if (!long.TryParse(artSpl[1], out art))
            {
                this.SetErrorForCurrentLine("Артикул в первой строчке должен быть целочисленным, не распознался.");

                return false;
            }

            _scrapItem.Art = art.ToString();
            _currentLineNumber++;

            bool emptyLineIs = false;

            if (string.IsNullOrWhiteSpace(CurrentLine))
            {
                _currentLineNumber++;
                emptyLineIs = true;
            }

            while (true)
            {
                if (CurrentLine.Contains("ПРОДАНО", StringComparison.CurrentCultureIgnoreCase))
                {
                    isSoldMark = true;
                    emptyLineIs = false;
                    _currentLineNumber++;

                    continue;
                }

                if (CurrentLine.Contains("Відправка", StringComparison.CurrentCultureIgnoreCase))
                {
                    emptyLineIs = true;
                    _currentLineNumber++;

                    continue;
                }

                break;
            }

            if (!emptyLineIs && !string.IsNullOrWhiteSpace(CurrentLine))
            {
                this.SetErrorForCurrentLine("Строка должна быть пуста");

                return false;
            }

            if (!emptyLineIs)
                _currentLineNumber++;

            return true;
        }

        private bool Parse2_ProductName()
        {
            _scrapItem.PhotoTitle = CurrentLine.Trim(_commaAndSpaceLikeCharacters);

            if (_scrapItem.PhotoTitle.Length > 100)
            {
                this.SetError(_scrapItem.PhotoTitle, "Слишком длинное название товара (более 100 символов).");

                return false;
            }

            _currentLineNumber++;

            return true;
        }

        private bool Parse3_DescriptionBeforePrice()
        {
            var descriptionBuilder = new StringBuilder();

            while (true)
            {
                if (_textSpl.Length <= _currentLineNumber)
                {
                    this.SetError(
                        "",
                        "Не обнаружен текст 'Ціна'. Так программа поймет где заканчивается описание и идёт цена. " +
                        "Возможно у вас пробел в той же строчке перед текстом 'Ціна' - его не должно быть."
                    );

                    return false;
                }

                string currentLine = CurrentLine;

                if (currentLine.StartsWith("ціна", StringComparison.CurrentCultureIgnoreCase))
                    break;

                if (currentLine.StartsWith("нова ціна", StringComparison.CurrentCultureIgnoreCase))
                    break;

                if (currentLine.StartsWith("акційна ціна", StringComparison.CurrentCultureIgnoreCase))
                    break;

                descriptionBuilder.AppendLine(currentLine);
                _currentLineNumber++;
            }

            _scrapItem.Description = descriptionBuilder.ToString();

            return true;
        }

        private bool Parse4_Prices(out List<(string, int)> specialPrices)
        {
            // This is clusterfuck but it works

            specialPrices = null;

            int? price = this.ParsePrice(true);

            if (price == null)
                return false;

            if (CurrentLine.Contains("нова", StringComparison.CurrentCultureIgnoreCase) || CurrentLine.Contains("акційна", StringComparison.CurrentCultureIgnoreCase))
            {
                _logger.Verbose("PriceNew set: " + price.Value);
                _scrapItem.PriceNew = price.Value;
            } else
            {
                _logger.Verbose("PriceOld set: " + price.Value);
                _scrapItem.PriceOld = price.Value;
            }

            _currentLineNumber++;

            specialPrices = new List<(string, int)>(0);

            if (!string.IsNullOrWhiteSpace(CurrentLine))
            {
                price = this.ParsePrice(false);

                if (price == null)
                {
                    /*if (CurrentLine.Contains("дефект", StringComparison.CurrentCultureIgnoreCase))
                    {
                        _currentLineNumber++;
                    }
                    else*/
                    if (CurrentLine.Contains("знижка", StringComparison.CurrentCultureIgnoreCase))
                    {
                        if (CurrentLine.Contains("грн", StringComparison.CurrentCultureIgnoreCase))
                        {
                            // Same line price
                            // Special price for some sizes can be stated before prices block
                            // "Знижка на розміри 134,140-320 грн"
                            // "СВЯТКОВА ЗНИЖКА 480 ГРН"

                            int priceStart = CurrentLine.IndexOf("-", StringComparison.CurrentCultureIgnoreCase) + "-".Length;
                            int priceEnd = CurrentLine.LastIndexOf("грн", StringComparison.CurrentCultureIgnoreCase);

                            if (priceStart == 0 && priceEnd != -1)
                            {
                                priceStart = CurrentLine.LastIndexOf(" ", priceEnd - " грн".Length, StringComparison.CurrentCultureIgnoreCase);
                            }

                            if (priceEnd == -1 || priceStart < 2)
                            {
                                this.SetErrorForCurrentLine("Не обнаружено '-' или пробела. Между '-' или пробелом и 'грн' должна быть цена.");

                                return false;
                            }

                            string sub = CurrentLine.Substring(priceStart, priceEnd - priceStart);

                            if (!int.TryParse(sub, out int price2))
                            {
                                this.SetErrorForCurrentLine(
$"Текст '{sub}' не распознан как цена. Пожалуйста используйте строку в формате близкому к 'ЗНИЖКА на розміри (з дефектом???) 134,140-320 грн' или 'СВЯТКОВА АБО ЩЕ ЯКАСЬ ЗНИЖКА 480 ГРН' - " +
$"цена должна быть между тире или пробелом и грн, размеры если их несколько - разделены запятой."
                                );

                                return false;
                            } else
                                price = price2;

                            string specialPriceText = CurrentLine.Substring(0, priceStart);
                            specialPrices.Add((specialPriceText, price.Value));
                            _currentLineNumber++;
                        } else
                        {
                            // With the price on the next line
                            string specialPriceText = CurrentLine;
                            _currentLineNumber++;
                            price = this.ParsePrice(true);

                            if (price == null)
                                return false;

                            specialPrices.Add((specialPriceText, price.Value));
                            _currentLineNumber++;
                        }

                        if (CurrentLine.Contains("*акція", StringComparison.CurrentCultureIgnoreCase))
                        {
                            _scrapItem.Description += Environment.NewLine + CurrentLine + Environment.NewLine;
                            _currentLineNumber++;

                            if (!string.IsNullOrWhiteSpace(CurrentLine))
                            {
                                this.SetErrorForCurrentLine("Строка должна быть пуста");

                                return false;
                            }
                        } else if (!string.IsNullOrWhiteSpace(CurrentLine))
                        {
                            this.SetErrorForCurrentLine("Строка должна быть пуста");

                            return false;
                        }
                    } else
                    {
                        if (CurrentLine.Contains("*акція", StringComparison.CurrentCultureIgnoreCase))
                        {
                            _scrapItem.Description += Environment.NewLine + CurrentLine + Environment.NewLine;
                            _currentLineNumber++;

                            if (!string.IsNullOrWhiteSpace(CurrentLine))
                            {
                                this.SetErrorForCurrentLine("Строка должна быть пуста");

                                return false;
                            }
                        } else
                        {
                            if (CurrentLine.Contains("Ціна на ") && CurrentLine.Contains("грн"))
                            {
                                int i1 = CurrentLine.IndexOf("Ціна на ", StringComparison.CurrentCultureIgnoreCase);
                                int i2 = CurrentLine.IndexOf("р", StringComparison.CurrentCultureIgnoreCase);
                                int i3 = CurrentLine.LastIndexOf("-", StringComparison.CurrentCultureIgnoreCase);
                                int i4 = CurrentLine.IndexOf("грн", StringComparison.CurrentCultureIgnoreCase);

                                if (i1 == -1 || i2 == -1 || i3 == -1 || i4 == -1 || i1 > i2 || i2 > i3 || i3 > i4)
                                {
                                    this.SetErrorForCurrentLine($"Текст не распознан как цена [5].");

                                    return false;
                                }

                                string size = CurrentLine.Substring(i1 + "Ціна на ".Length, i2 - i1 - "Ціна на ".Length).Trim();
                                string priceText = CurrentLine.Substring(i3 + "-".Length, i4 - i3 - "-".Length).Trim();
                                int p2;
                                
                                if (!int.TryParse(priceText, out p2))
                                {
                                    this.SetErrorForCurrentLine($"Текст {priceText} не распознан как цена [6].");

                                    return false;
                                }
                                
                                specialPrices.Add((size, p2));
                                _currentLineNumber++;
                            } else
                            {
                                this.SetErrorForCurrentLine($"Текст не распознан как цена [7].");

                                return false;
                            }
                        }
                    }
                } else
                {
                    if (CurrentLine.Contains("нова", StringComparison.CurrentCultureIgnoreCase) || CurrentLine.Contains("тижня", StringComparison.CurrentCultureIgnoreCase) || CurrentLine.Contains("акційна", StringComparison.CurrentCultureIgnoreCase))
                    {
                        if (_scrapItem.PriceNew != null)
                        {
                            this.SetErrorForCurrentLine("Дубликат цены. Не понятно где новая цена а где старая.");

                            return false;
                        }

                        _logger.Verbose("PriceNew set2: " + price.Value);
                        _scrapItem.PriceNew = price.Value;
                    } else
                    {
                        if (_scrapItem.PriceOld != null)
                        {
                            this.SetErrorForCurrentLine("Дубликат цены. Не понятно где новая цена а где старая.");

                            return false;
                        }

                        _logger.Verbose("PriceOld set2: " + price.Value);
                        _scrapItem.PriceOld = price.Value;
                    }

                    _currentLineNumber++;

                    if (!string.IsNullOrWhiteSpace(CurrentLine))
                    {
                        this.SetErrorForCurrentLine("Строка должна быть пуста");

                        return false;
                    }
                }
            }

            _currentLineNumber++;

            return true;
        }

        private bool Parse5_SizesBlock(bool isSoldMark)
        {
            if (CurrentLine.StartsWith("Заміри ", StringComparison.CurrentCultureIgnoreCase))
            {
                _scrapItem.QtyObjects.Add(new VkScrapItemQtyStr(CurrentLine));
                _currentLineNumber++;
            }

            string currentLineTextWithoutSpaces = IntStringUtil.RemoveWhitespace(CurrentLine);

            bool isSizeIsJustHeight = currentLineTextWithoutSpaces.StartsWith("зріст/", StringComparison.CurrentCultureIgnoreCase);

            bool isSizeIsJustSize =
                currentLineTextWithoutSpaces.StartsWith("розмір/",  StringComparison.CurrentCultureIgnoreCase) ||
                currentLineTextWithoutSpaces.StartsWith("розміри/", StringComparison.CurrentCultureIgnoreCase);

            bool isSizeSizeAndHeight =
                currentLineTextWithoutSpaces.StartsWith("Розмір(зріст)довжина",  StringComparison.CurrentCultureIgnoreCase) ||
                currentLineTextWithoutSpaces.StartsWith("Розмір(зріст)/довжина", StringComparison.CurrentCultureIgnoreCase);

            bool isSizeAgeAndSize = currentLineTextWithoutSpaces.StartsWith("вік/розмір", StringComparison.CurrentCultureIgnoreCase);

            if (!isSizeIsJustHeight && !isSizeIsJustSize && !isSizeAgeAndSize && !isSizeSizeAndHeight)
            {
                this.SetErrorForCurrentLine("Не обранужено строки 'зріст/' или 'вік/розмір' или 'розмір/' или 'Розмір(зріст)/довжина' .");

                return false;
            }

            if (CurrentLine.Contains("заміри", StringComparison.CurrentCultureIgnoreCase))
            {
                _scrapItem.QtyObjects.Add(new VkScrapItemQtyStr(CurrentLine));
                _currentLineNumber++;
            }

            _scrapItem.QtyObjects.Add(new VkScrapItemQtyStr(CurrentLine));
            _currentLineNumber++;

            while (true)
            {
                if (_textSpl.Length <= _currentLineNumber)
                    break;

                string currentLine = CurrentLine;

                if (currentLine.StartsWith("Додатково", StringComparison.InvariantCultureIgnoreCase))
                    break;

                // Skip allowed texts in sizes block
                if (string.IsNullOrEmpty(currentLine.Trim()) || currentLine.Trim().StartsWith("+-") ||
                    currentLine.Trim().StartsWith("похибка", StringComparison.CurrentCultureIgnoreCase) || currentLine.Trim().StartsWith("---"))
                {
                    _scrapItem.QtyObjects.Add(new VkScrapItemQtyStr(currentLine));
                    _currentLineNumber++;

                    continue;
                }

                // Split by / and
                var qtyUnit = new VkScrapItemQtyUnit();
                string[] splBySlash = currentLine.Split("/");

                if (splBySlash.Length < 2 && splBySlash.Length != 1)
                {
                    this.SetErrorForCurrentLine("Неправильная строка в блоке размеров / наличия. Не обнаружены дроби или текст типа +-, ------, Додатково.");

                    return false;
                }

                splBySlash[0] = splBySlash[0].Trim();

                // Split last segment by -
                // Parse quantity
                string[] lastSpl = splBySlash.Last().Split("-", StringSplitOptions.RemoveEmptyEntries);

                if (lastSpl.Length < 2)
                {
                    this.SetErrorForCurrentLine("Неправильная строка в блоке размеров / наличия. Не обнаружен текст типа '/70-2шт' или +-, ------, Додатково.");

                    return false;
                }

                bool noQty = lastSpl.Last().Contains("немає");

                if (noQty)
                {
                    qtyUnit.Quantity = 0;
                } else
                {
                    int lastSplLastShtTextIndex = lastSpl.Last().LastIndexOf("шт", StringComparison.CurrentCultureIgnoreCase);

                    if (lastSplLastShtTextIndex == -1)
                    {
                        this.SetErrorForCurrentLine("Неправильная строка в блоке размеров / наличия. Не обнаружен текст типа '/70-2шт'  или +-, ------, Додатково.");

                        return false;
                    }

                    string qtyStr = lastSpl.Last().Substring(0, lastSplLastShtTextIndex);
                    short qty;

                    if (!short.TryParse(qtyStr, out qty))
                    {
                        this.SetErrorForCurrentLine($"Неправильная строка в блоке размеров / наличия. Попытка распознать текст {qtyStr} как кол-во товара в наличии.");

                        return false;
                    }

                    qtyUnit.Quantity = qty;

                    if (isSoldMark)
                    {
                        this.SetErrorForCurrentLine("Написано ПРОДАНО но есть в наличии.");
                    }
                }

                // Perform some checks and assign sizes to qtyUnit
                string firstSize = splBySlash[0].Trim();

                if (isSizeIsJustHeight)
                {
                    bool heightSubSizeIsOk(string subSizeOrSize)
                    {
                        int firstSizeInt;

                        if (!int.TryParse(subSizeOrSize, out firstSizeInt) || firstSizeInt < 40 || firstSizeInt > 280) // I hope these limits will stay relevant in thcoming years.
                        {
                            this.SetErrorForCurrentLine("Непонятный размер (рост).");

                            return false;
                        }

                        return true;
                    }

                    if (firstSize.Contains(LinkedNaming.SIZES_RANGE_SPLITTER))
                    {
                        string[] firstSizeSplitByDash = firstSize.Split(LinkedNaming.SIZES_RANGE_SPLITTER);

                        if (firstSizeSplitByDash.Length != 2)
                        {
                            this.SetErrorForCurrentLine("Непонятный размер (рост) (два тире?).");

                            return false;
                        }

                        if (!heightSubSizeIsOk(firstSizeSplitByDash[0]))
                            return false;

                        if (!heightSubSizeIsOk(firstSizeSplitByDash[1]))
                            return false;
                    } else
                    {
                        if (!heightSubSizeIsOk(firstSize))
                            return false;
                    }

                    qtyUnit.Size_Height = firstSize;
                } else if (isSizeIsJustSize)
                {
                    string allowedInAdultSize = "LMSX";

                    if (firstSize.Any(ch => !allowedInAdultSize.Contains(ch)))
                    {
                        this.SetErrorForCurrentLine($"Неправильный размер '{firstSize}', допустимые символы '{allowedInAdultSize}'. Может быть вы имели в виду рост а не размер.");

                        return false;
                    }

                    qtyUnit.Size_Size = firstSize;
                } else if (isSizeAgeAndSize)
                {
                    string secondSize = splBySlash[1];

                    int firstSizeInt;

                    if (!int.TryParse(firstSize, out firstSizeInt) || firstSizeInt > 3000)
                    {
                        this.SetErrorForCurrentLine("Непонятный размер (возраст).");

                        return false;
                    }

                    int secondSizeInt;

                    if (!int.TryParse(secondSize, out secondSizeInt) || secondSizeInt > 3000)
                    {
                        this.SetErrorForCurrentLine("Непонятный размер (размер после возраста).");

                        return false;
                    }

                    qtyUnit.Size_Age = firstSize;
                    qtyUnit.Size_Size = secondSize;
                } else
                {
                    string[] firstSlashSizeSpl = firstSize.Split('(', ')');

                    firstSize = firstSlashSizeSpl[0];
                    string secondSize = firstSlashSizeSpl[1];

                    int firstSizeInt;

                    if (!int.TryParse(firstSize, out firstSizeInt) || firstSizeInt > 100)
                    {
                        this.SetErrorForCurrentLine("Размер > 100");

                        return false;
                    }

                    qtyUnit.Size_Size = firstSize;
                    qtyUnit.Size_Height = secondSize;
                }

                if (splBySlash.Length != 1)
                {
                    qtyUnit.Size_TheRestOfString = "";

                    foreach (string specSpl in splBySlash.Skip(1).SkipLast(1))
                    {
                        qtyUnit.Size_TheRestOfString += specSpl + "/";
                    }

                    qtyUnit.Size_TheRestOfString += lastSpl[0];
                }

                _scrapItem.QtyObjects.Add(qtyUnit);

                _currentLineNumber++;
            }

            if (_scrapItem.QtyObjects.OfType<VkScrapItemQtyUnit>().DistinctBy(q => q.SizeAllIdentifier).Count()
                !=
                _scrapItem.QtyObjects.OfType<VkScrapItemQtyUnit>().Count())
            {
                this.SetError("", "Несколько одинаковых размеров. Нехорошо.");

                return false;
            }

            if (!_scrapItem.QtyObjects.OfType<VkScrapItemQtyUnit>().Any())
            {
                this.SetError("", "Нет размеров.");

                return false;
            }

            return true;
        }

        private bool Parse6_ApplySpecialPricesToSizes(List<(string, int)> specialPrices)
        {
            if (specialPrices.Count == 0)
                return true;

            string[] allPossibleSizesNames = _scrapItem.QtyObjects.OfType<VkScrapItemQtyUnit>().SelectMany(qu => qu.Size_All).Where(s => s != null).ToArray();
            string[] allPossibleSizesNamesDistinct = allPossibleSizesNames.Distinct().ToArray();

            bool hasIntersectingAnySizes = allPossibleSizesNames.Length != allPossibleSizesNamesDistinct.Length;

            if (hasIntersectingAnySizes)
            {
                this.SetErrorForCurrentLine(
                    "При присутствии специальной скидки нельзя чтобы разные размерности были одинаковыми. То есть не понятно к какому размеру именно применить скидку."
                );

                return false;
            }

            foreach ((string, int) specialPricePair in specialPrices)
            {
                bool specialPriceAssigned = false;
                string text = specialPricePair.Item1;
                string[] spl = text.Split(' ', ',');

                foreach (string spl1 in spl)
                {
                    foreach (VkScrapItemQtyUnit size in _scrapItem.QtyObjects.OfType<VkScrapItemQtyUnit>())
                    {
                        if (size.Size_All.Any(s => s == spl1))
                        {
                            size.SpecialPriceReduction = specialPricePair.Item2;
                            specialPriceAssigned = true;

                            // We can have one new price for multiple sizes
                        }
                    }
                }

                if (!specialPriceAssigned)
                {
                    bool containsDigits = text.Any(char.IsDigit);

                    if (containsDigits)
                    {
                        this.SetError(text, "Найдена скидка, но не обнаружено размера к какому её применить.");
                        return false;
                    }

                    if (_scrapItem.PriceNew.HasValue)
                    {
                        this.SetError(text, "Много скидок слишком.");

                        return false;
                    }

                    if (!_scrapItem.PriceOld.HasValue)
                    {
                        this.SetError(text, "Ошибка распознавания цен [9].");

                        return false;
                    }

                    int oldPrice = _scrapItem.PriceOld.Value;
                    _logger.Verbose("PriceRep Spec: " + oldPrice + " " + specialPricePair.Item2);
                    
                    _scrapItem.PriceOld = specialPricePair.Item2;
                    _scrapItem.PriceNew = null;
                }
            }

            return true;
        }

        private void SetErrorForCurrentLine(string errorText)
        {
            _scrapItem.ParsingError = $"Строка {_currentLineNumber} ('{CurrentLine}') :: " + errorText;
            _scrapItem.ParsingResult = ParsingResult.ParsingError;
        }

        private void SetError(string line, string errorText)
        {
            _scrapItem.ParsingError = $"Строка ('{line}') :: " + errorText;
            _scrapItem.ParsingResult = ParsingResult.ParsingError;
        }

        private int? ParsePrice(bool reportErrors)
        {
            int priceStart = CurrentLine.IndexOf("ціна ТИЖНЯ", StringComparison.CurrentCultureIgnoreCase) + "ціна ТИЖНЯ".Length;

            if (priceStart == "ціна ТИЖНЯ".Length - 1)
                priceStart = CurrentLine.IndexOf("ціна", StringComparison.CurrentCultureIgnoreCase) + "ціна".Length;

            int priceEnd = CurrentLine.LastIndexOf("грн", StringComparison.CurrentCultureIgnoreCase);

            if (priceEnd == -1 || priceStart == "ціна".Length - 1)
            {
                if (reportErrors)
                    this.SetErrorForCurrentLine("Не обнаружено 'грн' или 'ціна'.");

                return null;
            }

            string sub = CurrentLine.Substring(priceStart, priceEnd - priceStart);
            int price;

            if (!int.TryParse(sub, out price))
            {
                if (reportErrors)
                    this.SetErrorForCurrentLine($"Текст {sub} не распознан как цена.");

                return null;
            }

            return price;
        }
    }
}