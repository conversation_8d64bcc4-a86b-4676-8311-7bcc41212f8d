using System.Collections.Immutable;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading;

using irisdropwebservice.AppConfig.ClassConfigurations;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;

using irisdropwebservice.Legacy;
using irisdropwebservice.Services.LinkService;
using irisdropwebservice.Services.Statistics;
using irisdropwebservice.Services.VkScrap.Remote;


using Microsoft.EntityFrameworkCore.Storage;

using SixLabors.ImageSharp;

using VkNet.Model;
using VkNet.Utils;

namespace irisdropwebservice.Services.VkScrap.Ins
{
    public record VkScrapAndParseSessionConfiguration(long GroupId, VkScrapCCInfo CcConf, CancellationToken CancellationToken);

    public class VkScrapAndParseSession
    {
        private ILogger Log { get; } = InvLog.Logger<VkScrapAndParseSession>();

        private readonly IDbAccessFactory<VkScrapDbAccess> _databaseAccessFactory;
        private readonly IVkApiCallsWorker _vkApiCalls;
        private readonly LeFileSystemService _fileSystemService;
        private readonly IStatisticsService _statisticsService;
        private readonly IServiceFactory<VkProductHistory.ScrapSession> _scrapSessionServiceFactory;
        private readonly GlobalUserErrorsManager _globalUserErrors;

        private readonly VkScrapAndParseSessionConfiguration _configuration;

        private VkScrapImmediateRemoteResult _intermediateFullSyncResult;
        private VkScrapItem[] _previousScrapData;
        private VkScrapGeneration _previousScrapGeneration;

        private DateTime _scrapStartTimeUtc;
        private long? _currentScrapGen;
        private volatile bool _began;

        private int _totalNewPhotoes;
        private int _totalAlbumChanges;
        private int _totalTextChanges;
        private int _totalDeletions;

        private int _notProcessedPhotos;

        private readonly List<string> _likelyNotAPhotoLogged = new();

        public VkScrapAndParseSession(
            IDbAccessFactory<VkScrapDbAccess> databaseAccessFactory,
            IVkApiCallsWorker vkApiCalls,
            VkScrapAndParseSessionConfiguration configuration,
            LeFileSystemService fileSystemService,
            IStatisticsService statisticsService,
            IServiceFactory<VkProductHistory.ScrapSession> scrapSessionServiceFactory,
            GlobalUserErrorsManager globalUserErrors
        )
        {
            _databaseAccessFactory = databaseAccessFactory;
            _vkApiCalls = vkApiCalls;
            _configuration = configuration;
            _fileSystemService = fileSystemService;
            _statisticsService = statisticsService;
            _scrapSessionServiceFactory = scrapSessionServiceFactory;
            _globalUserErrors = globalUserErrors;
        }

        public void DoMaintenance()
        {
            using VkScrapDbAccess access = _databaseAccessFactory.CreateAccess();

            List<ScrapItemShort> scrapItemsToFix;

            lock (_fileSystemService.SyncRoot)
            {
                ScrapItemShort[] allScrapItems = access.GetAllGenerationsScrapItemsShort();
                string[] allDirs = _configuration.CcConf.GetAllPhotoFilePathDirs();
                string[] allFiles = allDirs.SelectMany(System.IO.Directory.GetFiles).ToArray();
                var filesThatBelong = new List<string>();

                scrapItemsToFix = new List<ScrapItemShort>();

                foreach (ScrapItemShort scrapItem in allScrapItems)
                {
                    string filePath = _configuration.CcConf.GetPhotoFilePath(scrapItem.FullId);

                    if (!allFiles.Contains(filePath))
                    {
                        scrapItemsToFix.Add(scrapItem);

                        Log.Warning($"File {filePath} was not found, but it is a part of at least one of scrap generations.");

                        continue;
                    }

                    if (!filesThatBelong.Contains(filePath))
                        filesThatBelong.Add(filePath);
                }

                string[] orphanedFiles = allFiles.Except(filesThatBelong).ToArray();

                if (orphanedFiles.Length != 0)
                {
                    Log.Information($"Found {orphanedFiles.Length} orphaned files. Deleting them.");

                    foreach (string filePath in orphanedFiles)
                    {
                        _fileSystemService.DeleteFile(filePath);
                    }
                }
            }

            if (scrapItemsToFix.Count != 0)
            {
                Log.Error($"{scrapItemsToFix.Count} Files were not found, but they are a part of at least one of scrap generations, re-downloading.");

                foreach (ScrapItemShort scrapItemShort in scrapItemsToFix)
                {
                    this.ValidateDownloadReplaceFileIfNeeded(scrapItemShort.FullId, scrapItemShort.MaxPhotoSizeUri);
                }
            }
        }

        public void DoScrap()
        {
            try
            {
                if (_began)
                    throw new Exception("Re-using this class is not permitted.");

                _began = true;

                this.DoScrapInner();
            }
            catch (Exception)
            {
                _globalUserErrors.ClearAllProductErrors(GlobalUserErrorSource.VkScrap);
                _globalUserErrors.SetFlushableSourceError(GlobalUserErrorSource.VkScrap, "Нет данных относительно ошибок распознавания текста");

                throw;
            }
        }

        private VkProductHistory.ScrapSession _productHistory;

        private void DoScrapInner()
        {
            var timeFromBeginningTillDataIsFetched = Stopwatch.StartNew();
            var totalTime = Stopwatch.StartNew();

            _scrapStartTimeUtc = ServerClock.GetCurrentUtcTime().DateTime;
            _currentScrapGen = this.CreateAndStoreScrappingGeneration();

            (_previousScrapGeneration, _previousScrapData) = this.ReadMostRecentCompleteFullScrapGeneration();

            var fetchRemoteInfoTime = Stopwatch.StartNew();

            _intermediateFullSyncResult = this.FetchAllRemoteRelevantInfo();

            _productHistory = _scrapSessionServiceFactory.Create(new VkProductHistory.ScrapSessionInfo(_previousScrapData,
                    _previousScrapGeneration,
                    _scrapStartTimeUtc,
                    _currentScrapGen.Val()
                )
            );

            fetchRemoteInfoTime.Stop();
            timeFromBeginningTillDataIsFetched.Stop();

            long fetchSpecificScrapGen = _currentScrapGen.Val();

            using (VkScrapDbAccess completionDbAccess = _databaseAccessFactory.CreateAccess())
            using (IDbContextTransaction completionDbAccessTransaction = completionDbAccess.BeginTransaction())
            {
                using (VkScrapDbAccess continuousDbAccess = _databaseAccessFactory.CreateAccess())
                {
                    this.ProcessNewPhotos(continuousDbAccess); // Items are added to db here.
                    this.ProcessDif(continuousDbAccess);       // Items are added to db here.

                    this.SaveGeneralParsingErrors();

                    _productHistory.CommitLogItemsAndSaveToDb(completionDbAccess); // Use completionDbAccess when if generation is not marked as complete, we don't commit to log either.

                    continuousDbAccess.SaveChanges();
                }

                this.MarkCurrentScrappingGenerationAsComplete(completionDbAccess, timeFromBeginningTillDataIsFetched.Elapsed);

                completionDbAccessTransaction.Commit();
                completionDbAccess.SaveChanges();
            }

            _currentScrapGen = null;

            _statisticsService.SignalScrapAndParseHappened(_configuration.CcConf.StatSourceName);

            Log.Information(
                $"Scrap took {totalTime.Elapsed}.\r\nOf them {fetchRemoteInfoTime.Elapsed} fething from vk.com\r\nTotal: {_intermediateFullSyncResult.Items.Count} \r\nNew: {_totalNewPhotoes} \r\nAlbum changes: {_totalAlbumChanges} \r\nText changes: {_totalTextChanges} \r\nDeleted: {_totalDeletions}"
            );

            // It is important to mark this data as read, also we are making sure that it is at least partially valid to read.
            VkScrapItem[] res = VkScrapServiceWebApiExecutor.ReadScrappedDataFromSpecificGenerationAndMarkAsRead(_databaseAccessFactory, fetchSpecificScrapGen, ScrapReadMode.AlbumInfo);

            GC.KeepAlive(res);
        }

        #region Fetch remote

        private VkScrapImmediateRemoteResult FetchAllRemoteRelevantInfo()
        {
            Log.Information("Fetch remote photo and texts.");

            var res = new VkScrapImmediateRemoteResult();
            res.Albums = new List<LocalAlbum>();
            res.Items = new List<VkScrapItem>();

            VkCollection<PhotoAlbum> remoteAlbumInfos = _vkApiCalls.GetAlbums(_configuration.GroupId);

            // Albums only
            foreach (PhotoAlbum album in remoteAlbumInfos)
            {
                res.Albums.Add(new LocalAlbum()
                {
                    AlbumTitle = album.Title,
                    Description = album.Description,
                    VkGroupId = _configuration.GroupId,
                    VkAlbumId = album.Id,
                    ThumbSrc = album.ThumbSrc,
                    Created = album.Created,
                    Updated = album.Updated
                }
                );
            }

            // Get photo urls and texts in photos
            foreach (PhotoAlbum album in remoteAlbumInfos)
            {
                if (_configuration.CcConf.ExcludeRemoteAlbums.Any(name => album.Title.StartsWith(name, StringComparison.CurrentCultureIgnoreCase)))
                    continue;

                _configuration.CancellationToken.ThrowIfCancellationRequested();

                List<Photo> remotePhotoesFromOneAlbum = _vkApiCalls.GetAllAlbumPhotos(_configuration.GroupId, album.Id);

                int orderInAlbum = 1;

                foreach (Photo albumPhoto in remotePhotoesFromOneAlbum)
                {
                    if (_configuration.CcConf.ExcludeSpecificPhotoIds.Contains(albumPhoto.Id.Val()))
                    {
                        _notProcessedPhotos++;

                        continue;
                    }

                    res.Items.Add(this.VkComPhotoToPhotoAndText(albumPhoto, orderInAlbum++));
                }
            }

            Log.Information("Fetch remote photo and texts done.");

            return res;
        }

        private VkScrapItem VkComPhotoToPhotoAndText(Photo albumPhoto, int orderInAlbum)
        {
            ulong maxPhotoSize = albumPhoto.Sizes.Max(s => s.Height * s.Width);
            PhotoSize maxPhoto = albumPhoto.Sizes.First(photoSize => photoSize.Height * photoSize.Width == maxPhotoSize);

            if (maxPhoto.Url.AbsoluteUri == null)
                throw new Exception("maxPhoto.Url.AbsoluteUri == null");

            return new VkScrapItem()
            {
                ScrapGeneration = _currentScrapGen.Val(),
                MaxPhotoSizeUri = maxPhoto.Url.AbsoluteUri,
                Text = albumPhoto.Text,
                VkPhotoId = albumPhoto.Id.Val(),
                VkGroupId = _configuration.GroupId,
                VkAlbumId = albumPhoto.AlbumId.Val(),
                VkPhotoCreationDate = albumPhoto.CreateTime ?? DateTime.MinValue,
                OrderInAlbum = orderInAlbum
            };
        }

        #endregion

        #region Process dif

        private void ProcessNewPhotos(VkScrapDbAccess dbAccess)
        {
            List<VkScrapItem> remoteScrapItems = _intermediateFullSyncResult.Items;

            VkScrapItem[] allNewPhotoes = remoteScrapItems
                .ExceptBy(_previousScrapData.Select(vksi => vksi.VkPhotoId), vksi => vksi.VkPhotoId)
                .ToArray();

            _totalNewPhotoes = allNewPhotoes.Length;

            Log.Information($"Process new photos ({allNewPhotoes.Length}).");

            foreach (VkScrapItem scrapItem in allNewPhotoes)
            {
                string photoFilePath = _configuration.CcConf.GetPhotoFilePath(scrapItem.FullId);

                if (!_fileSystemService.FileExistsAndIsOk(photoFilePath))
                {
                    this.DownloadImageFile(scrapItem, photoFilePath);
                }

                Log.Verbose($"ProcessNewPhoto {scrapItem.VkComGroupPhotoUri}.");

                this.ParseAndWriteScrapItemToDbWithoutSavingChanges(dbAccess, scrapItem);

                _productHistory.LogLaterAddNewPhoto(scrapItem);

                _statisticsService.SignalVkPhotoWasCreated(_configuration.CcConf.StatSourceName, scrapItem.VkPhotoId);
            }

            Log.Information("Process all new photos end.");
        }

        // Items are added to db here.
        private void ParseAndWriteScrapItemToDbWithoutSavingChanges(VkScrapDbAccess dbAccess, VkScrapItem scrapItem)
        {
            if (!this.PhotoAndTextLooksLikeAProductInsteadOfRandomAnnouncementsAndAlike(scrapItem))
            {
                scrapItem.ParsingResult = ParsingResult.NotApplicable_LikelyNotAProduct;

                if (!_likelyNotAPhotoLogged.Contains(scrapItem.VkComGroupPhotoUri) && 
                    scrapItem.VkAlbumId != 302693929) // Sale album id
                {
                    _likelyNotAPhotoLogged.Add(scrapItem.VkComGroupPhotoUri);

                    Log.Warning($"Likely not a photo. {scrapItem.VkComGroupPhotoUri} Discarded.");
                }
            }
            else
            {
                this.ParsePhotoAndTextStruct(scrapItem);
            }

            dbAccess.AddScrapItem(scrapItem);
        }

        private void ProcessDif(VkScrapDbAccess dbAccess)
        {
            List<VkScrapItem> remotePTs = _intermediateFullSyncResult.Items;

            LocalAndRemotePhotoAndText[] pairs = remotePTs.Join(
                _previousScrapData,
                p => p.VkPhotoId,
                p => p.VkPhotoId,
                (remote, local) => new LocalAndRemotePhotoAndText() { Local = local, Remote = remote }
            ).ToArray();

            Log.Information($"Process diff ({pairs.Length} pairs).");

            foreach (LocalAndRemotePhotoAndText localAndRemotePhotoAndText in pairs)
            {
                VkScrapItem localScrapItem = localAndRemotePhotoAndText.Local;
                VkScrapItem remoteScrapItem = localAndRemotePhotoAndText.Remote;

                if (localScrapItem.Text == null || remoteScrapItem.Text == null)
                {
                    throw new Exception("Process diff {remoteScrapItem.VkComGroupPhotoUri} -- either texts are null.");
                }

                bool reallyUpdated = false;
                bool downloadedFile = false;

                // Changed album
                if (localScrapItem.VkAlbumId != remoteScrapItem.VkAlbumId)
                {
                    Log.Debug(
                        $"Album is changed ({localScrapItem.VkAlbumId} => {remoteScrapItem.VkAlbumId})" +
                        $" ({_intermediateFullSyncResult.GetAlbumTitleNotNull(localScrapItem)} => {_intermediateFullSyncResult.GetAlbumTitleNotNull(remoteScrapItem)}), " +
                        $"photoId={remoteScrapItem.VkPhotoId}. Copying photo file to different id."
                    );

                    string srcImgPath = _configuration.CcConf.GetPhotoFilePath(localScrapItem.FullId);
                    string destImgPath = _configuration.CcConf.GetPhotoFilePath(remoteScrapItem.FullId);

                    if (_fileSystemService.FileExistsAndIsOk(srcImgPath))
                    {
                        if (srcImgPath != destImgPath)
                        {
                            // We must retain old references to this photo file.
                            _fileSystemService.CopyFileLikeDownloadingTo(srcImgPath, destImgPath);
                        }
                    }
                    else
                    {
                        this.DownloadImageFile(remoteScrapItem, destImgPath);

                        downloadedFile = true;
                    }

                    _productHistory.LogLaterAlbumChanged(remoteScrapItem);

                    reallyUpdated = true;
                    _totalAlbumChanges++;
                }

                // Changed photoes
                if (localScrapItem.MaxPhotoSizeUri != null && localScrapItem.MaxPhotoSizeUri != remoteScrapItem.MaxPhotoSizeUri)
                {
                    Log.Verbose(
                        $"MaxPhotoSizeUri is changed, photoId={remoteScrapItem.VkPhotoId}, album={remoteScrapItem.VkAlbumId} ({_intermediateFullSyncResult.GetAlbumTitleNotNull(remoteScrapItem)})."
                    );

                    int localUriWithoutDomainNameDomainIndex = localScrapItem.MaxPhotoSizeUri.IndexOf("userapi.com", StringComparison.InvariantCulture);
                    int remoteUriWithoutDomainNameDomainIndex = remoteScrapItem.MaxPhotoSizeUri.IndexOf("userapi.com", StringComparison.InvariantCulture);

                    if (localUriWithoutDomainNameDomainIndex == -1 || remoteUriWithoutDomainNameDomainIndex == -1)
                    {
                        throw new NotSupportedException();
                    }

                    string localUriWithoutDomainName = localScrapItem.MaxPhotoSizeUri.Substring(localUriWithoutDomainNameDomainIndex + "userapi.com".Length);
                    string remoteUriWithoutDomainName = remoteScrapItem.MaxPhotoSizeUri.Substring(remoteUriWithoutDomainNameDomainIndex + "userapi.com".Length);

                    // xxx.userapi.com may change, filter this out
                    if (localUriWithoutDomainName != remoteUriWithoutDomainName)
                    {
                        Log.Debug($"MaxPhotoSizeUri has changed {remoteScrapItem.VkComGroupPhotoUri}.");

                        string photoFilePath = _configuration.CcConf.GetPhotoFilePath(localAndRemotePhotoAndText.Remote.FullId);

                        this.DownloadImageFile(remoteScrapItem, photoFilePath);

                        downloadedFile = true;

                        reallyUpdated = true;
                    }
                    else
                    {
                        Log.Verbose("MaxPhotoSizeUri change discarded as it is just a change of vk subdomain hosting.");
                    }
                }

                if (!downloadedFile)
                {
                    string fullId = remoteScrapItem.FullId;

                    this.ValidateDownloadReplaceFileIfNeeded(fullId, remoteScrapItem.MaxPhotoSizeUri);
                }

                bool textChanged = localScrapItem.Text != remoteScrapItem.Text;

                if (textChanged)
                {
                    _totalTextChanges++;
                    reallyUpdated = true;
                }

                if (reallyUpdated)
                    _statisticsService.SignalVkPhotoWasUpdated(_configuration.CcConf.StatSourceName, localScrapItem.VkPhotoId);

                // Changed texts
                this.ParseAndWriteScrapItemToDbWithoutSavingChanges(dbAccess, remoteScrapItem);

                if (textChanged)
                    _productHistory.LogLaterChangedPhotoDescription(remoteScrapItem);
            }

            // Deleted
            foreach (VkScrapItem localScrapItem in _previousScrapData)
            {
                ImmutableArray<VkScrapItem> remotePts1 = remotePTs.Where(p => p.VkPhotoId == localScrapItem.VkPhotoId).ToImmutableArray();

                if (remotePts1.Length > 1)
                {
                    Log.Warning(
                        $"Multiple remote results with the same photo id: photoId={localScrapItem.VkPhotoId}, album={localScrapItem.VkAlbumId} ({_intermediateFullSyncResult.GetAlbumTitleNotNull(localScrapItem)})."
                    );
                }

                if (remotePts1.Length > 0)
                    continue;

                // If deleted - we do nothing, because we may want to fetch older generations scrap.
                // This is a job for periodic cleanup of 100% outdated data.

                Log.Information(
                    $"Marking photo as deleted, photoId={localScrapItem.VkPhotoId}, album={localScrapItem.VkAlbumId} ({_intermediateFullSyncResult.GetAlbumTitleNotNull(localScrapItem)})."
                );

                _totalDeletions++;

                _productHistory.LogLaterDeleted(localScrapItem);

                _statisticsService.SignalVkPhotoWasDeleted(_configuration.CcConf.StatSourceName, localScrapItem.VkPhotoId);
            }

            Log.Information("Process diff end.");
        }

        private void ValidateDownloadReplaceFileIfNeeded(string fullId, string maxPhotoSizeUri)
        {
            string destImgPath = _configuration.CcConf.GetPhotoFilePath(fullId);

            (_, _, long vkPhotoId) = VkNamesAndUrls.ParseFullId(fullId);

            if (!_fileSystemService.FileExistsAndIsOk(destImgPath))
            {
                this.DownloadImageFile(fullId, destImgPath, maxPhotoSizeUri);

                Log.Warning($"Restored absent file {destImgPath}, photoId={vkPhotoId}");
            } else
            {
                bool isValid = _fileSystemService.ReadWithFileStream(destImgPath,
                    stream => {
                        try
                        {
                            SixLabors.ImageSharp.Image.Identify(stream);
                        }
                        catch (NotSupportedException exc)
                        {
                            Log.Warning($"File {destImgPath} is not a valid image file, photoId={vkPhotoId}");
                            Log.Warning(exc);

                            return false;
                        }
                        catch (InvalidImageContentException exc)
                        {
                            Log.Warning($"File {destImgPath} is not a valid image file, photoId={vkPhotoId}");
                            Log.Warning(exc);

                            return false;
                        }
                        catch (UnknownImageFormatException exc)
                        {
                            Log.Warning($"File {destImgPath} is not a valid image file, photoId={vkPhotoId}");
                            Log.Warning(exc);

                            return false;
                        }

                        return true;
                    }
                );

                if (!isValid)
                {
                    this.DownloadImageFile(fullId, destImgPath, maxPhotoSizeUri);

                    Log.Warning($"Restored damaged file {destImgPath}, photoId={vkPhotoId}");
                }
            }
        }

        #endregion

        #region Parsing

        private bool PhotoAndTextLooksLikeAProductInsteadOfRandomAnnouncementsAndAlike(VkScrapItem photoAndText)
        {
            string[] textSpl = photoAndText.Text.Split("\n");

            if (textSpl.Length < 4)
                return false;

            return true;
        }

        private void ParsePhotoAndTextStruct(VkScrapItem scrapItem)
        {
            var unit = new VkProductParser(_intermediateFullSyncResult.Albums, scrapItem);
            bool wasError = false;

            try
            {
                unit.Perform();
            }
            catch (Exception exc)
            {
                wasError = true;
                scrapItem.ParsingError = "НЕПРЕДВИДЕННАЯ ОШИБКА, ОБРАТИТЕСЬ К РАЗРАБОТЧИКУ: " + Environment.NewLine + exc.Message + Environment.NewLine + exc.StackTrace;

                Log.Error($"Parsing {scrapItem.VkComGroupPhotoUri} has failed.");
                Log.Error(exc);
            }

            if (!wasError && !string.IsNullOrWhiteSpace(scrapItem.ParsingError))
            {
                Log.Verbose($"Parsing {scrapItem.VkComGroupPhotoUri} has an expected parsing error:\r\n{scrapItem.ParsingError}.");
            }
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private void DownloadImageFile(VkScrapItem photoAndText, string filePath)
        {
            this.DownloadImageFile(photoAndText.FullId, filePath, photoAndText.MaxPhotoSizeUri);
        }

        private void DownloadImageFile(string fullId, string filePath, string maxPhotoSizeUri)
        {
            (long vkGroupId, long vkAlbumId, long vkPhotoId) = VkNamesAndUrls.ParseFullId(fullId);

            Log.Debug($"Downloading photo, album={vkAlbumId} ({_intermediateFullSyncResult?.GetAlbumTitleNotNull(vkGroupId, vkAlbumId)}), photoId={vkPhotoId}");

            _vkApiCalls.DownloadFileProtected(maxPhotoSizeUri, filePath);
        }

        private void SaveGeneralParsingErrors()
        {
            List<VkScrapItem> items = _intermediateFullSyncResult.Items;

            // General errors
            string generalErrors = this.CreateGeneralVkParserErrorsStringForUser(items);

            _globalUserErrors.SetFlushableSourceError(GlobalUserErrorSource.VkScrap, generalErrors);
            
            List<(string, string)> prestaArtsAndTexts = new();

            foreach (VkScrapItem vksi in items)
            {
                switch (vksi.ParsingResult)
                {
                    case ParsingResult.ParsingError:
                        if (string.IsNullOrWhiteSpace(vksi.Art) || vksi.Art == "0")
                            prestaArtsAndTexts.Add((vksi.VkComGroupPhotoUri, vksi.ParsingError ?? "Невідома помилка"));
                        else
                            prestaArtsAndTexts.Add((LinkedNaming.PrestaSiteReferenceFromVkAuthorityArt(vksi.Art), vksi.VkComGroupPhotoUri + " " + vksi.ParsingError));
                        break;
                    case ParsingResult.Success:
                        break;
                    case ParsingResult.NotApplicable_LikelyNotAProduct:
                    case ParsingResult.Unassigned:
                        break;
                }
            }

            _globalUserErrors.ClearAllProductErrors(GlobalUserErrorSource.VkScrap);
            _globalUserErrors.SetFlushableProductErrors(GlobalUserErrorSource.VkScrap, prestaArtsAndTexts);
        }

        private string CreateGeneralVkParserErrorsStringForUser(IList<VkScrapItem> parsedPhotoAndTexts)
        {
            // Set some general and other errors
            var allErrorsBdr = new StringBuilder();

            // Same Reference check& set errors
            IEnumerable<IGrouping<string, VkScrapItem>> grp = parsedPhotoAndTexts
                .Where(item => item.ParsingResult != ParsingResult.NotApplicable_LikelyNotAProduct)
                .GroupBy(item => item.Art);

            IGrouping<string, VkScrapItem>[] maliciousGroups = grp.Where(innerGrp => innerGrp.Count() > 1).ToArray();

            if (maliciousGroups.Any())
            {
                allErrorsBdr.AppendLine(
                    "🚨 Обнаружен товар с одинаковыми артикулами, арт должен быть уникален, либо старый товар с таким же артикулом должен быть перенесен в ПРОДАНО."
                );

                foreach (IGrouping<string, VkScrapItem> group in maliciousGroups)
                {
                    foreach (VkScrapItem vksi in group)
                    {
                        string error = $"Повтор артикула {group.Key}";

                        if (vksi.ParsingError == null)
                            vksi.ParsingError = error;
                        else
                            vksi.ParsingError = error + Environment.NewLine + vksi.ParsingError;

                        vksi.ParsingResult = ParsingResult.ParsingError;
                    }
                }
            }

            return allErrorsBdr.ToString();
        }
#endregion

        #region Generations

        private long CreateAndStoreScrappingGeneration()
        {
            using VkScrapDbAccess dbAccess = _databaseAccessFactory.CreateAccess();

            var genStruct = new VkScrapGeneration();
            genStruct.ScrapStartDateTime = _scrapStartTimeUtc;

            dbAccess.AddScrapGeneration(genStruct);
            dbAccess.SaveChanges();

            return genStruct.Id;
        }

        private (VkScrapGeneration, VkScrapItem[]) ReadMostRecentCompleteFullScrapGeneration()
        {
            using VkScrapDbAccess dbAccess = _databaseAccessFactory.CreateAccess();

            VkScrapGeneration lastCompleteGeneration = dbAccess.GetLastCompleteScrapGeneration();

            if (lastCompleteGeneration == null)
                return (null, Array.Empty<VkScrapItem>());

            VkScrapItem[] lastScrappedItems = dbAccess.GetScrapItemsFromGeneration(lastCompleteGeneration.Id, ScrapReadMode.Full);

            return (lastCompleteGeneration, lastScrappedItems);
        }

        private void MarkCurrentScrappingGenerationAsComplete(VkScrapDbAccess dbAccess, TimeSpan timeItTookToFetchInfoFromServer)
        {
            VkScrapGeneration genStruct = dbAccess.GetScrapGenerationById(_currentScrapGen.Val());

            genStruct.ScrapInfoFetchingDurationMs = timeItTookToFetchInfoFromServer;
            genStruct.IsComplete = true;

            dbAccess.UpdateScrapGeneration(genStruct);
        }

        #endregion
    }
}
