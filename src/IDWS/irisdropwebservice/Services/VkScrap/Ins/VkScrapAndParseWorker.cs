using System.Threading;
using System.Threading.Tasks;

using irisdropwebservice.AppConfig;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Workers;
using Invictus.Nomenklatura.Misc;

using irisdropwebservice.Legacy;
using irisdropwebservice.Services.LinkService.Ins;
using irisdropwebservice.Services.VkScrap.Remote;

using Microsoft.Extensions.Hosting;

using Serilog.Events;

namespace irisdropwebservice.Services.VkScrap.Ins
{
    public interface IVkScrapAndParseWorker : IWorker<IVkScrapAndParseWorker>
    {
        bool ScrapIsHappening { get; }

        void Authorize();

        [CanFail(3, LogLevel = LogEventLevel.Error)]
        void Scrap();

        [CanFail(3, LogLevel = LogEventLevel.Error)]
        void DoMaintenance();
    }

    public interface IVkScrapAndParseWorkerHelper
    {
        bool RequeueBackgroundTaskIfScrapIsHappening(ILogger logger, Action action, string methodName);
    }

    public class VkScrapAndParseWorker : 
        IWorkerImpl<IVkScrapAndParseWorker>, IVkScrapAndParseWorker,
        IVkScrapAndParseWorkerHelper
    {
        public WorkerConfiguration WorkerConfiguration { get; } = new WorkerConfiguration(
            "VKSCAP",
            new WorkerConfiguration.Thread("VkScrapAndParse", ThreadPriority.Normal, IsBackground: true),
            LogEventLevel.Information,
            AllowDirectCall: false
        );

        public ILogger Log { get; } = InvLog.Logger<VkScrapAndParseWorker>();

        public WorkerCore Core { get; set; }
        
        public IVkScrapAndParseWorker PublicInterface { get; set; }

        private readonly IServiceScopes _serviceScopes;
        private readonly IVkApiCallsWorker _vkApiCalls;
        private readonly InvTasks _threadedTasks;
        private readonly LeFileSystemService _fileSystemService;
        private readonly IHostApplicationLifetime _applicationLifetime;
        private readonly IServiceFactory<VkScrapAndParseSession> _scrapAndParseSessionFactory;
        private readonly IDbAccessFactory<VariousStoredDataDbAccess> _variousDbAccessFactory;
        private readonly ThisPreExpiredToken _tokenExpiry;

        private VkScrapAndParseSession _currentExecutingScrapSession;
        public bool ScrapIsHappening => _currentExecutingScrapSession != null;

        public VkScrapAndParseWorker(
            IHostApplicationLifetime applicationLifetime,
            IServiceFactory<VkScrapAndParseSession> scrapAndParseSessionFactory,
            IDbAccessFactory<VariousStoredDataDbAccess> variousDbAccessFactory,
            IVkApiCallsWorker vkApiCalls,
            LeFileSystemService fileSystemService,
            IServiceScopes serviceScopes,
            InvTasks threadedTasks
        )
        {
            _applicationLifetime = applicationLifetime;
            _scrapAndParseSessionFactory = scrapAndParseSessionFactory;
            _variousDbAccessFactory = variousDbAccessFactory;
            _vkApiCalls = vkApiCalls;
            _fileSystemService = fileSystemService;
            _serviceScopes = serviceScopes;
            _threadedTasks = threadedTasks;
            _tokenExpiry = new ThisPreExpiredToken(variousDbAccessFactory, Log, _vkApiCalls.InitializeAndAuthorize);
        }

        private class ThisPreExpiredToken : PreExpiredToken
        {
            public ThisPreExpiredToken(IDbAccessFactory<VariousStoredDataDbAccess> access, ILogger logger, Func<string, string> authorize)
                : base(access, logger, "VK_SESSION_TOKEN", "VK.COM", 2, authorize, 6)
            {
            }
        }

        public void Authorize()
        {
            _tokenExpiry.Authorize();
        }

        public void Scrap()
        {
            this.InScrapSession(() => _currentExecutingScrapSession.DoScrap());
        }

        public void DoMaintenance()
        {
            this.InScrapSession(() => _currentExecutingScrapSession.DoMaintenance());
        }

        private void InScrapSession(Action action)
        {
            try
            {
                using (IFactoryScope scope = _serviceScopes.EnterFactoryScope())
                {
                    lock (_fileSystemService.SyncRoot)
                    {
                        if (_currentExecutingScrapSession != null)
                            throw new Exception("Scrap is already happening");

                        var scrapConfiguration = new VkScrapAndParseSessionConfiguration(
                            RRAppConfig.SecretVkIrisDrop.GroupId,
                            CCAppConfig.VkScrapIrisDrop,
                            _applicationLifetime.ApplicationStopping
                        );

                        scope.AddService(scrapConfiguration);

                        _currentExecutingScrapSession = _scrapAndParseSessionFactory.Create();
                    }

                    action();
                }
            }
            finally
            {
                lock (_fileSystemService.SyncRoot)
                {
                    _currentExecutingScrapSession = null;
                }
            }
        }

        public bool RequeueBackgroundTaskIfScrapIsHappening(ILogger logger, Action action, string methodName)
        {
            if (ScrapIsHappening) // To be sure that there is no scrap generation that is not committed to the db yet but already created a file.
            {
                logger.Information($"Postponing {methodName} due to scrap is happening now.");

                Task.Delay(TimeSpan.FromMinutes(2))
                    .ContinueWithShortThread(_ =>
                        _threadedTasks.RunBackground(action, methodName)
                    );

                return true;
            }

            return false;
        }
    }
}