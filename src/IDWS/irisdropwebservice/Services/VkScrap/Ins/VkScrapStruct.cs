namespace irisdropwebservice.Services.VkScrap.Ins
{
    class VkScrapImmediateRemoteResult
    {
        public List<LocalAlbum> Albums { get; set; }
        public List<VkScrapItem> Items { get; set; }

        public string GetAlbumTitleNotNull(VkScrapItem pat)
        {
            return this.GetAlbumTitleNotNull(pat.VkGroupId, pat.VkAlbumId);
        }

        public string GetAlbumTitleNotNull(long groupId, long albumId)
        {
            LocalAlbum localAlbum = Albums.SingleOrDefault(album => album.VkAlbumId == albumId && album.VkGroupId == groupId);

            return localAlbum == null ? "(null)" : localAlbum.AlbumTitle;
        }

        public static string GetAlbumTitle(IEnumerable<LocalAlbum> albums, VkScrapItem pat)
        {
            LocalAlbum album = albums.SingleOrDefault(album => album.VkGroupId == pat.VkGroupId && album.VkAlbumId == pat.VkAlbumId);

            return album?.AlbumTitle;
        }
    }

    class LocalAlbum
    {
        public long VkGroupId { get; set; }
        public long VkAlbumId { get; set; }

        public string AlbumTitle { get; set; }
        public string Description { get; set; }
        public string ThumbSrc { get; set; }

        public DateTime? Created { get; set; }
        public DateTime? Updated { get; set; }
    }

    struct LocalAndRemotePhotoAndText
    {
        public VkScrapItem Local { get; set; }

        public VkScrapItem Remote { get; set; }
    }

    public readonly record struct ScrapItemShort(string MaxPhotoSizeUri, long VkGroupId, long VkAlbumId, long VkPhotoId)
    {
        public string FullId => VkNamesAndUrls.CreateFullId(VkGroupId, VkAlbumId, VkPhotoId);
    }
}