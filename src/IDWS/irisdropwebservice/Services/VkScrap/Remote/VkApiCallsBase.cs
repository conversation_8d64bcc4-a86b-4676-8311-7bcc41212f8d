using System.Net;
using System.Net.Http;

using Castle.DynamicProxy;

using Invictus.Nomenklatura.Exceptions;

using irisdropwebservice.AppConfig;
using irisdropwebservice.AppConfig.RemoteResources;

using Invictus.Nomenklatura.Misc;
using Invictus.Nomenklatura.Web;

using irisdropwebservice.Legacy;

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Logging;

using Serilog.Events;
using Serilog.Extensions.Logging;

using VkNet;
using VkNet.Exception;
using VkNet.Utils.AntiCaptcha;

using ILogger = Serilog.ILogger;
using WebRequestWithRetryResult = irisdropwebservice.Legacy.WebRequestWithRetryResult;

namespace irisdropwebservice.Services.VkScrap.Remote
{
    public abstract class VkApiCallsRawBase
    {
        private const int SLEEP_TIME_BETWEEN_REQUESTS = 600;

        protected VkApiCallsRawBase()
        {
        }
        
        private static DateTime _LastRequestEndTimeUtc = ServerClock.GetCurrentUtcTime().AddMinutes(-1).DateTime;

        protected static TimeSpan GetTimeToWait()
        {
            TimeSpan lastCallTimeDiff = ServerClock.GetCurrentUtcTime() - _LastRequestEndTimeUtc;

            if (lastCallTimeDiff.TotalMilliseconds < SLEEP_TIME_BETWEEN_REQUESTS)
                return TimeSpan.FromMilliseconds(SLEEP_TIME_BETWEEN_REQUESTS - (int)lastCallTimeDiff.TotalMilliseconds);

            return TimeSpan.Zero;
        }

        public abstract class InterceptorBase : IInterceptor
        {
            protected abstract ILogger Log { get; }

            private readonly VkProxyConfiguration _vkProxyConfiguration = RRAppConfig.SecretVkIrisDrop.Proxy;

            private readonly Func<IVkApiCallsWorker> _getDecoratedCalls;

            protected InterceptorBase(Func<IVkApiCallsWorker> getDecoratedCalls)
            {
                _getDecoratedCalls = getDecoratedCalls;
            }

            public void Intercept(IInvocation invocation)
            {
                WebRequestWithRetryOld.WebCallWithRetry(Log, () => this.ApiCallWrapperWebCallWithRetry(invocation));
            }

            private WebRequestWithRetryResult ApiCallWrapperWebCallWithRetry(IInvocation invocation)
            {
                try
                {
                    invocation.Proceed();

                    return WebRequestWithRetryResult.Success(null);
                }
                catch (AggregateException exc)
                {
                    Exception[] authFailExceptions = ExceptionUtil.GetInnerExceptionsOfType(exc, typeof(UserAuthorizationFailException).FullName).ToArray();

                    foreach (Exception authFailException in authFailExceptions)
                    {
                        if (authFailException.Message.Contains("access_token has expired"))
                        {
                            Log.Information("access_token has expired.");

                            // TODO: this is not shielded from further recursion.
                            _getDecoratedCalls().ReAuthorize();

                            return WebRequestWithRetryResult.MightRetry(exc);
                        }
                    }

                    Log.Information("VkApiCallsDecorator exception.");

                    RetryAdvice advice = WebRequestRetryExceptionBeHandler.GetWebRequestRetryAdvice(Log, exc, _vkProxyConfiguration.Url + ":" + _vkProxyConfiguration.Port);

                    return WebRequestWithRetryResult.FromExceptionAdvice(exc, advice);
                }
                catch (Exception exc)
                {
                    Log.Information("VkApiCallsDecorator exception.");

                    RetryAdvice advice = WebRequestRetryExceptionBeHandler.GetWebRequestRetryAdvice(Log, exc, _vkProxyConfiguration.Url + ":" + _vkProxyConfiguration.Port);

                    return WebRequestWithRetryResult.FromExceptionAdvice(exc, advice);
                }
                finally
                {
                    _LastRequestEndTimeUtc = ServerClock.GetCurrentUtcTime().DateTime;
                }
            }
        }

        private readonly VkProxyConfiguration _vkProxyConfiguration = RRAppConfig.SecretVkIrisDrop.Proxy;
        
        public enum LogLevelIntercept
        {
            None = 0,
            TreadDebugAsVerbose,
            NoDebugAndVerbose
        }

        public static ILogger<T> MsLogFromSerilogLog<T>(LogLevelIntercept logLevelIntercept)
        {
            var levelMap = new Dictionary<LogEventLevel, LogEventLevel?>();

            switch (logLevelIntercept)
            {
                case LogLevelIntercept.TreadDebugAsVerbose:
                    levelMap[LogEventLevel.Debug] = LogEventLevel.Verbose;
                    levelMap[LogEventLevel.Verbose] = null;
                    break;
                case LogLevelIntercept.NoDebugAndVerbose:
                    levelMap[LogEventLevel.Debug] = null;
                    levelMap[LogEventLevel.Verbose] = null;
                    break;
            }

            // Creates an instance of ILogger<IMyService>
            ILogger<T> microsoftLogger = LoggerFactoryExtensions.CreateLogger<T>(
                new SerilogLoggerFactory(new LogLevelChangeProxyLogger(Serilog.Log.Logger, levelMap))
            );
            
            return microsoftLogger;
        }
        
        private class LogLevelChangeProxyLogger : ILogger
        {
            private readonly ILogger _serilogLogger;
            private readonly Dictionary<LogEventLevel, LogEventLevel?> _levelChange;

            public LogLevelChangeProxyLogger(ILogger serilogLogger, Dictionary<LogEventLevel, LogEventLevel?> levelChange)
            {
                _serilogLogger = serilogLogger;
                _levelChange = new Dictionary<LogEventLevel, LogEventLevel?>();

                foreach (LogEventLevel value in Enum.GetValues(typeof(LogEventLevel)))
                {
                    _levelChange.Add(value, levelChange.ContainsKey(value) ? levelChange[value] : value);
                }
            }

            public void Write(LogEvent logEvent)
            {
                LogEventLevel? logLevel = _levelChange[logEvent.Level];

                if (!logLevel.HasValue)
                    return;

                LogEvent mfLogEvent;

                if (logEvent.TraceId.HasValue && logEvent.SpanId.HasValue)
                {
                    mfLogEvent = new LogEvent(
                        logEvent.Timestamp,
                        logLevel.Value,
                        logEvent.Exception,
                        logEvent.MessageTemplate,
                        logEvent.Properties.Select(prop => new LogEventProperty(prop.Key, prop.Value)),
                        logEvent.TraceId.Value,
                        logEvent.SpanId.Value
                    );
                }
                else
                {
                    mfLogEvent = new LogEvent(
                        logEvent.Timestamp,
                        logLevel.Value,
                        logEvent.Exception,
                        logEvent.MessageTemplate,
                        logEvent.Properties.Select(prop => new LogEventProperty(prop.Key, prop.Value))
                    );
                }

                _serilogLogger.Write(mfLogEvent);
            }
        }
        
        public static IServiceCollection MyAddSeriloggingForAllMsILoggers<T>(IServiceCollection services, LogLevelIntercept logLevelIntercept)
        {
            ILogger<T> logger = MsLogFromSerilogLog<T>(logLevelIntercept);
            services.TryAddSingleton(_ => logger);

            ILogger<T> logger2 = MsLogFromSerilogLog<T>(logLevelIntercept);
            services.TryAddSingleton<Microsoft.Extensions.Logging.ILogger>(_ => logger2);

            return services;
        }

        protected (VkApi, HttpClientHandler) CreateGenericApi()
        {
            var serviceCollection = new ServiceCollection();
            
            // CHECK
            serviceCollection.AddSerilog();
            serviceCollection.AddSingleton<ILoggerProvider>(new SerilogLoggerProvider());

            // All VkNet logs will be as like from VkApi ns.
            MyAddSeriloggingForAllMsILoggers<VkApi>(serviceCollection, LogLevelIntercept.NoDebugAndVerbose);

            var captchaSolver = new FooCaptchaSolver();
            serviceCollection.TryAddSingleton(captchaSolver);

            /*LoggingDelegatingHttpHandler httpClientHandler = _logTheseRequests.CreateDelegatingHttpHandler("VK.com", new HttpClientHandler
            {
                Proxy = VkApiCallsRawBase.CreateWebProxy(
                    _vkProxyConfiguration.Url,
                    _vkProxyConfiguration.Port,
                    _vkProxyConfiguration.Username,
                    _vkProxyConfiguration.Password
                )
            });*/
            var httpClientHandler = new HttpClientHandler
            {
                Proxy = CreateWebProxy(
                    _vkProxyConfiguration.Url,
                    _vkProxyConfiguration.Port,
                    _vkProxyConfiguration.Username,
                    _vkProxyConfiguration.Password
                )
            };

            var client = new HttpClient(httpClientHandler, true);

            serviceCollection.AddSingleton(client);

            var api = new VkApi(serviceCollection);
            api.CaptchaSolver = new FooCaptchaSolver();

            // _Api.CaptchaHandler = new CaptchaSolver();

            return (api, httpClientHandler);
        }

        // TODO: Is VK Proxy better? Referrer?
        protected static WebProxy CreateWebProxy(string proxyUrl, int port, string username, string password)
        {
            // Validate proxy address
            var proxyUri = new Uri($"{proxyUrl}:{port}");

            // Set credentials
            ICredentials credentials = username != null && password != null ? new NetworkCredential(username, password) : null;

            // Set proxy
            var proxy = new WebProxy(proxyUri, true, null, credentials);
            proxy.UseDefaultCredentials = false;
            proxy.Credentials = credentials;

            return proxy;
        }

        protected class FooCaptchaSolver : ICaptchaSolver //, ICaptchaHandler
        {
            // public int MaxCaptchaRecognitionCount { get; set; } = 100;

            public void CaptchaIsFalse()
            {
            }

            /*public T Perform<T>(Func<ulong?, string, T> action)
            {
                return default;
            }*/

            public string Solve(string url)
            {
                throw new NotImplementedException("Solving captcha is not implemented.");

                /*Debugger.Launch();

                string result = "";

                GC.KeepAlive(result);

                return result;*/
            }
        }
    }
}