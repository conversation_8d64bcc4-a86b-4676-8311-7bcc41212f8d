using System.Net.Http;

using System.Threading.Tasks;

using Invictus.Nomenklatura.Logg;

using irisdropwebservice.AppConfig;
using irisdropwebservice.AppConfig.RemoteResources;


using Invictus.Nomenklatura.Workers;

using irisdropwebservice.Legacy;

using Serilog.Events;

using VkNet;
using VkNet.Enums.SafetyEnums;
using VkNet.Model;
using VkNet.Utils;

namespace irisdropwebservice.Services.VkScrap.Remote
{
    public interface IVkApiCallsWorker : IWorker<IVkApiCallsWorker>
    {
        string InitializeAndAuthorize(string prevToken);
        string ReAuthorize();

        [CanFail(3)]
        VkCollection<PhotoAlbum> GetAlbums(long ownerId);

        [CanFail(3)]
        List<Photo> GetAllAlbumPhotos(long ownerId, long albumId);

        [CanFail(3)]
        void DownloadFileProtected(string address, string filePath);
    }

    public class VkApiCallsRawWorker : VkApiCallsRawBase, IWorkerImpl<IVkApiCallsWorker>, IVkApiCallsWorker
    {
        public WorkerConfiguration WorkerConfiguration { get; } = new WorkerConfiguration(
            "VKAPIC",
            new WorkerConfiguration.TaskScheduler("VkApiCalls", 1),
            LogEventLevel.Verbose,
            AllowDirectCall: true,
            GetCustomInterceptor: w => new ThisInterceptor(() => (IVkApiCallsWorker)w),
            ThrottlingConfiguration: new ThrottlingConfiguration()
            {
                GetTimeToWait = _ => GetTimeToWait(),
            }
        );

        public WorkerCore Core { get; set; }

        public ILogger Log { get; } = InvLog.Logger<VkApiCallsRawWorker>();

        public IVkApiCallsWorker PublicInterface { get; set; }

        public class ThisInterceptor : InterceptorBase
        {
            protected override ILogger Log { get; } = InvLog.Logger<ThisInterceptor>();

            public ThisInterceptor(Func<IVkApiCallsWorker> getDecoratedCalls)
                : base(getDecoratedCalls)
            {
            }
        }

        private readonly LeFileSystemService _fileSystemService;

        private VkApi _api;
        private HttpClient _httpClient;

        public VkApiCallsRawWorker(LeFileSystemService fileSystemService)
        {
            _fileSystemService = fileSystemService;
        }

        public string InitializeAndAuthorize(string prevToken)
        {
            (_api, HttpClientHandler httpClientHandler) = this.CreateGenericApi();

            _httpClient = new HttpClient(httpClientHandler, true);

            return this.ReAuthorize(prevToken);
        }

        public string ReAuthorize()
        {
            return this.ReAuthorize(null);
        }

        private string ReAuthorize(string prevToken)
        {
            VkComSecretRRInfo vkIrisDropAppConfig = RRAppConfig.SecretVkIrisDrop;

            var apiAuthParams = new ApiAuthParams
            {
                Login = vkIrisDropAppConfig.Login,
                Password = vkIrisDropAppConfig.Password,
                ApplicationId = (ulong)vkIrisDropAppConfig.AppId,
                Phone = vkIrisDropAppConfig.PhoneNumber,
                Settings = VkNet.Enums.Filters.Settings.All,
                TwoFactorAuthorization = () => {
                    throw new NotImplementedException();
                },
                IsTokenUpdateAutomatically = true,
                AccessToken = prevToken
            };

            _api.Authorize(apiAuthParams);

            return _api.Token;
        }

        public VkCollection<PhotoAlbum> GetAlbums(long ownerId)
        {
            return _api.Photo.GetAlbums(new PhotoGetAlbumsParams() { OwnerId = ownerId });
        }

        public List<Photo> GetAllAlbumPhotos(long ownerId, long albumId)
        {
            var res = new List<Photo>(32);

            const int ONE_API_CALL_COUNT = 500;
            ulong currentApiCallPhotoGetOffset = 0;

        _repeatApiCall:

            VkCollection<Photo> albumPhotos =
                _api.Photo.Get(new PhotoGetParams()
                    {
                        OwnerId = ownerId,
                        AlbumId = PhotoAlbumType.Id(albumId),
                        Count = ONE_API_CALL_COUNT,
                        Offset = currentApiCallPhotoGetOffset
                    }
                );

            res.AddRange(albumPhotos);

            if ((ulong)albumPhotos.Count + currentApiCallPhotoGetOffset < albumPhotos.TotalCount)
            {
                currentApiCallPhotoGetOffset += ONE_API_CALL_COUNT;

                goto _repeatApiCall;
            }

            return res;
        }

        public void DownloadFileProtected(string address, string filePath)
        {
            var uri = new Uri(address);
            Task<HttpResponseMessage> responseTask = Task.Run(() => _httpClient.GetAsync(uri));
            responseTask.Wait();

            _fileSystemService.DownloadWithFileStream(filePath,
                fs => {
                    Task copyContentTask = Task.Run(() => responseTask.Result.Content.CopyToAsync(fs));
                    copyContentTask.Wait();
                }
            );
        }

        /*
        public Task QCreateAlbum(string albumTitle, string description, long groupId)
        {
            return this.EnqueueTask(nameof(this.CreateAlbum), albumTitle, description, groupId);
        }

        private VkCollection<Photo> GetSeveralPhotos(long ownerId, long albumId, string[] photoIds)
        {
            return this.ApiCallWrapper(() => {
                    return _api.Photo.Get(new PhotoGetParams()
                        {
                            OwnerId = ownerId,
                            AlbumId = PhotoAlbumType.Id(albumId),
                            PhotoIds = photoIds
                        }
                    );
                }
            );
        }

        private PhotoAlbum CreateAlbum(string albumTitle, [DoNotLog] string description, long groupId)
        {
            return this.ApiCallWrapper(() => {
                    return _api.Photo.CreateAlbum(new PhotoCreateAlbumParams()
                        {
                            Title = albumTitle,
                            CommentsDisabled = null,
                            Description = description,
                            GroupId = groupId,
                            PrivacyComment = null,
                            PrivacyView = null,
                            UploadByAdminsOnly = false
                        }
                    );
                }
            );
        }

        private void GetGroupMessages()
        {
            ApiCallWrapper(() => {

                var conversations = _Api.Messages.GetConversations(new GetConversationsParams()
                {
                    Count = 50,
                });

                var conversation = conversations.Items.FirstOrDefault(calm => calm.Conversation.ChatSettings.Title == "НП ЖОВТЕНЬ");
                long chatId = conversation.LastMessage.ChatId.Value;

                var chat = _Api.Messages.GetChat(chatId);

                var chatHistory = _Api.Messages.GetHistory(new MessagesGetHistoryParams()
                {
                    Count = 50,
                    PeerId = chatId,
                });

                foreach (var msg in chatHistory.Messages)
                {
                    GC.KeepAlive(msg.Body);
                }



                return 0;
            });
        }
        */
    }
}