using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.ExteriorServ;

using irisdropwebservice.Services.VkScrap.Ins;
using irisdropwebservice.Services.VkScrap.Remote;

using JetBrains.Annotations;

using Microsoft.Extensions.DependencyInjection;

namespace irisdropwebservice.Services.VkScrap
{
    [UsedImplicitly]
    public class VkScrapExteriorServiceBuilder : ExteriorServiceBuilderBase<VkScrapExteriorService>
    {
        protected override void AddConcealedServices(ServiceCollection concealedServiceCollection)
        {
            concealedServiceCollection.AddWorkerSingleton<VkScrapAndParseWorker>();
            concealedServiceCollection.AddSingleton<IVkScrapAndParseWorkerHelper, VkScrapAndParseWorker>();
            concealedServiceCollection.AddWorkerSingleton<VkRemoteScrapWatcherWorker>();
            concealedServiceCollection.AddWorkerSingleton<VkApiCallsRawWorker>();

            concealedServiceCollection.AddSingleton<VkScrapServiceWebApiExecutor>();
            concealedServiceCollection.AddSingleton<IVkScrapServiceWebApiCallerInternal, VkScrapServiceWebApiCaller>();
            concealedServiceCollection.AddSingleton<IVkProductHistory, VkProductHistory>();

            concealedServiceCollection.AddFactory<VkScrapAndParseSession>();
            concealedServiceCollection.AddFactory<VkProductHistory.ScrapSession>();

            this.RegisterDatabaseTypes(concealedServiceCollection);

            base.AddConcealedServices(concealedServiceCollection);
        }

        protected override void ExposeConcealedServices()
        {
            // Used by Controllers
            this.ExposeSingleton<VkScrapServiceWebApiExecutor>();

            // Used by those who consumes vk scrap
            this.ExposeSingleton<IVkScrapFileDownload, IVkScrapServiceWebApiCallerInternal>();
            this.ExposeSingleton<IVkScrapGetLast, IVkScrapServiceWebApiCallerInternal>();
            this.ExposeSingleton<IVkProductHistoryPublic, IVkProductHistory>();

            base.ExposeConcealedServices();
        }
    }
}