using System.IO;
using System.Threading.Tasks;

using Invictus.Nomenklatura.App;

using irisdropwebservice.AppConfig;
using irisdropwebservice.AppConfig.ClassConfigurations;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.ExteriorServ;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;

using irisdropwebservice.Legacy;
using irisdropwebservice.Services.VkScrap.Ins;

using Microsoft.EntityFrameworkCore.Storage;


namespace irisdropwebservice.Services.VkScrap
{
    public class VkScrapExteriorService : IExteriorService
    {
        private readonly ILogger _logger = InvLog.Logger<VkScrapExteriorService>();

        private readonly IVkScrapAndParseWorker _scrapAndParseWorker;
        private readonly IVkScrapAndParseWorkerHelper _scrapAndParseWorkerHelper;
        private readonly InvTasks _invTasks;
        private readonly IVkRemoteScrapWatcherWorker _remoteScrapWatcherWorker;
        private readonly IVkScrapGetLast _serviceWebApiCaller;
        private readonly LeFileSystemService _fileSystemService;
        private readonly LeBackgroundTasks _backgroundTasks;
        private readonly IDbAccessFactory<VkScrapDbAccess> _vkScrapDbAccessFactory;

        public event Action<VkScrapResult> OnScrapFetchedEvent;

        private readonly VkScrapCCInfo _configuration = CCAppConfig.VkScrapIrisDrop;

        private readonly string _addLogDir;

        public VkScrapExteriorService(
            IVkScrapAndParseWorker scrapAndParseWorker,
            IVkRemoteScrapWatcherWorker remoteScrapWatcherWorker,
            IVkScrapGetLast serviceWebApiCaller,
            IVkProductHistory vkProductHistory,
            LeBackgroundTasks backgroundTasks,
            IDbAccessFactory<VkScrapDbAccess> vkScrapDbAccessFactory,
            LeFileSystemService fileSystemService,
            IVkScrapAndParseWorkerHelper scrapAndParseWorkerHelper,
            InvTasks invTasks,
            InvAppConfig appConfig
        )
        {
            _scrapAndParseWorker = scrapAndParseWorker;
            _remoteScrapWatcherWorker = remoteScrapWatcherWorker;
            _serviceWebApiCaller = serviceWebApiCaller;
            _backgroundTasks = backgroundTasks;
            _vkScrapDbAccessFactory = vkScrapDbAccessFactory;
            _fileSystemService = fileSystemService;
            _scrapAndParseWorkerHelper = scrapAndParseWorkerHelper;
            _invTasks = invTasks;

            _addLogDir = System.IO.Path.Combine(appConfig.BasicConfiguration.Dirs.AppDataDir);
            Directory.CreateDirectory(_addLogDir);
            
            vkProductHistory.Initialize();
        }

        public Task Run()
        {
            bool runningAny = false;

            if (!_configuration.IsScrapItemsProducer && string.IsNullOrWhiteSpace(RRAppConfig.Application.RemoteThisServiceUrl))
                throw new Exception("Configuraiton: IsScrapItemsProducer is false but no ProducerUrl is provided.");

            if (_configuration.IsScrapItemsProducer)
            {
                runningAny = true;

                this.InitializeProducer();
            }

            if (_configuration.IsScrapItemsConsumer)
            {
                runningAny = true;

                this.InitializeConsumer();
            }

            _logger.Information("Running.");

            if (!runningAny)
                _logger.Warning("This is service is configured to do nothing.");

            if (_configuration.IsScrapItemsProducer)
            {
                _backgroundTasks.AddPeriodicBackgroundTask(this,
                    TimeSpan.FromHours(1.1),
                    (_) => this.CleanupOldScrapGenerationsOnBackgroundThread(),
                    nameof(VkScrapExteriorService) + "::" + nameof(this.CleanupOldScrapGenerationsOnBackgroundThread)
                );

                // _invTasks.RunBackground(this.CleanupOldScrapGenerationsOnBackgroundThread, nameof(this.CleanupOldScrapGenerationsOnBackgroundThread));

                _backgroundTasks.AddFixedTimeOfDayBackgroundTask(
                    this.KickOffMaintenance,
                    nameof(VkScrapExteriorService) + "::" + nameof(this.KickOffMaintenance),
                    TimeSpan.FromHours(3)
                );
            }

            return Task.CompletedTask;
        }

        private void InitializeProducer()
        {
            _scrapAndParseWorker.Run(w => w.Authorize());
            
            TimeSpan[] scrapTimesOfDay = Enumerable.Range(0, 31).Select(i => TimeSpan.FromMinutes(20) + TimeSpan.FromMinutes(45 * i)).ToArray();

            _backgroundTasks.AddFixedTimeOfDayShortRunningForegroundTask(this.TryBeginVkScrap, "DoVkScrap", scrapTimesOfDay);
        }

        private void TryBeginVkScrap()
        {
            if (_configuration.IsScrapItemsProducer && ! _scrapAndParseWorker.ScrapIsHappening)
            {
                _scrapAndParseWorker.Run(w => w.Scrap());
            }
        }

        private void InitializeConsumer()
        {
            _remoteScrapWatcherWorker.OnScrapFetchedEvent += this.RemoteWatcherThreadOnScrapFetchedEvent;
            _remoteScrapWatcherWorker.Run(w => w.DoPollingCycles());
        }

        private void RemoteWatcherThreadOnScrapFetchedEvent(VkScrapResult obj)
        {
            Action<VkScrapResult> ev = this.OnScrapFetchedEvent;

            ev?.Invoke(obj);
        }

        public void TryGoFirstScrap()
        {
            this.TryBeginVkScrap();
        }

        public VkScrapResult GetAnyLastScrapResultFull()
        {
            return _serviceWebApiCaller.GetAnyLastScrapResult(ScrapReadMode.Full);
        }

        private void CleanupOldScrapGenerationsOnBackgroundThread()
        {
            DateTime keepOnlyFirstAndLast_BeforeDate = ServerClock.GetCurrentUtcTime().AddDays(-0.2).DateTime;

            // TODO: place LastFetch updates well.

            // Get all scrap generations
            VkScrapGeneration[] allScrapGenerations;

            using (VkScrapDbAccess access = _vkScrapDbAccessFactory.CreateAccess())
            {
                allScrapGenerations = access.GetAllScrapGenerations();
            }

            VkScrapGeneration[] scrapGenerationsToDelete = allScrapGenerations;

            // Exclude last 3 completed generations
            scrapGenerationsToDelete = scrapGenerationsToDelete.Except(
                scrapGenerationsToDelete
                    .Where(generation => generation.IsComplete)
                    .TakeLast(3)
            ).ToArray();

            // Exclude recently accessed.
            scrapGenerationsToDelete = scrapGenerationsToDelete.Except(
                scrapGenerationsToDelete
                    .Where(generation => generation.LastFetch != null && generation.LastFetch > keepOnlyFirstAndLast_BeforeDate)
            ).ToArray();

            // Delete midday generations
            var deleteMiddayGenerations = new List<VkScrapGeneration>();
            IEnumerable<IGrouping<DateTime, VkScrapGeneration>> dateGroups = scrapGenerationsToDelete.GroupBy(gen => gen.ScrapStartDateTime.Date);

            foreach (IGrouping<DateTime, VkScrapGeneration> dateGroup in dateGroups)
            {
                if (dateGroup.Count() < 2)
                    continue;

                deleteMiddayGenerations.AddRange(
                    dateGroup.OrderBy(gen => gen.ScrapStartDateTime)
                        .Skip(1)
                );
            }
            
            scrapGenerationsToDelete = deleteMiddayGenerations.Take(100).ToArray();
            
            _logger.Warning($"Deleting {scrapGenerationsToDelete.Length} scrap items.");

            this.LogTotalScrapItems();

            // Execute
            foreach (VkScrapGeneration scrapGeneration in scrapGenerationsToDelete)
            {
                long scrapGenerationId = scrapGeneration.Id;

                _logger.Information($"Deleting old scrap generation {scrapGenerationId}");
                
                using (VkScrapDbAccess access = _vkScrapDbAccessFactory.CreateAccess())
                {
                    access.ExecuteDeleteScrapGenerationRelatedRecords(scrapGenerationId);

                    access.SaveChanges();
                }

                using (VkScrapDbAccess access = _vkScrapDbAccessFactory.CreateAccess())
                {
                    access.DeleteScrapGeneration(scrapGenerationId);

                    access.SaveChanges();
                }
            }
            
            this.LogTotalScrapItems();
            
            _logger.Warning("Got total");
        }

        private void LogTotalScrapItems()
        {
            string fileName = Path.Combine(_addLogDir, DateTime.UtcNow.ToString("HH:m:s zzz").Replace(":", "_"));
            
            using (VkScrapDbAccess access = _vkScrapDbAccessFactory.CreateAccess())
            {
                File.WriteAllText(fileName, access.GetTotalScrapItems());
                
                _logger.Warning($"Total scrap items written to {fileName}");
            }
        }

        private void KickOffMaintenance()
        {
            _scrapAndParseWorker.Run(w => w.DoMaintenance());
        }
    }
}