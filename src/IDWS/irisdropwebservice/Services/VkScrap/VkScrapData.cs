using System.Text;

using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Misc;
using irisdropwebservice.Services.LinkService;
using irisdropwebservice.Services.VkScrap.Ins;

namespace irisdropwebservice.Services.VkScrap
{
    public enum ParsingResult : byte
    {
        Unassigned = 0,
        Success = 1,
        NotApplicable_LikelyNotAProduct = 2,
        ParsingError = 3
    }

    public enum ScrapReadMode : byte
    {
        None = 0,
        Full,
        AlbumInfo
    }

    public class VkScrapGeneration : IFriendGetDto
    {
        private readonly VkScrapGenerationDto _dto;
        private readonly bool _isNew;

        private void CheckReadOnly()
        {
            if (!_isNew)
                throw new Exception($"This {nameof(VkScrapGeneration)} is not new so it is readonly.");
        }

        object IFriendGetDto.Friend_GetDto()
        {
            return _dto;
        }

        public VkScrapGeneration()
        {
            _dto = new VkScrapGenerationDto();
            _isNew = true;
        }

        public VkScrapGeneration(VkScrapGenerationDto dto)
        {
            _dto = dto;
            _isNew = false;
        }

        public long Id => _dto.Id;

        public DateTime ScrapStartDateTime
        {
            get => _dto.ScrapStartDateTime;
            set {
                this.CheckReadOnly();
                _dto.ScrapStartDateTime = value;
            }
        }

        public DateTime? LastFetch
        {
            get => _dto.LastFetch;
            set => _dto.LastFetch = value;
        }

        public bool IsComplete
        {
            get => _dto.IsComplete;
            set => _dto.IsComplete = value;
        }

        public TimeSpan? ScrapInfoFetchingDurationMs
        {
            get => _dto.ScrapInfoFetchingDurationMs.HasValue ? TimeSpan.FromMilliseconds(_dto.ScrapInfoFetchingDurationMs.Value) : null;
            set => _dto.ScrapInfoFetchingDurationMs = value.HasValue ? (int)value.Value.TotalMilliseconds : throw new ArgumentNullException();
        }
    }

    public class VkScrapItem : IFriendGetDto
    {
        private readonly VkScrapItemDto _dto;
        private readonly bool _isNew;

        private void CheckReadOnly()
        {
            if (!_isNew)
                throw new Exception($"This {nameof(VkScrapItem)} is not new so it is readonly.");
        }

        object IFriendGetDto.Friend_GetDto()
        {
            return _dto;
        }

        public VkScrapItem()
        {
            _dto = new VkScrapItemDto();
            _isNew = true;
        }

        public VkScrapItem(VkScrapItemDto dto)
        {
            _dto = dto;
            _isNew = false;
        }

        public bool IsNew => _isNew;

        public long ScrapGeneration
        {
            get => _dto.ScrapGeneration;
            set {
                this.CheckReadOnly();
                _dto.ScrapGeneration = value;
            }
        }

        // Scrap

        public string MaxPhotoSizeUri
        {
            get => _dto.MaxPhotoSizeUri;
            set {
                this.CheckReadOnly();
                _dto.MaxPhotoSizeUri = value;
            }
        }

        public string Text
        {
            get => _dto.Text;
            set {
                this.CheckReadOnly();
                _dto.Text = value;
            }
        }

        public long VkGroupId
        {
            get => _dto.VkGroupId;
            set {
                this.CheckReadOnly();
                _dto.VkGroupId = value;
            }
        }

        public long VkAlbumId
        {
            get => _dto.VkAlbumId;
            set {
                this.CheckReadOnly();
                _dto.VkAlbumId = value;
            }
        }

        public long VkPhotoId
        {
            get => _dto.VkPhotoId;
            set {
                this.CheckReadOnly();
                _dto.VkPhotoId = value;
            }
        }

        public DateTime VkPhotoCreationDate
        {
            get => _dto.VkPhotoCreationDate;
            set {
                this.CheckReadOnly();
                _dto.VkPhotoCreationDate = value;
            }
        }

        public int OrderInAlbum
        {
            get => _dto.OrderInAlbum;
            set {
                this.CheckReadOnly();
                _dto.OrderInAlbum = value;
            }
        }

        public string FullId
        {
            get => VkNamesAndUrls.CreateFullId(VkGroupId, VkAlbumId, VkPhotoId);
            set => (VkGroupId, VkAlbumId, VkPhotoId) = VkNamesAndUrls.ParseFullId(value);
        }

        public string VkComGroupPhotoUri => VkNamesAndUrls.GetVkComGroupPhotoUri(FullId);

        // Parsing

        public ParsingResult ParsingResult
        {
            get => _dto.ParsingResult;
            set {
                this.CheckReadOnly();
                _dto.ParsingResult = value;
            }
        }

        public string AlbumTitle
        {
            get => _dto.AlbumTitle;
            set {
                this.CheckReadOnly();
                _dto.AlbumTitle = value;
            }
        }

        public string PhotoTitle
        {
            get => _dto.PhotoTitle;
            set {
                this.CheckReadOnly();
                _dto.PhotoTitle = value;
            }
        }

        public string Art
        {
            get => _dto.Art;
            set {
                this.CheckReadOnly();
                _dto.Art = value;
            }
        }

        public string Description
        {
            get => _dto.Description;
            set {
                this.CheckReadOnly();
                _dto.Description = value;
            }
        }

        public int? PriceNew
        {
            get => _dto.PriceNew;
            set {
                this.CheckReadOnly();
                _dto.PriceNew = value;
            }
        }

        public int? PriceOld
        {
            get => _dto.PriceOld;
            set {
                this.CheckReadOnly();
                _dto.PriceOld = value;
            }
        }

        public string FullTextHash
        {
            get => _dto.FullTextHash;
            set {
                this.CheckReadOnly();
                _dto.FullTextHash = value;
            }
        }

        public string ParsingError
        {
            get => _dto.ParsingError;
            set {
                this.CheckReadOnly();
                _dto.ParsingError = value;
            }
        }

        private List<object> _qtyObjectsCached;

        public List<object> QtyObjects
        {
            get {
                if (_qtyObjectsCached == null)
                {
                    if (IsNew)
                    {
                        _qtyObjectsCached = new List<object>();

                        return _qtyObjectsCached;
                    }

                    _qtyObjectsCached = _dto.QtyObjects
                        .Select(dto => dto switch
                            {
                                VkScrapItemQtyStrXml dtoAsQtyStr   => (object)new VkScrapItemQtyStr(dtoAsQtyStr),
                                VkScrapItemQtyUnitXml dtoAsQtyUnit => new VkScrapItemQtyUnit(dtoAsQtyUnit),
                                _                                  => throw TypeAbominationException.Variant("dto", dto)
                            }
                        )
                        .ToList();
                }

                return _qtyObjectsCached;
            }
        }

        public void WriteQtyObjectsToDto()
        {
            _dto.QtyObjects = _qtyObjectsCached == null 
                ? Array.Empty<IScrapItemQtyXml>() 
                : _qtyObjectsCached.Select(q => (IScrapItemQtyXml)((q as IFriendGetDto).Friend_GetDto())).ToArray();
        }
    }

    public class VkScrapItemQtyStr : IFriendGetDto
    {
        private readonly VkScrapItemQtyStrXml _dto;
        private readonly bool _isNew;

        private void CheckReadOnly()
        {
            if (!_isNew)
                throw new Exception($"This {nameof(VkScrapItemQtyStr)} is not new so it is readonly.");
        }

        object IFriendGetDto.Friend_GetDto()
        {
            return _dto;
        }

        public VkScrapItemQtyStr(string str)
        {
            _dto = new VkScrapItemQtyStrXml();
            _isNew = true;

            Value = str ?? "";
        }

        public VkScrapItemQtyStr(VkScrapItemQtyStrXml dto)
        {
            _dto = dto;
            _isNew = false;
        }

        public string Value
        {
            get => _dto.NonSizeArbitraryText;
            set {
                this.CheckReadOnly();
                _dto.NonSizeArbitraryText = value;
            }
        }
    }

    public class VkScrapItemQtyUnit : IFriendGetDto
    {
        private readonly VkScrapItemQtyUnitXml _dto;
        private readonly bool _isNew;

        private void CheckReadOnly()
        {
            if (!_isNew)
                throw new Exception($"This {nameof(VkScrapItemQtyUnit)} is not new so it is readonly.");
        }

        object IFriendGetDto.Friend_GetDto()
        {
            return _dto;
        }

        public VkScrapItemQtyUnit()
        {
            _dto = new VkScrapItemQtyUnitXml();
            _isNew = true;
        }

        public VkScrapItemQtyUnit(VkScrapItemQtyUnitXml dto)
        {
            _dto = dto;
            _isNew = false;
        }

        // Either variants are valid on a product, not both:
        //  - Height
        //  - Age and Size are 1:1 options
        //  - Size where only 'XLMS' is allowed

        public string Size_Size
        {
            get => _dto.Size_Size;
            set {
                this.CheckReadOnly();
                _dto.Size_Size = value;
            }
        }

        public string Size_Age
        {
            get => _dto.Size_Age;
            set {
                this.CheckReadOnly();
                _dto.Size_Age = value;
            }
        }

        public string Size_Height
        {
            get => _dto.Size_Height;
            set {
                this.CheckReadOnly();
                _dto.Size_Height = value;
            }
        }

        public string Size_Height_Fixed => LinkedNaming.AddCentimetersToHeightSize(Size_Height);

        public string Size_TheRestOfString
        {
            get => _dto.Size_TheRestOfString;
            set {
                this.CheckReadOnly();
                _dto.Size_TheRestOfString = value;
            }
        }

        public string[] Size_All => new string[] { Size_Size, Size_Age, Size_Height };

        public string SizeAllIdentifier => Size_Size + "__" + Size_Age + "__" + Size_Height;

        public void GetSizeAllIdentifierLocalized(StringBuilder quickStringBuilder)
        {
            bool hasPrev = false;

            if (!string.IsNullOrWhiteSpace(Size_Size))
            {
                quickStringBuilder.Append("Розмір: ");
                quickStringBuilder.Append(Size_Size);
                hasPrev = true;
            }

            if (!string.IsNullOrWhiteSpace(Size_Age))
            {
                if (hasPrev)
                    quickStringBuilder.Append(", ");
                quickStringBuilder.Append("Вік: ");
                quickStringBuilder.Append(Size_Age);
                hasPrev = true;
            }

            if (!string.IsNullOrWhiteSpace(Size_Height))
            {
                if (hasPrev)
                    quickStringBuilder.Append(", ");
                quickStringBuilder.Append("Зріст: ");
                quickStringBuilder.Append(Size_Height);
            }
        }

        public short Quantity
        {
            get => _dto.Quantity;
            set {
                this.CheckReadOnly();
                _dto.Quantity = value;
            }
        }

        public int? SpecialPriceReduction
        {
            get => _dto.SpecialPrice;
            set {
                this.CheckReadOnly();
                _dto.SpecialPrice = value;
            }
        }

        public override string ToString()
        {
            return Size_All
                       .Select(s => s ?? "-")
                       .Aggregate("", (s, d) => s + d, s => s)
                   + " :: "
                   + Quantity + "(" + Size_TheRestOfString + ")";
        }
    }

    public class VkScrapArtLogItem : IFriendGetDto
    {
        private readonly VkScrapArtLogDto _dto;

        public bool IsNew { get; }

        public int DataBlockLength => _dto.DataXmlBlock.Length + (_dto.DataXmlBlock2?.Length ?? 0);

        private void CheckReadOnly()
        {
            if (!IsNew)
                throw new Exception($"This {nameof(VkScrapItemQtyUnit)} is not new so it is readonly.");
        }

        object IFriendGetDto.Friend_GetDto()
        {
            return _dto;
        }

        public VkScrapArtLogItem()
        {
            _dto = new VkScrapArtLogDto();
            IsNew = true;
        }

        public VkScrapArtLogItem(VkScrapArtLogDto dto)
        {
            _dto = dto;
            IsNew = false;
        }

        public string Art
        {
            get => _dto.Art;
            set
            {
                this.CheckReadOnly();
                _dto.Art = value;
            }
        }

        public bool IsArtless
        {
            get => _dto.IsArtless;
            set
            {
                this.CheckReadOnly();
                _dto.IsArtless = value;
            }
        }

        private VkScrapArtLogData _dataCached;
        public VkScrapArtLogData Data
        {
            get
            {
                if (_dataCached == null)
                {
                    if (IsNew)
                    {
                        _dataCached = new VkScrapArtLogData();
                        _dataCached.ActionsGroups = new List<VkScrapArtLogDataActionGroup>();

                        return _dataCached;
                    }

                    _dataCached = _dto.Data;
                }

                return _dataCached;
            }
        }

        public void WriteDataToDto()
        {
            _dto.Data = _dataCached;
        }
    }

    public class VkScrapResult
    {
        public long GenerationId { get; set; }
        public VkScrapItem[] Items { get; set; }
    }
}