using System.IO;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;

using Invictus.Nomenklatura.Web;

using irisdropwebservice.AppConfig;
using irisdropwebservice.Controllers;

using irisdropwebservice.Services.Auth;
using irisdropwebservice.Services.VkScrap.Ins;

namespace irisdropwebservice.Services.VkScrap
{
    public interface IVkScrapFileDownload
    {
        byte[] DownloadFile(string vkFullPhotoId);

        string MakeScrapItemImgPhotoPublic(string vkFullPhotoId);
    }

    public interface IVkScrapGetLast
    {
        VkScrapResult GetAnyLastScrapResult(ScrapReadMode readMode);
    }

    public interface IVkScrapServiceWebApiCallerInternal : IVkScrapGetLast, IVkScrapFileDownload
    {
        long GetRemoteLastCompleteScrapGeneration();

        VkScrapResult GetRemoteScrapResult(long generation, ScrapReadMode scrapReadMode);

        VkScrapArtLogDto GetVkProductChangelog(string art);

        VkScrapArtLogDto[] GetAllVkProductsChangelog();
    }

    public class VkScrapServiceWebApiCaller : IVkScrapServiceWebApiCallerInternal
    {
        public VkScrapServiceWebApiCaller()
        {
        }
        
        public VkScrapResult GetAnyLastScrapResult(ScrapReadMode readMode)
        {
            long lastGeneration = this.GetRemoteLastCompleteScrapGeneration();

            if (lastGeneration <= 0)
                return new VkScrapResult() { GenerationId = -1, Items = Array.Empty<VkScrapItem>() };

            return this.GetRemoteScrapResult(lastGeneration, readMode);
        }

        public long GetRemoteLastCompleteScrapGeneration()
        {
            HttpClient client = this.CreateHttpClient("text/plain");

            string path = WebQueryUtil.GetControllerPath(
                client.BaseAddress.ToString(),
                typeof(IrisDropVkScrapController),
                nameof(IrisDropVkScrapController.GetLastCompleteScrapGeneration)
            );

            HttpResponseMessage response = client.GetAsync(path).GetAwaiter().GetResult();

            string resStr = response.Content.ReadAsStringAsync().GetAwaiter().GetResult();

            return long.Parse(resStr);
        }

        public VkScrapResult GetRemoteScrapResult(long generation, ScrapReadMode scrapReadMode)
        {
            VkScrapItemDto[] res = this.GetRemoteRawScrapResult(generation, scrapReadMode);

            return new VkScrapResult()
            {
                GenerationId = generation,
                Items = res.Select(dto => new VkScrapItem(dto)).ToArray()
            };
        }

        private VkScrapItemDto[] GetRemoteRawScrapResult(long generation, ScrapReadMode scrapReadMode)
        {
            HttpClient client = this.CreateHttpClient("application/json");

            string path = WebQueryUtil.GetControllerPathWithParameters(
                client.BaseAddress.ToString(),
                typeof(IrisDropVkScrapController),
                nameof(IrisDropVkScrapController.GetScrappedItemsFromGeneration),
                ("scrapGenerationId", Convert.ToString(generation)),
                ("readMode", Convert.ToString((byte)scrapReadMode))
            );

            VkScrapItemDto[] response = client.GetFromJsonAsync<VkScrapItemDto[]>(path).GetAwaiter().GetResult();

            return response;
        }

        public byte[] DownloadFile(string vkFullPhotoId)
        {
            HttpClient client = this.CreateHttpClient("application/octet-stream");

            string path = WebQueryUtil.GetControllerPathWithParameters(
                client.BaseAddress.ToString(),
                typeof(IrisDropVkScrapController),
                nameof(IrisDropVkScrapController.DownloadScrapItemImg),
                ("vkFullPhotoId", vkFullPhotoId)
            );

            HttpResponseMessage response = client.GetAsync(path).GetAwaiter().GetResult();

            string strRes = response.Content.ReadAsStringAsync().GetAwaiter().GetResult();
            strRes = strRes.Trim('\"');

            byte[] res = Convert.FromBase64String(strRes);

            return res;
        }

        public VkScrapArtLogDto GetVkProductChangelog(string art)
        {
            HttpClient client = this.CreateHttpClient("application/json");

            string path = WebQueryUtil.GetControllerPathWithParameters(
                client.BaseAddress.ToString(),
                typeof(IrisDropVkScrapController),
                nameof(IrisDropVkScrapController.GetVkProductChangelog),
                ("art", art)
            );

            VkScrapArtLogDto response = client.GetFromJsonAsync<VkScrapArtLogDto>(path).GetAwaiter().GetResult();

            return response;
        }

        public VkScrapArtLogDto[] GetAllVkProductsChangelog()
        {
            HttpClient client = this.CreateHttpClient("application/json");

            string path = WebQueryUtil.GetControllerPathWithParameters(
                client.BaseAddress.ToString(),
                typeof(IrisDropVkScrapController),
                nameof(IrisDropVkScrapController.GetAllVkProductsChangelog)
            );

            VkScrapArtLogDto[] response = client.GetFromJsonAsync<VkScrapArtLogDto[]>(path).GetAwaiter().GetResult();

            return response;
        }

        public string MakeScrapItemImgPhotoPublic(string vkFullPhotoId)
        {
            HttpClient client = this.CreateHttpClient("application/text");

            string path = WebQueryUtil.GetControllerPathWithParameters(
                client.BaseAddress.ToString(),
                typeof(IrisDropVkScrapController),
                nameof(IrisDropVkScrapController.MakeScrapItemImgPhotoPublic),
                ("vkFullPhotoId", vkFullPhotoId)
            );

            HttpResponseMessage response = client.PostAsync(path, new StreamContent(new MemoryStream(0))).GetAwaiter().GetResult();

            string strRes = response.Content.ReadAsStringAsync().GetAwaiter().GetResult();

            if (!response.IsSuccessStatusCode)
            {
                return null;
            }

            return strRes;
        }

        private HttpClient CreateHttpClient(string accept)
        {
            var client = new HttpClient();

            client.BaseAddress = new Uri(RRAppConfig.Application.RemoteThisServiceUrl);
            client.DefaultRequestHeaders.Accept.Clear();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(accept));

            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                BasicAuthenticationDefaults.AUTHENTICATION_SCHEME,
                RRAppConfig.Application.RemoteThisServiceApiKey
            );

            return client;
        }
    }
}