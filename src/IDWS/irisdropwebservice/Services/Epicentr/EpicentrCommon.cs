using System.IO;

using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;

using irisdropwebservice.AppConfig;
using irisdropwebservice.Libs.Epicentr;

namespace irisdropwebservice.Services.Epicentr;

public class EpicentrCommon
{
    public EpicentrCategoriesRoot CategoriesRoot { get; set; }
    
    public EpicentrCommon()
    {
        this.CreateCategorySchemas();
    }
    
    private void CreateCategorySchemas()
    {
        string filePath = Path.Combine(RRAppConfig.FileSystem.FullResourcesDir, "Epicentr", "all_categories.json.zip");

        var deployedResource = new DeployedResource(filePath, InvLog.Logger<DeployedResource>());
        
        using Stream s = deployedResource.GetFile();
        
        CategoriesRoot = new InvJsonSerializer().DeserializeForInternals<EpicentrCategoriesRoot>(s);
    }
}