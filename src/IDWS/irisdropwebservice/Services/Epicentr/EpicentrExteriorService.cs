using System.Threading.Tasks;

using Invictus.Nomenklatura.ExteriorServ;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.AppConfig;
using irisdropwebservice.Services.Chats;
using irisdropwebservice.Services.Epicentr.Ins;

using Telegram.Bot.Types;

namespace irisdropwebservice.Services.Epicentr;

public class EpicentrExteriorService : IExteriorService, ITelegramDialogueHandler, ITelegramGroupListener
{
    private readonly IEpicentrSyncWorker _worker;
    private readonly ILogger _logger = InvLog.Logger<EpicentrExteriorService>();
    private readonly ITelegramSendWorker _telegramSendWorker;
    private readonly EpicentrCommon _epicentrCommon;
    private readonly EpicentrSyncFlowEvent _epicentrSyncFlowEvent;

    public EpicentrExteriorService(IEpicentrSyncWorker worker, ITelegramSendWorker telegramSendWorker, EpicentrCommon epicentrCommon, EpicentrSyncFlowEvent epicentrSyncFlowEvent)
    {
        _worker = worker;
        _telegramSendWorker = telegramSendWorker;
        _epicentrCommon = epicentrCommon;
        _epicentrSyncFlowEvent = epicentrSyncFlowEvent;
    }
    
    public Task Run()
    {
        Task res = _worker.Run(w => w.Initialize());

        _logger.Information("Running.");

        return res;
    }
    
    public TelegramDialogueUpdateHandler CanHandleConversationInitiation(TelegramBotWho who, Update update)
    {
        if (who != TelegramBotWho.Staff)
            return null;

        if (update.Message?.Text == null)
            return null;

        if (update.Message.Chat.FirstName == null)
            return null;

        string messageText = update.Message.Text.Trim();

        return null;
    }

    private static object GetTextCommandCode(string messageText)
    {
        if (messageText == "NULL!TODO")
            return 0;

        return null;
    }
    
    private string ExecuteChatCommand(object commandCode)
    {
        switch (commandCode)
        {
            case 0: 

                return "NULL";
            default: return "?";
        }
    }

    public void BeginHandleUpdate(TelegramDialogue dialogue, Update update, object initialArg)
    {
        Task<string> task = Task.Run(() => this.ExecuteChatCommand(initialArg));

        dialogue.FinishAfterTask(task, new PostToTelegramOptions { DisableWebPagePreview = true, Silent = false, HtmlFormattingEnabled = true });
    }

    public void IncomingMessage(TelegramBotWho who, Update update)
    {
        if (who != TelegramBotWho.Staff)
            return;

        if (update.Message?.Text == null)
            return;

        if (update.Message.Chat.Id != TelegramChatId.FromUnknownChat(CCAppConfig.TelegramIrisDrop.ShopAdminAttentionChat.DefinitionName).ChatId)
            return;

        string messageText = update.Message.Text.Trim();

        object textCommandCode = GetTextCommandCode(messageText);
        
        if (textCommandCode == null)
            return;

        _telegramSendWorker.Do(w => w.PostToTelegramFromStaffAccount(this.ExecuteChatCommand(textCommandCode),
            CCAppConfig.TelegramIrisDrop.ShopAdminAttentionChat,
            new PostToTelegramOptions { DisableWebPagePreview = true, HtmlFormattingEnabled = true, Silent = false }
        ));
    }
    
}