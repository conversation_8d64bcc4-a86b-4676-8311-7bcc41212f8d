using System.Threading.Tasks;

using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.Legacy;

namespace irisdropwebservice.Services.Epicentr.Ins;

public class EpicentrSyncFlowEvent : ResoluteEventBase
{
    protected override ILogger Logger => InvLog.Logger<EpicentrSyncFlowEvent>();

    private readonly IEpicentrSyncWorker _syncWorker;

    public EpicentrSyncFlowEvent(IInvAppLifetime applicationLifetime, InvAppConfig appConfig, IEpicentrSyncWorker siteScanSyncWorker)
        : base(
            applicationLifetime, appConfig,
            TimeSpan.FromMinutes(1),
            TimeSpan.FromMinutes(10),
            ResoluteEventInitialState.CanFireNow
        )
    {
        _syncWorker = siteScanSyncWorker;
    }

    protected override object GetCurrentDefaultArg()
    {
        return null;
    }

    protected override Task OnTrigger(object arg)
    {
        return _syncWorker.Run(w => w.SyncOrders());
    }
}