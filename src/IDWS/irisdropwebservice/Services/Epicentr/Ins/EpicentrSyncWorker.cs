using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.IO;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Xml.Linq;

using Bukimedia.PrestaSharp.Entities;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.AppConfig;
using irisdropwebservice.AppConfig.ClassConfigurations;
using irisdropwebservice.AppConfig.RemoteResources;

using Invictus.Nomenklatura.Workers;
using irisdropwebservice.Libs.Epicentr;
using irisdropwebservice.Libs.PrestaSharp;
using Invictus.Nomenklatura.Misc;
using Invictus.Nomenklatura.Web;

using irisdropwebservice.Services.Chats;
using irisdropwebservice.Services.Epicentr.Remote;
using irisdropwebservice.Services.LinkService;
using irisdropwebservice.Services.PrestaSync;
using irisdropwebservice.Services.PrestaSync.Ins;
using irisdropwebservice.Services.PrestaSync.ServiceConductors;
using irisdropwebservice.Services.VkScrap;


using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Primitives;

using Serilog.Events;

namespace irisdropwebservice.Services.Epicentr.Ins;


public interface IEpicentrSyncWorker: IWorker<IEpicentrSyncWorker>
{
    void Initialize();

    [CanFail(50, LogLevel = LogEventLevel.Error)]
    void SyncProducts(ExternalSyncData prestaSyncData);
    
    [CanFail(2, LogLevel = LogEventLevel.Error)]
    void SyncOrders();
}

public class EpicentrSyncWorker : IWorkerImpl<IEpicentrSyncWorker>, IEpicentrSyncWorker
{
    public ILogger Log { get; } = InvLog.Logger<EpicentrSyncWorker>();

    public WorkerCore Core { get; set; }
    public IEpicentrSyncWorker PublicInterface { get; set; }

    public WorkerConfiguration WorkerConfiguration { get; } = new(
        "EPICEN",
        new WorkerConfiguration.Thread("EpiCentr", ThreadPriority.Normal, IsBackground: false),
        LogEventLevel.Information,
        AllowDirectCall: false
    );
    
    private readonly EpicentrPreparation _preparation;
    private readonly IPrestaProductSyncPublic _prestaProductSyncPublic;
    private readonly GlobalUserErrorsManager _globalUserErrorsManager;
    private readonly InvProductFeatureCentre _invProductFeatureCentre;
    private readonly IVkScrapFileDownload _vkScrapFileDownload;
    private readonly IPrestaWorkerPublic _prestaWorker;
    private readonly ITelegramSendWorker _telegramSendWorker;
    private readonly IHostApplicationLifetime _applicationLifetime;
    private readonly IEpicentrApiWorker _epicentrApiWorker;

    private ProductAttributesConductorService _productAttributes;
    
    private volatile bool _initializing;

    public EpicentrSyncWorker(
        EpicentrPreparation preparation,
        IPrestaProductSyncPublic prestaProductSyncPublic,
        GlobalUserErrorsManager globalUserErrorsManager,
        InvProductFeatureCentre invProductFeatureCentre,
        IVkScrapFileDownload vkScrapFileDownload,
        IPrestaWorkerPublic prestaWorker,
        ITelegramSendWorker telegramSendWorker,
        IHostApplicationLifetime applicationLifetime,
        IEpicentrApiWorker epicentrApiWorker
    )
    {
        _preparation = preparation;
        _prestaProductSyncPublic = prestaProductSyncPublic;
        _globalUserErrorsManager = globalUserErrorsManager;
        _invProductFeatureCentre = invProductFeatureCentre;
        _vkScrapFileDownload = vkScrapFileDownload;
        _prestaWorker = prestaWorker;
        _telegramSendWorker = telegramSendWorker;
        _applicationLifetime = applicationLifetime;
        _epicentrApiWorker = epicentrApiWorker;

        _prestaProductSyncPublic.DidPush += this.PrestaProductSyncPublicOnDidPush;
    }

    private void PrestaProductSyncPublicOnDidPush(object sender, DidPushEventArgs e)
    {
        PublicInterface.Run(w => w.SyncProducts(e.PrestaSyncData));
    }
    
    [SuppressMessage("ReSharper", "ConvertToLambdaExpression")]
    public void Initialize()
    {
        _initializing = true;
        
        // _preparation.ConvertEpiAttrsToInternalFormat();
        
        // this.FetchApiCategories();
    }
    
    private void FetchApiCategories()
    {
        CancellationToken ct = _applicationLifetime.ApplicationStopping;

        int page = 0;
        int pagesTotal;

        List<EpicentrApiCategoriesItem> allCategories = new(1024);
        WebResponse<EpicentrApiCategoriesRoot, EpicentrFailResponse> thisCats = default;
        
        do
        {
            thisCats = _epicentrApiWorker.GetCategories(page, ct);
            page++;
            
            if (thisCats.IsFail)
                throw new Exception("fail");

            pagesTotal = thisCats.Success.Pages;
            
            allCategories.AddRange(thisCats.Success.Items);
        } while (page < pagesTotal && thisCats.Success.Items.Count != 0);

        string res = new InvJsonSerializer().SerializeForInternals(allCategories, allCategories.GetType());
        
        File.WriteAllText(Path.Combine(FileSystemRRInfo.ExeResourcesDir, "Epicentr", "api_all_categories.json"), res);
        
        Log.Warning("Attrs done.");
    }
    
    [CanFail(2, LogLevel = LogEventLevel.Error)]
    public void SyncOrders()
    {
        if (!RRAppConfig.EpicentrIrisDrop.DoWork)
            return;
        
        WebResponse<EpicentrTotalOrdersResponse, EpicentrFailResponse> ordersResponse = _epicentrApiWorker.GetNewOrders(_applicationLifetime.ApplicationStopping);
        
        if (ordersResponse.IsFail)
            throw new Exception(ordersResponse.Fail.ToString());
        
        TelegramChatConfiguration chatId = CCAppConfig.TelegramIrisDrop.NewOrdersChat;

        bool newOrders = ordersResponse.Success.Total != 0;

        if (!newOrders)
            return;
        
        _telegramSendWorker.Do(w => w.PostToTelegramFromStaffAccount("Epicentr Нове замовлення",
                chatId,
                new PostToTelegramOptions()
                {
                    DisableWebPagePreview = true,
                    Silent = false
                }
            )
        );
    }

    private bool _tempSentOnce = false;

    public void SyncProducts(ExternalSyncData prestaSyncData)
    {
        if (!RRAppConfig.EpicentrIrisDrop.DoWork)
            return;
        
        if (!_initializing)
        {
            Log.Debug("Sync postponed 10 sec to wait for initialization.");
            /* return */
            Task.Delay(10 * 1000).ContinueWithShortThread(_ => PublicInterface.Run(w => w.SyncProducts(prestaSyncData))).Unwrap(); // TODO: implement task prolongation

            return;
        }

        _productAttributes = prestaSyncData.ProductAttributesConductor;

        List<ExternalSyncProduct> prestaProducts = prestaSyncData.AllPrestaProducts.ToList();

        // Filter: keep only vk products
        prestaProducts.RemoveAll(p => !LinkedNaming.IsPrestaSiteReferenceVkAuthorityArt(p.PrestaProduct.Reference));

        prestaProducts.RemoveAll(p => {
                InvProductCategory cat = _invProductFeatureCentre.GetMainCategory(p.PrestaProduct.Categories);

                if (cat.EpicentrCategoryName == null)
                    return true;

                return false;
            }
        );

        _globalUserErrorsManager.ClearAllProductErrors(GlobalUserErrorSource.EpicentrValidation);

        foreach (ExternalSyncProduct prestaProduct in prestaProducts.ToArray())
        {
            if (!this.ValidateProduct(prestaSyncData, prestaProduct))
            {
                prestaProducts.Remove(prestaProduct);
            }
        }
        
        prestaProducts.RemoveAll(p => {
                InvProductCategory cat = _invProductFeatureCentre.GetMainCategory(p.PrestaProduct.Categories);

                if (cat.EpicentrCategoryCode == -1)
                    return true;

                return false;
            }
        );

        byte[] resBytes = this.CreateExportXml(prestaProducts, prestaSyncData.LangAndTemplateConductor);

        string fileName = "AllProducts_" + DateTime.UtcNow.UtcToUkraineTime().ToStringUADateAndTimeShort() + ".xml";

        if (!_tempSentOnce)
        {
            _telegramSendWorker.Do(w => w.SendAttachFromStaffAccount(
                    CCAppConfig.TelegramIrisDrop.DevChat,
                    resBytes,
                    fileName,
                    "Товар для EpicentrK (TEST)"
                )
            );

            _tempSentOnce = true;
        }
    }

    private string GetTempImageUrl(ProductVariant variant)
    {
        string vkFullPhotoId = _prestaWorker.Do(w => w.GetMainVkPhotoIdFromProductId(variant.Product.ProductId));

        string imageReference = _vkScrapFileDownload.MakeScrapItemImgPhotoPublic(vkFullPhotoId);

        return $"https://ws.irisdrop.com.ua/res/get/img/vf/{imageReference}";
    }
    
    private byte[] CreateExportXml(List<ExternalSyncProduct> prestaProducts, LangAndTemplateConductorService langAndTemplateConductor)
    {
        ProductVariant[] allProductVariants = prestaProducts
            .SelectMany(p => p.ProductOptions.Select(po => new ProductVariant(p.PrestaProduct, po, _productAttributes)))
            .ToArray();
        
        var doc = new XDocument();
        var root = new XElement("yml_catalog");
        doc.Add(root);
        root.SetAttributeValue("date", "{" + DateTime.UtcNow.UtcToUkraineTime().ToString("yyyy-MM-dd hh:mm") + "}");
        var rootOffers = new XElement("offers");
        root.Add(rootOffers);

        foreach (ProductVariant variant in allProductVariants)
        {
            try
            {
                this.AddXmlVariant(langAndTemplateConductor, rootOffers, variant);
            }
            catch (Exception exc)
            {
                Log.Warning("For variant " + variant.Product);
                Log.Warning(exc.ToString());

                throw;
            }
        }

        var ms = new MemoryStream();
        doc.Save(ms, SaveOptions.None);
        ms.Position = 0;
        
        return ms.ToArray();
    }

    private string CreateEpicentrName(ProductVariant variant)
    {
        InvProductCategory invCategory = _invProductFeatureCentre.GetMainCategory(variant.Product.Categories);
        
        InvProductFeature colorFeature = invCategory.Features
            .Where(f => f.Platform == InvPlatform.Epicentr && f.Name.Contains("Колір виробника"))
            .SingleWith("Колір виробника" + variant);
        InvProductFeature fabricFeature = invCategory.Features
            .Where(f => f.Platform == InvPlatform.Epicentr && f.Name.EndsWith("Матеріал", StringComparison.InvariantCultureIgnoreCase))
            .SingleWith("Матеріал " + variant);

        ProductFeatureAndValueId[] colorFeaturesId = variant.Product.Features.Where(pfv => _productAttributes.GetFeatureText(pfv.FeatureId) == colorFeature.Name).ToArray();
        ProductFeatureAndValueId[] fabricFeaturesId = variant.Product.Features.Where(pfv => _productAttributes.GetFeatureText(pfv.FeatureId) == fabricFeature.Name).ToArray();

        if (colorFeaturesId.Length == 0)
            throw new Exception($"Product {variant.Product.Reference} should have color");
        if (fabricFeaturesId.Length == 0)
            throw new Exception($"Product {variant.Product.Reference} should have Матеріал set");

        string colorFeatureValue = string.Join(" ", colorFeaturesId.Select(pf => _productAttributes.GetFeatureValueText(pf.FeatureValueId)).ToArray());
        string fabricFeatureValue = string.Join(" ", fabricFeaturesId.Select(pf => _productAttributes.GetFeatureValueText(pf.FeatureValueId)).ToArray());
        
        var bdr = new StringBuilder();
        bdr.Append(invCategory.EpicentrCategoryNameSingular);
        bdr.Append(" ");
        bdr.Append(fabricFeatureValue);
        bdr.Append(" ");
        bdr.Append(variant.Option.AttributeOptions.Single().Name);
        bdr.Append(" ");
        bdr.Append(colorFeatureValue);

        return bdr.ToString();
    }

    private void AddXmlVariant(LangAndTemplateConductorService langAndTemplateConductor, XElement rootOffers, ProductVariant variant)
    {
        var offer = new XElement("offer");
        rootOffers.Add(offer);

        InvProductCategory invCategory = _invProductFeatureCentre.GetMainCategory(variant.Product.Categories);
        
        offer.SetAttributeValue("id",        variant.GetExternalId());
        offer.SetAttributeValue("available", variant.GetAvaliXml());

        string name = this.CreateEpicentrName(variant);
        offer.Add(new XElement("name", new XAttribute("lang", "ua"), name));
        
        offer.Add(new XElement("price",     variant.GetPrice()));
        offer.Add(new XElement("price_old", variant.GetOldPrice()));

        offer.Add(new XElement("category",
                new XAttribute("code", invCategory.EpicentrCategoryCode.ToString()),
                invCategory.EpicentrExportCategoryName
            )
        );

        offer.Add(new XElement("picture", this.GetTempImageUrl(variant)));

        string descText = variant.Product.ShortDescriptionHtml.Trim()
                          + Environment.NewLine
                          + Environment.NewLine
                          +
                          langAndTemplateConductor.GetVkSourcedDescriptionWithoutQuantity(
                              variant.Product.DescriptionHtml
                          );
            
        var desc = new XElement("description", new XAttribute("lang", "ua"), descText);
            
        offer.Add(desc);
            
        offer.Add(new XElement("attribute_set",
                new XAttribute("code", invCategory.EpicentrCategoryCode.ToString()),
                invCategory.EpicentrExportCategoryName
            )
        );

        void addFeatureElement(InvProductFeature invFeature, string languageCode, (string, string) v)
        {
            var epicentrFeatureEntry = (EpicentrFeatureEntry)invFeature.PlatformData;
                
            var featureElement = new XElement("param");
            offer.Add(featureElement);
                
            featureElement.SetAttributeValue("paramcode", epicentrFeatureEntry.AttributeCode);
            featureElement.SetAttributeValue("name",      epicentrFeatureEntry.FeatureName);

            if (invFeature.Type == InvProductFeatureType.FreeText)
            {
                if (languageCode != null)
                    featureElement.SetAttributeValue("lang", languageCode);
                    
                featureElement.SetValue(v.Item1);
            } else
            {
                featureElement.SetAttributeValue("valuecode", v.Item2);
                featureElement.SetValue(v.Item1);
            }
        }
        
        foreach (long featureId in variant.Product.Features.Select(pfv => pfv.FeatureId).Distinct())
        {
            string featureText = _productAttributes.GetFeatureText(featureId);
            
            if (featureText == InvProductFeatureCentre.EPICENTR_NAME_FEATURE)
                continue;

            InvProductFeature invFeature = GetInvProductFeature(featureText, invCategory);
                
            if (invFeature == null)
                continue;
            
            (string, string) v = variant.GetFeatureValue(invFeature);
            
            /*if (((EpicentrFeatureEntry)invFeature.PlatformData).AttributeCode == "country_of_origin")
                {
                    var featureElement = new XElement("country_of_origin");
                    offer.Add(featureElement);
                    
                    featureElement.SetAttributeValue("code", v.Item2);
                    featureElement.SetValue(v.Item1);
                } else*/
            {
                addFeatureElement(invFeature, null, v);
            }
        }

        foreach (InvProductFeature invFeature in invCategory.HiddenFeatures)
        {
            var epiFeatureEntry = (EpicentrFeatureEntry)invFeature.PlatformData;

            (string, string)? featureValue = epiFeatureEntry.AttributeCode switch
            {
                "ratio" => ("1", null),
                "brand" => ("Інше", "827b4a70220f11ea918e001e67ecc97b"),
                    
                // Розмір, Розмір (Міжнародний), Зріст
                "11972" or "8076" or "5575" => variant.GetOptionValue(invFeature),
                    
                "7286" => (variant.GetManufacturerSize(), null),
                _      => null
            };

            if (featureValue == null || string.IsNullOrWhiteSpace(featureValue.Value.Item1))
                continue;
                
            addFeatureElement(invFeature, null, featureValue.Value);
        }
    }

    private bool ValidateProduct(ExternalSyncData prestaSyncData, ExternalSyncProduct product)
    {            
        InvProductCategory mainCategory = _invProductFeatureCentre.GetMainCategory(product.PrestaProduct.Categories);

        if (!mainCategory.IsExportableCategory)
            throw new Exception($"Should be an exportable category. {product.PrestaProduct.Reference}");

        (product_feature, InvProductFeature)[] requiredFeatures =
            mainCategory.Features
                .Where(f => f.Platform == InvPlatform.Epicentr && f.IsMandatory)
                .Select(f => (prestaSyncData.ProductAttributesConductor.GetProductFeature(f.Name), f))
                .ToArray();
        
        var errors = new List<string>();
        
        foreach ((product_feature, InvProductFeature) requiredFeature in requiredFeatures)
        {
            ProductFeatureAndValueId currentProductFeature = product.PrestaProduct.Features.FirstOrDefault(f => f.FeatureId == requiredFeature.Item1.id);

            if (currentProductFeature.FeatureId == 0 || currentProductFeature.FeatureValueId == 0)
            {
                errors.Add($"Х-ка '<b>{requiredFeature.Item2.Name}</b>' пуста" + (requiredFeature.Item2.Type == InvProductFeatureType.Multiselect ? " (можна кілька значень)" : ""));
                continue;
            }
        }

        if (errors.Count != 0)
        {
            _globalUserErrorsManager.SetFlushableProductError(GlobalUserErrorSource.EpicentrValidation,
                product.PrestaProduct.Reference,
                string.Join(",\n", errors)
            );

            return false;
        }

        foreach (IGrouping<long,ProductFeatureAndValueId> group in product.PrestaProduct.Features.GroupBy(f => f.FeatureId))
        {
            long featureId = group.Key;
            
            string featureText = prestaSyncData.ProductAttributesConductor.GetFeatureText(featureId);

            InvProductFeature invFeature = GetInvProductFeature(featureText, mainCategory);
            
            if (invFeature == null)
                continue;

            if (group.Count() > 1 && invFeature.Type != InvProductFeatureType.Multiselect)
            {
                errors.Add($"Допустиме лише одне значення характеристики '<b>{invFeature.Name}</b>'");
                continue;
            }
            
            var epiFeature = (EpicentrFeatureEntry)invFeature.PlatformData;

            if (invFeature.Type == InvProductFeatureType.Select || invFeature.Type == InvProductFeatureType.Multiselect)
            {
                foreach (ProductFeatureAndValueId productFeatureAndValueId in group)
                {
                    string featureValueText = prestaSyncData.ProductAttributesConductor.GetFeatureValueText(productFeatureAndValueId.FeatureValueId);

                    if (!epiFeature.FeatureOptions.Any(fo => fo.OptionName.Equals(featureValueText, StringComparison.InvariantCultureIgnoreCase)))
                    {
                        // Log.Warning($"Wrong opt: {featureValueText} All: " + epiFeature.FeatureOptions.Aggregate("", (s, option) => s + option.OptionName + " "));
                        errors.Add($"Недопустиме значення характеристики '<b>{invFeature.Name}</b>'");
                        continue;
                    }
                }
            }
        }

        if (errors.Count != 0)
        {
            _globalUserErrorsManager.SetFlushableProductError(GlobalUserErrorSource.EpicentrValidation,
                product.PrestaProduct.Reference,
                string.Join(",\n", errors)
            );

            return false;
        }

        return true;
    }

    private static InvProductFeature GetInvProductFeature(string featureText, InvProductCategory mainCategory)
    {
        InvProductFeature invFeature = null;

        if (featureText == "Epicentr Сезон") // It contains duplicates lol
        {
            invFeature = mainCategory.Features
                .Where(f => f.Platform == InvPlatform.Epicentr && ((EpicentrFeatureEntry)f.PlatformData).AttributeCode == "2295")
                .SingleOrDefaultWith(featureText);
        }
        
        if (invFeature == null)
        {
            invFeature = mainCategory.Features
                .Where(f => f.Platform == InvPlatform.Epicentr && f.Name == featureText)
                .SingleOrDefaultWith(featureText);
        }

        return invFeature;
    }

    private readonly record struct ProductVariant(ProductInfo Product, ProductOption Option, ProductAttributesConductorService ProductAttributesService)
    {
        public string GetExternalId()
        {
            AttributeOption ae = Option.AttributeOptions.Single();

            string productOptionValueName = ae.Name;

            return LinkedNaming.ExternalSiteUniqueSkuIdFromVkAuthorityArt(Product.Reference, productOptionValueName);
        }

        public string GetAvaliXml()
        {
            return Option.Quantity < 1 ? "false" : "true";
        }

        public string GetPrice()
        {
            double price = ((double)Product.Price - CCAppConfig.PrestaIrisDrop.NonDropShippersPriceIncreaseUAH + RRAppConfig.Kasta.PriceIncrease) * 1.2;

            return ((int)Math.Floor(price)).ToString(CultureInfo.InvariantCulture);
        }

        public string GetOldPrice()
        {
            return "";
        }

        public string GetManufacturerSize()
        {
            AttributeOption ae = Option.AttributeOptions.Single();
            string productOptionValueName = ae.Name;

            return productOptionValueName;
        }
        
        public (string, string)? GetOptionValue(InvProductFeature invFeature)
        {
            if (invFeature.Type != InvProductFeatureType.Select && invFeature.Type != InvProductFeatureType.Multiselect)
                throw new Exception($"{invFeature.Name} should be of 'Select' or 'Multiselect' type");
            
            AttributeOption ae = Option.AttributeOptions.Single();
            string productOptionValueText = ae.Name;
            
            if (productOptionValueText.Length >= 3 && short.TryParse(productOptionValueText[..3], out short _))
            {
                productOptionValueText = productOptionValueText[..3];
            }
            
            var platformData = (EpicentrFeatureEntry) invFeature.PlatformData;

            EpicentrFeatureOption epiFeatureOption = platformData.FeatureOptions
                .Where(fo => fo.OptionName == productOptionValueText)
                .Take(1) // Epicentr is that bad
                .SingleOrDefault();

            if (epiFeatureOption == null)
                return null;
            
            return (productOptionValueText, epiFeatureOption.OptionCode);
        }
        
        public (string, string) GetFeatureValue(InvProductFeature invFeature)
        {
            ProductAttributesConductorService pas = ProductAttributesService;
            
            ProductFeatureAndValueId[] featuresWithValues = Product.Features
                .Where(f => invFeature.Name == pas.GetFeatureText(f.FeatureId))
                .ToArray();

            var bdr1 = new StringBuilder();
            var bdr2 = new StringBuilder();
            
            foreach (ProductFeatureAndValueId featureWithValueId in featuresWithValues)
            {
                string featureValueText = ProductAttributesService.GetFeatureValueText(featureWithValueId.FeatureValueId);
                bdr1.Append(featureValueText);
                bdr1.Append(",");

                var platformData = invFeature.PlatformData as EpicentrFeatureEntry;

                if (platformData != null && invFeature.Type != InvProductFeatureType.FreeText)
                {
                    EpicentrFeatureOption epiFeatureOption = platformData.FeatureOptions
                        .Where(fo => fo.OptionName == featureValueText)
                        .Take(1) // Epicentr is that bad
                        .Single2<EpicentrFeatureOption, Exception>(
                            $"Could not find epi feature value {featureValueText}",
                            $"More than one feature option found for {featureValueText}"
                        );
                    
                    bdr2.Append(epiFeatureOption.OptionCode);
                    bdr2.Append(",");
                }
            }

            if (bdr1.Length != 0)
            {
                bdr1.Length--;
            }
            if (bdr2.Length != 0)
            {
                bdr2.Length--;
            }

            return (bdr1.ToString(), bdr2.ToString());
        }
    }
}