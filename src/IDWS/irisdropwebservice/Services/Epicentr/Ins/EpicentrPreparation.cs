using System.Globalization;
using System.IO;
using System.Runtime.CompilerServices;
using System.Text;

using CsvHelper;
using CsvHelper.Configuration;

using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;

using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;

using irisdropwebservice.AppConfig;
using irisdropwebservice.AppConfig.RemoteResources;
using irisdropwebservice.Libs.Epicentr;

namespace irisdropwebservice.Services.Epicentr.Ins;

public class EpicentrPreparation
{
    private static readonly CsvClassMap _CsvClassMap = new();
    
    private readonly ILogger _logger = InvLog.Logger<EpicentrPreparation>();
    private readonly InvJsonSerializer _jsonSerializer = new InvJsonSerializer();

    public void ConvertEpiAttrsToInternalFormat()
    {
        _logger.Warning("ConvertEpiAttrsToInternalFormat");
        
        this.ConvertEpiAttrsToInternalFormat1();
        this.ConvertEpiAttrsToInternalFormat2();
        
        _logger.Warning("ConvertEpiAttrsToInternalFormat DONE");
    }

    private void ConvertEpiAttrsToInternalFormat2()
    {
        string[] filePaths = Directory.GetFiles(Path.Combine(RRAppConfig.FileSystem.FullResourcesDir, "Temp", "Epicentr"), "export-attribute*.csv");
        
        var root = new EpicentrCategoriesRoot();
        
        for (int i = 0; i < filePaths.Length; i++)
        {
            var ms = new MemoryStream();

            using (var fs = new FileStream(filePaths[i], FileMode.Open))
            {
                fs.CopyTo(ms);
            }

            ms.Position = 0;

            var sr = new StreamReader(ms);
            
            var csr = new CsvReader(sr, new CsvConfiguration(CultureInfo.InvariantCulture) { Delimiter = ";,;", Encoding = Encoding.UTF8 });

            csr.Context.RegisterClassMap<CsvClassMap>();

            List<EpicentrRawProductFeatureEntry> records = new();
            
            csr.Read();
            csr.ReadHeader();
            while (csr.Read())
            {
                var record = csr.GetRecord<EpicentrRawProductFeatureEntry>();
                records.Add(record);
            }
            
            List<EpicentrFeatureEntry> featureEntries = this.ConvertRawToFeatureEntries(records);

            string categoryName = Path.GetFileNameWithoutExtension(filePaths[i]);
            categoryName = categoryName.Substring(categoryName.LastIndexOf("_", StringComparison.InvariantCulture) + 1);

            var categoryEntry = new EpicentrCategoryEntry
            {
                Name = categoryName,
                Features = featureEntries
            };
            
            root.CategoryEntries.Add(categoryEntry);
        }

        string epicentrCategoriesJson = _jsonSerializer.SerializeForInternals(root, root.GetType());

        string jsonFilePath = Path.Combine(FileSystemRRInfo.ExeResourcesDir, "Epicentr", "all_categories.json");
        File.WriteAllText(jsonFilePath, epicentrCategoriesJson);
    }

    private List<EpicentrFeatureEntry> ConvertRawToFeatureEntries(IList<EpicentrRawProductFeatureEntry> entries)
    {
        var res = new List<EpicentrFeatureEntry>();
        
        foreach (IGrouping<int,EpicentrRawProductFeatureEntry> group in entries.GroupBy(e => e.Id))
        {
            EpicentrRawProductFeatureEntry first = group.First();

            var fe = new EpicentrFeatureEntry
            {
                FeatureName = first.Name,
                FeatureId = first.Id,
                FeatureType = first.Type,
                AttributeCode = first.AttributeCode
            };

            if (fe.FeatureType != "text" && fe.FeatureType != "string")
            {
                fe.FeatureOptions = new();
                
                foreach (EpicentrRawProductFeatureEntry rawProductFeatureEntry in group)
                {
                    fe.FeatureOptions.Add(new EpicentrFeatureOption
                        {
                            Prefix = rawProductFeatureEntry.Prefix,
                            Suffix = rawProductFeatureEntry.Suffix,
                            OptionCode = rawProductFeatureEntry.OptionCode,
                            OptionId = rawProductFeatureEntry.OptionId,
                            OptionName = rawProductFeatureEntry.OptionName
                        }
                    );
                }
            }
            
            res.Add(fe);
        }

        return res;
    }
    
    private void ConvertEpiAttrsToInternalFormat1()
    {
        string resourcesDir = Path.Combine(FileSystemRRInfo.ExeResourcesDir, "Epicentr");
        
        string[] resourceFilePaths = Directory.GetFiles(
            resourcesDir,
            "export-attribute*.xlsx",
            SearchOption.TopDirectoryOnly
        );

        if (resourceFilePaths.Length == 0)
            throw new Exception("Epicentr/export-attribute* not found");

        string tempPath = Path.Combine(RRAppConfig.FileSystem.FullResourcesDir, "Temp", "Epicentr");
        
        Directory.CreateDirectory(tempPath);
        
        for (int i = 0; i < resourceFilePaths.Length; i++)
        {
            using Stream stream = File.OpenRead(resourceFilePaths[i]);
            
            string filePath = Path.Combine(tempPath, Path.GetFileNameWithoutExtension(resourceFilePaths[i]) + ".csv");
            ConvertXlsxToCsv(stream, filePath);
        }
    }
    
    [Serializable]
    // ReSharper disable once ClassNeverInstantiated.Global
    public class EpicentrRawProductFeatureEntry
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public string AttributeCode { get; set; }
        public int? OptionId { get; set; }
        public string OptionName { get; set; }
        public string OptionCode { get; set; }
        public string Suffix { get; set; }
        public string Prefix { get; set; }
    }

    public class CsvClassMap : ClassMap<EpicentrRawProductFeatureEntry>
    {
        public CsvClassMap()
        {
            this.ApplyMapping();
        }

        private void ApplyMapping()
        {
            this.Map(m => m.Id).Index(0).Name("ID");
            this.Map(m => m.Name).Index(1).Name("Назва");
            this.Map(m => m.Type).Index(2).Name("Тип");
            this.Map(m => m.OptionId).Index(3).Name("ID опції");
            this.Map(m => m.OptionName).Index(4).Name("Назва опції");
            this.Map(m => m.AttributeCode).Index(5).Name("Код атрибута");
            this.Map(m => m.OptionCode).Index(6).Name("Код опції");
            this.Map(m => m.Suffix).Index(7).Name("Суфікс");
            this.Map(m => m.Prefix).Index(8).Name("Префікс");
        }
    }

    public static void ConvertXlsxToCsv(Stream xlsxFilePath, string csvFilePath)
    {
        // Open the spreadsheet document
        using (SpreadsheetDocument document = SpreadsheetDocument.Open(xlsxFilePath, false))
        {
            // Get the workbook part of the document
            WorkbookPart workbookPart = document.WorkbookPart;
            
            SharedStringItem[] ssi = null;
            
            SharedStringTablePart sharedStringTablePart = workbookPart.SharedStringTablePart;
            
            if (sharedStringTablePart != null)
            {
                ssi = sharedStringTablePart.SharedStringTable.Elements<SharedStringItem>().ToArray();
            }
            
            // Get the first worksheet
            Sheet sheet = workbookPart.Workbook.Sheets.Elements<Sheet>().FirstOrDefault();

            if (sheet == null)
            {
                throw new Exception("No sheet found.");
            }

            // Get the worksheet part
            var worksheetPart = (WorksheetPart)workbookPart.GetPartById(sheet.Id);

            // Get the sheet data
            SheetData sheetData = worksheetPart.Worksheet.Elements<SheetData>().FirstOrDefault();

            if (sheetData == null)
            {
                throw new Exception("No data found in the sheet.");
            }

            // Open the CSV file for writing
            using (var sw = new StreamWriter(csvFilePath, false, Encoding.UTF8, 1024 * 1024))
            {
                sw.AutoFlush = false;
                sw.Flush();
                
                int r = 0;
                
                var csvRow = new StringBuilder();
                
                // Loop through each row in the sheet data
                foreach (Row row in sheetData.Elements<Row>().ToArray())
                {
                    if (r++ % 5000 == 0)
                        sw.Flush();

                    csvRow.Clear();

                    // Loop through each cell in the row
                    Cell[] cells = row.Elements<Cell>().ToArray();
                    
                    foreach (Cell cell in cells.SkipLast(1))
                    {
                        string cellValue = GetCellValue(cell, ssi);
                        csvRow.Append(cellValue);
                        csvRow.Append(";,;");
                    }

                    if (cells.Length > 0)
                    {
                        string cellValue = GetCellValue(cells.Last(), ssi);
                        csvRow.Append(cellValue);
                    }
                    
                    if (csvRow.Length > 0)
                    {
                        sw.WriteLine(csvRow.ToString());
                    }
                }
                
                sw.Flush();
            }
        }
    }

    // Get the value of a cell, checking the data type
    
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    private static string GetCellValue(Cell cell, SharedStringItem[] ssi)
    {
        string value = cell.InnerText;

        // If the cell contains a shared string, get the string from the shared string table
        if (cell.DataType != null && cell.DataType.Value == CellValues.SharedString)
        {
            if (ssi != null)
            {
                value = ssi.ElementAt(int.Parse(value)).InnerText;
            }
        }
        
        return value;
    }
}