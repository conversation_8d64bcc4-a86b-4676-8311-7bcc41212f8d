using System.Diagnostics;
using System.Text;
using System.Threading;

using Castle.DynamicProxy;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Web;
using Invictus.Nomenklatura.Workers;

using irisdropwebservice.AppConfig;
using irisdropwebservice.Legacy;
using irisdropwebservice.Libs.Epicentr;

using Serilog.Events;

using WebRequestWithRetryResult = irisdropwebservice.Legacy.WebRequestWithRetryResult;

namespace irisdropwebservice.Services.Epicentr.Remote;

[CanFail(LogLevel = LogEventLevel.Verbose)]
public interface IEpicentrApiWorker : IWorker<IEpicentrApiWorker>
{
    WebResponse<EpicentrApiCategoriesRoot, EpicentrFailResponse> GetCategories(int page, CancellationToken ct);
    
    WebResponse<EpicentrTotalOrdersResponse, EpicentrFailResponse> GetNewOrders(CancellationToken ct);
}

public class EpicentrApiWorker :
    IWorkerImpl<IEpicentrApiWorker>,
    IEpicentrApiWorker
{
    public WorkerCore Core { get; set; }
    public ILogger Log { get; } = InvLog.Logger<EpicentrApiWorker>();

    public WorkerConfiguration WorkerConfiguration { get; } = new WorkerConfiguration(
        "EPIAPI",
        new WorkerConfiguration.TaskScheduler("EpicentrApi", 1),
        LogEventLevel.Debug,
        AllowDirectCall: true,
        GetCustomInterceptor: _ => new Interceptor()
    );

    public class Interceptor : IInterceptor
    {
        protected ILogger Log { get; } = InvLog.Logger<Interceptor>();

        public void Intercept(IInvocation invocation)
        {
            WebRequestWithRetryOld.WebCallWithRetry(Log, () => this.ApiCallWrapperWebCallWithRetry(invocation));
        }

        private WebRequestWithRetryResult ApiCallWrapperWebCallWithRetry(IInvocation invocation)
        {
            try
            {
                var sw = Stopwatch.StartNew();

                invocation.Proceed();

                sw.Stop();

                return WebRequestWithRetryResult.Success(null);
            }
            catch (Exception exc)
            {
                Log.Information("EpicentrApiWorker Interceptor exception.");

                RetryAdvice advice = WebRequestRetryExceptionBeHandler.GetWebRequestRetryAdvice(Log, exc);

                if (advice == RetryAdvice.WaitALot)
                    advice = RetryAdvice.ThrowFurther;

                return WebRequestWithRetryResult.FromExceptionAdvice(exc, advice);
            }
        }
    }

    IEpicentrApiWorker IWorkerImpl<IEpicentrApiWorker>.PublicInterface { get; set; }
    
    private readonly JsonRestClient _jsonRestClient;
    private readonly JsonRestClient _merchantJsonRestClient;

    public EpicentrApiWorker(InvTasks threadedTasks)
    {
        _jsonRestClient = new JsonRestClient(threadedTasks, "Epicentr", Encoding.UTF8, "https://api.epicentrm.com.ua/v2/pim/", true);
        _jsonRestClient.SetAuthorizationBearer(RRAppConfig.EpicentrIrisDrop.ApiKey);
        
        _merchantJsonRestClient = new JsonRestClient(threadedTasks,"EpicentrMerchant", Encoding.UTF8, "https://merchant-api.epicentrm.com.ua/", true);
        _merchantJsonRestClient.SetAuthorizationBearer(RRAppConfig.EpicentrIrisDrop.ApiKey);
    }

    public WebResponse<EpicentrApiCategoriesRoot, EpicentrFailResponse> GetCategories(int page, CancellationToken ct)
    {
        // param:
        // paramcode is attributecode
        // name is displayName 
        //  lang="ru"
        // attribute_code from epicen features
        
        List<KeyValuePair<string, string>> kvs = new();
        
        kvs.Add(new KeyValuePair<string, string>("page", page.ToString()));

        WebResponse<EpicentrApiCategoriesRoot, EpicentrFailResponse> r = _jsonRestClient.Get<EpicentrApiCategoriesRoot, EpicentrFailResponse>(
            "categories",
            kvs.ToArray(),
            false,
            ct
        );

        return r;
    }

    public WebResponse<EpicentrTotalOrdersResponse, EpicentrFailResponse> GetNewOrders(CancellationToken ct)
    {
        List<KeyValuePair<string, string>> kvs = new();
        
        kvs.Add(new KeyValuePair<string, string>("filter[statusCode][]", "new"));

        WebResponse<EpicentrTotalOrdersResponse, EpicentrFailResponse> r = _merchantJsonRestClient.Get<EpicentrTotalOrdersResponse, EpicentrFailResponse>(
            "v3/oms/orders/total",
            kvs.ToArray(),
            false,
            ct
        );

        return r;
    }
}