using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.ExteriorServ;

using irisdropwebservice.Services.Epicentr.Ins;
using irisdropwebservice.Services.Epicentr.Remote;

using JetBrains.Annotations;

using Microsoft.Extensions.DependencyInjection;

namespace irisdropwebservice.Services.Epicentr;

[UsedImplicitly]
public class EpicentrExteriorServiceBuilder: ExteriorServiceBuilderBase<EpicentrExteriorService>
{
    protected override void AddConcealedServices(ServiceCollection concealedServiceCollection)
    {
        concealedServiceCollection.AddWorkerSingleton<EpicentrSyncWorker>();
        concealedServiceCollection.AddWorkerSingleton<EpicentrApiWorker>();
        concealedServiceCollection.AddSingleton<EpicentrPreparation>();
        
        concealedServiceCollection.AddSingleton<EpicentrSyncFlowEvent>();

        concealedServiceCollection.AddSingleton<EpicentrCommon>();
        
        this.RegisterDatabaseTypes(concealedServiceCollection);

        base.AddConcealedServices(concealedServiceCollection);
    }
    
    protected override void ExposeConcealedServices()
    {
        this.ExposeSingleton<EpicentrCommon>();
        
        base.ExposeConcealedServices();
    }
}