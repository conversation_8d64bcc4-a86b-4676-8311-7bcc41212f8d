using irisdropwebservice.Libs.PromSharp;
using System.Threading;

using irisdropwebservice.Services.PromSync.Remote;


namespace irisdropwebservice.Services.PromSync.Ins
{
    // WARN: this will store prom deleted products. what do.
    public class PromProductCache
    {
        private readonly IPromSiteWebApiCallsWorker _webApi;
        private readonly Dictionary<string, List<string>> _skuToExternalIds = new();
        private readonly List<PromProduct> _cachedProducts = new();

        private DateTime _lastProductsUpdateStartUtc = DateTime.MinValue;

        public PromProductCache(IPromSiteWebApiCallsWorker webApi)
        {
            _webApi = webApi;
        }

        // WARN: in case of new product option this will return a list of cached ones
        public List<string> GetExternalIdsFromSku(string sku, CancellationToken ct)
        {
            lock (_skuToExternalIds)
            {
                return _skuToExternalIds.GetValueOrDefault(sku);
            }
        }

        public List<PromProduct> FetchProductsAndUpdateCache(CancellationToken ct)
        {
            lock (_skuToExternalIds)
            {
                DateTime lastUpdateUtc = _lastProductsUpdateStartUtc;

                try
                {
                    _lastProductsUpdateStartUtc = DateTime.UtcNow.AddHours(-0.5);

                    // This doesn't work
                    
                    /*
                    List<PromProduct> res = _webApi.Do(w => w.GetAllProducts(lastUpdateUtc, ct));
                    
                    foreach (PromProduct promProduct in res)
                    {
                        int fromCacheIndex = _cachedProducts.IndexOf(p => p.ExternalId == promProduct.ExternalId);

                        if (fromCacheIndex == -1)
                            _cachedProducts.Add(promProduct);
                        else
                            _cachedProducts[fromCacheIndex] = promProduct;
                    }
                    */
                    
                    List<PromProduct> res = _webApi.Do(w => w.GetAllProducts(new DateTime(2023, 1, 1, 1, 1, 1), ct));
                    
                    _cachedProducts.Clear();

                    foreach (PromProduct product in res)
                    {
                        _cachedProducts.Add(product);
                    }

                    _skuToExternalIds.Clear();

                    foreach (string sku in _cachedProducts.Select(p => p.Sku).Distinct())
                    {
                        _skuToExternalIds[sku] = new List<string>(8);
                    }

                    foreach (PromProduct promProduct in _cachedProducts)
                    {
                        _skuToExternalIds[promProduct.Sku].Add(promProduct.ExternalId);
                    }

                    return _cachedProducts;
                }
                catch (Exception)
                {
                    _lastProductsUpdateStartUtc = lastUpdateUtc;

                    throw;
                }
            }
        }
    }
}
