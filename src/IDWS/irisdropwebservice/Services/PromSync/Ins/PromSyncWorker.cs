using System.Threading;
using System.IO;
using System.Threading.Tasks;
using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;
using Bukimedia.PrestaSharp.Entities;

using Microsoft.Extensions.Hosting;

using Serilog.Events;

using CsvHelper;
using CsvHelper.Configuration;

using Invictus.Nomenklatura.Workers;
using irisdropwebservice.Libs.PromSharp;
using irisdropwebservice.Services.PromSync.Remote;
using irisdropwebservice.Services.VkScrap;

using irisdropwebservice.Services.LinkService;
using irisdropwebservice.Services.PrestaSync.Ins;

using Invictus.Nomenklatura.Misc;
using irisdropwebservice.Services.PrestaSync.ServiceConductors;
using irisdropwebservice.AppConfig.RemoteResources;
using irisdropwebservice.AppConfig;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.Libs.PrestaSharp;
using irisdropwebservice.Services.LinkService.Ins;
using irisdropwebservice.Services.Chats;
using irisdropwebservice.Services.PrestaSync;

using Telegram.Bot.Types;

using Enumerable = System.Linq.Enumerable;

namespace irisdropwebservice.Services.PromSync.Ins
{
    public interface IPromSyncWorker : IWorker<IPromSyncWorker>
    {
        [CanFail(3, LogLevel = LogEventLevel.Error)]
        void Sync(ExternalSyncData externalSyncData);

        void Initialize();

        [CanFail(3, LogLevel = LogEventLevel.Error)]
        void SyncSpecificProductsKeywords(string[] skuFrom, string[] skuTo, Action<string> progressText, CancellationToken ct);
    }

    public class PromSyncWorker : ITelegramDialogueHandler,
        IWorkerImpl<IPromSyncWorker>, IPromSyncWorker
    {
        public ILogger Log { get; } = InvLog.Logger<PromSyncWorker>();

        public WorkerCore Core { get; set; }
        public IPromSyncWorker PublicInterface { get; set; }

        public WorkerConfiguration WorkerConfiguration { get; } = new(
            "ROMSNC",
            new WorkerConfiguration.Thread("PromSync", ThreadPriority.Normal, IsBackground: false),
            LogEventLevel.Information,
            AllowDirectCall: false
        );

        private readonly IHostApplicationLifetime _applicationLifetime;
        private readonly IPromSiteWebApiCallsWorker _webApi;
        private readonly IPrestaProductSyncPublic _prestaProductSyncPublic;
        private readonly IVkScrapFileDownload _vkScrapFileDownload;
        private readonly IPrestaWorkerPublic _prestaWorker;
        private readonly IDbAccessFactory<VariousStoredDataDbAccess> _databaseAccessFactory;
        private readonly PromProductCache _productCache;
        private readonly InvProductFeatureCentre _invProductFeatureCentre;
        private readonly GlobalUserErrorsManager _globalUserErrorsManager;
        private readonly ITelegramSendWorker _telegramSendWorker;
        private readonly IPriceManager _priceManager;

        public static PromRRCustomerInfo CustomerConfiguration = RRAppConfig.PromCustomerBestBaby;

        private static readonly CsvClassMap _CsvClassMap = new();

        private readonly InvJsonSerializer _jsonSerializer = new();

        private volatile bool _initialized;
        private bool _lastFillWasNewProducts = false;

        public PromSyncWorker(
            IHostApplicationLifetime applicationLifetime,
            IPromSiteWebApiCallsWorker webApi,
            IPrestaProductSyncPublic prestaProductSyncPublic,
            IPrestaWorkerPublic prestaWorker,
            IVkScrapFileDownload vkScrapFileDownload,
            TelegramChatBotPolling telegramChatBotPolling,
            IDbAccessFactory<VariousStoredDataDbAccess> databaseAccessFactory,
            InvProductFeatureCentre invProductFeatureCentre,
            GlobalUserErrorsManager globalUserErrorsManager,
            ITelegramSendWorker telegramSendWorker,
            IPriceManager priceManager
        )
        {
            _applicationLifetime = applicationLifetime;
            _webApi = webApi;
            _prestaProductSyncPublic = prestaProductSyncPublic;
            _prestaWorker = prestaWorker;
            _vkScrapFileDownload = vkScrapFileDownload;
            _databaseAccessFactory = databaseAccessFactory;
            _invProductFeatureCentre = invProductFeatureCentre;
            _globalUserErrorsManager = globalUserErrorsManager;
            _telegramSendWorker = telegramSendWorker;
            _productCache = new PromProductCache(_webApi);
            _priceManager = priceManager;

            _prestaProductSyncPublic.DidPush += this.PrestaProductSyncPublicOnDidPush;

            telegramChatBotPolling.AddDialogueHandler(this);
        }

        private void PrestaProductSyncPublicOnDidPush(object sender, DidPushEventArgs e)
        {
            PublicInterface.Run(w => w.Sync(e.PrestaSyncData));
        }

        public void Initialize()
        {
            Task.Run(() => {
                    Thread.Sleep(15 * 1000);
                    _initialized = true;
                }
            );
        }

        public void Sync(ExternalSyncData prestaSyncData)
        {
            if (!CCAppConfig.Prom.DoWork)
                return;

            if (prestaSyncData == null)
                return;

            if (!_initialized)
            {
                Log.Debug("Sync postponed 2000 ms to wait for initialization.");
                /* return */
                Task.Delay(2000).ContinueWithShortThread(_ => PublicInterface.Run(w => w.Sync(prestaSyncData))).Unwrap(); // TODO: implement task prolongation

                return;
            }

            if (_applicationLifetime.ApplicationStopping.IsCancellationRequested)
                return;

            if (_prestaProductSyncPublic.IsSiteFillHappening)
            {
                Log.Warning("prestaProductSyncPublic.IsSiteFillHappening, postponing Sync() for 1 min.");

                Task.Delay(TimeSpan.FromMinutes(1)).ContinueWithShortThread(_ => PublicInterface.Run(w => w.Sync(prestaSyncData)));
                return;
            }

            this.SyncInner(prestaSyncData);
        }

        private bool ValidateMatch(ExternalSyncData prestaSyncData, Match match)
        {
            // Validate presta product

            ProductInfo pp = match.PrestaProduct;

            if (pp == null)
                return true;

            if ((pp.Tags?.Length ?? 0) < 1)
            {
                _globalUserErrorsManager.SetFlushableProductError(GlobalUserErrorSource.PrestaValidation, // This should be moved to presta validation later
                    pp.Reference,
                    "Відсутні пошукові запити"
                );
                // match.DesiredAction = DesiredAction.SkipDueToFailedValidation;
                // return false;
            }

            InvProductCategory mainCategory = _invProductFeatureCentre.GetMainCategory(match.PrestaProduct.Categories);

            if (!mainCategory.IsExportableCategory)
                throw new Exception($"Should have an exportable category. {match.PrestaProduct.Reference}");

            (product_feature, InvProductFeature)[] requiredFeatures =
                mainCategory.Features
                    .Where(f => f.Platform == InvPlatform.Prom && f.IsMandatory)
                    .Select(f => (prestaSyncData.ProductAttributesConductor.GetProductFeature(f.Name), f))
                    .ToArray();

            var errors = new List<string>();

            foreach ((product_feature, InvProductFeature) requiredFeature in requiredFeatures)
            {
                ProductFeatureAndValueId currentProductFeature = match.PrestaProduct.Features.FirstOrDefault(f => f.FeatureId == requiredFeature.Item1.id);

                if (currentProductFeature.FeatureId == 0 || currentProductFeature.FeatureValueId == 0)
                {
                    errors.Add($"Х-ка '<b>{requiredFeature.Item2.Name}</b>' пуста" + (requiredFeature.Item2.Type == InvProductFeatureType.Multiselect ? " (можна кілька значень)" : ""));
                    continue;
                }
            }

            if (errors.Count != 0)
            {
                _globalUserErrorsManager.SetFlushableProductError(GlobalUserErrorSource.PromValidation,
                    match.PrestaProduct.Reference,
                    string.Join(",\n", errors)
                );
                match.DesiredAction = DesiredAction.SkipDueToFailedValidation;
                return false;
            }

            IList<(PromAttributeDef, PromAttributeValueDef[])> promFeatureValues =
                this.GetPromAttributeValueDefsFromPrestaProductFeatures(
                    prestaSyncData.ProductAttributesConductor,
                    mainCategory,
                    match.PrestaProduct.Features,
                    true
                );

            foreach ((PromAttributeDef, PromAttributeValueDef[]) promAttrValueDefPair in promFeatureValues)
            {
                if (promAttrValueDefPair.Item2.Length == 0)
                {
                    errors.Add($"Х-ка '<b>Prom {promAttrValueDefPair.Item1.NameUK}</b>' заповнена <b>неправильно</b>");
                }
            }

            if (errors.Count != 0)
            {
                _globalUserErrorsManager.SetFlushableProductError(GlobalUserErrorSource.PromValidation,
                    match.PrestaProduct.Reference,
                    string.Join(",\n", errors)
                );
                match.DesiredAction = DesiredAction.SkipDueToFailedValidation;
                return false;
            }

            return true;
        }

        private IList<(PromAttributeDef, PromAttributeValueDef[])> GetPromAttributeValueDefsFromPrestaProductFeatures(
            ProductAttributesConductorService productAttributesConductor,
            InvProductCategory category,
            IList<ProductFeatureAndValueId> productFeatures,
            bool forValidation
        )
        {
            List<(PromAttributeDef, PromAttributeValueDef[])> res = new();

            foreach (IGrouping<long, ProductFeatureAndValueId> grp in productFeatures.GroupBy(pf => pf.FeatureId))
            {
                string featureText = productAttributesConductor.GetFeatureText(grp.Key);

                InvProductFeature invFeature = category.Features.SingleOrDefault(f => f.Platform == InvPlatform.Prom && f.Name == featureText);

                if (invFeature == null)
                    continue;

                string[] featureValueTexts = grp.Select(pf => productAttributesConductor.GetFeatureValueText(pf.FeatureValueId)).ToArray();

                switch (invFeature.PlatformData)
                {
                    case PromBoolArrayCompositeAttributeDef boolArray:
                        if (forValidation)
                            break;

                        PromAttributeDef[] boolAttrTrues = boolArray.BoolAttributes.Where(s => featureValueTexts.Contains(s.NameUK)).ToArray();

                        foreach (PromAttributeDef boolAttrTrue in boolAttrTrues)
                        {
                            res.Add((boolAttrTrue, new[]
                                {
                                    new PromAttributeValueDef()
                                    {
                                        Id = 0,
                                        NameUK = "Так",
                                        NameRU = "Да"
                                    }
                                })
                            );
                        }

                        break;
                    case PromAttributeDef schemaItem:
                        List<PromAttributeValueDef> promSizes = schemaItem.AttributeValues.Where(s => featureValueTexts.Contains(s.NameUK)).ToList();

                        if (forValidation)
                        {
                            res.Add((schemaItem, promSizes.ToArray()));
                            break;
                        }

                        foreach (PromAttributeValueDef promAttributeValueDef in promSizes.ToArray())
                        {
                            if (promAttributeValueDef.NameUK == "[пусто]")
                                promSizes.Remove(promAttributeValueDef);
                        }

                        if (promSizes.Count != 0)
                        {
                            res.Add((schemaItem, promSizes.ToArray()));
                        }

                        break;
                    default: throw new Exception($"Unknown platform data type {invFeature.PlatformData.GetType().FullName}");
                }
            }



            return res;
        }

        public void SyncInner(ExternalSyncData prestaSyncData)
        {
            CancellationToken ct = _applicationLifetime.ApplicationStopping;

            Log.Debug("SyncInner");

            string importId;

            using (VariousStoredDataDbAccess access = _databaseAccessFactory.CreateAccess())
            {
                importId = access.TryGetValue("PROM_LAST_IMPORT_ID", CustomerConfiguration.CustomerId);
            }

            if (importId != null)
            {
                PromImportStatusResponse status = _webApi.Do(w => w.GetExportStatus(importId, ct));

                if (status.Message != null && status.Message.StartsWith("No import data found", StringComparison.InvariantCultureIgnoreCase))
                {
                    Log.Warning("No import data found");
                } else
                {
                    if (status.Status == "SUCCESS")
                    {
                        Log.Information($"Proceeding. Prev. import status is: " + _jsonSerializer.SerializeForInternals(status, status.GetType()));
                    } else if (status.Status != "PARTIAL" && status.Imported < status.Total)
                    {
                        Log.Warning($"Still waiting for previous export to prom to be completed. {_jsonSerializer.SerializeForInternals(status, status.GetType())}");

                        return;
                    } else
                    {
                        Log.Error($"Prev. import status is improper: " + _jsonSerializer.SerializeForInternals(status, status.GetType()));
                    }
                }
            }

            // 1: Get prom remote products
            Task<List<PromProduct>> fetchPromProducts = Task.Run(() => _productCache.FetchProductsAndUpdateCache(ct));

            if (ct.IsCancellationRequested)
                return;

            List<ExternalSyncProduct> allPrestaProducts = prestaSyncData.AllPrestaProducts.ToList();

            // Filter: keep only vk products
            allPrestaProducts.RemoveAll(p => !LinkedNaming.IsPrestaSiteReferenceVkAuthorityArt(p.PrestaProduct.Reference));

            // Filter: according to customer config
            allPrestaProducts.RemoveAll(p => {
                    InvProductCategory mainCategory = _invProductFeatureCentre.GetMainCategory(p.PrestaProduct.Categories);

                    return !mainCategory.PromGroupId.HasValue;
                }
            );

            // allPrestaProducts = allPrestaProducts.Take(100).ToList();

            List <PromProduct> promSiteProducts = fetchPromProducts.Result;

            Log.Debug("Prom site products: " + promSiteProducts.Aggregate("", (s, p) => s += p.ExternalId + " "));

            foreach (PromProduct promProduct in promSiteProducts)
            {
                if (promProduct.SellingType == "retail")
                    promProduct.Prices = null;
            }

            // Filter: keep only vk->presta products
            promSiteProducts.RemoveAll(p => !LinkedNaming.IsExternalSiteReferenceLinkedToPrestaSourcedFromVk(p.Sku));

            foreach (IGrouping<string, PromProduct> promProducts in promSiteProducts.GroupBy(p => p.ExternalId))
            {
                if (promProducts.Count() > 1)
                {
                    throw new Exception($"Multiple prom products with the same ExtId, at least: {promProducts.Key}");
                }
            }

            // Group by product with sizes
            List<PromProductVariationGroup> promProductGroups = promSiteProducts
                .GroupBy(p => p.VariationGroupId ?? -1)
                .Select(group => new PromProductVariationGroup() { Products = group.ToList() }).ToList();

            // 3: Match remote products to presta products
            IEnumerable<EnumerableMatch.Result<string, ExternalSyncProduct, PromProductVariationGroup>> matches =
                allPrestaProducts.Match(
                    right: promProductGroups,
                    selectKeyLeft: p => string.IsNullOrWhiteSpace(p.PrestaProduct.Reference) ? null : LinkedNaming.ExternalSiteReferenceFromPrestaSourcedVkAuthorityArt(p.PrestaProduct.Reference),
                    selectKeyRight: p => p.Sku
                );

            List<Match> actionItems = matches.Select(
                (EnumerableMatch.Result<string, ExternalSyncProduct, PromProductVariationGroup> r) => {
                    switch (r.Type)
                    {
                        case EnumerableMatch.ResultType.OnlyLeftSingle:
                            // Presta product only
                            return new Match() { ExternalPrestaProduct = r.LeftSingle, DesiredAction = DesiredAction.CreatePromProducts };
                        case EnumerableMatch.ResultType.OnlyRightSingle:
                            // Prom product only

                            return new Match()
                            {
                                PromProductGroup = r.RightSingle,
                                DesiredAction = this.IsProductGroupInactivated(r.RightSingle)
                                    ? DesiredAction.None
                                    : DesiredAction.InactivatePromProduct
                            };
                        case EnumerableMatch.ResultType.OneToOne:
                            // Both
                            return new Match()
                            {
                                ExternalPrestaProduct = r.LeftSingle,
                                PromProductGroup = r.RightSingle,
                                DesiredAction = DesiredAction.UpdateIfNeeded
                            };
                        // Options below are not the most optimal program behavior
                        case EnumerableMatch.ResultType.OnlyLeftMultiple:
                        case EnumerableMatch.ResultType.ManyToMany:
                        case EnumerableMatch.ResultType.LeftMultipleOneRight:
                        case EnumerableMatch.ResultType.OnlyRightMultiple:
                        case EnumerableMatch.ResultType.LeftOneRightMultiple:
                            throw new Exception("The app cannot handle multiple presta or prom products with the same reference/sku/art.");

                        default: throw TypeAbominationException.Enum(typeof(EnumerableMatch.ResultType), r.Type);
                    }

                }
            ).ToList();

            _globalUserErrorsManager.ClearAllProductErrors(GlobalUserErrorSource.PromValidation);

            foreach (Match match in actionItems.ToList())
            {
                if (match.ExternalPrestaProduct == null)
                    continue;

                if (!this.ValidateMatch(prestaSyncData, match))
                    actionItems.Remove(match);
            }

            List<Match> updateProducts = actionItems.Where(m => m.DesiredAction == DesiredAction.UpdateIfNeeded).ToList();
            List<(Match, ProductOption)> toAddNewSizes = new();

            foreach (Match match in updateProducts)
            {
                if (this.NeedCreateNewSizes(match, toAddNewSizes))
                    match.DesiredAction = DesiredAction.CreatePromProducts;
            }

            updateProducts = actionItems.Where(m => m.DesiredAction == DesiredAction.UpdateIfNeeded).ToList();

            bool allowNewProductsOnly;

            List<Match> newProducts = actionItems.Where(m => m.DesiredAction == DesiredAction.CreatePromProducts)
                .Take(100) // Prom doesn't like too many products
                .ToList();

            bool hasNewProducts = newProducts.Count > 0;

            if (_lastFillWasNewProducts)
            {
                allowNewProductsOnly = false;
                _lastFillWasNewProducts = false;
            } else
            {
                allowNewProductsOnly = hasNewProducts;
                _lastFillWasNewProducts = hasNewProducts;

            }

            if (ct.IsCancellationRequested)
                return;

            if (allowNewProductsOnly && newProducts.Count > 0)
            {
                // 5: Create prom products using import function

                Log.Information($"Send new products {newProducts.Count}, sku=" + GetMatchSkuText(newProducts));

                foreach (Match newProduct in newProducts)
                {
                    this.FillNewPromProduct(prestaSyncData.LangAndTemplateConductor, prestaSyncData.ProductAttributesConductor, newProduct);
                }

                string csvText = this.MakeCsv(newProducts, prestaSyncData);

                if (ct.IsCancellationRequested)
                    return;

                if (csvText != null)
                {
                    // 6: Execute import function for new products

                    _telegramSendWorker.Do(w => w.SendAttachFromStaffAccount(
                            CCAppConfig.TelegramIrisDrop.DevChat,
                            Encoding.UTF8.GetBytes(csvText),
                            "PromExportTest.csv",
                            "Експорт"
                        )
                    );

                    importId = _webApi.Do(w => w.ExportProductsToProm(csvText, ct));

                    using (VariousStoredDataDbAccess access = _databaseAccessFactory.CreateAccess())
                    {
                        access.SetValue("PROM_LAST_IMPORT_ID",  CustomerConfiguration.CustomerId, importId);
                        access.SetValue("PROM_LAST_IMPORT_CSV", CustomerConfiguration.CustomerId, csvText);
                    }

                    if (importId != null)
                        Log.Information($"Exporting products to prom, id={importId}");
                }
            }

            if (!allowNewProductsOnly)
            {
                List<Match> deactivateProducts = actionItems.Where(m => m.DesiredAction == DesiredAction.InactivatePromProduct).ToList();

                /*Match[] productsToCheckForFixing = updateProducts.Concat(deactivateProducts).ToArray();

                foreach (Match match in productsToCheckForFixing)
                {
                    this.TryPickUpPromProductsWithUserEditedSku(prestaSyncData.LangAndTemplateConductor, capturedPromProducts, match);
                }*/

                var productsToUpdateTotal = new List<Match>();

                // 7: Edit existing prom products

                foreach (Match updateProduct in updateProducts)
                {
                    if (this.UpdatePromProduct(updateProduct))
                        productsToUpdateTotal.Add(updateProduct);
                }

                // 8: deactivate prom products if possible using import function

                foreach (Match deactivateProduct in deactivateProducts)
                {
                    if (this.DeactivatePromProduct(deactivateProduct))
                        productsToUpdateTotal.Add(deactivateProduct);
                }

                if (productsToUpdateTotal.Count > 0)
                {
                    Log.Information($"Total {productsToUpdateTotal.Count} to send to prom, sku=" + GetMatchSkuText(productsToUpdateTotal));

                    this.DoEditProductsWrapper(
                        productsToUpdateTotal.SelectMany(m => m.PromProductGroup.Products).ToArray(),
                        false,
                        ct
                    );
                }
            }
        }

        private string DoEditProductsWrapper(IList<PromProduct> promProducts, bool includeKeywords, CancellationToken ct)
        {
            // Prepare for EditProducts()

            foreach (PromProduct promProduct in promProducts)
            {
                // 'in_stock' is not allowed
                promProduct.InStock = null;

                promProduct.Presence = promProduct.QuantityInStock is > 0
                    ? "available"
                    : "not_available";
            }

            PromProductEdit[] editProductsArgument = promProducts
                .Select(p => PromProductEdit.FromFullProduct(p, includeKeywords))
                .ToArray();

            // 9: Execute import function for non-new products (csv with reduced field count)
            Dictionary<string, string> errors = _webApi.Do(w => w.EditProducts(editProductsArgument, ct));

            if (errors != null && errors.Any())
            {
                string error = string.Join(" ; ", errors.Select(e => e.Key + " :: " + e.Value));

                Log.Error("Error while editing prom products: " + error);

                string json = _jsonSerializer.SerializeForInternals(editProductsArgument, editProductsArgument.GetType());

                Log.Error("json:\r\n" + json);

                return error;
            } else
            {
                Log.Information($"Updated {promProducts.Select(p => p.Sku).Distinct().Count()} prom products.");

                return null;
            }
        }

        private static string GetMatchSkuText(IList<Match> matches)
        {
            if (matches.Count == 0)
                return "";

            var bdr = new StringBuilder();
            Match last = matches.Last();

            foreach (Match match in matches)
            {
                if (match.PrestaProduct != null)
                {
                    bdr.Append(match.PrestaProduct.Reference);
                    bdr.Append(", ");
                    continue;
                }

                bdr.Append(match.PromProductGroup.Sku);

                if (match != last)
                    bdr.Append(", ");
            }

            return bdr.ToString();
        }

        public static string GetProductOptionValueText(ProductOption productOption)
        {
            AttributeOption ae = productOption.AttributeOptions.Single();

            string productOptionValueName = ae.Name;

            return LinkedNaming.TryRemoveCentimetersFromHeightSize(productOptionValueName);
        }

        private string MakeCsv(IList<Match> newProducts, ExternalSyncData prestaSyncData)
        {
            using var ms = new MemoryStream();
            using var sw = new StreamWriter(ms);
            using var csw = new CsvWriter(sw, CultureInfo.InvariantCulture);

            csw.Context.RegisterClassMap<CsvClassMap>();

            csw.WriteHeader<PromProductCsv>();

            _CsvClassMap.WriteCharacteristicsHeader(csw, 30);

            csw.NextRecord();

            bool any = false;

            foreach (Match newProduct in newProducts)
            {
                InvProductCategory mainCategory = _invProductFeatureCentre.GetMainCategory(newProduct.PrestaProduct.Categories);

                foreach (ProductOption productOption in newProduct.ExternalPrestaProduct.ProductOptions)
                {
                    string productOptionValueName = GetProductOptionValueText(productOption);

                    string externalId = LinkedNaming.ExternalSiteReferenceFromPrestaSourcedVkAuthorityArt(newProduct.PrestaProduct.Reference) + " " + productOptionValueName;

                    var promProductCsv = newProduct.PromProductGroup.Products.SingleOrDefault(p => p.ExternalId == externalId) as PromProductCsv;

                    if (promProductCsv == null)
                        continue;

                    any = true;

                    csw.WriteRecord(promProductCsv);

                    _CsvClassMap.WriteCharacteristicsForProductOption(prestaSyncData.ProductAttributesConductor, mainCategory, csw, promProductCsv, productOption);

                    IList<(PromAttributeDef, PromAttributeValueDef[])> promFeatureValues =
                        this.GetPromAttributeValueDefsFromPrestaProductFeatures(
                            prestaSyncData.ProductAttributesConductor,
                            mainCategory,
                            newProduct.PrestaProduct.Features,
                            false
                        );

                    foreach ((PromAttributeDef, PromAttributeValueDef[]) promAttrValueDefPair in promFeatureValues)
                    {
                        if (promAttrValueDefPair.Item2.Length == 0)
                            continue;

                        string val = string.Join("|", promAttrValueDefPair.Item2.Select(v => v.NameRU));

                        _CsvClassMap.WriteCharacteristicRaw(csw, promAttrValueDefPair.Item1.NameRU, "", val);
                    }

                    csw.NextRecord();
                }
            }

            if (!any)
                return null;

            csw.Flush();
            sw.Flush();

            ms.Position = 0;

            using var sr = new StreamReader(ms);

            return sr.ReadToEnd();
        }

        private void FillNewPromProduct(LangAndTemplateConductorService langAndTemplateConductor,  ProductAttributesConductorService productAttributesConductor, Match match)
        {
            if (match.ExternalPrestaProduct.ProductOptions.Length == 0)
                throw new Exception("ProductOptions.Length == 0 is not supported");

            long productId = match.PrestaProduct.ProductId;

            string vkFullPhotoId = _prestaWorker.Do(w => w.GetMainVkPhotoIdFromProductId(productId));

            match.PromProductGroup.Products.Clear();

            foreach (ProductOption productOption in match.ExternalPrestaProduct.ProductOptions)
            {
                string productOptionValueName = GetProductOptionValueText(productOption);
                string externalId = LinkedNaming.ExternalSiteReferenceFromPrestaSourcedVkAuthorityArt(match.PrestaProduct.Reference) + " " + productOptionValueName;

                PromProduct promProduct = match.PromProductGroup.Products.SingleOrDefault(p => p.ExternalId == externalId);

                if (productOption.Quantity == 0)
                    continue;

                this.CreateNewPromProduct(langAndTemplateConductor, productAttributesConductor, match, productOption, vkFullPhotoId);
            }

            this.UpdatePromProduct(match);
        }

        private int GetPrice(Match match)
        {
            // Check for price override
            float? overridePrice = _priceManager.GetOverridePrice(match.PrestaProduct.Reference, CustomerConfiguration.CustomerId);

            if (overridePrice.HasValue)
                return (int)Math.Ceiling(overridePrice.Value);

            // Original price calculation
            double price = (((int) match.PrestaProduct.Price) - CCAppConfig.PrestaIrisDrop.NonDropShippersPriceIncreaseUAH + CustomerConfiguration.PriceIncrease);
            price *= CustomerConfiguration.PricePercentTop;
            price = Math.Round(Math.Round(price, 0) / 10, 0) * 10;

            return (int)Math.Ceiling(price);
        }

        private void CreateNewPromProduct(
            LangAndTemplateConductorService langAndTemplateConductor,
            ProductAttributesConductorService productAttributesConductor,
            Match match,
            ProductOption productOption,
            string vkFullPhotoId
        )
        {
            InvProductCategory mainCategory = _invProductFeatureCentre.GetMainCategory(match.PrestaProduct.Categories);

            string productOptionValueName = GetProductOptionValueText(productOption);

            string imageReference = _vkScrapFileDownload.MakeScrapItemImgPhotoPublic(vkFullPhotoId);

            string desc = IntStringUtil.ConvertSomeHtmlToText(
                              match.PrestaProduct.ShortDescriptionHtml.Trim()
                          )
                          + Environment.NewLine
                          + Environment.NewLine
                          +
                          langAndTemplateConductor.GetVkSourcedDescriptionWithoutQuantity(
                              IntStringUtil.ConvertSomeHtmlToText(match.PrestaProduct.DescriptionHtml)
                          );

            desc = IntStringUtil.ConvertNewlinesToHtmlBr(desc);

            double price = this.GetPrice(match);

            string manufacturerCountry =
                productAttributesConductor.GetFeatureValueText(
                    match.PrestaProduct
                        .Features
                        .Single(f => productAttributesConductor.GetFeatureText(f.FeatureId) == "Країна виробництва").FeatureValueId
                );

            string fullName = match.PrestaProduct.Name;

            string color = productAttributesConductor.GetFeatureValueText(
                match.PrestaProduct
                    .Features
                    .Single(f => productAttributesConductor.GetFeatureText(f.FeatureId) == "Prom Колір").FeatureValueId
            );

            string fabric = match.PrestaProduct.Features
                .Where(f => productAttributesConductor.GetFeatureText(f.FeatureId).StartsWith("Prom") &&
                            productAttributesConductor.GetFeatureText(f.FeatureId).EndsWith("Тип тканини", StringComparison.InvariantCultureIgnoreCase)
                )
                .Select(f => f.FeatureValueId)
                .Select(f => productAttributesConductor.GetFeatureValueText(f))
                .SingleOrDefault();

            if (!string.IsNullOrWhiteSpace(fabric))
                fabric = " " + fabric;

            string forWho = mainCategory.KastaCategoryId.Value.AffiliationId == 9223 ? " Для Дівчинки" : "";

            var p = new PromProductCsv
            {
                Name = fullName + " " + color + fabric + forWho,
                Sku = LinkedNaming.ExternalSiteReferenceFromPrestaSourcedVkAuthorityArt(match.PrestaProduct.Reference),
                Description = desc,
                SellingType = "retail",
                Price = (int)price,
                Currency = "UAH",

                Group = new PromGroup() { Id = mainCategory.PromGroupId.Value, Name = "" },

                Category = new PromCategory() { Id = mainCategory.PromCategoryId.Value, Caption = mainCategory.PromCategoryLink },

                MainImage = $"https://ws.irisdrop.com.ua/res/get/img/vf/{imageReference}",
                Status = "draft",
                QuantityInStock = productOption.Quantity,
                MeasureUnit = "шт.",

                // VariationBaseId = null,
                VariationGroupId = match.PrestaProduct.ProductId,

                // Manufacturer = manufacturerCountry,
                ManufacturerCountry = manufacturerCountry,

                ProductCurrentLocation = "Черкассы",

                KeywordsUk = string.Join(", ", match.PrestaProduct.Tags)
            };

            p.NameMultilang = new PromNameMultilang();
            p.NameMultilang.Uk = p.Name;
            p.DescriptionMultilang = new PromNameMultilang();
            p.DescriptionMultilang.Uk = p.Description;

            p.ExternalId = p.Sku + " " + productOptionValueName;

            Log.Debug("Presta product: " + DebugUtil.DumpObject(match.PrestaProduct));
            Log.Debug("Prom create product: " + DebugUtil.DumpObject(p));

            match.PromProductGroup.Products.Add(p);
        }

        private bool NeedCreateNewSizes(Match match, List<(Match, ProductOption)> list)
        {
            bool any = false;

            foreach (ProductOption productOption in match.ExternalPrestaProduct.ProductOptions)
            {
                bool hasQuantity = productOption.Quantity > 0;

                if (!hasQuantity) // Prom allows limited amount of products (variations), we don't want to waste space.
                    continue;

                string productOptionValueName = GetProductOptionValueText(productOption);
                string externalId = LinkedNaming.ExternalSiteReferenceFromPrestaSourcedVkAuthorityArt(match.PrestaProduct.Reference) + " " + productOptionValueName;

                PromProduct promProduct = match.PromProductGroup.Products.SingleOrDefault(p => p.ExternalId == externalId);

                if (promProduct != null)
                    continue;

                any = true;

                list.Add((match, productOption));
            }

            return any;
        }

        private bool UpdatePromProduct(Match match)
        {
            bool updatedAny = false;

            double price = this.GetPrice(match);

            foreach (ProductOption productOption in match.ExternalPrestaProduct.ProductOptions)
            {
                string productOptionValueName = GetProductOptionValueText(productOption);
                string externalId = LinkedNaming.ExternalSiteReferenceFromPrestaSourcedVkAuthorityArt(match.PrestaProduct.Reference) + " " + productOptionValueName;

                PromProduct promProduct = match.PromProductGroup.Products.SingleOrDefault(p => p.ExternalId == externalId);

                bool hasQuantity = productOption.Quantity > 0;

                if (promProduct == null)
                {
                    if (hasQuantity)
                    {
                        string e = "Invalid prom products, sku=" +
                                   string.Join(",", match.PromProductGroup.Products.Select(p => p.Sku)) +
                                   " extId=" +
                                   string.Join(",", match.PromProductGroup.Products.Select(p => p.ExternalId)) + "ExternalId " + externalId + " not found.";

                        Log.Error(e);
                    }

                    continue;
                }

                // TODO: TEMP65 check
                string desiredStatus = (!hasQuantity && promProduct.VariationBaseId != promProduct.VariationGroupId) ? "not_on_display" : "on_display";

                hasQuantity = hasQuantity || promProduct.VariationBaseId == promProduct.VariationGroupId;

                Log.Verbose($"PROM QTYS: {promProduct.Sku} {promProduct.ExternalId} {promProduct.QuantityInStock ?? 0} {productOption.Quantity} PRIC: {promProduct.Price} {price}");

                if ((promProduct.QuantityInStock ?? 0) != productOption.Quantity || promProduct.Status != desiredStatus || promProduct.InStock != hasQuantity)
                {
                    promProduct.QuantityInStock = productOption.Quantity;
                    promProduct.InStock = hasQuantity;
                    promProduct.Status = desiredStatus;

                    updatedAny = true;
                }

                if (promProduct.Price == null || Math.Abs((double)promProduct.Price - price) > 0.1)
                {
                    promProduct.Price = price;

                    updatedAny = true;
                }
            }

            return updatedAny;
        }

        private bool DeactivatePromProduct(Match match)
        {
            bool updatedAny = false;

            foreach (PromProduct promProduct in match.PromProductGroup.Products)
            {
                if (((promProduct.QuantityInStock ?? 0) == 0 || promProduct.QuantityInStock == null) &&
                    promProduct.Status == "not_on_display")
                    continue;

                promProduct.QuantityInStock = 0;
                promProduct.InStock = false;
                promProduct.Status = "not_on_display";

                updatedAny = true;
            }

            return updatedAny;
        }

        private bool IsProductGroupInactivated(PromProductVariationGroup group)
        {
            return group.Products.All(p => p.QuantityInStock == 0);
        }

        TelegramDialogueUpdateHandler ITelegramDialogueHandler.CanHandleConversationInitiation(TelegramBotWho whichBot, Update request)
        {
            if (whichBot != TelegramBotWho.Staff)
                return null;
            if (request.Message == null || request.Message.Text == null)
                return null;
            if (request.Message.Chat.Id != CustomerConfiguration.ThisCustomerTelegramId)
                return null;

            string text = request.Message.Text.Trim();

            if (!text.StartsWithEither(StringComparison.InvariantCultureIgnoreCase, "promocopy", "promcopy", "промкопия", "промкопія"))
                return null;

            return (dialogue, update) =>
            {
                Task task = Task.Run(() =>
                            this.PromCopyCommand(
                                update.Message.Text,
                                t => dialogue.Reply(t, new PostToTelegramOptions() { Silent = false })
                            )
                        );

                dialogue.FinishAfterTask(task);
            };
        }

        private Task PromCopyCommand(string commandText, Action<string> progressText)
        {
            CancellationToken ct = _applicationLifetime.ApplicationStopping;

            string[] spl = IntStringUtil.SplitCommand(commandText);

            if (spl.Length < 3)
            {
                progressText("Помилка синтаксису команди.");

                return Task.CompletedTask;
            }

            string[] skuFrom = new string[] { spl[1] };
            string[] skuTo = spl[2..];

            return PublicInterface.Run(w => w.SyncSpecificProductsKeywords(skuFrom, skuTo, progressText, ct));
        }

        public void SyncSpecificProductsKeywords(string[] skuFrom, string[] skuTo, Action<string> progressText, CancellationToken ct)
        {
            string[] skuAll = Enumerable.Concat(skuFrom, skuTo).ToArray();

            List<string>[] allExternalIdsMany = skuAll.Select(sku => _productCache.GetExternalIdsFromSku(sku, ct)).ToArray();

            if (allExternalIdsMany.Any(list => list == null))
            {
                progressText("Будь ласка зачекайте хвилинку-дві...");

                _productCache.FetchProductsAndUpdateCache(ct);
            } else
            {
                progressText("Зчитування товарів в prom...");
            }

            allExternalIdsMany = skuAll.Select(sku => _productCache.GetExternalIdsFromSku(sku, ct)).ToArray();

            if (allExternalIdsMany.Any(list => list == null))
            {
                progressText("Товар не знайдено.");
                return;
            }

            var targetPromProducts = new List<PromProduct>();
            var keywordsToCopy = new List<string>();

            foreach (string externalId in allExternalIdsMany.SelectMany(_ => _))
            {
                PromProduct product = _webApi.Do(w => w.GetProductByExternalId(externalId, ct));

                if (product.ExternalId != externalId)
                {
                    progressText("Неочікувана помилка при обробці товарів.");
                    return;
                }

                if (skuFrom.Contains(product.Sku))
                {
                    IEnumerable<string> keywords = product.Keywords
                        .Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(keyword => keyword.Trim());

                    keywordsToCopy.AddRange(keywords);
                } else
                {
                    targetPromProducts.Add(product);
                }
            }

            foreach (PromProduct targetPromProduct in targetPromProducts)
            {
                IEnumerable<string> keywords = targetPromProduct.Keywords
                    .Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(keyword => keyword.Trim());

                targetPromProduct.Keywords = string.Join(", ", Enumerable.Concat(keywords, keywordsToCopy).Distinct());
            }

            progressText("Оновлення товарів на prom...");

            string error = this.DoEditProductsWrapper(targetPromProducts, true, ct);

            if (error != null)
            {
                progressText("Виникли помилки при оновленні деяких товарів.");
                return;
            }

            progressText("Успіх.");
        }

        public class PromProductVariationGroup
        {
            public string Sku => Products.FirstOrDefault()?.Sku;

            public List<PromProduct> Products { get; set; } = new();
        }

        public class Match
        {
            public PromProductVariationGroup PromProductGroup { get; set; } = new();
            public ExternalSyncProduct ExternalPrestaProduct { get; set; }

            public ProductInfo PrestaProduct => ExternalPrestaProduct?.PrestaProduct;

            public DesiredAction DesiredAction { get; set; }
        }

        public enum DesiredAction
        {
            Invalid = 0,
            None,
            CreatePromProducts,
            InactivatePromProduct,
            UpdateIfNeeded,
            SkipDueToFailedValidation
        }

        public class CsvClassMap : ClassMap<PromProductCsv>
        {
            public CsvClassMap()
            {
                this.ApplyMapping();
            }

            private void ApplyMapping()
            {
                this.Map(m => m.Sku).Index(0).Name("Код_товару");
                this.Map(m => m.Name).Index(1).Name("Назва_позиції");
                this.Map(m => m.NameMultilang.Uk).Index(2).Name("Назва_позиції_укр");
                this.Map(m => m.Keywords).Index(3).Name("Пошукові_запити");
                this.Map(m => m.KeywordsUk).Index(4).Name("Пошукові_запити_укр");
                this.Map(m => m.Description).Index(5).Name("Опис");
                this.Map(m => m.DescriptionMultilang.Uk).Index(6).Name("Опис_укр");

                this.Map(m => m.SellingType).Index(7).Name("Тип_товару")
                    .Convert(new ConvertToString<PromProductCsv>(args => "r"));

                this.Map(m => m.Price).Index(8).Name("Ціна");
                this.Map(m => m.Currency).Index(9).Name("Валюта")
                    .Convert(new ConvertToString<PromProductCsv>(args => "UAH"));

                this.Map(m => m.MeasureUnit).Index(10).Name("Одиниця_виміру")
                    .Convert(new ConvertToString<PromProductCsv>(args => "шт."));

                this.Map(m => m.MinimumOrderQuantity).Index(11).Name("Мінімальний_обсяг_замовлення");

                this.Map(m => m.WholesalePrice).Index(12).Name("Оптова_ціна");
                this.Map(m => m.WholesaleMinQuantity).Index(13).Name("Мінімальне_замовлення_опт");

                this.Map(m => m.MainImage).Index(14).Name("Посилання_зображення");
                this.Map(m => m.InStock).Index(15).Name("Наявність")
                    .Convert(new ConvertToString<PromProductCsv>(args => args.Value.InStock.HasValue && args.Value.InStock.Value ? "!" : "-"));

                this.Map(m => m.QuantityInStock).Index(16).Name("Кількість");
                this.Map(m => m.Group.Id)              .Index(17).Name("Номер_групи");
                this.Map(m => m.Group.Name)            .Index(18).Name("Назва_групи");
                this.Map(m => m.Category.Caption).Index(19).Name("Посилання_підрозділу");
                this.Map(m => m.Unused15).Index(20).Name("Можливість_поставки");
                this.Map(m => m.ShipmentDate).Index(21).Name("Термін_поставки");
                this.Map(m => m.MethodOfPacking).Index(22).Name("Спосіб_пакування");
                this.Map(m => m.MethodOfPackingUk).Index(23).Name("Спосіб_пакування_укр");
                this.Map(m => m.Id).Index(24).Name("Унікальний_ідентифікатор");
                this.Map(m => m.ExternalId).Index(25).Name("Ідентифікатор_товару");
                this.Map(m => m.Category.Id).Index(26).Name("Ідентифікатор_підрозділу");
                this.Map(m => m.Unused14)              .Index(27).Name("Ідентифікатор_групи");
                this.Map(m => m.Manufacturer).Index(28).Name("Виробник");
                this.Map(m => m.ManufacturerCountry).Index(29).Name("Країна_виробник");
                this.Map(m => m.Discount).Index(30).Name("Знижка");
                this.Map(m => m.VariationGroupId).Index(31).Name("ID_групи_різновидів");
                this.Map(m => m.Unused111).Index(32).Name("Особисті_нотатки");
                this.Map(m => m.Unused2).Index(33).Name("Продукт_на_сайті");
                this.Map(m => m.Unused3).Index(34).Name("Термін_дії_знижки_від");
                this.Map(m => m.Unused4).Index(35).Name("Термін_дії_знижки_до");
                this.Map(m => m.Unused112).Index(36).Name("Ціна_від")
                    .Convert(new ConvertToString<PromProductCsv>(args => "-"));

                this.Map(m => m.Discount).Index(37).Name("HTML_заголовок");
                this.Map(m => m.Unused13).Index(38).Name("HTML_заголовок_укр");
                this.Map(m => m.Unused5).Index(39).Name("HTML_опис");
                this.Map(m => m.Unused6).Index(40).Name("HTML_опис_укр");
                this.Map(m => m.Unused7).Index(41).Name("Код_маркування_(GTIN)");
                this.Map(m => m.Unused8).Index(42).Name("Номер_пристрою_(MPN)");
                this.Map(m => m.Unused9).Index(43).Name("Вага,кг");
                this.Map(m => m.Unused11).Index(44).Name("Ширина,см");
                this.Map(m => m.Unused12).Index(45).Name("Висота,см");
                this.Map(m => m.Unused10).Index(46).Name("Довжина,см");
                this.Map(m => m.ProductCurrentLocation).Index(47).Name("Де_знаходиться_товар");
            }

            public void WriteCharacteristicsHeader(CsvWriter w, int count)
            {
                for (int i = 0; i < count; i++)
                {
                    w.WriteField("Назва_Характеристики");
                    w.WriteField("Одиниця_виміру_Характеристики");
                    w.WriteField("Значення_Характеристики");
                }
            }

            public void WriteCharacteristicRaw(CsvWriter w, string name, string measurement, string val)
            {
                w.WriteField(name);
                w.WriteField(measurement);
                w.WriteField(val);
            }

            private static readonly (string, short)[] _SizeHeightToMinHumanAge = new (string, short)[]
            {
                ( "116", 6  ),
                ( "122", 7  ),
                ( "128", 8  ),
                ( "134", 9  ),
                ( "140", 10 ),
                ( "146", 11 ),
                ( "152", 12 ),
                ( "158", 13 ),
                ( "164", 14 ),
                ( "170", 15 )
            };

            public void WriteCharacteristicsForProductOption(ProductAttributesConductorService productAttributesConductor, InvProductCategory mainCategory, CsvWriter w, PromProduct promProduct, ProductOption productOption)
            {
                // Size

                AttributeOption ae = productOption.AttributeOptions.Single();

                string productOptionValueName = GetProductOptionValueText(productOption);
                string productSizeDescription = productAttributesConductor.GetProductSizeDescriptionByProductOptionId(ae.AttributeId);

                InvProductFeature sz1Feature = mainCategory.HiddenFeatures.SingleOrDefault(f => f.Name == "Prom Розмір дитячого одягу");
                InvProductFeature sz2Feature =  mainCategory.HiddenFeatures.SingleOrDefault(f => f.Name == "Prom Розміри дитячої білизни і купальників");

                switch (productSizeDescription)
                {
                    case "Зріст":

                        if (sz1Feature != null)
                        {
                            w.WriteField("Размер детской одежды (по росту)");
                            w.WriteField("см.");
                            w.WriteField(productOptionValueName);
                        } else if (sz2Feature != null)
                        {
                            string sz2FeatureValue =
                                ((PromAttributeDef)sz2Feature.PlatformData)
                                .AttributeValues
                                .SingleOrDefault(av => av.NameUK.Contains(productOptionValueName + " "))
                                .NameRU;

                            w.WriteField("Размеры детского белья и купальников");
                            w.WriteField("");
                            w.WriteField(sz2FeatureValue);
                        } else
                        {
                            throw new Exception($"Prom category {mainCategory.PromCategoryId} has no known Height feature. All hidden features: "
                                                + (string.Join(", ", mainCategory.HiddenFeatures.Select(f => f.Name).ToArray())));
                        }

                        break;
                    default:
                        w.WriteField(productSizeDescription);
                        w.WriteField("");
                        w.WriteField(productOptionValueName);
                        break;
                }

                switch (productSizeDescription)
                {
                    case "Зріст":
                        InvProductFeature ageFeature = mainCategory.HiddenFeatures.SingleOrDefault(f => f.Name == "Prom Вікова група");

                        if (ageFeature == null)
                            break;

                        // Each prom cat has very DIFFERENT allowed values for age *facepalm*
                        w.WriteField("Возрастная группа");
                        w.WriteField("");

                        string sizeCut = productOptionValueName.Substring(0, 3);


                        List<PromAttributeValueDef> ageValues = ((PromAttributeDef)ageFeature.PlatformData).AttributeValues;
                        string[] ageValuesOrdered = ageValues.Select(v => v.NameRU).Order().ToArray();
                        short age = _SizeHeightToMinHumanAge.Single(kv => kv.Item1 == sizeCut).Item2;
                        string chosenAgeValue = null;

                    _inc:
                        foreach (string ageValue in ageValuesOrdered)
                        {
                            string firstAgeValue = Regex.Match(ageValue, @"\d+").Value;

                            if (short.Parse(firstAgeValue) == age)
                            {
                                chosenAgeValue = ageValue;
                                break;
                            }
                        }

                        if (chosenAgeValue == null)
                        {
                            age++;
                            goto _inc;
                        }

                        w.WriteField(chosenAgeValue);
                        break;
                }
            }
        }
    }
}
