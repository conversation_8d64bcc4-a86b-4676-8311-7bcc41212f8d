using System.Globalization;
using System.Threading;
using System.IO;
using System.Text;

using Invictus.Nomenklatura.Workers;
using irisdropwebservice.Libs.PromSharp;
using Invictus.Nomenklatura.Misc;

using Serilog.Events;

using Castle.DynamicProxy;
using System.Diagnostics;
using System.Threading.Tasks;

using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Web;

using irisdropwebservice.Legacy;

using Refit;

using WebRequestWithRetryResult = irisdropwebservice.Legacy.WebRequestWithRetryResult;

namespace irisdropwebservice.Services.PromSync.Remote
{
    [CanFail(LogLevel = LogEventLevel.Verbose)]
    public interface IPromSiteWebApiCallsWorker : IWorker<IPromSiteWebApiCallsWorker>
    {
        Task<List<PromProduct>> GetAllProducts(DateTime updatedAfterTimeUtc, CancellationToken ct);
        Task<List<PromProduct>> GetProducts(DateTime updatedAfterTimeUtc, long lastId, CancellationToken ct);
        Task<PromProduct> GetProductByExternalId(string externalId, CancellationToken ct);

        Task<string> ExportProductsToProm([DoNotLog] string csv, CancellationToken ct);
        Task<PromImportStatusResponse> GetExportStatus(string importId, CancellationToken ct);

        Task<Dictionary<string, string>> EditProducts([DoNotLog] IList<PromProductEdit> products, CancellationToken ct);
    }

    public class PromWebApiCallsWorker :
        IWorkerImpl<IPromSiteWebApiCallsWorker>, IPromSiteWebApiCallsWorker
    {
        public WorkerCore Core { get; set; }
        public ILogger Log { get; } = InvLog.Logger<PromWebApiCallsWorker>();

        public WorkerConfiguration WorkerConfiguration { get; } = new WorkerConfiguration(
            "ROAPI2",
            new WorkerConfiguration.TaskScheduler("PromWebApiCalls", 1),
            LogEventLevel.Debug,
            AllowDirectCall: true,
            GetCustomInterceptor: _ => new Interceptor()
        );

        public class Interceptor : IInterceptor
        {
            protected ILogger Log { get; } = InvLog.Logger<Interceptor>();

            public void Intercept(IInvocation invocation)
            {
                WebRequestWithRetryOld.WebCallWithRetry(Log, () => this.ApiCallWrapperWebCallWithRetry(invocation));
            }

            private WebRequestWithRetryResult ApiCallWrapperWebCallWithRetry(IInvocation invocation)
            {
                try
                {
                    var sw = Stopwatch.StartNew();

                    invocation.Proceed();

                    sw.Stop();


                    return WebRequestWithRetryResult.Success(null);
                }
                catch (Exception exc)
                {
                    Log.Information("PromWebApiCallsWorker Interceptor exception.");

                    RetryAdvice advice = WebRequestRetryExceptionBeHandler.GetWebRequestRetryAdvice(Log, exc);

                    if (advice == RetryAdvice.WaitALot)
                        advice = RetryAdvice.ThrowFurther;

                    return WebRequestWithRetryResult.FromExceptionAdvice(exc, advice);
                }
            }
        }

        IPromSiteWebApiCallsWorker IWorkerImpl<IPromSiteWebApiCallsWorker>.PublicInterface { get; set; }

        private readonly IPromRefitApi _api;

        public PromWebApiCallsWorker(PromRefitApiFactory apiFactory)
        {
            _api = apiFactory.Create();
        }

        public async Task<List<PromProduct>> GetAllProducts(DateTime updatedAfterTimeUtc, CancellationToken ct)
        {
            var res = new List<PromProduct>();
            long lastId = 0;

            do
            {
                List<PromProduct> thisRes = await this.GetProducts(updatedAfterTimeUtc, lastId, ct);

                if (thisRes.Count == 0)
                    break;

                bool anyAdded = false;
                foreach (PromProduct promProduct in thisRes)
                {
                    if (!res.Any(p => p.Id == promProduct.Id))
                    {
                        res.Add(promProduct);
                        anyAdded = true;
                    }
                }
                if (!anyAdded)
                    break;

                lastId = thisRes.Last().Id;

                Thread.Sleep(980);
            } while (true);

            return res;
        }

        public async Task<List<PromProduct>> GetProducts(DateTime updatedAfterTimeUtc, long lastId, CancellationToken ct)
        {
            if (updatedAfterTimeUtc == DateTime.MinValue)
                updatedAfterTimeUtc = new DateTime(2010, 01, 01, 01, 01, 01, DateTimeKind.Utc);

            string lastModifiedFrom = $"{updatedAfterTimeUtc:yyyy-MM-dd}T{updatedAfterTimeUtc:HH:mm:ss}";
            
            PromProductsListResponse response = await _api.GetProductsList(
                lastModifiedFrom,
                "2040-07-01T12:00:00",
                "5000",
                lastId.ToString(CultureInfo.InvariantCulture)
            );
            
            return response.Products;
        }

        public async Task<PromProduct> GetProductByExternalId(string externalId, CancellationToken ct)
        {
            PromSingleProductResponse response = this.ReturnOrThrow(await _api.GetProductByExternalId(externalId));
            return response.Product;
        }

        public async Task<Dictionary<string, string>> EditProducts(IList<PromProductEdit> productsAll, CancellationToken ct)
        {
            foreach (PromProductEdit[] productsChunk in productsAll.Chunk(90))
            {
                PromEditProductResponse response = this.ReturnOrThrow(await _api.EditProducts(productsChunk));

                if (response.Errors == null || response.Errors.Count == 0)
                    continue;

                return response.Errors
                    .ToDictionary(
                        d => d.Key,
                        d => string.Join(", ", d.Value.Select(v => $"{v.Key} :: {v.Value}"))
                    );
            }

            return null;
        }

        public async Task<string> ExportProductsToProm(string csv, CancellationToken ct)
        {
            var options = new PromImportProductsOptionsAll
            {
                ForceUpdate = false,
                MarkMissingProductAs = "none",
                OnlyAvailable = false
            };

            byte[] fileBytes = Encoding.UTF8.GetBytes(csv);
            string currentTime = DateTime.UtcNow.UtcToUkraineTime().ToStringUADateAndTimeShortSort();
            string fileName = $"IRIS-INV_NewProducts_{currentTime}.csv";
            
            var streamPart = new StreamPart(new MemoryStream(fileBytes), fileName, "text/csv");
            PromImportFileResponse response = this.ReturnOrThrow(await _api.ImportFile(streamPart, options));

            if (response.Status == "success")
                return response.Id;

            Log.Error($"Unsuccessful {nameof(this.ExportProductsToProm)}. {response.Id} {response.Status}");
            return null;
        }

        public async Task<PromImportStatusResponse> GetExportStatus(string importId, CancellationToken ct)
        {
            return this.ReturnOrThrow(await _api.GetImportStatus(importId));
        }
        
        private TSuccess ReturnOrThrow<TSuccess, TFail>(WebResponse<TSuccess, TFail> r)
            where TSuccess : class 
            where TFail : class
        {
            if (r.IsSuccess)
                return r.Success;

            Log.Error("Prom API returned error: \r\n" + DebugUtil.DumpObject(r.Fail));

            throw new Exception("Prom API returned error");
        }
    }
}
