using System.Net.Http;
using System.Net.Http.Headers;

using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Web;

using irisdropwebservice.AppConfig;
using irisdropwebservice.Services.PromSync.Ins;

namespace irisdropwebservice.Services.PromSync.Remote;

public class PromRefitApiFactory : RefitApiFactoryBase<IPromRefitApi>
{
    public PromRefitApiFactory(InvTasks invTasks, InvAppConfig invAppConfig)
        : base (invTasks, RRAppConfig.Prom.ApiUrl)
    {
    }

    protected override void ModifyHttpClient(HttpClient httpClient)
    {
        httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", PromSyncWorker.CustomerConfiguration.Token);
    }
}