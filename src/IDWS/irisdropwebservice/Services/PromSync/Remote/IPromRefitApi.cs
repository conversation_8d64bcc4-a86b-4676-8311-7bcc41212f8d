using System.Threading.Tasks;

using Invictus.Nomenklatura.Web;

using irisdropwebservice.Libs.PromSharp;

using Refit;

namespace irisdropwebservice.Services.PromSync.Remote;

public interface IPromRefitApi
{
    [Get("/products/list")]
    Task<PromProductsListResponse> GetProductsList(
        [Query, AliasAs("last_modified_from")] string lastModifiedFrom,
        [Query, AliasAs("last_modified_to")] string lastModifiedTo,
        [Query, AliasAs("limit")] string limit,
        [Query, AliasAs("last_id")] string lastId
    );

    [Get("/products/by_external_id/{externalId}")]
    Task<WebResponse<PromSingleProductResponse, string>> GetProductByExternalId(string externalId);

    [Post("/products/edit")]
    Task<WebResponse<PromEditProductResponse, string>> EditProducts([Body] IList<PromProductEdit> products);

    [Multipart]
    [Post("/products/import_file")]
    Task<WebResponse<PromImportFileResponse, string>> ImportFile(
        [AliasAs("file")] StreamPart file,
        [AliasAs("data")] PromImportProductsOptionsAll options
    );

    [Get("/products/import/status/{importId}")]
    Task<WebResponse<PromImportStatusResponse, string>> GetImportStatus(string importId);
}