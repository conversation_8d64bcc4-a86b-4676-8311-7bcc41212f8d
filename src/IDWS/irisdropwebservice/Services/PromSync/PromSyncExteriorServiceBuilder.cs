using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.ExteriorServ;

using irisdropwebservice.Services.PromSync.Ins;
using irisdropwebservice.Services.PromSync.Remote;

using JetBrains.Annotations;

using Microsoft.Extensions.DependencyInjection;

namespace irisdropwebservice.Services.PromSync
{
    [UsedImplicitly]
    public class PromSyncExteriorServiceBuilder : ExteriorServiceBuilderBase<PromSyncExteriorService>
    {
        protected override void AddConcealedServices(ServiceCollection concealedServiceCollection)
        {
            concealedServiceCollection.AddWorkerSingleton<PromSyncWorker>();
            concealedServiceCollection.AddWorkerSingleton<PromWebApiCallsWorker>();
            concealedServiceCollection.AddSingleton<PromSyncCommon>();

            concealedServiceCollection.AddSingleton<PromRefitApiFactory>();
            
            this.RegisterDatabaseTypes(concealedServiceCollection);

            base.AddConcealedServices(concealedServiceCollection);
        }

        protected override void ExposeConcealedServices()
        {
            this.ExposeSingleton<PromSyncCommon>();

            base.ExposeConcealedServices();
        }
    }
}
