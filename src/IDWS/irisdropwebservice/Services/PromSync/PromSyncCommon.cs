using System.Globalization;
using System.IO;
using System.Text;
using System.Xml.Serialization;

using CsvHelper;
using CsvHelper.Configuration;

using irisdropwebservice.AppConfig.RemoteResources;
using irisdropwebservice.Libs.PromSharp;
using irisdropwebservice.Services.Epicentr.Ins;

namespace irisdropwebservice.Services.PromSync;

public class PromSyncCommon
{
    public PromCategoryDefinitions PromCategoryDefinitions { get; } = new PromCategoryDefinitions();

    public PromCategoryDef GetCategoryById(int id) => PromCategoryDefinitions.CategoryList.SingleOrDefault(c => c.Id == id);
 
    private XmlSerializer _categoryDefSerializer = new XmlSerializer(typeof(PromCategoryDefinitions));
    
    public PromSyncCommon()
    {
        // this.PrepareCategoryDefinitions();
        this.CreateCategoryDefinitions();
    }

    private void PrepareCategoryDefinitions()
    {
        string promResourcesPath = Path.Combine(FileSystemRRInfo.ExeResourcesDir, "Prom");
        string categoriesFilePath = Directory.GetFiles(promResourcesPath, "Prom.ua_categories*.xlsx").Single();
        
        // Open the spreadsheet document
        using (FileStream fs = File.Open(categoriesFilePath, FileMode.Open))
            EpicentrPreparation.ConvertXlsxToCsv(fs, Path.Combine(promResourcesPath, "prom_cats_last.csv"));
    }
    
    private void CreateCategoryDefinitions()
    {
        string promResourcesPath = Path.Combine(FileSystemRRInfo.ExeResourcesDir, "Prom");

        List<PromCategoryDef> categoryDefinitions = new();

        string[] resourceFiles = Directory.GetFiles(promResourcesPath, "prom_cat*.xml");

        if (resourceFiles.Length == 0)
            throw new Exception("prom_cat*.xml not found");
            
        foreach (string resourceFilePath in resourceFiles)
        {
            using var reader = new StreamReader(resourceFilePath);
            
            var catDef = (PromCategoryDefinitions)_categoryDefSerializer.Deserialize(reader);
            
            categoryDefinitions.AddRange(catDef.CategoryList);
        }

        PromCategoryDefinitions.CategoryList = categoryDefinitions.ToList();

        _categoryDefSerializer = null;
        
        // Read cats file
        
        string categoriesFilePath = Directory.GetFiles(promResourcesPath, "prom_cats_last.csv").Single();

        using (FileStream fs = File.Open(categoriesFilePath, FileMode.Open))
        {
            var ms = new MemoryStream();
            
            fs.CopyTo(ms);

            ms.Position = 0;

            var sr = new StreamReader(ms);

            var csr = new CsvReader(sr,
                new CsvConfiguration(CultureInfo.InvariantCulture)
                {
                    Delimiter = ";,;",
                    Encoding = Encoding.UTF8
                }
            );
            
            csr.Context.RegisterClassMap<CsvClassMap>();
            
            csr.Read();
            csr.ReadHeader();

            while (csr.Read())
            {
                var record = csr.GetRecord<PromCategoryDefRaw>();

                foreach (PromCategoryDef promCategoryDef in  PromCategoryDefinitions.CategoryList)
                {
                    if (promCategoryDef.Id != record.Id)
                        continue;

                    promCategoryDef.Cat1 = record.Cat1;
                    promCategoryDef.Cat2 = record.Cat2;
                    promCategoryDef.Cat3 = record.Cat3;
                    promCategoryDef.Cat4 = record.Cat4;
                    promCategoryDef.CatLink = record.CatLink;
                }
            }
        }
    }
    
    public class CsvClassMap : ClassMap<PromCategoryDefRaw>
    {
        public CsvClassMap()
        {
            this.ApplyMapping();
        }

        private void ApplyMapping()
        {
            this.Map(m => m.Id).Name("Идентификатор_подраздела");
            this.Map(m => m.Cat1).Name("Категория1");
            this.Map(m => m.Cat2).Name("Категория2");
            this.Map(m => m.Cat3).Name("Категория3");
            this.Map(m => m.Cat4).Name("Категория4");
            this.Map(m => m.CatLink).Name("Адрес_подраздела");
        }
    }

}