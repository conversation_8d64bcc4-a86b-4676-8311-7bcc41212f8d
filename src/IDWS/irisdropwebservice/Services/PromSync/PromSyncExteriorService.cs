
using System.Threading.Tasks;

using Invictus.Nomenklatura.ExteriorServ;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.Services.PromSync.Ins;

namespace irisdropwebservice.Services.PromSync
{
    public class PromSyncExteriorService : IExteriorService
    {
        private readonly ILogger _logger = InvLog.Logger<PromSyncExteriorService>();
        
        private readonly IPromSyncWorker _promSyncWorker;

        public PromSyncExteriorService(
            IPromSyncWorker promSyncWorker
        )
        {
            _promSyncWorker = promSyncWorker;
        }

        public Task Run()
        {
            _logger.Information("Running.");

            return _promSyncWorker.Run(w => w.Initialize());
        }
    }
}
