using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.ExteriorServ;

using irisdropwebservice.Services.DropUa.Ins;

using JetBrains.Annotations;

using Microsoft.Extensions.DependencyInjection;

namespace irisdropwebservice.Services.DropUa
{
    [UsedImplicitly]
    public class DropUaExteriorServiceBuilder : ExteriorServiceBuilderBase<DropUaExteriorService>
    {
        protected override void AddConcealedServices(ServiceCollection concealedServiceCollection)
        {
            concealedServiceCollection.AddWorkerSingleton<DropUaScanWorker>();
            // concealedServiceCollection.AddWorkerSingleton<SiteScanWebApiCallsWorker>();

            concealedServiceCollection.AddSingleton<DropUaSyncFlowEvent>();

            this.RegisterDatabaseTypes(concealedServiceCollection);

            base.AddConcealedServices(concealedServiceCollection);
        }

        protected override void ExposeConcealedServices()
        {
            // this.ExposeSingleton<IPrestaWorkerPublic>(parentServiceCollection);

            base.ExposeConcealedServices();
        }
    }
}
