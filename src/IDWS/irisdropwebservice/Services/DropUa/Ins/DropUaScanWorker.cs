using System.Threading.Tasks;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Workers;

using Serilog.Events;

namespace irisdropwebservice.Services.DropUa.Ins
{
    public interface ISiteScanWorker : IWorker<ISiteScanWorker>
    {
        [CanFail(3, LogLevel = LogEventLevel.Error)]
        void Sync();

        void Initialize();
    }

    public class DropUaScanWorker : IWorkerImpl<ISiteScanWorker>, ISiteScanWorker
    {
        public ILogger Log { get; } = InvLog.Logger<DropUaScanWorker>();

        public WorkerCore Core { get; set; }
        public ISiteScanWorker PublicInterface { get; set; }

        public WorkerConfiguration WorkerConfiguration { get; } = new(
            "DROPUA",
            new WorkerConfiguration.TaskScheduler("DropUa", 1),
            LogEventLevel.Information,
            AllowDirectCall: false
        );

        private volatile bool _initializing;


        public void Initialize()
        {
            _initializing = true;
        }

        public void Sync()
        {
            if (!_initializing)
            {
                /* return */
                Task.Delay(500).ContinueWithShortThread(_ => PublicInterface.Run(w => w.Sync())).Unwrap(); // TODO: implement task prolongation

                return;
            }

            this.SyncInner();
        }

        private void SyncInner()
        {
        }
    }
}
