using System.Threading.Tasks;

using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.Legacy;

namespace irisdropwebservice.Services.DropUa.Ins
{
    public class DropUaSyncFlowEvent : ResoluteEventBase
    {
        protected override ILogger Logger => InvLog.Logger<DropUaSyncFlowEvent>();

        private readonly ISiteScanWorker _siteScanSyncWorker;

        public DropUaSyncFlowEvent(IInvAppLifetime applicationLifetime, InvAppConfig appConfig,ISiteScanWorker siteScanSyncWorker)
            : base(
                applicationLifetime, appConfig,
                TimeSpan.FromDays(1),
                TimeSpan.FromDays(7),
                ResoluteEventInitialState.Default
            )
        {
            _siteScanSyncWorker = siteScanSyncWorker;
        }

        protected override object GetCurrentDefaultArg()
        {
            return null;
        }

        protected override Task OnTrigger(object arg)
        {
            return _siteScanSyncWorker.Run(w => w.Sync());
        }
    }
}
