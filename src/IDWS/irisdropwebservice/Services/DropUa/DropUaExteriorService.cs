using System.Threading.Tasks;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.ExteriorServ;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.Services.DropUa.Ins;

namespace irisdropwebservice.Services.DropUa
{
    public class DropUaExteriorService : IExteriorService
    {
        private readonly ILogger _logger = InvLog.Logger<DropUaExteriorService>();

        private readonly DropUaSyncFlowEvent _triggerSyncFlowEvent;
        private readonly ISiteScanWorker _siteScanWorker;

        public DropUaExteriorService(
            DropUaSyncFlowEvent triggerSyncFlowEvent,
            ISiteScanWorker siteScanWorker
        )
        {
            _triggerSyncFlowEvent = triggerSyncFlowEvent;
            _siteScanWorker = siteScanWorker;
        }

        public Task Run()
        {
            _logger.Information("Running.");

            return _siteScanWorker.Run(w => w.Initialize()).ContinueWithShortThread(_ => _triggerSyncFlowEvent.ActivateIfNot());
        }

        public void QueueTriggerSyncFlow()
        {
            _triggerSyncFlowEvent.QueueTryTrigger();
        }
    }
}
