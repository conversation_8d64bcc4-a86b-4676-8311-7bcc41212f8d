using System.IO;
using System.Threading.Tasks;

using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;

using irisdropwebservice.AppConfig.RemoteResources;
using irisdropwebservice.Libs.KastaSharp;

namespace irisdropwebservice.Services.KastaSync
{
    public class KastaSyncCommon
    {
        public KastaMyCategorySchema[] CategorySchemas { get; set; }

        public KastaSyncCommon()
        {
            this.CreateCategorySchemas();
        }

        public IEnumerable<KastaCategorySchemaItem> GetKastaSchemaItems(KastaCategoryId kastaCategoryId)
        {
            KastaMyCategorySchemaKind schemaKind = CategorySchemas
                .Single(sc => sc.Category.AffiliationId == kastaCategoryId.AffiliationId)
                .KindsWithSchemas
                .Single(sc => sc.CategoryKindItself.KindId == kastaCategoryId.KindId && sc.CategoryKindItself.AffiliationId == kastaCategoryId.AffiliationId);

            IEnumerable<KastaCategorySchemaItem> schemaItems = schemaKind.SchemaItems;

            return schemaItems;
        }

        public bool IsCharacteristicIdSize(KastaCategoryId kastaCategoryId, long typeId)
        {
            string typeIdStr = typeId.ToString();

            KastaCategorySchemaItem schemaItem = this.GetKastaSchemaItems(kastaCategoryId).SingleOrDefault(s => s.KeyName == typeIdStr);

            if (schemaItem == null)
                return false;

            return schemaItem.Type == "characteristic";
        }

        private void CreateCategorySchemas()
        {
            List<KastaMyCategorySchema> categorySchemas = new();

            string[] resourceFiles = Directory.GetFiles(Path.Combine(FileSystemRRInfo.ExeResourcesDir, "Kasta"), "kasta_cats*.json.zip");

            if (resourceFiles.Length == 0)
                throw new Exception("kasta_cats*.json not found");

            Parallel.ForEach(
                resourceFiles, 
                new ParallelOptions { MaxDegreeOfParallelism = 2 },
                resourceFilePath =>
                {
                    var deployedResource = new DeployedResource(resourceFilePath, InvLog.Logger<DeployedResource>());

                    using var r = new StreamReader(deployedResource.GetFile());

                    var schema = new InvJsonSerializer().DeserializeForInternals<KastaMyCategorySchema>(r.ReadToEnd());

                    lock (categorySchemas)
                    {
                        categorySchemas.Add(schema);
                    }
                }
            );

            CategorySchemas = categorySchemas.ToArray();
        }
    }
}
