using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.ExteriorServ;

using irisdropwebservice.Services.KastaSync.Ins;
using irisdropwebservice.Services.KastaSync.Remote;

using JetBrains.Annotations;

using Microsoft.Extensions.DependencyInjection;

namespace irisdropwebservice.Services.KastaSync
{
    [UsedImplicitly]
    public class KastaSyncExteriorServiceBuilder : ExteriorServiceBuilderBase<KastaSyncExteriorService>
    {
        protected override void AddConcealedServices(ServiceCollection concealedServiceCollection)
        {
            concealedServiceCollection.AddWorkerSingleton<KastaSyncWorker>();
            concealedServiceCollection.AddWorkerSingleton<KastaSiteWebApiCallsWorker>();
            
            concealedServiceCollection.AddSingleton<KastaSyncFlowEvent>();
            concealedServiceCollection.AddSingleton<KastaSyncCommon>();

            this.RegisterDatabaseTypes(concealedServiceCollection);

            base.AddConcealedServices(concealedServiceCollection);
        }

        protected override void ExposeConcealedServices()
        {
            // this.ExposeSingleton<IPrestaWorkerPublic>(parentServiceCollection);
            this.ExposeSingleton<KastaSyncCommon>();

            base.ExposeConcealedServices();
        }
    }
}
