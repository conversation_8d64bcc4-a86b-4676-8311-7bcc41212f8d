using System.Diagnostics;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Threading;

using Castle.DynamicProxy;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;
using Invictus.Nomenklatura.Web;
using Invictus.Nomenklatura.Workers;

using irisdropwebservice.AppConfig;
using irisdropwebservice.Legacy;
using irisdropwebservice.Libs.KastaSharp;

using Serilog.Events;

using WebRequestWithRetryResult = irisdropwebservice.Legacy.WebRequestWithRetryResult;

namespace irisdropwebservice.Services.KastaSync.Remote
{
    [CanFail(LogLevel = LogEventLevel.Verbose)]
    public interface IKastaSiteWebApiCallsWorker : IWorker<IKastaSiteWebApiCallsWorker>
    {
        WebResponse<KastaCategoriesListResponse, KastaResponse400> GetAllCategories(CancellationToken ct);
        WebResponse<KastaCategoryDetailsResponse, KastaResponse400> GetCategoryDetails(long kindId, long affiliationId, CancellationToken ct);

        WebResponse<List<KastaProduct>, KastaResponse400> GetAllProducts(CancellationToken ct);
        WebResponse<KastaProductsListResponse, KastaResponse400> GetProducts(long? cursor, CancellationToken ct);

        WebResponse<KastaSubmitPhotoResponse, KastaResponse400> SubmitPhoto(string url, CancellationToken ct);
        WebResponse<KastaSubmitProductsResponse, KastaResponse400> SubmitProducts(List<KastaProductSubmit> products, KastaCategoryId kastaCategoryId, bool isUpdate, CancellationToken ct);

        WebResponse<KastaUpdateProductResponse, KastaResponse400> UpdateStock(List<KastaUpdateStockRequestItem> products, CancellationToken ct);
        WebResponse<KastaUpdateProductResponse, KastaResponse400> UpdatePrices(List<KastaUpdatePricesRequestItem> products, CancellationToken ct);

        WebResponse<KastaOrdersListResponse, KastaResponse400> GetOrders(DateTime from, CancellationToken ct);
    }

    public class KastaSiteWebApiCallsWorker :
        IWorkerImpl<IKastaSiteWebApiCallsWorker>, IKastaSiteWebApiCallsWorker
    {
        public WorkerCore Core { get; set; }
        public ILogger Log { get; } = InvLog.Logger<KastaSiteWebApiCallsWorker>();

        public WorkerConfiguration WorkerConfiguration { get; } = new WorkerConfiguration(
            "KASAPI",
            new WorkerConfiguration.TaskScheduler("KastaWebApiCalls", 1),
            LogEventLevel.Debug,
            AllowDirectCall: true,
            GetCustomInterceptor: _ => new Interceptor()
        );

        public class Interceptor : IInterceptor
        {
            protected ILogger Log { get; } = InvLog.Logger<Interceptor>();

            public void Intercept(IInvocation invocation)
            {
                WebRequestWithRetryOld.WebCallWithRetry(Log, () => this.ApiCallWrapperWebCallWithRetry(invocation));
            }

            private WebRequestWithRetryResult ApiCallWrapperWebCallWithRetry(IInvocation invocation)
            {
                try
                {
                    var sw = Stopwatch.StartNew();

                    invocation.Proceed();

                    sw.Stop();


                    return WebRequestWithRetryResult.Success(null);
                }
                catch (Exception exc)
                {
                    Log.Information("KastaSiteWebApiCallsWorker Interceptor exception.");

                    RetryAdvice advice = WebRequestRetryExceptionBeHandler.GetWebRequestRetryAdvice(Log, exc);

                    if (advice == RetryAdvice.WaitALot)
                        advice = RetryAdvice.ThrowFurther;

                    return WebRequestWithRetryResult.FromExceptionAdvice(exc, advice);
                }
            }
        }

        IKastaSiteWebApiCallsWorker IWorkerImpl<IKastaSiteWebApiCallsWorker>.PublicInterface { get; set; }

        private readonly JsonRestClient _jsonRestClient;

        public KastaSiteWebApiCallsWorker(InvTasks threadedTasks)
        {
            _jsonRestClient = new JsonRestClient(threadedTasks, "Kasta", Encoding.UTF8, RRAppConfig.Kasta.ApiUrl, true);
            _jsonRestClient.SetAuthorization(RRAppConfig.Kasta.ApiKey);
        }

        public WebResponse<List<KastaProduct>, KastaResponse400> GetAllProducts(CancellationToken ct)
        {
            var res = new List<KastaProduct>();
            long? cursor = null;
            WebResponse<KastaProductsListResponse, KastaResponse400> thisRes;

            do
            {
                thisRes = this.GetProducts(cursor, ct);

                if (thisRes.IsFail)
                    return WebResponse<List<KastaProduct>, KastaResponse400>.FailNew(thisRes.Fail, thisRes.StatusCode);

                res.AddRange(thisRes.Success.Items);

                cursor = thisRes.Success.Cursor;

                if (cursor == null || thisRes.Success.Items.Count == 0)
                    break;

                Thread.Sleep(500);
            } while (true);

            return WebResponse<List<KastaProduct>, KastaResponse400>.SuccessNew(res, thisRes.StatusCode);
        }

        public WebResponse<KastaCategoriesListResponse, KastaResponse400> GetAllCategories(CancellationToken ct)
        {
            WebResponse<KastaCategoriesListResponse, KastaResponse400> r = _jsonRestClient.Get<KastaCategoriesListResponse, KastaResponse400>(
                "supplier-content/category/all",
                null,
                false,
                ct
            );

            return r;
        }

        public WebResponse<KastaCategoryDetailsResponse, KastaResponse400> GetCategoryDetails(long kindId, long affiliationId, CancellationToken ct)
        {
            WebResponse<KastaCategoryDetailsResponse, KastaResponse400> r = _jsonRestClient.Get<KastaCategoryDetailsResponse, KastaResponse400>(
                "supplier-content/category/details",
                new KeyValuePair<string, string>[]
                {
                    new ("kind_id", kindId.ToString()),
                    new ("affiliation_id", affiliationId.ToString())
                },
                false,
                ct
            );

            Thread.Sleep(300);

            return r;
        }

        public WebResponse<KastaProductsListResponse, KastaResponse400> GetProducts(long? cursor, CancellationToken ct)
        {
            List<KeyValuePair<string, string>> kvs = new();

            if (cursor.HasValue)
                kvs.Add(new KeyValuePair<string, string>("cursor", cursor.Val().ToString()));

            kvs.Add(new KeyValuePair<string, string>("include_extended_characteristics", "true"));
            // kvs.Add(new KeyValuePair<string, string>("barcode", ));

            WebResponse<KastaProductsListResponse, KastaResponse400> r = _jsonRestClient.Get<KastaProductsListResponse, KastaResponse400>(
                "products/list",
                kvs.ToArray(),
                false,
                ct
            );
            
            return r;
        }

        public WebResponse<KastaSubmitPhotoResponse, KastaResponse400> SubmitPhoto(string url, CancellationToken ct)
        {
            WebResponse<KastaSubmitPhotoResponse, KastaResponse400> r = _jsonRestClient.Post<KastaSubmitPhotoResponse, KastaResponse400>(
                "supplier-content/submit/image",
                new List<RestClientPostParameter>
                {
                    new (new StringContent(url, Encoding.UTF8), "url", null),
                },
                false,
                ct
            );

            return r;
        }

        public WebResponse<KastaSubmitProductsResponse, KastaResponse400> SubmitProducts(List<KastaProductSubmit> products, KastaCategoryId kastaCategoryId, bool isUpdate, CancellationToken ct)
        {
            var request = new KastaSubmitProductRequest
            {
                AffiliationId = kastaCategoryId.AffiliationId,
                KindId = kastaCategoryId.KindId,
                Data = products,
                Update = isUpdate
            };

            var jsonContent = JsonContent.Create(request);

            string text = new InvJsonSerializer().SerializeForInternals(jsonContent.Value, jsonContent.ObjectType);

            Log.Debug("KastaSubmit debug1: " + text);

            WebResponse<KastaSubmitProductsResponse, KastaResponse400> r = _jsonRestClient.Post<KastaSubmitProductsResponse, KastaResponse400>(
                "supplier-content/submit/products",
                new List<RestClientPostParameter>
                {
                    new (jsonContent, "", null)
                },
                false,
                ct
            );

            if (r.IsSuccess)
            {
                Log.Debug("KastaSubmit debug2: " + new InvJsonSerializer().SerializeForInternals(r.Success, r.Success.GetType()));
            }

            return r;
        }

        public WebResponse<KastaUpdateProductResponse, KastaResponse400> UpdateStock(List<KastaUpdateStockRequestItem> products, CancellationToken ct)
        {
            var request = new KastaUpdateStockRequest
            {
                Items = products
            };

            var jsonContent = JsonContent.Create(request);

            WebResponse<KastaUpdateProductResponse, KastaResponse400> r = _jsonRestClient.Post<KastaUpdateProductResponse, KastaResponse400>(
                "products/update-stock/id",
                new List<RestClientPostParameter>
                {
                    new (jsonContent, "", null)
                },
                false,
                ct
            );

            return r;
        }

        public WebResponse<KastaUpdateProductResponse, KastaResponse400> UpdatePrices(List<KastaUpdatePricesRequestItem> products, CancellationToken ct)
        {
            var request = new KastaUpdatePricesRequest
            {
                Items = products
            };

            var jsonContent = JsonContent.Create(request);

            WebResponse<KastaUpdateProductResponse, KastaResponse400> r = _jsonRestClient.Post<KastaUpdateProductResponse, KastaResponse400>(
                "products/update-price/id",
                new List<RestClientPostParameter>
                {
                    new (jsonContent, "", null)
                },
                false,
                ct
            );

            return r;
        }

        public WebResponse<KastaOrdersListResponse, KastaResponse400> GetOrders(DateTime from, CancellationToken ct)
        {
            List<KeyValuePair<string, string>> kvs = new();

            kvs.Add(new KeyValuePair<string, string>("from", from.ToString("s")));

            WebResponse<KastaOrdersListResponse, KastaResponse400> r = _jsonRestClient.Get<KastaOrdersListResponse, KastaResponse400>(
                "orders/list",
                kvs.ToArray(),
                false,
                ct
            );

            return r;
        }
    }
}
