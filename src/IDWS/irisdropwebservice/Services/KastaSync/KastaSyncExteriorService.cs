using System.Threading.Tasks;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.ExteriorServ;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.AppConfig;
using irisdropwebservice.Services.Chats;
using irisdropwebservice.Services.KastaSync.Ins;
using irisdropwebservice.Services.LinkService.Ins;

using Telegram.Bot.Types;

namespace irisdropwebservice.Services.KastaSync
{
    public class KastaSyncExteriorService : IExteriorService, ITelegramDialogueHandler, ITelegramGroupListener
    {
        private readonly ILogger _logger = InvLog.Logger<KastaSyncExteriorService>();
        private readonly KastaSyncFlowEvent _triggerSyncFlowEvent;
        private readonly ITelegramSendWorker _telegramSendWorker;
        private readonly IKastaSyncWorker _kastaSyncWorker;
        private readonly IDbAccessFactory<VariousStoredDataDbAccess> _databaseAccessFactory;

        public const string KASTA_RESUBMIT_REJECTED = "KASTA_RESUBMIT_REJECTED";
        public const string KASTA_RESUBMIT_UNKNOWN = "KASTA_RESUBMIT_UNKNOWN";
        public const string KASTA_PHOTO_CACHE = "KASTA_PHOTO_CACHE";

        public KastaSyncExteriorService(
            IKastaSyncWorker kastaSyncWorker,
            KastaSyncFlowEvent triggerSyncFlowEvent1,
            ITelegramSendWorker telegramSendWorker,
            IDbAccessFactory<VariousStoredDataDbAccess> databaseAccessFactory,
            TelegramChatBotPolling telegramChatBotPolling
        )
        {
            _kastaSyncWorker = kastaSyncWorker;
            _triggerSyncFlowEvent = triggerSyncFlowEvent1;
            _telegramSendWorker = telegramSendWorker;
            _databaseAccessFactory = databaseAccessFactory;
            
            telegramChatBotPolling.AddDialogueHandler(this);
            telegramChatBotPolling.AddGroupListener(this);
        }

        public Task Run()
        {
            _logger.Information("Running.");

            return _kastaSyncWorker.Run(w => w.Initialize()).ContinueWith(_ => {
                _triggerSyncFlowEvent.QueueTryTrigger();
            });
        }
        
        public TelegramDialogueUpdateHandler CanHandleConversationInitiation(TelegramBotWho who, Update update)
        {
            if (who != TelegramBotWho.Staff)
                return null;

            if (update.Message?.Text == null)
                return null;

            if (update.Message.Chat.FirstName == null)
                return null;

            string messageText = update.Message.Text.Trim();

            Func<string> action = this.GetCommandDelegate(messageText);
            
            if (action == null)
                return null;
            
            return (a,b) => this.BeginHandleUpdate(a, b, action);
        }

        private Func<string> GetCommandDelegate(string messageText)
        {
            switch (messageText)
            {
                case "/kasta_resubmit_all_rejected": return this.SetNextUpdateResubmitRejectedProductsAll;
                case "/kasta_resubmit_all_unknown": return this.SetNextUpdateResubmitProductsWithoutStatusAll;
            }

            return null;
        }
        
        private string SetNextUpdateResubmitRejectedProductsAll()
        {
            using (VariousStoredDataDbAccess access = _databaseAccessFactory.CreateAccess())
            {
                 access.SetValue(KASTA_RESUBMIT_REJECTED, 2, "all");
            }
            
            return "При наступному оновленні усі відхилені товари будуть оброблені наново.";
        }
        
        private string SetNextUpdateResubmitProductsWithoutStatusAll()
        {
            using (VariousStoredDataDbAccess access = _databaseAccessFactory.CreateAccess())
            {
                access.SetValue(KASTA_RESUBMIT_UNKNOWN, 2, "all");
            }
            
            return "При наступному оновленні усі товари з невідомим статусом будуть оброблені наново.";
        }
        
        private void BeginHandleUpdate(TelegramDialogue dialogue, Update update, Func<string> action)
        {
            Task<string> task = Task.Run(action);

            dialogue.FinishAfterTask(task, new PostToTelegramOptions { DisableWebPagePreview = true, Silent = false, HtmlFormattingEnabled = true });
        }

        public void IncomingMessage(TelegramBotWho who, Update update)
        {
            if (who != TelegramBotWho.Staff)
                return;

            if (update.Message?.Text == null)
                return;

            if (update.Message.Chat.Id != TelegramChatId.FromUnknownChat(CCAppConfig.TelegramIrisDrop.ShopAdminAttentionChat.DefinitionName).ChatId)
                return;

            string messageText = update.Message.Text.Trim();
            
            Func<string> action = this.GetCommandDelegate(messageText);
            
            if (action == null)
                return;

            _telegramSendWorker.Do(w => w.PostToTelegramFromStaffAccount(
                action(),
                CCAppConfig.TelegramIrisDrop.ShopAdminAttentionChat,
                new PostToTelegramOptions { DisableWebPagePreview = true, HtmlFormattingEnabled = true, Silent = false }
            ));
        }
    }
}
