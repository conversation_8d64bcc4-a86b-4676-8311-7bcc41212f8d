using System.Threading.Tasks;

using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.Legacy;

namespace irisdropwebservice.Services.KastaSync.Ins
{
    public class KastaSyncFlowEvent : ResoluteEventBase
    {
        protected override ILogger Logger => InvLog.Logger<KastaSyncFlowEvent>();

        private readonly IKastaSyncWorker _siteScanSyncWorker;

        public KastaSyncFlowEvent(IInvAppLifetime applicationLifetime, InvAppConfig appConfig, IKastaSyncWorker siteScanSyncWorker)
            : base(
                applicationLifetime, appConfig,
                TimeSpan.FromMinutes(1),
                TimeSpan.FromMinutes(10),
                ResoluteEventInitialState.CanFireNow
            )
        {
            _siteScanSyncWorker = siteScanSyncWorker;
        }

        protected override object GetCurrentDefaultArg()
        {
            return null;
        }

        protected override Task OnTrigger(object arg)
        {
            return _siteScanSyncWorker.Run(w => w.Sync());
        }
    }
}
