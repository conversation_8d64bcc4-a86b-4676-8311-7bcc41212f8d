using System.Globalization;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

using Bukimedia.PrestaSharp.Entities;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;
using Invictus.Nomenklatura.Web;
using Invictus.Nomenklatura.Workers;

using irisdropwebservice.AppConfig;
using irisdropwebservice.AppConfig.ClassConfigurations;
using irisdropwebservice.AppConfig.RemoteResources;
using irisdropwebservice.Libs.KastaSharp;
using irisdropwebservice.Libs.PrestaSharp;
using irisdropwebservice.Services.Chats;
using irisdropwebservice.Services.KastaSync.Remote;
using irisdropwebservice.Services.LinkService;
using irisdropwebservice.Services.LinkService.Ins;
using irisdropwebservice.Services.PrestaSync;
using irisdropwebservice.Services.PrestaSync.Ins;
using irisdropwebservice.Services.PrestaSync.ServiceConductors;
using irisdropwebservice.Services.PrestaSync.SiteFillConductors;
using irisdropwebservice.Services.VkScrap;

using Microsoft.Extensions.Hosting;

using Serilog.Events;

namespace irisdropwebservice.Services.KastaSync.Ins
{
    public interface IKastaSyncWorker : IWorker<IKastaSyncWorker>
    {
        [CanFail(5, LogLevel = LogEventLevel.Error)]
        void SyncProducts(ExternalSyncData prestaSyncData);

        [CanFail(2, LogLevel = LogEventLevel.Error)]
        void Sync();

        void Initialize();
    }

    public class KastaSyncWorker : IWorkerImpl<IKastaSyncWorker>, IKastaSyncWorker
    {
        public ILogger Log { get; } = InvLog.Logger<KastaSyncWorker>();

        public WorkerCore Core { get; set; }
        public IKastaSyncWorker PublicInterface { get; set; }

        public WorkerConfiguration WorkerConfiguration { get; } = new(
            "KASTA0",
            new WorkerConfiguration.Thread("KastaSync", ThreadPriority.Normal, IsBackground: false),
            LogEventLevel.Information,
            AllowDirectCall: false
        );

        private readonly IHostApplicationLifetime _applicationLifetime;
        private readonly IKastaSiteWebApiCallsWorker _webApi;
        private readonly IPrestaProductSyncPublic _prestaProductSyncPublic;
        private readonly IVkScrapFileDownload _vkScrapFileDownload;
        private readonly IPrestaWorkerPublic _prestaWorker;
        private readonly KastaSyncCommon _kastaCommon;
        private readonly GlobalUserErrorsManager _globalUserErrorsManager;
        private readonly IDbAccessFactory<VariousStoredDataDbAccess> _databaseAccessFactory;
        private readonly ITelegramSendWorker _telegramSendWorker;
        private readonly RazorTemplateRender _razorViewRender;
        private readonly InvProductFeatureCentre _invProductFeatureCentre;
        private readonly IPriceManager _priceManager;

        private readonly InvJsonSerializer _jsonSerializer = new();
        
        private KastaRRInfo _config => RRAppConfig.Kasta;

        private volatile bool _initializing;

        public KastaSyncWorker(
            IHostApplicationLifetime applicationLifetime,
            IKastaSiteWebApiCallsWorker webApi,
            IPrestaProductSyncPublic prestaProductSyncPublic,
            IVkScrapFileDownload vkScrapFileDownload,
            IPrestaWorkerPublic prestaWorker,
            KastaSyncCommon kastaCommon,
            GlobalUserErrorsManager globalUserErrorsManager,
            IDbAccessFactory<VariousStoredDataDbAccess> databaseAccessFactory,
            ITelegramSendWorker telegramSendWorker,
            RazorTemplateRender razorViewRender,
            InvProductFeatureCentre invProductFeatureCentre,
            IPriceManager priceManager
        )
        {
            _applicationLifetime = applicationLifetime;
            _webApi = webApi;
            _prestaProductSyncPublic = prestaProductSyncPublic;
            _vkScrapFileDownload = vkScrapFileDownload;
            _prestaWorker = prestaWorker;
            _kastaCommon = kastaCommon;
            _globalUserErrorsManager = globalUserErrorsManager;
            _databaseAccessFactory = databaseAccessFactory;
            _telegramSendWorker = telegramSendWorker;
            _razorViewRender = razorViewRender;
            _invProductFeatureCentre = invProductFeatureCentre;
            _priceManager = priceManager;

            _prestaProductSyncPublic.DidPush += this.PrestaProductSyncPublicOnDidPush;
        }
        

        private void PrestaProductSyncPublicOnDidPush(object sender, DidPushEventArgs e)
        {
            PublicInterface.Run(w => w.SyncProducts(e.PrestaSyncData));
        }

        public void Initialize()
        {
            Task.Delay(30 * 1000).ContinueWithShortThread(_ => {
                    _initializing = true;
                }
            );

            // 0: Get all kasta categories and details

            // this.FetchAllKastaCategories(ct);
        }

        public void Sync()
        {
            CancellationToken ct = _applicationLifetime.ApplicationStopping;

            if (!_config.DoWork)
                return;

            if (!_initializing)
            {
                Log.Debug("Sync postponed 10 sec to wait for initialization.");
                /* return */
                Task.Delay(10 * 1000).ContinueWithShortThread(_ => PublicInterface.Run(w => w.Sync())).Unwrap(); // TODO: implement task prolongation

                return;
            }

            if (_applicationLifetime.ApplicationStopping.IsCancellationRequested)
                return;

            string lastSyncTimeStr;

            using (VariousStoredDataDbAccess access = _databaseAccessFactory.CreateAccess())
            {
                lastSyncTimeStr = access.TryGetValue("KASTA_LAST_ORDERS_SYNC_TIME", 2);
            }

            DateTime utcNow = DateTime.UtcNow;

            DateTime lastSyncTime = string.IsNullOrWhiteSpace(lastSyncTimeStr)
                ? new DateTime(2024, 1, 1, 1, 1, 1, DateTimeKind.Utc)
                : DateTime.ParseExact(lastSyncTimeStr, PrestaSharpConstants.PRODUCT_DATE_TIMES_FORMAT, CultureInfo.InvariantCulture);

            WebResponse<KastaOrdersListResponse, KastaResponse400> ordersResponse = _webApi.GetOrders(lastSyncTime, ct);

            if (ordersResponse.IsFail)
                throw new Exception(ordersResponse.ToString());

            TelegramChatConfiguration chatId = CCAppConfig.TelegramIrisDrop.NewOrdersChat;

            foreach (KastaOrdersListResponseItem kastaOrder in ordersResponse.Success.Items)
            {
                bool isNewOrder = true;
                
                StringBuilder bdr = new();
                bdr.Append($"Kasta нове замовлення {kastaOrder.Id} \r\n {kastaOrder.Statuses.First().CreatedAt.ToStringUADateAndTimeShort()}.");

                if (kastaOrder.Statuses.Count != 0)
                {
                    bdr.Append(" Статуси: ");

                    foreach (KastaStatus kastaOrderStatus in kastaOrder.Statuses)
                    {
                        if (kastaOrderStatus.Type == KastaOrderStatus.ConfirmedBySupplier)
                        {
                            isNewOrder = false;
                            break;
                        }
                        
                        bdr.Append(Enum.GetName(kastaOrderStatus.Type));

                        if (kastaOrder.Statuses.Last() != kastaOrderStatus)
                        {
                            bdr.Append(", ");
                        }
                    }
                }

                if (isNewOrder)
                {
                    _telegramSendWorker.Do(w => w.PostToTelegramFromStaffAccount(bdr.ToString(),
                            chatId,
                            new PostToTelegramOptions
                            {
                                DisableWebPagePreview = true,
                                Silent = false
                            }
                        )
                    );
                }
            }

            using (VariousStoredDataDbAccess access = _databaseAccessFactory.CreateAccess())
            {
                access.SetValue("KASTA_LAST_ORDERS_SYNC_TIME", 2, utcNow.ToString(PrestaSharpConstants.PRODUCT_DATE_TIMES_FORMAT, CultureInfo.InvariantCulture));
            }
        }

        public void SyncProducts(ExternalSyncData prestaSyncData)
        {
            if (!_config.DoWork)
                return;
            
            if (prestaSyncData == null)
                return;

            if (!_initializing)
            {
                Log.Debug("SyncProducts postponed 2000 ms to wait for initialization.");
                /* return */
                Task.Delay(2000).ContinueWithShortThread(_ => PublicInterface.Run(w => w.SyncProducts(prestaSyncData))).Unwrap(); // TODO: implement task prolongation

                return;
            }

            if (_applicationLifetime.ApplicationStopping.IsCancellationRequested)
                return;

            if (_prestaProductSyncPublic.IsSiteFillHappening)
            {
                Log.Warning("prestaProductSyncPublic.IsSiteFillHappening, posponing SyncProducts() for 15 sec.");

                Task.Delay(TimeSpan.FromSeconds(15)).ContinueWithShortThread(_ => PublicInterface.Run(w => w.SyncProducts(prestaSyncData)));
                return;
            }

            this.SyncInner(prestaSyncData);
        }

        private void SyncInner(ExternalSyncData prestaSyncData)
        {
            CancellationToken ct = _applicationLifetime.ApplicationStopping;

            Log.Debug("SyncInner");

            // 1: Get kasta remote products
            Task<WebResponse<List<KastaProduct>, KastaResponse400>> kastaProductsTask = _webApi.Run(w => w.GetAllProducts(ct));
            
            if (ct.IsCancellationRequested)
                return;

            List<ExternalSyncProduct> allPrestaProducts = prestaSyncData.AllPrestaProducts.ToList();

            // Filter: keep only vk products
            allPrestaProducts.RemoveAll(p => !LinkedNaming.IsPrestaSiteReferenceVkAuthorityArt(p.PrestaProduct.Reference));

            // Filter: only non-vk, ready cats
            allPrestaProducts.RemoveAll(p =>
                !_invProductFeatureCentre.GetMainCategory(p.PrestaProduct.Categories).IsExportableCategory
            );

            if (kastaProductsTask.Result.IsFail)
                throw new Exception(kastaProductsTask.Result.Fail.ToString());
            
            List<KastaProduct> kastaSiteProducts = kastaProductsTask.Result.Success;

            // Filter: keep only vk->presta products
            kastaSiteProducts.RemoveAll(p => !LinkedNaming.IsExternalSiteReferenceLinkedToPrestaSourcedFromVk(p.Art));

            List<KastaProductVariationGroup> kastaProductGroups = kastaSiteProducts
                .GroupBy(p => p.Art)
                .Select(group => new KastaProductVariationGroup { Products = group.Cast<KastaProductBase>().ToList() })
                .ToList();

            // 3: Match remote products to presta products
            IEnumerable<EnumerableMatch.Result<string, ExternalSyncProduct, KastaProductVariationGroup>> matches =
                allPrestaProducts.Match(
                    right: kastaProductGroups,
                    selectKeyLeft: p => string.IsNullOrWhiteSpace(p.PrestaProduct.Reference) ? null : LinkedNaming.ExternalSiteReferenceFromPrestaSourcedVkAuthorityArt(p.PrestaProduct.Reference),
                    selectKeyRight: p => p.Art
                );

            bool restoreAllRejected;
            bool restoreAllUnknownStatus;
            var photoCache = new KastaPhotoCache();
            
            using (VariousStoredDataDbAccess access = _databaseAccessFactory.CreateAccess())
            {
                restoreAllRejected = access.TryGetValue(KastaSyncExteriorService.KASTA_RESUBMIT_REJECTED, 2) == "all";
                restoreAllUnknownStatus = access.TryGetValue(KastaSyncExteriorService.KASTA_RESUBMIT_UNKNOWN, 2) == "all";
                
                string photoCacheStr = access.TryGetValue(KastaSyncExteriorService.KASTA_PHOTO_CACHE, 2);

                if (!string.IsNullOrWhiteSpace(photoCacheStr))
                {
                    byte[] photoCacheObj = Convert.FromBase64String(photoCacheStr);
                    photoCache = SerializationUtil<KastaPhotoCache>.DeserializeFromBinaryCompressed(photoCacheObj);
                }
            }

            List<Match> actionItems = matches.Select(
                r => {
                    bool productIsInKastaProcessing;

                    switch (r.Type)
                    {
                        case EnumerableMatch.ResultType.OnlyLeftSingle:
                            // Presta product only
                            return new Match { ExternalPrestaProduct = r.LeftSingle, DesiredAction = DesiredAction.Submit_CreateKastaProducts };
                        case EnumerableMatch.ResultType.OnlyRightSingle:
                            // Kasta products only
                            productIsInKastaProcessing = r.RightSingle.Products
                                .Cast<KastaProduct>()
                                .Any(KastaProductStatusIsShouldWait);

                            return new Match
                            {
                                KastaProductGroup = r.RightSingle,
                                DesiredAction = productIsInKastaProcessing || this.IsProductGroupInactivated(r.RightSingle)
                                    ? DesiredAction.Skip
                                    : DesiredAction.DeactivateWithoutValidation
                            };
                        case EnumerableMatch.ResultType.OneToOne:
                            // Both
                            productIsInKastaProcessing = r.RightSingle.Products
                                .Cast<KastaProduct>()
                                .Any(KastaProductStatusIsShouldWait);

                            bool productIsRejected = r.RightSingle.Products
                                .Cast<KastaProduct>()
                                .Any(KastaProductStatusIsRejected);
                            
                            bool productUnknownStatus = r.RightSingle.Products
                                .Cast<KastaProduct>()
                                .Any(KastaProductStatusIsUnknown);

                            var match = new Match
                            {
                                ExternalPrestaProduct = r.LeftSingle,
                                KastaProductGroup = r.RightSingle,
                                DesiredAction = productIsRejected
                                    ? (restoreAllRejected ? DesiredAction.Submit_CreateKastaProducts : DesiredAction.SkipAndReportRejection)
                                    : (productUnknownStatus 
                                        ? (restoreAllUnknownStatus ? DesiredAction.Submit_CreateKastaProducts : DesiredAction.SkipAndReportUnknownState)
                                        : (productIsInKastaProcessing 
                                            ? DesiredAction.Skip 
                                            : DesiredAction.UpdateIfNeeded)
                                    )
                            };

                            if (match.DesiredAction == DesiredAction.Submit_CreateKastaProducts)
                            {
                                match.KastaProductGroup = new KastaProductVariationGroup();
                            }

                            return match;
                        // Options below are not the most optimal program behavior
                        case EnumerableMatch.ResultType.OnlyLeftMultiple:
                        case EnumerableMatch.ResultType.ManyToMany:
                        case EnumerableMatch.ResultType.LeftMultipleOneRight:
                        case EnumerableMatch.ResultType.OnlyRightMultiple:
                        case EnumerableMatch.ResultType.LeftOneRightMultiple:
                            throw new Exception("The app cannot handle multiple presta or kasta products with the same reference/sku/art.");

                        default: throw TypeAbominationException.Enum(typeof(EnumerableMatch.ResultType), r.Type);
                    }
                }
            ).ToList();
            
            if (ct.IsCancellationRequested)
                return;

            // 4: Validate presta products, also set some vars like Color
            
            _globalUserErrorsManager.ClearAllProductErrors(GlobalUserErrorSource.KastaValidation);

            Match[] productsToValidate = actionItems
                .Where(m => m.DesiredAction is DesiredAction.Submit_CreateKastaProducts or DesiredAction.UpdateIfNeeded or DesiredAction.SkipAndReportRejection or DesiredAction.SkipAndReportUnknownState)
                .ToArray();

            foreach (Match match in productsToValidate)
            {
                this.PreValidatePrestaProduct(prestaSyncData, match);
            }

            List<Match> newOrToUpdateProducts = actionItems
                .Where(m => m.DesiredAction == DesiredAction.Submit_CreateKastaProducts)
                .ToList();

            // 5: Check if we need to re-submit products (characteristics change)
            List<Match> updateProducts = actionItems.Where(m => m.DesiredAction == DesiredAction.UpdateIfNeeded).ToList();

            foreach (Match match in updateProducts.ToArray())
            {
                bool updateCharacteristics = this.NeedsToUpdateCharacteristics(prestaSyncData, match, photoCache);

                if (updateCharacteristics)
                {
                    match.KastaColor = null;
                    match.PrevKastaColor = match.KastaProductGroup.Products.First().Color;
                    match.KastaProductGroup.Products.Clear();
                    match.DesiredAction = DesiredAction.Submit_UpdateCharacteristics;

                    this.PreValidatePrestaProduct(prestaSyncData, match);

                    updateProducts.Remove(match);

                    if (match.DesiredAction != DesiredAction.SkipDueToFailedValidation)
                        newOrToUpdateProducts.Add(match);
                }
            }

            // 6: Create kasta products
            foreach (Match newProduct in newOrToUpdateProducts.ToArray())
            {
                try
                {
                    bool update = newProduct.DesiredAction == DesiredAction.Submit_UpdateCharacteristics;
                    
                    // if (update)
                    //     continue;
                    
                    this.CreateNewKastaProductGroup(prestaSyncData.CategoriesConductor, prestaSyncData.ProductAttributesConductor, prestaSyncData.LangAndTemplateConductor, newProduct);
                }
                catch (Exception e)
                {
                    Log.Error(e);
                    Log.Error("One new product error " + newProduct.KastaProductGroup.Art);

                    newOrToUpdateProducts.Remove(newProduct);
                }
                
            }

            bool updatePhotoCache = false;
            
            foreach (IGrouping<DesiredAction, Match> desiredActionGroup in newOrToUpdateProducts.GroupBy(m => m.DesiredAction))
            {
                bool update = desiredActionGroup.Key == DesiredAction.Submit_UpdateCharacteristics;

                if (!update && desiredActionGroup.Key != DesiredAction.Submit_CreateKastaProducts)
                    throw new Exception("newProducts list contains unknown desired status");
                
                // if (update)
                //    continue;
                
                foreach (IGrouping<KastaCategoryId, Match> kastaCategoryGroup in desiredActionGroup.GroupBy(m => m.KastaCategoryId))
                {
                    Match[] submitMatches = kastaCategoryGroup.ToArray();

                    KastaCategoryId categoryId = submitMatches[0].KastaCategoryId;

                    List<KastaProductSubmit> submitProducts = submitMatches
                        .SelectMany(m => m.KastaProductGroup.Products.Cast<KastaProductSubmit>())
                        .ToList();

                    Log.Information($"Kasta submit (update={update}): {string.Join(',', submitProducts.Select(p => p?.UniqueSkuId ?? "(null)"))}");

                    // Execute
                    WebResponse<KastaSubmitProductsResponse, KastaResponse400> submitResponse = _webApi.Do(w => w.SubmitProducts(submitProducts, categoryId, update, ct));

                    if (submitResponse.IsFail)
                    {
                        Log.Error("Kasta submit fail: " + submitResponse.Fail);
                    } else
                    {
                        foreach (Match match in kastaCategoryGroup)
                        {
                            updatePhotoCache = true;
                            photoCache.SubmittedPhotos[match.KastaProductGroup.Art] = match.PrestaProduct.PhotoId;
                        }
                    }
                }
            }

            if (updatePhotoCache)
            {
                Log.Information($"updatePhotoCache, objects: {photoCache.SubmittedPhotos.Count}");
                
                using (VariousStoredDataDbAccess access = _databaseAccessFactory.CreateAccess())
                {
                    byte[] photoCacheObj  = SerializationUtil<KastaPhotoCache>.SerializeToBinaryCompressed(photoCache);
                    string photoCacheStr = Convert.ToBase64String(photoCacheObj);
                    
                    access.SetValue(KastaSyncExteriorService.KASTA_PHOTO_CACHE, 2, photoCacheStr);
                }
            }

            var productsToUpdateStock = new List<KastaUpdateStockRequestItem>();
            var productsToUpdatePrices = new List<KastaUpdatePricesRequestItem>();

            // 7: Edit existing products
            
            foreach (Match updateProduct in updateProducts)
            {
                this.UpdateKastaProduct(updateProduct, productsToUpdateStock, productsToUpdatePrices);
            }

            List<Match> deactivateProducts = actionItems.Where(m => m.DesiredAction == DesiredAction.DeactivateWithoutValidation).ToList();

            foreach (Match deactivateProduct in deactivateProducts)
            {
                this.DeactivateKastaProduct(deactivateProduct, productsToUpdateStock);
            }

            if (newOrToUpdateProducts.Count != 0 || productsToUpdateStock.Count != 0 || productsToUpdatePrices.Count != 0)
            {
                string updStr = newOrToUpdateProducts
                    .Where(m => m.DesiredAction == DesiredAction.Submit_UpdateCharacteristics)
                    .Aggregate("", (s, m) => s + " " + m.PrestaProduct.Reference);
                
                Log.Write(
                    updStr.Length > 2 ? LogEventLevel.Warning : LogEventLevel.Information,
                    $"sendNewProducts: {newOrToUpdateProducts.Count} (upd: {updStr}) " +
                    $"sendStock: {productsToUpdateStock.Count} sendPrices: {productsToUpdatePrices.Count}"
                );
            }

            if (productsToUpdateStock.Count > 0)
            {
                // Execute
                WebResponse<KastaUpdateProductResponse, KastaResponse400> response = _webApi.UpdateStock(productsToUpdateStock, ct);

                if (response.IsFail)
                    throw new Exception(response.Fail.ToString());
            }

            if (productsToUpdatePrices.Count > 0)
            {
                // Execute
                WebResponse<KastaUpdateProductResponse, KastaResponse400> response = _webApi.UpdatePrices(productsToUpdatePrices, ct);

                if (response.IsFail)
                    throw new Exception(response.Fail.ToString());
            }
            
            using (VariousStoredDataDbAccess access = _databaseAccessFactory.CreateAccess())
            {
                access.SetValue(KastaSyncExteriorService.KASTA_RESUBMIT_REJECTED, 2, "none");
                access.SetValue(KastaSyncExteriorService.KASTA_RESUBMIT_UNKNOWN, 2, "none");
            }
        }

        private bool NeedsToUpdateCharacteristics(ExternalSyncData prestaSyncData, Match match, KastaPhotoCache photoCache)
        {
            InvProductCategory mainCategory = _invProductFeatureCentre.GetMainCategory(match.PrestaProduct.Categories);
                
            if (!mainCategory.IsExportableCategory)
                throw new Exception($"Should have an exportable category. {match.PrestaProduct.Reference}");

            List<KastaProductCharacteristic> kastaCharacteristicsForProductGroup = this.GetKastaFeaturesFromPrestaProduct(
                prestaSyncData.ProductAttributesConductor,
                mainCategory.KastaCategoryId.Value,
                match.PrestaProduct
            );
                
            bool updateCharacteristics = false;

            if (match.KastaProductGroup.Products.Count != 0)
            {
                string art = match.KastaProductGroup.Art;
                    
                if (!photoCache.SubmittedPhotos.ContainsKey(art))
                    updateCharacteristics = true;
                else if (photoCache.SubmittedPhotos.TryGetValue(art, out string photoId) && match.PrestaProduct.PhotoId != photoId)
                    updateCharacteristics = true;
                    
                if (updateCharacteristics)
                {
                    Log.Information($"Photo update: {art} {match.PrestaProduct.PhotoId}");

                    return true;
                }
            }

            foreach (KastaProduct kastaProduct in match.KastaProductGroup.Products.OfType<KastaProduct>())
            {
                updateCharacteristics = kastaProduct.ExtendedCharacteristics
                    .Where(ec => _kastaCommon.IsCharacteristicIdSize(mainCategory.KastaCategoryId.Value, ec.TypeId))
                    .GroupBy(ec => ec.TypeId)
                    .Any(
                        grp => {
                            KastaProductCharacteristic matchingRequiredCharacteristic =
                                kastaCharacteristicsForProductGroup.SingleOrDefault(c => c.KeyName == grp.Key.ToString());

                            if ((matchingRequiredCharacteristic == null) != (grp.All(ec => ec.ValueId == null)))
                            {
                                Log.Information(
                                    $"Char update: {kastaProduct.Art} {grp.Key.ToString()} {matchingRequiredCharacteristic == null} {grp.All(ec => ec.ValueId == null)}"
                                );

                                if (grp.Key is 17 or 492)
                                    return false; // dunno
                                
                                return true;
                            }

                            if (matchingRequiredCharacteristic == null)
                                return false;

                            bool r = !this.AreListsEqual(matchingRequiredCharacteristic.Data.Ids, grp.Where(ec => ec.ValueId.HasValue).Select(ec => ec.ValueId.Value));

                            if (r)
                            {
                                Log.Information($"Char update: {kastaProduct.Art} + \r\n" +
                                                $"req: {string.Join(',', matchingRequiredCharacteristic.Data.Ids.Select(l => l.ToString()))} \r\n" +
                                                $"cur: {string.Join(',', grp.Where(ec => ec.ValueId.HasValue).Select(ec => ec.ValueId.Value).Select(l => l.ToString()))}\r\n" +
                                                $"\r\n"
                                );
                            }

                            return r;
                        }
                    );

                if (updateCharacteristics)
                    return true;
            }

            return false;
        }

        private static bool KastaProductStatusIsShouldWait(KastaProduct p)
        {
            return p.Status is
                // KastaProductStatus.Draft or 
                KastaProductStatus.ContractChangeRequest or 
                KastaProductStatus.ContractChangePending or 
                KastaProductStatus.InProduction or 
                KastaProductStatus.ContentPending or 
                KastaProductStatus.PricePending;
        }

        private static bool KastaProductStatusIsUnknown(KastaProduct p)
        {
            return p.Status == null;
        }

        private static bool KastaProductStatusIsRejected(KastaProduct p)
        {
            return p.Status is
                KastaProductStatus.BuyerDeclinedPrice or
                KastaProductStatus.Rejected;
        }

        private void PreValidatePrestaProduct(ExternalSyncData prestaSyncData, Match match)
        {
            if (match.DesiredAction == DesiredAction.SkipAndReportRejection)
            {
                _globalUserErrorsManager.SetFlushableProductError(GlobalUserErrorSource.KastaValidation,
                    match.PrestaProduct.Reference,
                    "<b>Модерація Kasta відхилила товар. /kasta_resubmit_all_rejected</b>"
                );
                return;
            }
            if (match.DesiredAction == DesiredAction.SkipAndReportUnknownState)
            {
                _globalUserErrorsManager.SetFlushableProductError(GlobalUserErrorSource.KastaValidation,
                    match.PrestaProduct.Reference,
                    "<b>Статус товару невідомий. /kasta_resubmit_all_unknown</b>"
                );
                return;
            }

            InvProductCategory mainCategory = _invProductFeatureCentre.GetMainCategory(match.PrestaProduct.Categories);
            
            if (!mainCategory.IsExportableCategory)
                throw new Exception($"Should have an exportable category. {match.PrestaProduct.Reference}");
            
            (product_feature, InvProductFeature)[] requiredFeatures =
                mainCategory.Features
                    .Where(f => f.Platform == InvPlatform.Kasta && f.IsMandatory)
                    .Select(f => (prestaSyncData.ProductAttributesConductor.GetProductFeature(f.Name), f))
                    .ToArray();

            var errors = new List<string>();

            foreach ((product_feature, InvProductFeature) requiredFeature in requiredFeatures)
            {
                ProductFeatureAndValueId currentProductFeature = match.PrestaProduct.Features.FirstOrDefault(f => f.FeatureId == requiredFeature.Item1.id);

                if (currentProductFeature.FeatureId == 0 || currentProductFeature.FeatureValueId == 0)
                {
                    errors.Add($"Х-ка '<b>{requiredFeature.Item2.Name}</b>' пуста" + (requiredFeature.Item2.Type == InvProductFeatureType.Multiselect ? " (можна кілька значень)" : ""));
                    continue;
                }

                if (requiredFeature.Item2.Name == "Колір")
                {
                    if (match.KastaColor != null)
                    {
                        errors.Add($"Х-ка '<b>{requiredFeature.Item2}</b>' не може мати кілька значень");
                    }

                    string colorFeatureValueText = prestaSyncData.ProductAttributesConductor.GetFeatureValueText(currentProductFeature.FeatureValueId);

                    if (match.PrevKastaColor != null && match.PrevKastaColor != colorFeatureValueText)
                    {
                        errors.Add("Для зміни кольору Kasta потребує видалення товару");
                    }

                    match.KastaColor = colorFeatureValueText;
                }
            }

            if (errors.Count != 0)
            {
                _globalUserErrorsManager.SetFlushableProductError(GlobalUserErrorSource.KastaValidation,
                    match.PrestaProduct.Reference,
                    string.Join(",\n", errors)
                );
                match.DesiredAction = DesiredAction.SkipDueToFailedValidation;
                return;
            }

            IList<(KastaCategorySchemaItem, long?)> kastaFeatureValues =
                this.GetKastaCategorySchemaItemsWithKastaValueIdsFromProductFeatures(
                    prestaSyncData.ProductAttributesConductor,
                    mainCategory,
                    match.PrestaProduct.Features
                );

            foreach ((KastaCategorySchemaItem, long?) kastaFeatureValue in kastaFeatureValues)
            {
                if (!kastaFeatureValue.Item2.HasValue)
                {
                    errors.Add($"Х-ка '<b>{kastaFeatureValue.Item1.HumanName}</b>' має невірне значення" + (kastaFeatureValue.Item1.Requirements.IsMulti ? " (можна кілька значень)" : ""));
                }
            }

            if (errors.Count != 0)
            {
                _globalUserErrorsManager.SetFlushableProductError(GlobalUserErrorSource.KastaValidation,
                    match.PrestaProduct.Reference, 
                    string.Join(",\n", errors)
                );
                match.DesiredAction = DesiredAction.SkipDueToFailedValidation;
                return;
            }

            foreach (ProductOption productOption in match.ExternalPrestaProduct.ProductOptions)
            {
                AttributeOption ae = productOption.AttributeOptions.Single();

                string productOptionValueName = ae.Name;

                (long?, long?) kastaSizeIds = this.GetKastaSizeId(mainCategory.KastaCategoryId.Value, productOptionValueName, false);

                if (kastaSizeIds.Item1 == null)
                {
                    errors.Add($"Невірний розмір {productOptionValueName}");
                    return;
                }
            }

            if (errors.Count != 0)
            {
                _globalUserErrorsManager.SetFlushableProductError(GlobalUserErrorSource.KastaValidation,
                    match.PrestaProduct.Reference,
                    string.Join(",\n", errors)
                );
                match.DesiredAction = DesiredAction.SkipDueToFailedValidation;
                return;
            }

            _globalUserErrorsManager.SetFlushableProductError(GlobalUserErrorSource.KastaValidation, match.PrestaProduct.Reference, null);
        }

        private void CreateNewKastaProductGroup(
            CategoriesConductorService categoriesConductor,
            ProductAttributesConductorService productAttributesConductor,
            LangAndTemplateConductorService langAndTemplateConductor,
            Match match
        )
        {
            if (match.ExternalPrestaProduct.ProductOptions.Length == 0)
                throw new Exception("ProductOptions.Length == 0 is not supported");

            long productId = match.PrestaProduct.ProductId;

            InvProductCategory mainCategory = _invProductFeatureCentre.GetMainCategory(match.PrestaProduct.Categories);
            
            if (!mainCategory.IsExportableCategory)
                throw new Exception($"Should have an exportable category. {match.PrestaProduct.Reference}");

            match.KastaCategoryId = mainCategory.KastaCategoryId.Value;

            bool isUpdate = match.DesiredAction == DesiredAction.Submit_UpdateCharacteristics;

            string vkFullPhotoId = _prestaWorker.Do(w => w.GetMainVkPhotoIdFromProductId(productId));

            string imageReference = _vkScrapFileDownload.MakeScrapItemImgPhotoPublic(vkFullPhotoId);

            WebResponse<KastaSubmitPhotoResponse, KastaResponse400> submitPhotoResponse = _webApi.SubmitPhoto($"https://ws.irisdrop.com.ua/res/get/img/vf/{imageReference}", CancellationToken.None);

            if (submitPhotoResponse.IsFail)
                throw new Exception(submitPhotoResponse.Fail.ToString());

            foreach (ProductOption productOption in match.ExternalPrestaProduct.ProductOptions)
            {
                KastaProductSubmit newKastaProduct = this.CreateNewKastaProduct(langAndTemplateConductor, match, productOption, submitPhotoResponse.Success.Path);

                match.KastaProductGroup.Products.Add(newKastaProduct);

                AttributeOption ae = productOption.AttributeOptions.Single();

                string productOptionValueName = ae.Name;

                (long?, long?) kastaSizeIds = this.GetKastaSizeId(match.KastaCategoryId, productOptionValueName, true);

                newKastaProduct.Characteristics = new List<KastaProductCharacteristic>();
                newKastaProduct.Characteristics.Add(new KastaProductCharacteristic
                {
                    Data = new KastaProductCharacteristicData
                    {
                        Sizes = new KastaProductCharacteristicDataSize { KastaSize = kastaSizeIds.Item1.Value }
                    },
                    KeyName = "kasta_size"
                });

                if (kastaSizeIds.Item2.HasValue)
                {
                    newKastaProduct.Characteristics.Add(new KastaProductCharacteristic
                        {
                            Data = new KastaProductCharacteristicData
                            {
                                Sizes = new KastaProductCharacteristicDataSize { KastaSize = kastaSizeIds.Item2.Value }
                            },
                            KeyName = "kasta_sizemax"
                        }
                    );
                }
            }

            List<KastaProductCharacteristic> kastaCharacteristicsForProductGroup = this.GetKastaFeaturesFromPrestaProduct(
                productAttributesConductor,
                match.KastaCategoryId,
                match.PrestaProduct
            );

            foreach (KastaProductSubmit kastaProduct in match.KastaProductGroup.Products.Cast<KastaProductSubmit>())
            {
                kastaProduct.Characteristics.AddRange(kastaCharacteristicsForProductGroup);
            }
        }

        private List<KastaProductCharacteristic> GetKastaFeaturesFromPrestaProduct(ProductAttributesConductorService productAttributesConductor, 
            KastaCategoryId kastaCategoryId, ProductInfo prestaProduct)
        {
            InvProductCategory category = _invProductFeatureCentre.GetMainCategory(kastaCategoryId);
            
            IList<(KastaCategorySchemaItem, long?)> kastaFeatureValues =
                this.GetKastaCategorySchemaItemsWithKastaValueIdsFromProductFeatures(
                    productAttributesConductor,
                    category,
                    prestaProduct.Features
                );

            List<KastaProductCharacteristic> kastaCharacteristicsForProductGroup = new();

            foreach (IGrouping<KastaCategorySchemaItem, (KastaCategorySchemaItem, long?)> kastaFeatureValueGroup in kastaFeatureValues.GroupBy(kfv => kfv.Item1))
            {
                kastaCharacteristicsForProductGroup.Add(new KastaProductCharacteristic
                    {
                        Data = new KastaProductCharacteristicData
                        {
                            Ids = kastaFeatureValueGroup.Select(id => id.Item2.Value).ToList()
                        },
                        KeyName = kastaFeatureValueGroup.Key.KeyName
                    }
                );
            }

            return kastaCharacteristicsForProductGroup;
        }
        
        private (long?, long?) GetKastaSizeId(KastaCategoryId kastaCategoryId, string sizeValue, bool throwException, bool ignoreSizeRangeSplitter = false)
        {
            if (!ignoreSizeRangeSplitter && sizeValue.Contains(LinkedNaming.SIZES_RANGE_SPLITTER))
            {
                string[] spl = sizeValue.Split(LinkedNaming.SIZES_RANGE_SPLITTER, StringSplitOptions.TrimEntries);

                if (spl.Length != 2)
                    throw new ArgumentException($"sizeValue {sizeValue} is invalid.");

                long? id1 = this.GetKastaSizeId(kastaCategoryId, spl[0], throwException, true).Item1;
                long? id2 = this.GetKastaSizeId(kastaCategoryId, spl[1], throwException, true).Item1;

                return (id1, id2);
            }

            KastaCategorySchemaItem schemaItem = _kastaCommon.GetKastaSchemaItems(kastaCategoryId)
                .Single(s => s.Type == "size" && s.KeyName == "kasta_size");

            if (schemaItem.SizeCharts.Count == 0)
                throw new Exception($"KastaSizeChart for {kastaCategoryId}, {sizeValue} was not found");

            if (schemaItem.SizeCharts.Count == 1)
            {
                KastaCategorySizeChart sizeChart = schemaItem.SizeCharts[0];

                KastaCategorySize kastaSizeSpec = sizeChart.Sizes.SingleOrDefault(s => s.Value.Equals(sizeValue, StringComparison.InvariantCultureIgnoreCase));

                return (kastaSizeSpec?.Id, null);
            }

            foreach (KastaCategorySizeChart sizeChart in schemaItem.SizeCharts)
            {
                if (!sizeChart.Name.Contains("Таблица размеров", StringComparison.InvariantCultureIgnoreCase))
                    continue;

                KastaCategorySize kastaSizeSpec = sizeChart.Sizes.SingleOrDefault(s => s.Value.Equals(sizeValue, StringComparison.InvariantCultureIgnoreCase));

                if (kastaSizeSpec != null)
                {
                    return (kastaSizeSpec.Id, null);
                }
            }

            if (throwException)
            {
                StringBuilder bdr = new();

                foreach (KastaCategorySizeChart kastaCategorySizeChart in schemaItem.SizeCharts)
                {
                    bdr.AppendLine(kastaCategorySizeChart.Name);

                    foreach (KastaCategorySize kastaCategorySize in kastaCategorySizeChart.Sizes)
                    {
                        bdr.Append(kastaCategorySize.Value);
                        bdr.Append(" ");
                    }

                    bdr.AppendLine();
                    bdr.AppendLine();
                }

                throw new Exception($"KastaSizeChart for {kastaCategoryId}, {sizeValue} was not found. " + bdr);
            }

            return (null, null);
        }

        private KastaProductSubmit CreateNewKastaProduct(
            LangAndTemplateConductorService langAndTemplateConductor,
            Match match,
            ProductOption productOption,
            string imageWebPath
        )
        {
            AttributeOption ae = productOption.AttributeOptions.Single();

            string productOptionValueName = ae.Name;

            string desc = IntStringUtil.ConvertSomeHtmlToText(
                              match.PrestaProduct.ShortDescriptionHtml.Trim()
                          )
                          + Environment.NewLine
                          + Environment.NewLine
                          +
                          langAndTemplateConductor.GetVkSourcedDescriptionWithoutQuantity(
                              IntStringUtil.ConvertSomeHtmlToText(match.PrestaProduct.DescriptionHtml)
                          );

            // desc = IntStringUtil.ConvertNewlinesToHtmlBr(desc);

            var p = new KastaProductSubmit
            {
                NameUk = match.PrestaProduct.Name,
                Stock = productOption.Quantity,
                UniqueSkuId = LinkedNaming.ExternalSiteUniqueSkuIdFromVkAuthorityArt(match.PrestaProduct.Reference, productOptionValueName),
                Code = LinkedNaming.ExternalSiteReferenceFromPrestaSourcedVkAuthorityArt(match.PrestaProduct.Reference),
                DescriptionUk = desc,
                Images = new List<string> { imageWebPath },
                Brand = "Україна",
                Size = productOptionValueName,
                Color = match.KastaColor,
                NewPrice = this.GetPrice(match)
            };

            p.OldPrice = p.NewPrice;

            return p;
        }

        private void UpdateKastaProduct(Match match, List<KastaUpdateStockRequestItem> outUpdateStock, List<KastaUpdatePricesRequestItem> outUpdatePrices)
        {
            foreach (ProductOption productOption in match.ExternalPrestaProduct.ProductOptions)
            {
                AttributeOption ae = productOption.AttributeOptions.Single();

                string productOptionValueName = ae.Name;
                string uniqueSkuId = LinkedNaming.ExternalSiteUniqueSkuIdFromVkAuthorityArt(match.PrestaProduct.Reference, productOptionValueName);

                KastaProduct kastaProduct = match.KastaProductGroup.Products.OfType<KastaProduct>().SingleOrDefault(p => p.UniqueSkuId == uniqueSkuId);

                if (kastaProduct == null)
                {
                    // TODO: error
                    Log.Warning("Invalid kasta products, code=" +
                              string.Join(",", match.KastaProductGroup.Products.Select(p => p.Code)) +
                              " extId=" +
                              string.Join(",", match.KastaProductGroup.Products.Select(p => p.UniqueSkuId))
                    );

                    return;
                }

                if (kastaProduct.LastUpdateStock != productOption.Quantity) // TotalStock? SupplierStock?
                {
                    Log.Debug($"{kastaProduct.Art} stock is {kastaProduct.LastUpdateStock}, should be {productOption.Quantity}");

                    outUpdateStock.Add(new KastaUpdateStockRequestItem
                    {
                        UniqueSkuId = uniqueSkuId,
                        Stock = productOption.Quantity
                    });
                }

                int prestaPrice = this.GetPrice(match);

                if (Math.Abs((int)Math.Ceiling(kastaProduct.NewPrice) - prestaPrice) > 0.001)
                {
                    Log.Debug($"{kastaProduct.Art} price is {kastaProduct.NewPrice}, should be {prestaPrice}");
                    
                    outUpdatePrices.Add(new KastaUpdatePricesRequestItem
                    {
                        UniqueSkuId = uniqueSkuId,
                        NewPrice = prestaPrice
                    });
                }
            }
        }

        private int GetPrice(Match match)
        {
            // Check for price override
            float? overridePrice = _priceManager.GetOverridePrice(match.PrestaProduct.Reference, 0);
            
            if (overridePrice.HasValue)
                return (int)Math.Ceiling(overridePrice.Value);
            
            // Original price calculation
            double price = (((int) match.PrestaProduct.Price) - CCAppConfig.PrestaIrisDrop.NonDropShippersPriceIncreaseUAH + _config.PriceIncrease);
            price *= 1.2;
            price = Math.Round(Math.Round(price, 0) / 10, 0) * 10;

            return (int)Math.Ceiling(price);
        }

        private double GetSupplierPrice(Match match)
        {
            double prestaPrice = (double)match.PrestaProduct.Price - CCAppConfig.PrestaIrisDrop.NonDropShippersPriceIncreaseUAH;

            return prestaPrice;
        }

        private void DeactivateKastaProduct(Match match, List<KastaUpdateStockRequestItem> outUpdateStock)
        {
            foreach (KastaProduct kastaProduct in match.KastaProductGroup.Products.OfType<KastaProduct>())
            {
                if (kastaProduct.LastUpdateStock != 0) // TotalStock? SupplierStock?
                {
                    outUpdateStock.Add(new KastaUpdateStockRequestItem
                    {
                        UniqueSkuId = kastaProduct.UniqueSkuId,
                        Stock = 0
                    });
                }
            }
        }

        private bool IsProductGroupInactivated(KastaProductVariationGroup group)
        {
            return group.Products.OfType<KastaProduct>().All(p => p.TotalStock == 0);
        }

        public IList<(KastaCategorySchemaItem, long?)> GetKastaCategorySchemaItemsWithKastaValueIdsFromProductFeatures(
            ProductAttributesConductorService productAttributesConductor,
            InvProductCategory category,
            IList<ProductFeatureAndValueId> productFeatures
        )
        {
            List<(KastaCategorySchemaItem, long?)> res = new();

            foreach (ProductFeatureAndValueId productFeatureIds in productFeatures)
            {
                string featureText = productAttributesConductor.GetFeatureText(productFeatureIds.FeatureId);
                
                InvProductFeature invFeature = category.Features.SingleOrDefault(f => f.Platform == InvPlatform.Kasta && f.Name == featureText);
                
                if (invFeature == null)
                    continue;
                
                var schemaItem = (KastaCategorySchemaItem)invFeature.PlatformData;

                string featureValueText = productAttributesConductor.GetFeatureValueText(productFeatureIds.FeatureValueId);
                
                KastaCategorySize kastaSize = schemaItem.ValueIds.FirstOrDefault(s => s.Value == featureValueText);

                res.Add((schemaItem, kastaSize?.Id));
            }

            return res;
        }

        private void FetchAllKastaCategories(CancellationToken ct)
        {
            WebResponse<KastaCategoriesListResponse, KastaResponse400> categories = _webApi.Do(w => w.GetAllCategories(ct));

            if (categories.IsFail)
                throw new Exception("");

            List<KastaCategory> kastaCategories = categories.Success.Items;

            foreach (KastaCategory kastaCategory in kastaCategories)
            {
                /*string[] relevantTopCategories = new[]
                {
                    "Чоловікам"
                };

                if (!relevantTopCategories.Contains(kastaCategory.Name))
                    continue;*/

                var kastaCategorySchemas = new List<KastaMyCategorySchemaKind>();

                foreach (KastaCategoryKind kastaCategoryKind in kastaCategory.Kinds)
                {
                    WebResponse<KastaCategoryDetailsResponse, KastaResponse400> schemaResponse =
                        _webApi.Do(w => w.GetCategoryDetails(kastaCategoryKind.KindId, kastaCategoryKind.AffiliationId, ct));

                    if (schemaResponse.IsFail)
                        throw new Exception("");

                    kastaCategorySchemas.Add(
                        new KastaMyCategorySchemaKind { CategoryKindItself = kastaCategoryKind, SchemaItems = schemaResponse.Success.Schema }
                    );
                }

                var schema = new KastaMyCategorySchema
                {
                    Category = kastaCategory,
                    KindsWithSchemas = kastaCategorySchemas
                };

                string json = _jsonSerializer.SerializeForInternals(schema, typeof(KastaMyCategorySchema));

                using (var sw = new StreamWriter(File.Open(@$"C:\Data\kasta_cats{schema.Category.NameAlias}.json", FileMode.Create)))
                {
                    sw.Write(json);
                }
            }

            GC.KeepAlive(0);
        }


        public class KastaProductVariationGroup
        {
            public string Art => Products.FirstOrDefault()?.Code;

            public List<KastaProductBase> Products { get; set; } = new();
        }

        public class Match
        {
            public KastaProductVariationGroup KastaProductGroup { get; set; } = new();
            public ExternalSyncProduct ExternalPrestaProduct { get; set; }

            public ProductInfo PrestaProduct => ExternalPrestaProduct?.PrestaProduct;

            public DesiredAction DesiredAction { get; set; }

            public string PrevKastaColor { get; set; }
            public string KastaColor { get; set; }
            public KastaCategoryId KastaCategoryId { get; set; }
        }

        public enum DesiredAction
        {
            Invalid = 0,

            Skip,
            SkipDueToFailedValidation,
            SkipAndReportRejection,
            SkipAndReportUnknownState,
            
            Submit_CreateKastaProducts,
            Submit_UpdateCharacteristics,

            UpdateIfNeeded,
            DeactivateWithoutValidation
        }

        private bool AreListsEqual<T>(IEnumerable<T> list1, IEnumerable<T> list2)
        {
            // Check if both lists contain the same elements regardless of order
            List<T> sortedList1 = list1.OrderBy(x => x).ToList();
            List<T> sortedList2 = list2.OrderBy(x => x).ToList();

            return sortedList1.SequenceEqual(sortedList2);
        }
    }
}
