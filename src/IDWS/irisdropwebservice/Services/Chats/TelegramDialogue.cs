using System.Threading.Tasks;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Logg;

using Telegram.Bot.Types;
using Telegram.Bot.Types.Enums;
using Telegram.Bot.Types.ReplyMarkups;

namespace irisdropwebservice.Services.Chats
{
    public class TelegramDialogue
    {
        private readonly ILogger _logger = InvLog.Logger<TelegramDialogue>();

        private readonly ITelegramSendWorkerPrivate _sendWorker;
        
        public TelegramBotWho WhichBot { get; }

        public ITelegramDialogueHandler DialogueHandler { get; }
        public TelegramDialogueUpdateHandler UpdateHandler { get; }

        public TelegramChatId BotChatId { get; init; }
        public TelegramChatId ChatId { get; }

        public bool IsShutDown { get; private set; }

        private bool _addedKeyboard;

        public event Action OnShutDown;

        public TelegramDialogue(ITelegramSendWorkerPrivate sendWorker, TelegramBotWho whichBot, ITelegramDialogueHandler dialogueHandler, TelegramDialogueUpdateHandler updateHandler, TelegramChatId botChatId, TelegramChatId chatId)
        {
            _sendWorker = sendWorker;
            BotChatId = botChatId;
            WhichBot = whichBot;
            DialogueHandler = dialogueHandler;
            UpdateHandler = updateHandler;
            ChatId = chatId;

            _sendWorker.Do(w => w.SendChatAction(whichBot, chatId, ChatAction.Typing));
        }

        public Task Reply(string msg, PostToTelegramOptions options)
        {
            var tasks = new List<Task>();

            lock (this)
            {
                if (options == null)
                {
                    options = new PostToTelegramOptions { RemoveKeyboard = _addedKeyboard, Silent = false };
                } else
                {
                    if (options.RemoveKeyboard.HasValue)
                        throw new Exception($"Options.{options.RemoveKeyboard} is managed by the dialogue class itself.");

                    options = options with { RemoveKeyboard = _addedKeyboard };
                }

                tasks.Add(_sendWorker.Run(w => w.PostToTelegram(WhichBot, msg, ChatId, options)));

                _addedKeyboard = false;
            }

            return Task.WhenAll(tasks);
        }

        public Task ReplyEnd(string msg, PostToTelegramOptions options)
        {
            var tasks = new List<Task>();
            tasks.Add(this.Reply(msg, options));
            tasks.Add(this.EndCore());
            return Task.WhenAll(tasks);
        }

        public Task End()
        {
            var tasks = new List<Task>();

            tasks.Add(this.EndCore());

            return Task.WhenAll(tasks);
        }

        private Task EndCore()
        {
            IsShutDown = true;

            Action ev = this.OnShutDown;

            if (ev != null)
                ev();

            return Task.CompletedTask;
        }

        public Task Reply(string message, ReplyKeyboardMarkup keyboard)
        {
            var tasks = new List<Task>();

            lock (this)
            {
                _addedKeyboard = true;

                tasks.Add(_sendWorker.Run(w => w.SendKeyboard(WhichBot, ChatId, message, keyboard)));
            }

            return Task.WhenAll(tasks);
        }

        public void Fail()
        {
            this.ReplyEnd("Сталася програмна помилка, зверніться до адміністратора або повторіть спробу пізніше.", null);
        }

        public void FinishAfterTask(Task<string> strTask, PostToTelegramOptions options)
        {
            strTask.ContinueWithShortThread(t => this.ReplyEnd(t.Result, options),
                TaskContinuationOptions.NotOnCanceled | TaskContinuationOptions.NotOnFaulted
            );

            strTask.ContinueWithShortThread(t => {
                _logger.Error("Error when processing dialogue.");
                _logger.Error(t.Exception);

                this.Fail();
            }, TaskContinuationOptions.OnlyOnFaulted);
        }

        public void FinishAfterTask(Task task)
        {
            task.ContinueWithShortThread(_ => this.End(),
                TaskContinuationOptions.NotOnCanceled | TaskContinuationOptions.NotOnFaulted
            );

            task.ContinueWithShortThread(t => {
                _logger.Error("Error when processing dialogue.");
                _logger.Error(t.Exception);

                this.Fail();
            }, TaskContinuationOptions.OnlyOnFaulted);
        }

        public static void BeginSendFailMessage(TelegramBotWho whichBot, ITelegramSendWorkerPrivate sendWorker, TelegramChatId chatId)
        {
            sendWorker.Do(w => w.PostToTelegram(whichBot, "Сталася програмна помилка, зверніться до адміністратора або повторіть спробу пізніше.", chatId, new PostToTelegramOptions { RemoveKeyboard = false, Silent = false }));
        }
    }

    public delegate void TelegramDialogueUpdateHandler(TelegramDialogue dialogue, Update update);

    public interface ITelegramDialogueHandler
    {
        TelegramDialogueUpdateHandler CanHandleConversationInitiation(TelegramBotWho whichBot, Update request);
    }

    public interface ITelegramChannelListener
    {
        void IncomingPost(Update update);
    }

    public interface ITelegramGroupListener
    {
        void IncomingMessage(TelegramBotWho who, Update update);
    }
}
