using System.Net.Http;
using System.Threading;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.ExteriorServ;
using Invictus.Nomenklatura.Workers;

using irisdropwebservice.Services.Chats.Ins;

using JetBrains.Annotations;

using Microsoft.Extensions.DependencyInjection;

using Telegram.Bot;

namespace irisdropwebservice.Services.Chats
{
    [UsedImplicitly]
    public class ChatsExteriorServiceBuilder : ExteriorServiceBuilderBase<ChatsExteriorService>
    {
        protected override void AddConcealedServices(ServiceCollection concealedServiceCollection)
        {
            concealedServiceCollection.AddWorkerSingleton<TelegramChatBotSendWorker>();

            concealedServiceCollection.AddFactory<ITelegramBotClient, TelegramBotClient>(preferredConstructor: new[] { typeof(string), typeof(HttpClient), typeof(CancellationToken) });
            concealedServiceCollection.AddSingleton<TelegramChatBotClients>();
            concealedServiceCollection.AddSingleton<TelegramChatBotContacts>();
            concealedServiceCollection.AddSingleton<TelegramChatBotPolling>();
            concealedServiceCollection.AddSingleton<TelegramCustomerChatBotTalkingRoutine>();
            concealedServiceCollection.AddSingleton<TelegramDbHandler>();

            this.RegisterDatabaseTypes(concealedServiceCollection);

            base.AddConcealedServices(concealedServiceCollection);
        }

        protected override void ExposeConcealedServices()
        {            
            this.ExposeSingleton<ITelegramDbHandler, TelegramDbHandler>();
            this.ExposeSingleton<ITelegramDbContacts, TelegramChatBotContacts>();
            this.ExposeSingleton<ITelegramSendWorker>();

            this.ExposeSingleton<TelegramChatBotPolling>();

            this.ExposeSingleton<IWorkerFather<TelegramChatBotSendWorker>>();
            
            base.ExposeConcealedServices();
        }
    }
}
