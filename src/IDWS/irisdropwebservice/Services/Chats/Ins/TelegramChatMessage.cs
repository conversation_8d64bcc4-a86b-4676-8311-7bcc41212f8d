using System.ComponentModel.DataAnnotations.Schema;

namespace irisdropwebservice.Services.Chats.Ins
{
    [Table("TelegramChatMessage")]
    public class TelegramChatMessage
    {
        public long Id { get; set; }
        public string ChatId { get; set; } // Either ChatDefinition or long ChatId
        public byte ChatType { get; set; }
        public int MessageId { get; set; }
        public long SenderId { get; set; }
        public string MessageText { get; set; }
        public DateTime DateTimeUtc { get; set; }
    }

    public enum OrderMessageInfoType
    {
        None = 0,
        Order_StaffNewOrder = 100,
        Order_StaffPackAndSendOrder = 101,
        Order_StaffReduceVkAvaliPaidOrder = 102,
        Reservation_StaffUnconfirmedReservation = 200,
        Customer_OrderProcessing = 300
    }

    [Table("TelegramChatMessageLinkedInfo")]
    public class TelegramChatMessageLinkedInfo
    {
        public long Id { get; set; }
        public long ChatMessageId { get; set; }
        public string Data { get; set; }
        public OrderMessageInfoType InfoType { get; set; }
    }
}
