using Invictus.Nomenklatura.Exceptions;

using Telegram.Bot.Types;

namespace irisdropwebservice.Services.Chats.Ins
{
    public class GenericChatBotResponse : ITelegramDialogueHandler
    {
        public TelegramDialogueUpdateHandler CanHandleConversationInitiation(TelegramBotWho whichBot, Update request)
        {
            Message message = request.GetAnyMessage();

            if (message != null && message.Text == "/start" && whichBot == TelegramBotWho.ForCustomers)
                return null;

            return (dialogue, update) =>
            {
                Message message = update.GetAnyMessage();

                if (message.Text == "/start")
                {
                    switch (dialogue.WhichBot)
                    {
                        case TelegramBotWho.Staff:
                            dialogue.ReplyEnd("😎", null);

                            return;
                        default: throw TypeAbominationException.Enum(typeof(TelegramBotWho), dialogue.WhichBot);
                    }
                }

                dialogue.ReplyEnd(
                    dialogue.WhichBot == TelegramBotWho.ForCustomers
                        ? "Введена невірна команда. Будь ласка скористайтесь меню."
                        : "Моя твоя не розуміть.",
                    null
                );
            };
        }
    }
}
