using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Misc;

using irisdropwebservice.AppConfig;

using Telegram.Bot;

namespace irisdropwebservice.Services.Chats.Ins
{
    public class TelegramChatBotClients
    {
        private readonly ITelegramBotClient _irisDropStaffBotClient;
        private readonly ITelegramBotClient _irisDropCustomersBotClient;

        public bool TwoBotsAreTheSame { get; }

        public TelegramChatBotClients(IServiceFactory<ITelegramBotClient> telegramBotClientServiceFactory)
        {
            if (RRAppConfig.TelegramIrisDrop.StaffChatBotClientToken != RRAppConfig.TelegramIrisDrop.CustomersChatBotClientToken)
            {
                _irisDropStaffBotClient = telegramBotClientServiceFactory.Create(RRAppConfig.TelegramIrisDrop.StaffChatBotClientToken);
                _irisDropCustomersBotClient = telegramBotClientServiceFactory.Create(RRAppConfig.TelegramIrisDrop.CustomersChatBotClientToken);
            } else
            {
                _irisDropCustomersBotClient = _irisDropStaffBotClient = telegramBotClientServiceFactory.Create(RRAppConfig.TelegramIrisDrop.StaffChatBotClientToken);
                TwoBotsAreTheSame = true;
            }
        }

        public ITelegramBotClient GetBotClient(TelegramBotWho whichOne)
        {
            switch (whichOne)
            {
                case TelegramBotWho.ForCustomers: return _irisDropCustomersBotClient;
                case TelegramBotWho.Staff: return _irisDropStaffBotClient;
                default: throw TypeAbominationException.Enum(typeof(TelegramBotWho), whichOne);
            }
        }

        public bool IsStaffBot(ITelegramBotClient telegramBotClient)
        {
            return telegramBotClient == _irisDropStaffBotClient;
        }

        public TelegramBotWho GetBotWho(ITelegramBotClient telegramBotClient)
        {
            if (_irisDropCustomersBotClient == _irisDropStaffBotClient)
                throw new Exception("Do not use this method when one bot does both functions.");

            if (telegramBotClient == _irisDropCustomersBotClient)
                return TelegramBotWho.ForCustomers;

            if (telegramBotClient == _irisDropStaffBotClient)
                return TelegramBotWho.Staff;

            throw new Exception("Unkown bot.");
        }

        public IEnumerable<ITelegramBotClient> GetAllBotClients()
        {
            if (TwoBotsAreTheSame)
            {
                yield return _irisDropStaffBotClient;
            } else
            {
                yield return _irisDropStaffBotClient;
                // yield return _irisDropCustomersBotClient;
            }
        }
    }
}
