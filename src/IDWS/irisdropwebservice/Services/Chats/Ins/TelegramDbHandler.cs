using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Misc;

using Microsoft.EntityFrameworkCore.Storage;

using Telegram.Bot.Types;

namespace irisdropwebservice.Services.Chats.Ins
{
    public class TelegramDbHandler : ITelegramDbHandler
    {
        private readonly IDbAccessFactory<TelegramChatMessagesDbAccess> _dbAccessFactory;

        public TelegramDbHandler(IDbAccessFactory<TelegramChatMessagesDbAccess> dbAccessFactory)
        {
            _dbAccessFactory = dbAccessFactory;
        }

        public TelegramChatMessage SaveIncomingUpdate(Update update)
        {
            if (update.Message == null && update.ChannelPost == null)
                return null;

            Message msg = update.GetAnyMessage();

            return this.SaveMessage(msg);
        }

        public void SaveOutgoingUpdate(Message message)
        {
            this.SaveMessage(message);
        }

        public void LinkMessageToData(Message message, string data, OrderMessageInfoType infoType)
        {
            using TelegramChatMessagesDbAccess access = _dbAccessFactory.CreateAccess();

            TelegramChatId chatId = message.GetChatId();

            if (chatId.MessageThreadId.HasValue)
                throw new NotImplementedException();

            TelegramChatMessage dbMessage = access.GetMessageByChatIdAndMessageId(chatId.DbChatId, message.MessageId);

            if (dbMessage == null)
                throw new Exception("Message not found.");

            access.AddNewOrderMessageInfo(
                new TelegramChatMessageLinkedInfo
                {
                    ChatMessageId = dbMessage.Id,
                    InfoType = infoType,
                    Data = data
                }
            );

            access.SaveChanges();
        }

        public void UpdateLinkedData(TelegramChatMessageLinkedInfo chatLinkedInfo)
        {
            using TelegramChatMessagesDbAccess access = _dbAccessFactory.CreateAccess();
            using IDbContextTransaction transaction = access.BeginTransaction();

            access.DeleteLinkedInfoById(chatLinkedInfo.Id);
            access.AddNewOrderMessageInfo(chatLinkedInfo);

            transaction.Commit();
            access.SaveChanges();
        }

        private TelegramChatMessage SaveMessage(Message msg)
        {
            string text = msg.GetAnyTechnicalMessageText();

            if (text == null)
                return null;

            TelegramChatId chatId = msg.GetChatId();

            if (chatId.MessageThreadId.HasValue)
                throw new NotImplementedException();

            using TelegramChatMessagesDbAccess access = _dbAccessFactory.CreateAccess();

            TelegramChatMessage existingMessage = access.GetMessageByChatIdAndMessageId(chatId.DbChatId, msg.MessageId);

            if (existingMessage != null)
                return existingMessage;

            var res = new TelegramChatMessage
            {
                ChatId = chatId.DbChatId,
                ChatType = (byte)msg.Chat.Type,
                MessageId = msg.MessageId,
                MessageText = text,
                SenderId = msg.From?.Id ?? msg.Chat.Id,
                DateTimeUtc = ServerClock.GetCurrentUtcTime().DateTime
            };

            access.AddNewMessage(res);
            access.SaveChanges();

            return res;
        }

        public TelegramChatMessage[] GetLast7DaysRecordedMessages(TelegramChatId chatId)
        {
            using TelegramChatMessagesDbAccess access = _dbAccessFactory.CreateAccess();

            return access.GetMessagesAfterDate(chatId.DbChatId, ServerClock.GetCurrentUtcTime().AddDays(-7).DateTime);
        }

        public TelegramChatMessage[] GetAllMessagesFromUserToUser(TelegramChatId senderId, TelegramChatId recipientId, int take)
        {
            using TelegramChatMessagesDbAccess access = _dbAccessFactory.CreateAccess();

            TelegramChatMessage[] msgs = access.GetMessagesByChatIdAndSenderId(recipientId.DbChatId, senderId.ChatId, take);

            return msgs;
        }

        public TelegramChatMessageAndLinkedInfo[] GetMessagesAndAssociatedDataByDbIds(long[] dbIds)
        {
            using TelegramChatMessagesDbAccess access = _dbAccessFactory.CreateAccess();

            TelegramChatMessage[] messages = access.GetMessagesByDbIds(dbIds);
            TelegramChatMessageLinkedInfo[] messageInfos = access.GetMessageInfosByMessageDbIds(dbIds);

            return LinkMessageAndLinkedInfo(messages, messageInfos);
        }

        public TelegramChatMessage GetDbMessageByChatIdAndMessageId(TelegramChatId chatId, int messageId)
        {
            using TelegramChatMessagesDbAccess access = _dbAccessFactory.CreateAccess();

            if (chatId.MessageThreadId.HasValue)
                throw new NotImplementedException();

            return access.GetMessageByChatIdAndMessageId(chatId.DbChatId, messageId);
        }

        public TelegramChatMessageAndLinkedInfo[] GetMessagesAndAssociatedDataByInfoType(OrderMessageInfoType infoType)
        {
            using TelegramChatMessagesDbAccess access = _dbAccessFactory.CreateAccess();

            TelegramChatMessageLinkedInfo[] messageInfos = access.GetMessageInfosByInfoType(infoType);
            TelegramChatMessage[] messages = access.GetMessagesByDbIds(messageInfos.Select(mi => mi.ChatMessageId).ToArray());

            return LinkMessageAndLinkedInfo(messages, messageInfos);
        }

        public TelegramChatMessageAndLinkedInfo[] GetMessagesByInfoTypeAndData(OrderMessageInfoType infoType, string data)
        {
            using TelegramChatMessagesDbAccess access = _dbAccessFactory.CreateAccess();

            TelegramChatMessageLinkedInfo[] messageInfos = access.GetMessageInfosByInfoTypeAndData(infoType, data);
            TelegramChatMessage[] messages = access.GetMessagesByDbIds(messageInfos.Select(mi => mi.ChatMessageId).ToArray());

            return LinkMessageAndLinkedInfo(messages, messageInfos);
        }

        private static TelegramChatMessageAndLinkedInfo[] LinkMessageAndLinkedInfo(TelegramChatMessage[] messages, TelegramChatMessageLinkedInfo[] messageInfos)
        {
            var res = new List<TelegramChatMessageAndLinkedInfo>();

            foreach (TelegramChatMessage telegramChatMessage in messages)
            {
                TelegramChatMessageLinkedInfo mi = messageInfos.FirstOrDefault(m => m.ChatMessageId == telegramChatMessage.Id);

                res.Add(new TelegramChatMessageAndLinkedInfo(telegramChatMessage, mi));
            }

            return res.ToArray();
        }
    }
}
