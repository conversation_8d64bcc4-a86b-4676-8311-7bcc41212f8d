using irisdropwebservice.AppConfig;
using irisdropwebservice.AppConfig.ClassConfigurations;

using Telegram.Bot.Types;

namespace irisdropwebservice.Services.Chats.Ins
{
    public static class TelegramUpdateExtensions
    {
        public static TelegramChatId GetChatId(this Message message)
        {
            ArgumentNullException.ThrowIfNull(message);

            return TelegramChatId.FromUnknownChatNameOrUser(message.Chat.Id);
        }

        public static (TelegramChatId, string)? GetChatIdAndText(this Update update)
        {
            Message msg = update.GetAnyMessage();

            if (msg == null)
                return null;

            return (TelegramChatId.FromUnknownChatNameOrUser(msg.Chat.Id), msg.GetAnyTechnicalMessageText());
        }

        public static string GetText(this Update update)
        {
            return update.GetAnyMessage()?.Text;
        }

        public static bool IsFromChatWithThisNameOrTheSameChatWithDifferentName(this Update update, TelegramChatConfiguration chatConfiguration)
        {
            Message msg = update.GetAnyMessage();

            if (msg == null)
                return false;

            return msg.Chat.Id == RRAppConfig.TelegramIrisDrop.GetChatId(chatConfiguration.DefinitionName);
        }

        public static Message GetAnyMessage(this Update update)
        {
            if (update.Message != null)
                return update.Message;

            if (update.EditedMessage != null)
                return update.EditedMessage;

            if (update.ChannelPost != null)
                return update.ChannelPost;
            
            if (update.EditedChannelPost != null)
                return update.EditedChannelPost;

            return null;
        }

        public static string GetAnyTechnicalMessageText(this Message message)
        {
            string text = message.Text;

            if (text != null)
                return text;

            text = message.GetTechnicalStickerText();

            return text;
        }

        public static string GetTechnicalStickerText(this Message message)
        {
            if (message.Sticker == null || message.Sticker.Emoji == null || message.Sticker.SetName == null)
                return null;

            return ChatsUtil.GetTechnicalStickerText(message.Sticker.SetName, message.Sticker.Emoji);
        }
    }
}
