using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Logg;

using PhoneNumbers;

using Telegram.Bot.Types;

namespace irisdropwebservice.Services.Chats.Ins
{
    public class TelegramChatBotContacts : ITelegramDbContacts
    {
        private readonly ILogger _logger = InvLog.Logger<TelegramChatBotContacts>();

        private static readonly PhoneNumberUtil _PhoneNumberUtil = PhoneNumberUtil.GetInstance();

        private readonly IDbAccessFactory<CustomerInfoDbAccess> _dbAccessFactory;

        public TelegramChatBotContacts(IDbAccessFactory<CustomerInfoDbAccess> dbAccessFactory)
        {
            _dbAccessFactory = dbAccessFactory;
        }

        public void HandleAnyUpdate(Update update)
        {
            Message message = update.GetAnyMessage();

            if (message == null)
                return;

            if (message.Chat.FirstName == null)
                return; // Not a user

            using CustomerInfoDbAccess dbAccess = _dbAccessFactory.CreateAccess();

            CustomerInfo customerInfo = dbAccess.GetCustomerInfoByTelegramUserIdTracked(message.Chat.Id) ?? dbAccess.NewCustomerInfoTracked();

            customerInfo.TelegramFirstName = message.Chat.FirstName;
            customerInfo.TelegramUserId = message.Chat.Id;

            if (message.Chat.LastName != null)
                customerInfo.TelegramLastName = message.Chat.LastName;

            customerInfo.TelegramLastChatId = message.Chat.Id;

            if (message.From?.Username != null)
                customerInfo.TelegramUserHandle = message.From?.Username;

            dbAccess.SaveChanges();
        }

        public bool IsValidPhoneNumber(string phoneNumberString)
        {
            try
            {
                PhoneNumber phoneNumber = _PhoneNumberUtil.Parse(phoneNumberString, "UA");

                _PhoneNumberUtil.Format(phoneNumber, PhoneNumberFormat.INTERNATIONAL);
            }
            catch (Exception exc)
            {
                _logger.Warning(exc);

                return false;
            }

            return true;
        }

        public void RegisterTelegramContactAndSubscribe(Contact contact, long chatId)
        {
            using CustomerInfoDbAccess dbAccess = _dbAccessFactory.CreateAccess();

            CustomerInfo customerInfo = dbAccess.GetCustomerInfoByTelegramUserIdTracked(chatId) ?? dbAccess.NewCustomerInfoTracked();

            _logger.Debug($"RegisterTelegramContactAndSubscribe with contact info: {contact.PhoneNumber} ; {contact.FirstName} ; {contact.LastName} ; {contact.UserId ?? -1}");

            PhoneNumber phoneNumber = _PhoneNumberUtil.Parse(contact.PhoneNumber, "UA");

            string phoneNumberInternational = _PhoneNumberUtil.Format(phoneNumber, PhoneNumberFormat.INTERNATIONAL);

            customerInfo.PhoneNumberInternational = phoneNumberInternational;
            customerInfo.TelegramLastChatId = chatId;
            customerInfo.TelegramFirstName = contact.FirstName;
            customerInfo.TelegramLastName = contact.LastName;
            customerInfo.TelegramUserId = contact.UserId ?? 0;
            customerInfo.SubToTelegramFromSiteUpdates = true;

            dbAccess.SaveChanges();
        }

        public CustomerInfo FindCustomerInfoByUserId(long userId)
        {
            using CustomerInfoDbAccess dbAccess = _dbAccessFactory.CreateAccess();

            CustomerInfo customerInfo = dbAccess.GetCustomerInfoByTelegramUserIdTracked(userId);

            return customerInfo;
        }

        public CustomerInfo FindCustomerByPhone(string phoneNumberText)
        {
            if (string.IsNullOrWhiteSpace(phoneNumberText))
                return null;

            try
            {
                PhoneNumber phoneNumber = _PhoneNumberUtil.Parse(phoneNumberText, "UA");

                string phoneNumberInternational = _PhoneNumberUtil.Format(phoneNumber, PhoneNumberFormat.INTERNATIONAL);

                using CustomerInfoDbAccess dbAccess = _dbAccessFactory.CreateAccess();

                CustomerInfo customerInfo = dbAccess.GetCustomerInfoByPhoneNumber(phoneNumberInternational);

                return customerInfo;
            }
            catch (NumberParseException exc)
            {
                _logger.Warning(exc);

                return null;
            }
        }

        public void UnsubscribeUserFromTelegram(long userId)
        {
            using CustomerInfoDbAccess dbAccess = _dbAccessFactory.CreateAccess();

            CustomerInfo customerInfo = dbAccess.GetCustomerInfoByTelegramUserIdTracked(userId);

            if (customerInfo == null)
                return;

            customerInfo.SubToTelegramFromSiteUpdates = false;

            dbAccess.SaveChanges();
        }

        CustomerInfo ITelegramDbContacts.GetCustomerInfoByPhone(string phone)
        {
            return this.FindCustomerByPhone(phone);
        }
    }
}
