using Invictus.Nomenklatura.Misc;

using Telegram.Bot.Types;
using Telegram.Bot.Types.ReplyMarkups;

namespace irisdropwebservice.Services.Chats.Ins
{
    public class TelegramCustomerChatBotTalkingRoutine : ITelegramDialogueHandler
    {
        private enum DialogueState
        {
            None = 0,
            WaitingForAreYouOutCustomerResponse,
            WaitingForAreYouStillOutCustomerResponse,
            WaitingForContact
        }

        private readonly TelegramChatBotContacts _telegramChatBotContacts;
        private readonly ITelegramDbHandler _telegramDbHandler;
        private readonly Dictionary<TelegramDialogue, DialogueState> _dialogueStates = new(); // TODO: WeakReference.

        public TelegramCustomerChatBotTalkingRoutine(TelegramChatBotContacts telegramChatBotContacts, ITelegramDbHandler telegramDbHandler)
        {
            _telegramChatBotContacts = telegramChatBotContacts;
            _telegramDbHandler = telegramDbHandler;
        }

        public TelegramDialogueUpdateHandler CanHandleConversationInitiation(TelegramBotWho whichBot, Update request)
        {
            if (whichBot != TelegramBotWho.ForCustomers)
                return null;

            if (request.Message == null)
                return null;

            if (request.Message.Text != "/start")
                return null;

            return this.HandleUpdate;
        }

        private void HandleUpdate(TelegramDialogue dialogue, Update update)
        {
            if (update.Message == null)
                return;

            DialogueState dialogueState;

            if (!_dialogueStates.TryGetValue(dialogue, out dialogueState))
            {
                _dialogueStates[dialogue] = dialogueState = DialogueState.None;
            }

            string messageText = update.Message.Text;

            if (messageText == "/start")
            {
                CustomerInfo thisCustomerInfo = _telegramChatBotContacts.FindCustomerInfoByUserId(update.Message.Chat.Id);

                var buttons = new List<KeyboardButton>(3);

                if (thisCustomerInfo != null && thisCustomerInfo.SubToTelegramFromSiteUpdates)
                {
                    buttons.Add(new KeyboardButton("Так"));
                    buttons.Add(new KeyboardButton("Так, але я більше не хочу отримувати сповіщення."));
                    buttons.Add(new KeyboardButton("Більше ні."));
                    buttons.Add(new KeyboardButton("Останні сповіщення."));

                    _dialogueStates[dialogue] = DialogueState.WaitingForAreYouStillOutCustomerResponse;
                } else
                {
                    buttons.Add(new KeyboardButton("Так"));
                    buttons.Add(new KeyboardButton("Ні"));

                    _dialogueStates[dialogue] = DialogueState.WaitingForAreYouOutCustomerResponse;
                }

                var keyboard = new ReplyKeyboardMarkup(buttons) { ResizeKeyboard = true };

                dialogue.Reply("Чи являєтесь ви дропшипером IrisDrop?", keyboard);

                return;
            }

            bool any;

            switch (dialogueState)
            {
                case DialogueState.WaitingForAreYouOutCustomerResponse:
                    switch (messageText)
                    {
                        case "Так":
                            KeyboardButton button = KeyboardButton.WithRequestContact("Надіслати мій контакт.");
                            var button2 = new KeyboardButton("Відміна");

                            var keyboard = new ReplyKeyboardMarkup(button, button2) { ResizeKeyboard = true };

                            _dialogueStates[dialogue] = DialogueState.WaitingForContact;

                            dialogue.Reply(
                                "Якщо ви хочете отримувати сповіщення про статуси замовлень, бронювання, тощо, будь ласка натисніть на кнопку 'Надіслати мій контакт'. " +
                                "Увага! Номер телефону цього телеграм аккаунту повинен співпадати з номером телефону який ви вказали на сайті!",
                                keyboard
                            );
                            return;
                        case "Ні":
                            dialogue.ReplyEnd("Тоді цей бот - не для вас.", null);
                            return;
                    }
                    break;
                case DialogueState.WaitingForAreYouStillOutCustomerResponse:
                    switch (messageText)
                    {
                        case "Так":
                            dialogue.ReplyEnd("Ви підписані на cповіщення з сайту: про статуси замовлень, бронювання, тощо.", null);
                            return;
                        case "Більше ні.":
                        case "Так, але я більше не хочу отримувати сповіщення.":

                            _telegramChatBotContacts.UnsubscribeUserFromTelegram(update.Message.Chat.Id);

                            dialogue.ReplyEnd("Ви були успішно відписані від оповіщень з сайту!", null);

                            return;
                        case "Останні сповіщення.":
                            TelegramChatMessage[] messages = _telegramDbHandler.GetAllMessagesFromUserToUser(dialogue.BotChatId, dialogue.ChatId, 200);
                            TelegramChatMessageAndLinkedInfo[] linkedInfos = _telegramDbHandler.GetMessagesAndAssociatedDataByDbIds(messages.Select(m => m.Id).ToArray());

                            any = false;
                            foreach (TelegramChatMessageAndLinkedInfo linkedInfo in linkedInfos.OrderBy(li => li.Message.DateTimeUtc))
                            {
                                if (linkedInfo.ChatLinkedInfo == null)
                                    continue;
                                if (linkedInfo.ChatLinkedInfo.InfoType != OrderMessageInfoType.Customer_OrderProcessing)
                                    continue;

                                dialogue.Reply(
                                    linkedInfo.Message.DateTimeUtc.UtcToUkraineTime().ToStringUADateAndTimeWithoutYear() + " " + linkedInfo.Message.MessageText,
                                    new PostToTelegramOptions()
                                );

                                any = true;
                            }

                            if (any)
                            {
                                dialogue.End();
                            } else
                            {
                                dialogue.ReplyEnd("Сповіщень немає.", null);
                            }

                            return;
                    }
                    break;
                case DialogueState.WaitingForContact:
                    if (messageText == "Відміна")
                    {
                        dialogue.ReplyEnd("Нажаль у цьому випадку ми не зможемо присилати вам сповіщення.", null);
                        return;
                    }
                    Contact contact = update.Message.Contact;

                    if (update.Message.Chat.FirstName != contact.FirstName || update.Message.Chat.LastName != contact.LastName)
                    {
                        dialogue.ReplyEnd("Невірний контакт.", null);
                        return;
                    }

                    if (string.IsNullOrWhiteSpace(contact.PhoneNumber))
                    {
                        dialogue.ReplyEnd("Нажаль у вашого контакту немає номеру телефону або він схований і ми не зможемо присилати вам сповіщення.", null);
                        return;
                    }

                    if (!_telegramChatBotContacts.IsValidPhoneNumber(contact.PhoneNumber))
                    {
                        dialogue.ReplyEnd("Нажаль ми не зможемо надсилати вам сповіщення за цим номером телефону.", null);
                        return;
                    }

                    _telegramChatBotContacts.RegisterTelegramContactAndSubscribe(contact, update.Message.Chat.Id);

                    dialogue.Reply("Тепер ви будете отримувати сповіщення на цей телеграм-аккаунт!", new PostToTelegramOptions());

                    // TODO: this is ultra slow
                    /*any = false;
                    foreach (customer customer in _prestaWebApi.GetAllCustomers())
                    {
                        Thread.Sleep(50);
                        if (_telegramChatBotContacts.FindCustomerByPhone(_prestaWebApi.Customers_GetCustomer(customer.id.Val()).PhoneNumber) != null)
                        {
                            any = true;
                            break;
                        }
                    }
                    if (!any)
                    {
                        dialogue.Reply("Зверніть увагу що користувача з цим номером телефону наразі на сайті не існує.", new PostToTelegramOptions());
                    }*/

                    dialogue.End();

                    break;
            }
        }
    }
}
