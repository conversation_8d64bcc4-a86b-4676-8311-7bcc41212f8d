using Invictus.Nomenklatura.App2;

using Microsoft.EntityFrameworkCore;

namespace irisdropwebservice.Services.Chats.Ins
{
    public class CustomerInfoDbAccess : DbAccessBase
    {
        private readonly MyDbContext _dbContext;

        public CustomerInfoDbAccess(DbAccessOptions accessOptions, MyDbContext context)
            : base(accessOptions, context)
        {
            _dbContext = context;
        }

        public CustomerInfo GetCustomerInfoByTelegramUserIdTracked(long telegramUserId)
        {
            return _dbContext.CustomerInfos
                .FirstOrDefault(ci => ci.TelegramUserId == telegramUserId);
        }

        public CustomerInfo GetCustomerInfoByPhoneNumber(string phoneNumber)
        {
            string phoneNumberFullLength = phoneNumber.PadRight(32);

            return _dbContext.CustomerInfos
                .AsNoTracking()
                .FirstOrDefault(ci => ci.PhoneNumberFullLength == phoneNumberFullLength);
        }

        public CustomerInfo NewCustomerInfoTracked()
        {
            var res = new CustomerInfo();
            _dbContext.CustomerInfos.Add(res);

            return res;
        }

        public class MyDbContext : DbContext
        {
            public virtual DbSet<CustomerInfo> CustomerInfos { get; set; }

            public MyDbContext(DbContextOptions<MyDbContext> options)
                : base(options)
            {
            }

            protected override void OnModelCreating(ModelBuilder modelBuilder)
            {
                modelBuilder.Entity<CustomerInfo>()
                    .Ignore(nameof(CustomerInfo.PhoneNumberInternational))
                    .Ignore(nameof(CustomerInfo.TelegramUserHandleLink))
                    .Property(nameof(CustomerInfo.PhoneNumberFullLength))
                    .HasColumnName("PhoneNumber");

                base.OnModelCreating(modelBuilder);
            }
        }
    }
}
