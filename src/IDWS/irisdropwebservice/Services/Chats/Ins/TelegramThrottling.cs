using Invictus.Nomenklatura.Misc;

namespace irisdropwebservice.Services.Chats.Ins
{
    public class TelegramThrottling
    {
        public static TelegramThrottling NonStrict { get; } = new TelegramThrottling(false);

        private record SenderThrottling(RateLimitCounter[] SenderRateLimitter)
        {
            public Dictionary<long, RateLimitCounter> ReceiversRateLimitters { get; } = new ();
        }

        private readonly bool _isStrictRateLimit;
        private readonly Dictionary<long, SenderThrottling> _rateLimits = new ();

        private TelegramThrottling(bool isStrictRateLimit)
        {
            _isStrictRateLimit = isStrictRateLimit;
        }

        public TimeSpan GetWaitTime(long sender, TelegramChatId chatId)
        {
            RateLimitCounter[] toWaitFor = this.GetThrottlingToWait(sender, chatId.ChatId).ToArray();

            return toWaitFor.Max(t => t.GetWaitTime());
        }

        public void RegisterActionIrregardless(long sender, TelegramChatId chatId)
        {
            RateLimitCounter[] toWaitFor = this.GetThrottlingToWait(sender, chatId.ChatId).ToArray();

            foreach (RateLimitCounter throttling in toWaitFor)
            {
                throttling.RegisterActionIrregardless();
            }
        }

        private IEnumerable<RateLimitCounter> GetThrottlingToWait(long sender, long chatId)
        {
            if (!_rateLimits.TryGetValue(sender, out SenderThrottling st))
            {
                var senderRateLimits = new RateLimitCounter[2];

                if (_isStrictRateLimit)
                {
                    senderRateLimits[0] = new RateLimitCounter(1, TimeSpan.FromSeconds(20));
                    senderRateLimits[1] = new RateLimitCounter(2, TimeSpan.FromSeconds(60));
                }
                else
                {
                    senderRateLimits[0] = new RateLimitCounter(1, TimeSpan.FromMilliseconds(500));
                    senderRateLimits[1] = new RateLimitCounter(17, TimeSpan.FromSeconds(22));
                }

                _rateLimits[sender] = st = new SenderThrottling(senderRateLimits);
            }

            foreach (RateLimitCounter rateLimitter in st.SenderRateLimitter)
            {
                yield return rateLimitter;
            }

            if (!st.ReceiversRateLimitters.ContainsKey(chatId))
            {
                st.ReceiversRateLimitters[chatId] = _isStrictRateLimit
                    ? new RateLimitCounter(1, TimeSpan.FromMinutes(1))
                    : new RateLimitCounter(17, TimeSpan.FromMinutes(1));
            }

            yield return st.ReceiversRateLimitters[chatId];
        }
    }
}
