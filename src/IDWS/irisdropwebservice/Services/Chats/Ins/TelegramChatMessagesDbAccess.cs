using Invictus.Nomenklatura.App2;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace irisdropwebservice.Services.Chats.Ins
{
    public class TelegramChatMessagesDbAccess : DbAccessBase
    {
        private readonly MyDbContext _dbContext;

        public TelegramChatMessagesDbAccess(DbAccessOptions accessOptions, MyDbContext context)
            : base(accessOptions, context)
        {
            _dbContext = context;
        }

        public IDbContextTransaction BeginTransaction()
        {
            return _dbContext.Database.BeginTransaction();
        }

        public void AddNewMessage(TelegramChatMessage telegramChatMessage)
        {
            _dbContext.TelegramChatMessages.Add(telegramChatMessage);
        }

        public TelegramChatMessage GetMessageByChatIdAndMessageId(string chatId, int messageId)
        {
            return _dbContext.TelegramChatMessages.SingleOrDefault(m => m.ChatId == chatId && m.MessageId == messageId);
        }

        public TelegramChatMessage[] GetMessagesByChatIdAndSenderId(string chatId, long senderId, int take)
        {
            return _dbContext.TelegramChatMessages
                .Where(m => m.ChatId == chatId && m.SenderId == senderId)
                .OrderByDescending(m => m.DateTimeUtc)
                .Take(take)
                .ToArray();
        }

        public void AddNewOrderMessageInfo(TelegramChatMessageLinkedInfo telegramChatMessageLinkedInfo)
        {
            _dbContext.TelegramOrderMessageInfos.Add(telegramChatMessageLinkedInfo);
        }

        public void DeleteLinkedInfoById(long id)
        {
            _dbContext.TelegramOrderMessageInfos.RemoveRange(_dbContext.TelegramOrderMessageInfos.Where(i => i.Id == id).ToArray());
        }

        public TelegramChatMessage[] GetMessagesAfterDate(string chatId, DateTime dateTimeUtc)
        {
            return _dbContext.TelegramChatMessages.Where(m => m.ChatId == chatId && m.DateTimeUtc > dateTimeUtc).ToArray();
        }

        public TelegramChatMessage[] GetMessagesByDbIds(long[] messageIds)
        {
            return _dbContext.TelegramChatMessages.Where(m => messageIds.Contains(m.Id)).ToArray();
        }

        public TelegramChatMessageLinkedInfo[] GetMessageInfosByMessageDbIds(long[] chatMessageDbIds)
        {
            return _dbContext.TelegramOrderMessageInfos.Where(m => chatMessageDbIds.Contains(m.ChatMessageId)).ToArray();
        }

        public TelegramChatMessageLinkedInfo[] GetMessageInfosByInfoType(OrderMessageInfoType infoType)
        {
            return _dbContext.TelegramOrderMessageInfos.Where(m => m.InfoType == infoType).ToArray();
        }

        public TelegramChatMessageLinkedInfo[] GetMessageInfosByInfoTypeAndData(OrderMessageInfoType infoType, string data)
        {
            return _dbContext.TelegramOrderMessageInfos.Where(m => m.InfoType == infoType && m.Data == data).ToArray();
        }

        public override void SaveChanges()
        {
            _dbContext.SaveChanges();
        }

        public class MyDbContext : DbContext
        {
            public virtual DbSet<TelegramChatMessage> TelegramChatMessages { get; set; }
            public virtual DbSet<TelegramChatMessageLinkedInfo> TelegramOrderMessageInfos { get; set; }

            public MyDbContext(DbContextOptions<MyDbContext> options)
                : base(options)
            {
            }

            protected override void OnModelCreating(ModelBuilder modelBuilder)
            {
                modelBuilder.Entity<CustomerInfo>()
                    .Ignore(nameof(CustomerInfo.PhoneNumberInternational))
                    .Property(nameof(CustomerInfo.PhoneNumberFullLength))
                    .HasColumnName("PhoneNumber");

                base.OnModelCreating(modelBuilder);
            }
        }
    }
}
