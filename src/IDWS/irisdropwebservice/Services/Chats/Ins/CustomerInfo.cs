using System.ComponentModel.DataAnnotations.Schema;

namespace irisdropwebservice.Services.Chats.Ins
{
    [Table("CustomerInfo")]
    public class CustomerInfo
    {
        public long Id { get; set; }

        public string TelegramFirstName { get; set; }
        public string TelegramLastName { get; set; }

        public string TelegramUserHandle { get; set; }
        public string TelegramUserHandleLink => "t.me/" + TelegramUserHandle;

        public string PhoneNumberFullLength { get; set; }

        public string PhoneNumberInternational
        {
            get => PhoneNumberFullLength.Trim();
            set => PhoneNumberFullLength = value;
        }

        public long TelegramUserId { get; set; }
        public long TelegramLastChatId { get; set; }

        public bool SubToTelegramFromSiteUpdates { get; set; }
    }
}