/*using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

using irisdropwebservice.AppConfig;
using irisdropwebservice.Framework.ExteriorServicing;
using Invictus.Nomenklatura.Misc;
using irisdropwebservice.Services.Chats.Ins;

using Microsoft.Extensions.Hosting;

using Serilog.Events;

using TL;

using Update = Telegram.Bot.Types.Update;

namespace irisdropwebservice.Services.Chats
{
    [AutoRegisterInExteriorService]
    public class TelegramUserThread : TelegramThreadBase
    {
        protected override LogEventLevel LogLevel => LogEventLevel.Information;
        protected override ILogger Log { get; } = InvLog.Logger<TelegramUserThread>();
        protected override ThreadPriority Priority => ThreadPriority.AboveNormal;

        private readonly ITelegramChatBotWorker _telegramChatBotWorker;
        private readonly TelegramChatBotPolling _telegramChatBotPolling;

        public TelegramUserThread(IInvAppLifetime applicationLifetime, InvAppConfig appConfig,ITelegramChatBotWorker telegramChatBotWorker, TelegramChatBotPolling telegramChatBotPolling)
            : base(applicationLifetime, isStrictRateLimit: true)
        {
            _telegramChatBotWorker = telegramChatBotWorker;
            _telegramChatBotPolling = telegramChatBotPolling;
        }

        private string Config(string what)
        {
            return what switch
            {
                "session_pathname" => @"C:\Data\IrisDropWs\Session",

                "device_model"     => "PC 64-bit",
                "system_version"   => Environment.OSVersion.ToString(),
                "app_version"      => this.GetType().Assembly.GetName().Version.ToString(),
                "system_lang_code" => CultureHandler.UkrainianCulture.TwoLetterISOLanguageName,
                "lang_pack"        => "",
                "lang_code"        => CultureHandler.UkrainianCulture.TwoLetterISOLanguageName,
                "user_id"          => "-1",

                "email_verification_code" => throw new NotImplementedException("WTelegram config : " + what),
                "first_name"       => throw new NotImplementedException("WTelegram config : " + what),
                "last_name"        => throw new NotImplementedException("WTelegram config : " + what),
                _                  => throw new NotImplementedException("WTelegram config : " + what)
            };
        }

        private class VerificationResponseWait : ITelegramDialogueHandler
        {
            private readonly TaskCompletionSource<string> _tcs = new();

            public Task<string> Result => _tcs.Task;

            public TelegramDialogueUpdateHandler CanHandleConversationInitiation(TelegramBotWho whichBot, Update request)
            {
                if (whichBot != TelegramBotWho.Staff)
                    return null;

                if (request.Message == null || request.Message.Text == null)
                    return null;

                const string PATTERN = @"(?i)\b(?:vercode)\s+\d+\b";

                string text = request.Message.Text.Trim();

                if (!Regex.IsMatch(text, PATTERN))
                    return null;

                return (dialogue, update, initialArg) =>
                {
                    if (update.Message == null || update.Message.Text == null)
                        return;

                    string[] spl = update.Message.Text.Split(' ', StringSplitOptions.RemoveEmptyEntries);

                    _tcs.SetResult(spl[1]);

                    dialogue.End();
                };
            }
        }

        private string GetVerificationCodeViaBot()
        {
            var waitForVerification = new VerificationResponseWait();

            _telegramChatBotPolling.AddDialogueHandler(waitForVerification);

            try
            {

                _telegramChatBotWorker
                    .QPostToTelegramFromStaffAccount(
                        "Будь ласка пришліть код верифікації до аккаунту 'Оператор IrisDrop' у форматі 'vercode XXXXX', чекаю 30 секунд",
                        TelegramChatId.FromUnknownChatNameOrUser(RRAppConfig.TelegramIrisDrop.ChatDefinitions.DeveloperUserChatId),
                        new PostToTelegramOptions() { Silent = false }
                    )
                    .Wait(_applicationLifetime.ApplicationStopping);

                waitForVerification.Result.Wait(_applicationLifetime.ApplicationStopping);

                return waitForVerification.Result.Result;
            }
            finally
            {
                _telegramChatBotPolling.RemoveDialogueHandler(waitForVerification);
            }
        }

        public async Task PostSomething()
        {
            using var client = new WTelegram.Client(this.Config);

            await client.LoginUserIfNeeded();

            Contacts_ResolvedPeer userName = await client.Contacts_ResolveUsername("...");

            // var contact = await client.Contacts_ImportContacts(new[] { new InputPhoneContact { phone = "+PHONENUMBER" } });

            GC.KeepAlive(RRAppConfig.TelegramIrisDrop.ChatDefinitions.DeveloperUserChatId);

            Message message = await client.SendMessageAsync(new InputPeerUser(userName.User.id, userName.User.access_hash), "Привіт! Це повідомлення було відправлено автоматично! Але також я (Макс) можу зайти і відправити ще щось сам!");

            GC.KeepAlive(message);
        }
    }
}
*/