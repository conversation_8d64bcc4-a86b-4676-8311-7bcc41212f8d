using irisdropwebservice.AppConfig;
using irisdropwebservice.AppConfig.ClassConfigurations;
using irisdropwebservice.Services.Chats.Ins;

using Telegram.Bot.Types;

namespace irisdropwebservice.Services.Chats
{
    public interface ITelegramDbContacts
    {
        CustomerInfo GetCustomerInfoByPhone(string phone);
    }

    public record TelegramChatMessageAndLinkedInfo(TelegramChatMessage Message, TelegramChatMessageLinkedInfo ChatLinkedInfo);

    public interface ITelegramDbHandler
    {
        void LinkMessageToData(Message message, string data, OrderMessageInfoType infoType);
        void UpdateLinkedData(TelegramChatMessageLinkedInfo chatLinkedInfo);
        TelegramChatMessage[] GetLast7DaysRecordedMessages(TelegramChatId chatId);
        TelegramChatMessageAndLinkedInfo[] GetMessagesAndAssociatedDataByDbIds(long[] dbMessageIds);
        TelegramChatMessageAndLinkedInfo[] GetMessagesAndAssociatedDataByInfoType(OrderMessageInfoType infoType);
        TelegramChatMessageAndLinkedInfo[] GetMessagesByInfoTypeAndData(OrderMessageInfoType infoType, string data);
        TelegramChatMessage GetDbMessageByChatIdAndMessageId(TelegramChatId chatId, int messageId);
        TelegramChatMessage[] GetAllMessagesFromUserToUser(TelegramChatId senderId, TelegramChatId recipientId, int take);
    }

    public enum TelegramBotWho
    {
        Foe = 0,
        Staff,
        ForCustomers
    }

    public record PostToTelegramOptions
    {
        public bool? RemoveKeyboard { get; set; } = null;
        public bool Silent { get; set; } = true;
        public bool DisableWebPagePreview { get; set; } = false;
        public int? ReplyToMessageId { get; set; }
        public bool HtmlFormattingEnabled { get; set; } = false;
    }

    public record AutotestUserPostToTelegramOptions : PostToTelegramOptions
    {
        public bool IsChannelPost { get; set; }
    }

    /*
     * It has 3 states:
     *  - Has ChatId but no Definition
     *  - Has ChatId and Definition
     *  - Has ChatName with both Definition and ChatId
     */
    public readonly record struct TelegramChatId
    {
        public long ChatId { get; }
        public int? MessageThreadId { get; }

        private readonly string _chatDefinitionName;

        public TelegramChatConfiguration? ChatConfiguration { get; }

        public string ChatDefinitionName => ChatConfiguration?.DefinitionName ?? _chatDefinitionName;

        public string DbChatId => ChatDefinitionName ?? ChatId.ToString();

        public static TelegramChatId FromUnknownChatNameOrUser(long chatId)
        {
            return new TelegramChatId(chatId);
        }

        public static TelegramChatId FromUnknownChat(string chatDefinitionName)
        {
            return new TelegramChatId(chatDefinitionName);
        }

        private TelegramChatId(long chatId)
        {
            ChatId = chatId;
            ChatConfiguration = null;
            _chatDefinitionName = RRAppConfig.TelegramIrisDrop.GetChatDefinitionNameByChatId(chatId);
            MessageThreadId = null;
        }

        private TelegramChatId(string chatDefinitionName)
        {
            ChatConfiguration = null;
            _chatDefinitionName = chatDefinitionName;
            ChatId = RRAppConfig.TelegramIrisDrop.GetChatId(chatDefinitionName);
            MessageThreadId = null;
        }

        private TelegramChatId(TelegramChatConfiguration chatConfiguration)
        {
            ChatConfiguration = chatConfiguration;
            string chatDefinitionName = chatConfiguration.DefinitionName;
            ChatId = RRAppConfig.TelegramIrisDrop.GetChatId(chatDefinitionName);
            MessageThreadId = null;
            _chatDefinitionName = null;
        }

        public static implicit operator TelegramChatId(TelegramChatConfiguration s) => new (s);

        public override string ToString()
        {
            if (ChatDefinitionName != null)
                return "HasDefinition: " + ChatDefinitionName;

            return "OnlyId: " + ChatId;
        }
    }
}
