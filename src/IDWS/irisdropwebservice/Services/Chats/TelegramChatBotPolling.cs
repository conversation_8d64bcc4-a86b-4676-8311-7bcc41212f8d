using System.Threading;
using System.Threading.Tasks;

using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.AppConfig;
using irisdropwebservice.Services.Chats.Ins;

using Telegram.Bot;
using Telegram.Bot.Exceptions;
using Telegram.Bot.Polling;
using Telegram.Bot.Types;
using Telegram.Bot.Types.Enums;

namespace irisdropwebservice.Services.Chats
{
    public class TelegramChatBotPolling : IUpdateHandler
    {
        private const TelegramBotWho DEFAULT_TEST_BOT_MODE = TelegramBotWho.Staff;

        public const string SET_TEST_BOT_TO_STAFF_COMMAND = "/staff";
        public const string SET_TEST_BOT_TO_CUSTOMERS_COMMAND1 = "/client";
        public const string SET_TEST_BOT_TO_CUSTOMERS_COMMAND2 = "/customer";

        private readonly ILogger _logger = InvLog.Logger<TelegramChatBotPolling>();

        private readonly IInvAppLifetime _applicationLifetime;
        private readonly ITelegramSendWorkerPrivate _sendWorker;
        private readonly TelegramChatBotClients _chatBotClients;
        private readonly TelegramDbHandler _telegramDbHandler;
        private TelegramChatBotContacts _chatBotContacts;

        private record DialogueKey(TelegramBotWho Who, TelegramChatId ChatId);

        private readonly List<ITelegramDialogueHandler> _firstDialogueHandlers = new();
        private readonly List<ITelegramDialogueHandler> _lastDialogueHandlers = new();
        private readonly List<ITelegramChannelListener> _channelListeners = new();
        private readonly List<ITelegramGroupListener> _groupListeners = new();
        private readonly List<(DialogueKey key, TelegramDialogue dialogue)> _activeDialogues = new();

        private readonly Dictionary<TelegramChatId, TelegramBotWho> _currentTestBotMode = new();

        public TelegramChatBotPolling(IInvAppLifetime applicationLifetime, InvAppConfig appConfig,TelegramChatBotClients chatBotClients, TelegramDbHandler telegramDbHandler, ITelegramSendWorkerPrivate sendWorker)
        {
            _applicationLifetime = applicationLifetime;
            _chatBotClients = chatBotClients;
            _telegramDbHandler = telegramDbHandler;
            _sendWorker = sendWorker;
        }

        public void AddDialogueHandler(ITelegramDialogueHandler dialogueHandler)
        {
            lock (_firstDialogueHandlers)
            {
                _firstDialogueHandlers.Add(dialogueHandler);
            }
        }

        public void AddLastDialogueHandler(ITelegramDialogueHandler dialogueHandler)
        {
            lock (_lastDialogueHandlers)
            {
                _lastDialogueHandlers.Add(dialogueHandler);
            }
        }

        public void RemoveDialogueHandler(ITelegramDialogueHandler dialogueHandler)
        {
            lock (_firstDialogueHandlers)
            {
                if (!_firstDialogueHandlers.Remove(dialogueHandler))
                    throw new Exception("Not removed");
            }
        }

        public void AddChannelListener(ITelegramChannelListener channelListener)
        {
            lock (_channelListeners)
            {
                _channelListeners.Add(channelListener);
            }
        }

        public void AddGroupListener(ITelegramGroupListener groupListener)
        {
            lock (_groupListeners)
            {
                _groupListeners.Add(groupListener);
            }
        }

        public void Activate(TelegramChatBotContacts chatBotContacts)
        {
            _chatBotContacts = chatBotContacts;

            foreach (ITelegramBotClient telegramBotClient in _chatBotClients.GetAllBotClients())
            {
                telegramBotClient.StartReceiving(this,
                    // new ReceiverOptions()
                    // {
                    //     AllowedUpdates = new UpdateType[] {
                    //         UpdateType.Message,
                    //         UpdateType.ChannelPost,
                    //         UpdateType.EditedMessage,
                    //         UpdateType.EditedChannelPost,
                    //     }
                    // },
                    cancellationToken: _applicationLifetime.ApplicationStopping);
            }

            _logger.Information("Bots are subscribed to messages.");
        }

        private TelegramDialogue FindActiveDialogue(TelegramBotWho who, TelegramChatId chatId)
        {
            return _activeDialogues.SingleOrDefault(d => d.key.Who == who && d.key.ChatId == chatId).dialogue;
        }

        Task IUpdateHandler.HandleUpdateAsync(ITelegramBotClient botClient, Update update, CancellationToken cancellationToken)
        {
            // 1: Handle generic.

            if (update.IsFromChatWithThisNameOrTheSameChatWithDifferentName(CCAppConfig.TelegramIrisDrop.IrisGarbageChat))
                return Task.CompletedTask;

            (TelegramChatId, string)? chatIdAndText = update.GetChatIdAndText();

            if (chatIdAndText == null)
            {
                _logger.Debug("Received BOT message, but cannot process.");

                return Task.CompletedTask;
            }

            TelegramChatId chatId = chatIdAndText.Value.Item1;
            string text = chatIdAndText.Value.Item2;

            if ((update?.GetAnyMessage()?.SenderChat?.Id ?? 0) == -1010001)
            {
                return Task.CompletedTask;
            }

            if (text != null && text.StartsWith("__"))
                text = text.Remove(0, 2).Replace("_", " ");

            _telegramDbHandler.SaveIncomingUpdate(update);

            _chatBotContacts.HandleAnyUpdate(update);

            // 2: Find which bot.

            TelegramBotWho who;

            if (_chatBotClients.TwoBotsAreTheSame)
            {
                if (update.Message != null)
                {
                    if (update.Message.Text == SET_TEST_BOT_TO_CUSTOMERS_COMMAND1 || update.Message.Text == SET_TEST_BOT_TO_CUSTOMERS_COMMAND2)
                    {
                        _sendWorker.Do(w => w.PostToTelegram(TelegramBotWho.Staff, "Тепер я бот для клієнтів.", chatId, new PostToTelegramOptions()));
                        _sendWorker.Do(w => w.PostToTelegram(TelegramBotWho.Staff, "click /start", chatId, new PostToTelegramOptions()));

                        _currentTestBotMode[chatId] = TelegramBotWho.ForCustomers;

                        foreach ((_, TelegramDialogue dialogue) in _activeDialogues.Where(d => d.key.ChatId == chatId).ToArray())
                            this.DialogueDeactivated(dialogue);

                        return Task.CompletedTask;
                    }

                    if (update.Message.Text == SET_TEST_BOT_TO_STAFF_COMMAND)
                    {
                        _sendWorker.Do(w => w.PostToTelegram(TelegramBotWho.Staff, "Тепер я бот для співробітників.", chatId, new PostToTelegramOptions()));
                        _sendWorker.Do(w => w.PostToTelegram(TelegramBotWho.Staff, "click /start", chatId, new PostToTelegramOptions()));

                        _currentTestBotMode[chatId] = TelegramBotWho.Staff;

                        foreach ((_, TelegramDialogue dialogue) in _activeDialogues.Where(d => d.key.ChatId == chatId).ToArray())
                            this.DialogueDeactivated(dialogue);

                        return Task.CompletedTask;
                    }
                }

                if (!_currentTestBotMode.TryGetValue(chatId, out who))
                    who = DEFAULT_TEST_BOT_MODE;
            }
            else
            {
                who = _chatBotClients.GetBotWho(botClient);
            }

            // 3: Handle non-dialogue cases.

            if (update.Message != null)
            {
                if (update.Message.Chat.Type != ChatType.Private)
                {
                    lock (_groupListeners)
                    {
                        foreach (ITelegramGroupListener telegramGroupListener in _groupListeners)
                        {
                            telegramGroupListener.IncomingMessage(who, update);
                        }
                    }

                    return Task.CompletedTask;
                }

                _logger.Information($"Received BOT message, chatId={chatId}, text={text ?? "(empty)"}.");
            }
            else if (update.EditedMessage != null)
            {
                if (update.EditedMessage.Chat.Type != ChatType.Private)
                {
                    _logger.Verbose($"Received non-private edited message, chatId={chatId}, discarding");

                    return Task.CompletedTask;
                }

                _logger.Information($"Received BOT EDITED message, chatId={chatId}, text={text ?? "(empty)"}.");
            } else if ((update.ChannelPost != null || update.EditedChannelPost != null) && _chatBotClients.IsStaffBot(botClient))
            {
                lock (_channelListeners)
                {
                    foreach (ITelegramChannelListener telegramChannelListener in _channelListeners)
                    {
                        telegramChannelListener.IncomingPost(update);
                    }
                }

                return Task.CompletedTask;
            }
            else
            {
                _logger.Debug($"Received unrecognized message, chatId={chatId}, discarding");

                return Task.CompletedTask;
            }

            // 4: Find active dialogue, push messages to that dialogue.

            if (who == TelegramBotWho.Staff && chatId.ChatId != 372319067)
            {
                _logger.Warning(update.Message.Chat.FirstName + " " + update.Message.Chat.LastName + " " + update.Message.Chat.Username + " --> " + update.Message.Text);
            }

            TelegramDialogue activeDialogue = this.FindActiveDialogue(who, chatId);

            if (activeDialogue != null)
            {
                this.BeginHandleDialogUpdate(activeDialogue, update, activeDialogue.UpdateHandler);

                return Task.CompletedTask;
            }

            // 5: Find who can handle conversation initiation.

            ITelegramDialogueHandler[] dialogueHandlers;

            lock (_firstDialogueHandlers)
            {
                dialogueHandlers = _firstDialogueHandlers.ToArray();
            }

            var canHandleList = new List<(ITelegramDialogueHandler, TelegramDialogueUpdateHandler)>();

            bool tryAddCanHandle(ITelegramDialogueHandler dialogueHandler)
            {
                try
                {
                    TelegramDialogueUpdateHandler handler;
                    if ((handler = dialogueHandler.CanHandleConversationInitiation(who, update)) != null)
                    {
                        canHandleList.Add((dialogueHandler, handler));

                        return false;
                    }
                }
                catch (Exception exc)
                {
                    _logger.Error($"Dialogue handler {dialogueHandler.GetType().FullName} threw an exception");
                    _logger.Error(exc);

                    TelegramDialogue.BeginSendFailMessage(who, _sendWorker, chatId);

                    return true;
                }

                return false;
            }

            foreach (ITelegramDialogueHandler dialogueHandler in dialogueHandlers)
            {
                bool hasError = tryAddCanHandle(dialogueHandler);

                if (hasError)
                    return Task.CompletedTask;
            }

            if (canHandleList.Count == 0)
            {
                lock (_lastDialogueHandlers)
                {
                    dialogueHandlers = _lastDialogueHandlers.ToArray();
                }

                foreach (ITelegramDialogueHandler dialogueHandler in dialogueHandlers)
                {
                    bool hasError = tryAddCanHandle(dialogueHandler);

                    if (hasError)
                        return Task.CompletedTask;
                }
            }

            dialogueHandlers = null;

            // 6: Act upon findings

            if (canHandleList.Count == 0)
                return Task.CompletedTask;

            if (canHandleList.Count > 1)
            {
                _logger.Fatal("Multiple active dialogues detected.");

                TelegramDialogue.BeginSendFailMessage(who, _sendWorker, chatId);

                Task.Delay(1000).ContinueWithShortThread(_ => _applicationLifetime.StopApplication());
            }

            (ITelegramDialogueHandler, TelegramDialogueUpdateHandler) chosenHandler = canHandleList[0];

            activeDialogue = new TelegramDialogue(_sendWorker,
                who,
                chosenHandler.Item1,
                chosenHandler.Item2,
                TelegramChatId.FromUnknownChatNameOrUser(botClient.BotId),
                chatId
            );

            _activeDialogues.Add((new DialogueKey(who, chatId), activeDialogue));

            activeDialogue.OnShutDown += () => this.DialogueDeactivated(activeDialogue);

            this.BeginHandleDialogUpdate(activeDialogue, update, chosenHandler.Item2);

            return Task.CompletedTask;
        }

        private void BeginHandleDialogUpdate(TelegramDialogue activeDialogue, Update update, TelegramDialogueUpdateHandler handler)
        {
            try
            {
                handler(activeDialogue, update);
            }
            catch (Exception exc)
            {
                _logger.Error("Exception during dialog update handling");
                _logger.Error(exc);

                TelegramDialogue.BeginSendFailMessage(activeDialogue.WhichBot, _sendWorker, activeDialogue.ChatId);

                this.DialogueDeactivated(activeDialogue);
            }
        }

        private void DialogueDeactivated(TelegramDialogue activeDialogue)
        {
            if (!activeDialogue.IsShutDown)
                activeDialogue.End();

            _activeDialogues.RemoveAll(d => d.dialogue == activeDialogue);
        }

        Task IUpdateHandler.HandleErrorAsync(ITelegramBotClient botClient, Exception exception, HandleErrorSource source, CancellationToken cancellationToken)
        {
            Exception apiRequestException = ExceptionUtil
                .GetInnerExceptionsOfType(exception, typeof(ApiRequestException).FullName)
                .FirstOrDefault();

            if (apiRequestException == null || !apiRequestException.Message.Contains("Bad Gateway"))
            {
                _logger.Fatal("HandlePollingErrorAsync. Stopping application.");
                _logger.Fatal(exception);

                // ReSharper disable once MethodSupportsCancellation
                Task.Delay(3000)

                    // ReSharper disable once MethodSupportsCancellation
                    .ContinueWithDefault(_ => _applicationLifetime.StopApplication());
            }

            // ReSharper disable once MethodSupportsCancellation
            return Task.Delay(10 * 1000);
        }
    }
}