using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

using Castle.DynamicProxy;

using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;
using Invictus.Nomenklatura.Web;
using Invictus.Nomenklatura.Workers;

using irisdropwebservice.Legacy;
using irisdropwebservice.Services.Chats.Ins;

using OneOf;

using Serilog.Events;

using Telegram.Bot;
using Telegram.Bot.Exceptions;
using Telegram.Bot.Types;
using Telegram.Bot.Types.Enums;
using Telegram.Bot.Types.ReplyMarkups;

using WebRequestWithRetryResult = irisdropwebservice.Legacy.WebRequestWithRetryResult;

namespace irisdropwebservice.Services.Chats
{
    public interface ITelegramSendWorker : IWorker<ITelegramSendWorker>
    {
        Task SendTypingActionFromStaffAccount(TelegramChatId chatId);
        Task<Message[]> PostToTelegramFromStaffAccount([DoNotLog] OneOf<string, Sticker> text, TelegramChatId chatId, PostToTelegramOptions options = null);
        Task<Message[]> PostToTelegramFromCustomersAccount([DoNotLog] OneOf<string, Sticker> text, TelegramChatId chatId, PostToTelegramOptions options = null);
        Task<Message> ForwardMessageFromStaffAccount(TelegramChatId from, TelegramChatId to, int messageId);
        Task<Message> SendAttachFromStaffAccount(TelegramChatId chatId, [DoNotLog] byte[] contents, string fileName, string caption);
    }

    public interface ITelegramSendWorkerPrivate : IWorker<ITelegramSendWorkerPrivate>
    {
        Task<Message[]> PostToTelegram(TelegramBotWho who, [DoNotLog] OneOf<string, Sticker> text, TelegramChatId chatId, PostToTelegramOptions options);
        Message PostToTelegramSingleMessage(TelegramBotWho who, [DoNotLog] OneOf<string, Sticker> text, TelegramChatId chatId, PostToTelegramOptions options);
        void SendChatAction(TelegramBotWho who, TelegramChatId chatId, ChatAction chatAction);
        void DeleteMessage(TelegramBotWho who, TelegramChatId chatId, int messageId);
        void SendKeyboard(TelegramBotWho who, TelegramChatId chatId, [DoNotLog] string text, ReplyKeyboardMarkup keyboard);
        Message ForwardMessage(TelegramBotWho who, TelegramChatId from, TelegramChatId to, int messageId);
        Message SendAttach(TelegramBotWho who, TelegramChatId chatId, [DoNotLog] byte[] contents, string fileName, string caption);
    }

    public interface ITelegramSendWorkerAutotest : IWorker<ITelegramSendWorkerAutotest>
    {
        Task<Message[]> PostToTelegramFromUserAccount([DoNotLog] string text, TelegramChatId chatId, AutotestUserPostToTelegramOptions options = null);
    }

    public class TelegramChatBotSendWorker :
        IWorkerImpl<ITelegramSendWorker>, ITelegramSendWorker, 
        IWorkerImpl<ITelegramSendWorkerPrivate>, ITelegramSendWorkerPrivate,
        IWorkerImpl<ITelegramSendWorkerAutotest>, ITelegramSendWorkerAutotest
    {
        public WorkerConfiguration WorkerConfiguration { get; } = new(
            "TGSEND",
            new WorkerConfiguration.Thread("TelegramChatBotSend", ThreadPriority.AboveNormal, IsBackground: false),
            LogEventLevel.Verbose,
            AllowDirectCall: false,
            GetCustomInterceptor: w => new Interceptor((TelegramChatBotSendWorker)w),
            ThrottlingConfiguration: new ThrottlingConfiguration
            {
                ParametersByType = new Dictionary<string, Type>
                {
                    { "who", typeof(TelegramBotWho) }, 
                    { "chatId", typeof(TelegramChatId) }
                },
                GetTimeToWait = GetTimeToWait,
                ExceptMethods = new List<string>
                {
                    nameof(ITelegramSendWorkerPrivate.SendChatAction), 
                    nameof(ITelegramSendWorkerPrivate.PostToTelegram),
                    nameof(ITelegramSendWorker.SendTypingActionFromStaffAccount),
                    nameof(ITelegramSendWorker.PostToTelegramFromStaffAccount),
                    nameof(ITelegramSendWorker.PostToTelegramFromCustomersAccount),
                    nameof(ITelegramSendWorker.ForwardMessageFromStaffAccount),
                    nameof(ITelegramSendWorker.SendAttachFromStaffAccount),
                    nameof(ITelegramSendWorkerAutotest.PostToTelegramFromUserAccount)
                }
            }
        );

        private static TimeSpan GetTimeToWait(Dictionary<string, object> vars)
        {
            return TelegramThrottling.NonStrict.GetWaitTime(
                (long)((TelegramBotWho)vars["who"]),
                (TelegramChatId)vars["chatId"]
            );
        }

        public ILogger Log { get; } = InvLog.Logger<TelegramChatBotSendWorker>();

        public WorkerCore Core { get; set; }

        private class Interceptor : IInterceptor
        {
            private readonly TelegramChatBotSendWorker _worker;

            public Interceptor(TelegramChatBotSendWorker worker)
            {
                _worker = worker;
            }

            public void Intercept(IInvocation invocation)
            {
                WebRequestWithRetryOld.WebCallWithRetry(_worker.Log,
                    () => {
                        try
                        {
                            invocation.Proceed();

                            return WebRequestWithRetryResult.Success(null);
                        }
                        catch (ApiRequestException exc)
                        {
                            _worker.Log.Error("PostToTelegram ApiRequestException exception.");
                            _worker.Log.Error(exc);

                            bool waitLong = exc.Message.Contains("Too Many Requests: retry");

                            if (waitLong)
                                return WebRequestWithRetryResult.FromExceptionAdvice(exc, RetryAdvice.WaitALot);

                            bool retry = exc.Message.Contains("Bad Gateway");

                            if (retry)
                                return WebRequestWithRetryResult.MightRetry(exc);

                            return WebRequestWithRetryResult.FromExceptionAdvice(exc, RetryAdvice.ThrowFurther);
                        }
                    }
                );
            }
        }

        ITelegramSendWorker IWorkerImpl<ITelegramSendWorker>.PublicInterface { get; set; }
        ITelegramSendWorkerPrivate IWorkerImpl<ITelegramSendWorkerPrivate>.PublicInterface { get; set; }
        ITelegramSendWorkerAutotest IWorkerImpl<ITelegramSendWorkerAutotest>.PublicInterface { get; set; }

        private ITelegramSendWorkerPrivate SelfPublic => ((IWorkerImpl<ITelegramSendWorkerPrivate>)this).PublicInterface;

        private readonly TelegramChatBotClients _telegramChatBotClients;
        private readonly TelegramDbHandler _telegramDbHandler;

        public TelegramChatBotSendWorker(TelegramChatBotClients telegramChatBotClients, TelegramDbHandler telegramDbHandler)
        {
            _telegramChatBotClients = telegramChatBotClients;
            _telegramDbHandler = telegramDbHandler;
        }

        public const int TELEGRAM_MAX_MESSAGE_LENGTH = 3750;

        public void SendChatAction(TelegramBotWho who, TelegramChatId chatId, ChatAction chatAction)
        {
            ITelegramBotClient botClient = _telegramChatBotClients.GetBotClient(who);

            this.ExceptionWrapper(() => botClient.SendChatActionAsync(chatId.ChatId, chatAction).GetAwaiter().GetResult());
        }

        public async Task<Message[]> PostToTelegram(TelegramBotWho who, OneOf<string, Sticker> text, TelegramChatId chatId, PostToTelegramOptions options)
        {
            if (who != TelegramBotWho.Foe && text.IsT0)
            {
                string addText = "";

                if (_telegramChatBotClients.TwoBotsAreTheSame)
                {
                    switch (who)
                    {
                        case TelegramBotWho.Staff:
                            addText = "[Бот для співробітників]";

                            break;
                        case TelegramBotWho.ForCustomers:
                            addText = "[Бот для клієнтів]";

                            break;
                        default: throw TypeAbominationException.Enum(typeof(TelegramBotWho), who);
                    }
                }

                text = addText + "\r\n" + text.AsT0;
            }

            var messages = new List<Task<Message>>();

            if (text.IsT1)
            {
                messages.Add(SelfPublic.Run(w => w.PostToTelegramSingleMessage(who, text, chatId, options)));
            } else
            {
                List<string> lines = IntStringUtil.MultilineStrings(text.AsT0.Split('\n'), TELEGRAM_MAX_MESSAGE_LENGTH);

                foreach (string strChunk in lines)
                {
                    PostToTelegramOptions privateOptionsCl = options with { };

                    messages.Add(SelfPublic.Run(w => w.PostToTelegramSingleMessage(who, strChunk, chatId, privateOptionsCl)));

                    options.RemoveKeyboard = false;
                }

                Log.Debug($"Telegram bot message sent, text={(text.AsT0.Length > 92 ? text.AsT0.Substring(0, 92) + "..." : text.AsT0)}");
            }

            await Task.WhenAll(messages.ToArray());

            return messages.Select(m => m.Result).ToArray();
        }

        public Message PostToTelegramSingleMessage(TelegramBotWho who, OneOf<string, Sticker> text, TelegramChatId chatId, PostToTelegramOptions options)
        {
            ITelegramBotClient botClient;

            if (who != TelegramBotWho.Foe)
            {
                TelegramThrottling.NonStrict.RegisterActionIrregardless((long)who, chatId);

                botClient = _telegramChatBotClients.GetBotClient(who);
            } else
            {
                bool isChannelPost = ((AutotestUserPostToTelegramOptions)options).IsChannelPost;

                if (text.IsT0)
                    text = (isChannelPost ? "[__AT_USR1_]" : "[__AT_USR0_]") + text;
                else
                    throw new NotImplementedException();

                botClient = _telegramChatBotClients.GetBotClient(TelegramBotWho.Staff);
            }


            var cts = new CancellationTokenSource();

            Message message = null;

            this.ExceptionWrapper(() => {
                    if (text.IsT0)
                    {
                        string encodedText = options.HtmlFormattingEnabled
                            ? text.AsT0
                            : HttpUtility.HtmlEncode(text.AsT0);

                        try
                        {
                            message = botClient.SendTextMessageAsync(
                                chatId: chatId.ChatId,
                                messageThreadId: chatId.MessageThreadId,
                                replyMarkup: (options.RemoveKeyboard != null && options.RemoveKeyboard.Value) ? new ReplyKeyboardRemove() : null,
                                replyParameters: options.ReplyToMessageId == null ? null : new ReplyParameters { MessageId = options.ReplyToMessageId.Val() },
                                text: encodedText,
                                parseMode: ParseMode.Html,
                                disableNotification: options.Silent,
                                cancellationToken: cts.Token,
                                linkPreviewOptions: new LinkPreviewOptions { IsDisabled = options.DisableWebPagePreview}
                            ).GetAwaiter().GetResult();
                        }
                        catch (ApiRequestException exc)
                        {
                            if (exc.Message.Contains("can't parse entities") && options.HtmlFormattingEnabled)
                            {
                                options.HtmlFormattingEnabled = false;

                                encodedText = options.HtmlFormattingEnabled
                                    ? text.AsT0
                                    : HttpUtility.HtmlEncode(text.AsT0);
                                
                                message = botClient.SendTextMessageAsync(
                                    chatId: chatId.ChatId,
                                    messageThreadId: chatId.MessageThreadId,
                                    replyMarkup: (options.RemoveKeyboard != null && options.RemoveKeyboard.Value) ? new ReplyKeyboardRemove() : null,
                                    replyParameters: options.ReplyToMessageId == null ? null : new ReplyParameters { MessageId = options.ReplyToMessageId.Val() },
                                    text: encodedText,
                                    parseMode: ParseMode.Html,
                                    disableNotification: options.Silent,
                                    cancellationToken: cts.Token,
                                    linkPreviewOptions: new LinkPreviewOptions { IsDisabled = options.DisableWebPagePreview}
                                ).GetAwaiter().GetResult();
                            }
                        }
                    } else
                    {
                        message = botClient.SendStickerAsync(
                            chatId: chatId.ChatId,
                            messageThreadId: chatId.MessageThreadId,
                            replyMarkup: (options.RemoveKeyboard != null && options.RemoveKeyboard.Value) ? new ReplyKeyboardRemove() : null,
                            replyParameters: options.ReplyToMessageId == null ? null : new ReplyParameters { MessageId = options.ReplyToMessageId.Val() },
                            sticker: new InputFileId(text.AsT1.FileUniqueId),
                            disableNotification: options.Silent,
                            cancellationToken: cts.Token
                        ).GetAwaiter().GetResult();
                    }
                }
            );

            if (message != null)
                _telegramDbHandler.SaveOutgoingUpdate(message);

            return message;
        }

        public void DeleteMessage(TelegramBotWho who, TelegramChatId chatId, int messageId)
        {
            TelegramThrottling.NonStrict.RegisterActionIrregardless((long)who, chatId);

            ITelegramBotClient botClient = _telegramChatBotClients.GetBotClient(who);

            this.ExceptionWrapper(() => botClient.DeleteMessageAsync(chatId.ChatId, messageId).GetAwaiter().GetResult());
        }

        public void SendKeyboard(TelegramBotWho who, TelegramChatId chatId, string text, ReplyKeyboardMarkup keyboard)
        {
            TelegramThrottling.NonStrict.RegisterActionIrregardless((long)who, chatId);

            ITelegramBotClient botClient = _telegramChatBotClients.GetBotClient(who);

            this.ExceptionWrapper(() => botClient.SendTextMessageAsync(
                    chatId.ChatId,
                    text,
                    messageThreadId: chatId.MessageThreadId,
                    replyMarkup: keyboard
                ).GetAwaiter().GetResult()
            );
        }

        public Message ForwardMessage(TelegramBotWho who, TelegramChatId from, TelegramChatId to, int messageId)
        {
            TelegramThrottling.NonStrict.RegisterActionIrregardless((long)who, to);

            if (from.MessageThreadId.HasValue || to.MessageThreadId.HasValue)
                throw new NotImplementedException();

            ITelegramBotClient botClient = _telegramChatBotClients.GetBotClient(who);

            try
            {
                Message message = null;

                this.ExceptionWrapper(() => {
                        message = botClient.ForwardMessageAsync(to.ChatId, from.ChatId, messageId).GetAwaiter().GetResult();
                    }
                );

                return message;
            }
            catch (ApiRequestException exc)
            {
                if (exc.Message.Contains("message to forward not found"))
                    return null;

                throw;
            }
        }

        public Message SendAttach(TelegramBotWho who, TelegramChatId chatId, byte[] contents, string fileName, string caption)
        {
            TelegramThrottling.NonStrict.RegisterActionIrregardless((long)who, chatId);

            ITelegramBotClient botClient = _telegramChatBotClients.GetBotClient(who);

            Message message = null;

            this.ExceptionWrapper(() => {
                    message = botClient.SendDocumentAsync(chatId.ChatId,
                        InputFile.FromStream(new MemoryStream(contents), fileName),
                        chatId.MessageThreadId,
                        caption: caption,
                        disableContentTypeDetection: false
                    ).GetAwaiter().GetResult();
                }
            );

            return message;
        }

        Task ITelegramSendWorker.SendTypingActionFromStaffAccount(TelegramChatId chatId)
        {
            return SelfPublic.Run(w => w.SendChatAction(TelegramBotWho.Staff, chatId, ChatAction.Typing));
        }

        Task<Message[]> ITelegramSendWorker.PostToTelegramFromStaffAccount(OneOf<string, Sticker> text, TelegramChatId chatId, PostToTelegramOptions options)
        {
            options ??= new PostToTelegramOptions();

            return SelfPublic.Run(w => w.PostToTelegram(TelegramBotWho.Staff, text, chatId, options));
        }

        Task<Message[]> ITelegramSendWorker.PostToTelegramFromCustomersAccount(OneOf<string, Sticker> text, TelegramChatId chatId, PostToTelegramOptions options)
        {
            options ??= new PostToTelegramOptions();

            return SelfPublic.Run(w => w.PostToTelegram(TelegramBotWho.ForCustomers, text, chatId, options));
        }

        Task<Message[]> ITelegramSendWorkerAutotest.PostToTelegramFromUserAccount([DoNotLog] string text, TelegramChatId chatId, AutotestUserPostToTelegramOptions options)
        {
            return SelfPublic.Run(w => w.PostToTelegram(TelegramBotWho.Foe, text, chatId, options));
        }

        Task<Message> ITelegramSendWorker.ForwardMessageFromStaffAccount(TelegramChatId from, TelegramChatId to, int messageId)
        {
            return SelfPublic.Run(w => w.ForwardMessage(TelegramBotWho.Staff, from, to, messageId));
        }

        Task<Message> ITelegramSendWorker.SendAttachFromStaffAccount(TelegramChatId chatId, byte[] contents, string fileName, string caption)
        {
            return SelfPublic.Run(w => w.SendAttach(TelegramBotWho.Staff, chatId, contents, fileName, caption));
        }

        private void ExceptionWrapper(Action action)
        {
            try
            {
                action();
            }
            catch (ApiRequestException exc)
            {
                if (exc.Message.Contains("Forbidden: bot was blocked by the user"))
                    return;

                throw;
            }
        }
    }
}