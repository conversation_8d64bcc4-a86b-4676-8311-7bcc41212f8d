using System.Threading;
using System.Threading.Tasks;

using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.ExteriorServ;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.Services.Chats.Ins;

using PhoneNumbers;

namespace irisdropwebservice.Services.Chats
{
    public class ChatsExteriorService : IExteriorService
    {
        private readonly ILogger _logger = InvLog.Logger<ChatsExteriorService>();

        private readonly ITelegramSendWorker _telegramSendWorker;
        private readonly TelegramChatBotPolling _telegramChatBotPolling;
        private readonly TelegramChatBotContacts _telegramChatBotContacts;
        private readonly TelegramCustomerChatBotTalkingRoutine _telegramCustomerChatBotTalkingRoutine;
        private readonly IServiceProvider _serviceProvider;

        public ChatsExteriorService(
            InvGlobalExceptionHandling globalExceptionHandling,
            ITelegramSendWorker telegramSendWorker,
            TelegramChatBotPolling telegramChatBotPolling,
            TelegramChatBotContacts telegramChatBotContacts,
            TelegramCustomerChatBotTalkingRoutine telegramCustomerChatBotTalkingRoutine,
            IServiceProvider serviceProvider
        )
        {
            _telegramSendWorker = telegramSendWorker;
            _telegramChatBotPolling = telegramChatBotPolling;
            _telegramChatBotContacts = telegramChatBotContacts;
            _telegramCustomerChatBotTalkingRoutine = telegramCustomerChatBotTalkingRoutine;
            _serviceProvider = serviceProvider;

            globalExceptionHandling.AddPreHaltAction(this.PreHaltAction);
        }

        public Task Run()
        {
            WriteLogToTelegramBotOrStatisticsLogEventSink.SetServiceProvider(_serviceProvider);

            return Task.CompletedTask;
        }

        public void Activate()
        {
            _telegramChatBotPolling.AddLastDialogueHandler(new GenericChatBotResponse());
            _telegramChatBotPolling.AddDialogueHandler(_telegramCustomerChatBotTalkingRoutine);

            _telegramChatBotPolling.Activate(_telegramChatBotContacts);

            // _telegramUserThread.StartThread();

            // Got BANNED for this.
            // Task.Run(() => telegramUserThread.PostSomething());
        }
        
        private void PreHaltAction(Exception obj)
        {
            if (!WriteLogToTelegramBotOrStatisticsLogEventSink.WasCreatedAndIsProbablyActive)
                return;
        
            // Wait for logger to queue error to telegram chatbot.
            SpinWait.SpinUntil(() => !_telegramSendWorker.Core.HasWorkItemsInQueue, millisecondsTimeout: 750);
        }

        public CustomerInfo GetSubscribedCustomerTelegramUserAndChatIdByPhone(string phoneNumber)
        {
            try
            {
                CustomerInfo customerInfo = _telegramChatBotContacts.FindCustomerByPhone(phoneNumber);

                if (customerInfo == null)
                    return null;

                if (!customerInfo.SubToTelegramFromSiteUpdates)
                    return null;

                return customerInfo;
            }
            catch (NumberParseException)
            {
                _logger.Warning("Phone number " + phoneNumber + " is invalid.");
                return null;
            }
        }
    }
}
