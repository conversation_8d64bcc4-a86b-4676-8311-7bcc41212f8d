using Serilog.Configuration;
using Serilog.Core;
using Serilog.Events;
using Serilog.Formatting;

namespace irisdropwebservice.Services.Chats;

public static class ChatExtensions
{
    public static LoggerConfiguration TelegramChatBot(
        this LoggerSinkConfiguration sinkConfiguration,
        ITextFormatter formatter,
        LogEventLevel restrictedToMinimumLevel = LogEventLevel.Verbose,
        LoggingLevelSwitch levelSwitch = null
    )
    {
        if (sinkConfiguration == null)
        {
            throw new ArgumentNullException(nameof(sinkConfiguration));
        }

        if (formatter == null)
        {
            throw new ArgumentNullException(nameof(formatter));
        }

        return sinkConfiguration.Sink(new WriteLogToTelegramBotOrStatisticsLogEventSink(formatter), restrictedToMinimumLevel, levelSwitch);
    }
}