using System.ComponentModel.DataAnnotations.Schema;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Misc;

using Microsoft.EntityFrameworkCore;

namespace irisdropwebservice.Services.PersistentTasks
{
    [Table("PersistentTask")]
    public class GenericPersistentTaskDto
    {
        [Column("Id")]
        public int Id { get; set; }

        [Column("TaskName")]
        public string TaskName { get; set; }

        [Column("Created")]
        public DateTime CreatedUtc { get; set; }
        
        [Column("MinFireTime")]
        public DateTimeOffset? MinFireTime { get; set; }

        [Column("TaskXmlBlock")]
        public byte[] TaskXmlBlock { get; set; }

        public GenericPersistentTaskXml Data { get; set; }
    }

    public class GenericPersistentTaskXml
    {
        public IGenericPersistentTaskData Data { get; set; }
    }

    public class GenericPersistentTaskDbAccess : DbAccessBase
    {
        private readonly MyDbContext _dbContext;

        public GenericPersistentTaskDbAccess(DbAccessOptions accessOptions, MyDbContext context)
            : base(accessOptions, context)
        {
            _dbContext = context;
        }

        public void Add(GenericPersistentTaskDto task)
        {
            task.TaskXmlBlock = SerializationUtil<GenericPersistentTaskXml>.SerializeToBinaryCompressed(task.Data);
            
            _dbContext.PersistentTasks.Add(task);
        }

        public GenericPersistentTaskDto Take()
        {
            DateTimeOffset nowUtc = ServerClock.GetCurrentUtcTime();
            
            GenericPersistentTaskDto taskDto = _dbContext.PersistentTasks
                .AsNoTracking()
                .Where(t => t.MinFireTime == null || t.MinFireTime <= nowUtc)
                .OrderBy(t => t.Id)
                .FirstOrDefault();

            if (taskDto == null)
                return null;

            taskDto.Data = SerializationUtil<GenericPersistentTaskXml>.DeserializeFromBinaryCompressed(taskDto.TaskXmlBlock);

            return taskDto;
        }

        public void Remove(long taskId)
        {
            GenericPersistentTaskDto taskDto = _dbContext.PersistentTasks.FirstOrDefault(t => t.Id == taskId);
            if (taskDto == null)
                return;

            _dbContext.PersistentTasks.Remove(taskDto);
        }

        public class MyDbContext : DbContext
        {
            public virtual DbSet<GenericPersistentTaskDto> PersistentTasks { get; set; }

            public MyDbContext(DbContextOptions<MyDbContext> options)
                : base(options)
            {
            }

            protected override void OnModelCreating(ModelBuilder modelBuilder)
            {
                modelBuilder.Entity<GenericPersistentTaskDto>()
                    .Ignore(nameof(GenericPersistentTaskDto.Data));

                base.OnModelCreating(modelBuilder);
            }
        }
    }
}
