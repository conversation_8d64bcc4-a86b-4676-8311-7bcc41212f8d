using System.Threading.Tasks;

using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.Legacy;

namespace irisdropwebservice.Services.PersistentTasks
{
    public class PersistentTasksResoluteEvent : ResoluteEventBase
    {
        protected override ILogger Logger { get; } = InvLog.Logger<PersistentTasksResoluteEvent>();

        private readonly PersistentTasksService.Executor _executor;

        public PersistentTasksResoluteEvent(IInvAppLifetime applicationLifetime, InvAppConfig invAppConfig, PersistentTasksService.Executor executor)
            : base(applicationLifetime, invAppConfig, TimeSpan.FromMilliseconds(200), TimeSpan.FromMinutes(2), ResoluteEventInitialState.CanFireNow)
        {
            _executor = executor;
        }

        protected override Task OnTrigger(object arg)
        {
            return _executor.TryExecuteOnePersistentTask(out bool? _);
        }

        protected override object GetCurrentDefaultArg()
        {
            return null;
        }
    }
}
