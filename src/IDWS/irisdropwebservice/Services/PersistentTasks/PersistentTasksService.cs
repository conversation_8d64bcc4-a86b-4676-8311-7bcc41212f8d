using System.Reflection;
using System.Threading.Tasks;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;

using Microsoft.Extensions.Hosting;

namespace irisdropwebservice.Services.PersistentTasks
{
    public interface IGenericPersistentTaskData
    {
        int GetStableHashCode();
    }

    public interface IPersistentTaskHandler
    {
    }

    public interface IPersistentTaskHandler<in TTaskData> : IPersistentTaskHandler
        where TTaskData : IGenericPersistentTaskData
    {
        Task BeginExecuteTask(TTaskData taskData);
    }

    public class PersistentTasksService
    {
        private readonly ILogger _logger;

        private readonly Executor _executor;
        private readonly PersistentTasksResoluteEvent _resoluteEvent;

        public PersistentTasksService(Executor executor, PersistentTasksResoluteEvent resoluteEvent, ILogger logger)
        {
            _executor = executor;
            _resoluteEvent = resoluteEvent;
            _logger = logger;
        }

        public void RegisterHandler<T>(string taskName, IPersistentTaskHandler<T> handler)
            where T : IGenericPersistentTaskData
        {
            _executor.RegisterHandler(taskName, handler);
        }

        private void AddTask(string taskName, IGenericPersistentTaskData data, DateTimeOffset? minFireTime)
        {
            _logger.Debug($"Adding task {taskName} {data.GetStableHashCode()}");

            _executor.AddTask(taskName, data, minFireTime);
        }

        public void QueueTask(string taskName, IGenericPersistentTaskData data, DateTimeOffset? minFireTime)
        {
            this.AddTask(taskName, data, minFireTime);

            _resoluteEvent.QueueTryTrigger();
        }

        public void AllowExecution()
        {
            _executor.AllowExecution();

            _resoluteEvent.QueueTryTrigger();
        }

        public bool ExecuteAllAndWait()
        {
            bool any = false;
            bool? queueIsEmpty;

            do
            {
                Task t = _executor.TryExecuteOnePersistentTask(out queueIsEmpty);
                if (t != Task.CompletedTask)
                    any = true;
                t.Wait();
            } while (!queueIsEmpty.HasValue || !queueIsEmpty.Value);

            return any;
        }

        public class Executor
        {
            public ILogger Logger { get; }= InvLog.Logger<PersistentTasksService>();

            private readonly IHostApplicationLifetime _applicationLifetime;
            private readonly Dictionary<string, IPersistentTaskHandler> _persistentTaskHandlers = new();
            private readonly IDbAccessFactory<GenericPersistentTaskDbAccess> _dbAccessFactory;
            private readonly List<long> _executingTaskIds = new List<long>();

            private bool _executionAllowed;

            public Executor(IDbAccessFactory<GenericPersistentTaskDbAccess> dbAccessFactory, IHostApplicationLifetime applicationLifetime)
            {
                _dbAccessFactory = dbAccessFactory;
                _applicationLifetime = applicationLifetime;
            }

            public void RegisterHandler<T>(string taskName, IPersistentTaskHandler<T> handler)
                where T : IGenericPersistentTaskData
            {
                if (_persistentTaskHandlers.ContainsKey(taskName))
                    throw new Exception($"Handler for {taskName} is already registered.");

                SerializationUtil<GenericPersistentTaskXml>.AddKnownType(typeof(T));

                _persistentTaskHandlers[taskName] = handler;
            }

            public void AddTask(string taskName, IGenericPersistentTaskData data, DateTimeOffset? minFireTime)
            {
                using GenericPersistentTaskDbAccess dbAccess = _dbAccessFactory.CreateAccess();

                dbAccess.Add(new GenericPersistentTaskDto()
                    {
                        TaskName = taskName,
                        Data = new GenericPersistentTaskXml()
                        {
                            Data = data
                        },
                        CreatedUtc = ServerClock.GetCurrentUtcTime().DateTime,
                        MinFireTime = minFireTime
                    }
                );
                
                dbAccess.SaveChanges();
            }

            private void RemoteTaskFromDb(long taskId)
            {
                using GenericPersistentTaskDbAccess dbAccess = _dbAccessFactory.CreateAccess();

                dbAccess.Remove(taskId);
                dbAccess.SaveChanges();
            }

            public void AllowExecution()
            {
                _executionAllowed = true;
            }

            public Task TryExecuteOnePersistentTask(out bool? queueIsEmpty)
            {
                queueIsEmpty = null;

                if (!_executionAllowed)
                    return Task.CompletedTask;

                if (_executingTaskIds.Count != 0)
                    return Task.CompletedTask;
                
                using GenericPersistentTaskDbAccess dbAccess = _dbAccessFactory.CreateAccess();

                GenericPersistentTaskDto task = dbAccess.Take();

                if (task == null)
                {
                    queueIsEmpty = true;

                    return Task.CompletedTask;
                }

                if (!_persistentTaskHandlers.TryGetValue(task.TaskName, out IPersistentTaskHandler handler))
                    throw new Exception("No handler found for task with name " + task.TaskName);

                queueIsEmpty = false;

                return this.TryExecuteOnePersistentTask(task, handler);
            }

            private Task TryExecuteOnePersistentTask(GenericPersistentTaskDto task, IPersistentTaskHandler handler)
            {
                Logger.Debug($"Took task {task.TaskName} {task.Data.Data.GetStableHashCode()}");

                Type[] specificInterfaces = handler.GetType().GetInterfaces();
                Type chosenSpecificInterface = null;

                foreach (Type specificInterface in specificInterfaces)
                {
                    if (!specificInterface.IsGenericType || specificInterface.GetGenericTypeDefinition() != typeof(IPersistentTaskHandler<>))
                        continue;

                    Type genericTypeArg = specificInterface.GetGenericArguments()[0];

                    if (genericTypeArg.FullName == task.Data.Data.GetType().FullName)
                    {
                        chosenSpecificInterface = specificInterface;
                        break;
                    }
                }

                if (chosenSpecificInterface == null)
                    throw new Exception($"Persistent task handler {handler.GetType().FullName}, cannot handle {task.Data.Data.GetType().FullName}");

                MethodInfo methodInfo = chosenSpecificInterface.GetMethod(nameof(IPersistentTaskHandler<IGenericPersistentTaskData>.BeginExecuteTask)/*, BindingFlags.Instance | BindingFlags.Public*/);

                _executingTaskIds.Add(task.Id);

                var taskHandlingTask = (Task)methodInfo.Invoke(handler, new object[] { task.Data.Data });

                Task successTask = taskHandlingTask.ContinueWithShortThread(
                    _ => {
                        Logger.Debug($"Task {task.TaskName} {task.Data.Data.GetStableHashCode()} has been successfully executed.");
                        this.RemoteTaskFromDb(task.Id);
                        _executingTaskIds.Remove(task.Id);
                    },
                    TaskContinuationOptions.OnlyOnRanToCompletion
                );

                Task failureTask = taskHandlingTask.ContinueWithShortThread(
                    taskExecutionTask => {
                        _executingTaskIds.Remove(task.Id);

                        Logger.Fatal($"Faulted presistent tasks ({task.TaskName} {task.Data.Data.GetStableHashCode()}) are not supported. Stopping application.");
                        Logger.Fatal(taskExecutionTask.Exception);

                        Task.Delay(500).ContinueWithDefault(_ => _applicationLifetime.StopApplication());
                    },
                    TaskContinuationOptions.NotOnRanToCompletion
                );

                return Task.WhenAny(failureTask, successTask);
            }
        }
    }
}
