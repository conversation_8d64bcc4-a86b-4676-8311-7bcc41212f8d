using System.ComponentModel.DataAnnotations.Schema;

using Invictus.Nomenklatura.App2;

using Microsoft.EntityFrameworkCore;

namespace irisdropwebservice.Services.Auth.Ins
{
    public class AuthorizationDbAccess : DbAccessBase
    {
        private readonly MyDbContext _dbContext;

        public AuthorizationDbAccess(DbAccessOptions accessOptions, MyDbContext context)
            : base(accessOptions, context)
        {
            _dbContext = context;
        }

        public bool IsValidApiKey(string scheme, string apiKey)
        {
            if (scheme != BasicAuthenticationDefaults.AUTHENTICATION_SCHEME)
                throw new NotImplementedException("scheme");

            if (apiKey.StartsWith(scheme + " "))
                apiKey = apiKey.Substring(scheme.Length + " ".Length);

            return _dbContext.ApiKeys.Any(key => key.ApiKey == apiKey);
        }

        public class MyDbContext : DbContext
        {
            public virtual DbSet<ApiKeysDto> ApiKeys { get; set; }

            public MyDbContext(DbContextOptions<MyDbContext> options)
                : base(options)
            {
            }
        }
    }

    [Table("ApiKeys")]
    public class ApiKeysDto
    {
        public long Id { get; set; }

        public string ApiKey { get; set; }
    }
}