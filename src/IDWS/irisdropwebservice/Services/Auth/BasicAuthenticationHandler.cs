using System.Security.Claims;
using System.Text.Encodings.Web;
using System.Threading.Tasks;

using Invictus.Nomenklatura.App2;

using irisdropwebservice.Services.Auth.Ins;

using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace irisdropwebservice.Services.Auth
{
    public class BasicAuthenticationHandler : AuthenticationHandler<AuthenticationSchemeOptions>
    {
        private readonly IDbAccessFactory<AuthorizationDbAccess> _dbAccessFactory;

        public BasicAuthenticationHandler(
            IOptionsMonitor<AuthenticationSchemeOptions> options,
            ILoggerFactory logger,
            UrlEncoder encoder,
            IDbAccessFactory<AuthorizationDbAccess> dbAccessFactory
        )
            : base(options, logger, encoder)
        {
            _dbAccessFactory = dbAccessFactory;
        }

        protected override Task<AuthenticateResult> HandleAuthenticateAsync()
        {
            // No authorization header, so throw no result.
            if (!Request.Headers.ContainsKey("Authorization"))
            {
                return Task.FromResult(AuthenticateResult.Fail("Missing Authorization header"));
            }

            string authorizationHeader = Request.Headers["Authorization"].ToString();

            using AuthorizationDbAccess dbAccess = _dbAccessFactory.CreateAccess();

            // authorizationHeader is our password
            bool isOk = dbAccess.IsValidApiKey(BasicAuthenticationDefaults.AUTHENTICATION_SCHEME, authorizationHeader);

            if (!isOk)
            {
                return Task.FromResult(AuthenticateResult.Fail("The API key is incorrect"));
            }

            // Authenticate the client using basic authentication
            var client = new BasicAuthenticationClient
            {
                AuthenticationType = BasicAuthenticationDefaults.AUTHENTICATION_SCHEME,
                IsAuthenticated = true,
                Name = authorizationHeader
            };

            // Set the client ID as the name claim type.
            var claimsPrincipal = new ClaimsPrincipal(new ClaimsIdentity(client,
                    new[]
                    {
                        new Claim(ClaimTypes.Name, authorizationHeader) // TODO: I need to figure out wtf is this.
                    }
                )
            );

            // Return a success result.
            return Task.FromResult(AuthenticateResult.Success(new AuthenticationTicket(claimsPrincipal, Scheme.Name)));
        }

        protected override Task HandleChallengeAsync(AuthenticationProperties properties)
        {

            return base.HandleChallengeAsync(properties);
        }
    }
}