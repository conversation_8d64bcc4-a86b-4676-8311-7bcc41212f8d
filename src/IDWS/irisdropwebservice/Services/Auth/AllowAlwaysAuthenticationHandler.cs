using System.Security.Claims;
using System.Text.Encodings.Web;
using System.Threading.Tasks;

using irisdropwebservice.Services.Auth.Ins;

using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace irisdropwebservice.Services.Auth
{
    public class AllowAlwaysAuthenticationHandler : AuthenticationHandler<AuthenticationSchemeOptions>
    {
        public AllowAlwaysAuthenticationHandler(IOptionsMonitor<AuthenticationSchemeOptions> options, ILoggerFactory logger, UrlEncoder encoder)
            : base(options, logger, encoder)
        {
        }

        protected override Task<AuthenticateResult> HandleAuthenticateAsync()
        {
            string clientId = "free_for_all";

            // Authenticate the client using basic authentication
            var client = new BasicAuthenticationClient
            {
                AuthenticationType = BasicAuthenticationDefaults.AUTHENTICATION_SCHEME,
                IsAuthenticated = true,
                Name = clientId
            };

            // Set the client ID as the name claim type.
            var claimsPrincipal = new ClaimsPrincipal(new ClaimsIdentity(client,
                    new[]
                    {
                        new Claim(ClaimTypes.Name, clientId)
                    }
                )
            );

            return Task.FromResult(AuthenticateResult.Success(new AuthenticationTicket(claimsPrincipal, Scheme.Name)));
        }
    }
}