using System.Text.Json.Serialization;

namespace irisdropwebservice.Services.Rozetka.Structures;

public class RozErrorContent
{
    [JsonPropertyName("message")]
    public string Message { get; set; }

    [JsonPropertyName("code")]
    public int Code { get; set; }
}

public enum RozErrorCode
{
    // 1000 Common
    ServerError = 0,
    RequiredParameter = 1001,
    IncorrectDataFormat = 1002,
    ProblemWithMessageBroker = 1003,
    IncorrectUsernamePassword = 1004,
    CheckCorrectnessOfData = 1005,
    UnableToCreateFile = 1006,
    CanNotCreateDirectory = 1007,
    RegistrationSellerError = 1008,
    NotFindParams = 1009,
    AccessDenied = 1010,
    DirectoryNotWritable = 1011,
    FileCopyFailed = 1012,
    FileNotFound = 1013,
    FileAlreadyExists = 1014,
    UnexpectedError = 1015,
    MailFail = 1016,
    Yii2DataReaderReturned = 1017,
    IncorrectEnterToken = 1018,
    EntityNotFound = 1019,
    IncorrectUsernamePassword2 = 1020,
    UserNotFound = 1021,
    StartDate = 1022,
    EndDate = 1023,
    Period = 1024,
    MarketNotFound = 1025,
    EmptyExtension = 1026,
    EmptyFileNames = 1027,
    ErrorDubleEmail = 1029,
    ErrorResendEmail = 1030,
    ModelCannotBeSaved = 1031,
    ModelNotFound = 1404,
    ErrorNovaPoshta = 1101,
    ErrorPrintTtn = 1110,
    ErrorCreatePaymentMerchantKeys = 1201,
    ErrorUpdatePaymentMerchantKeys = 1202,
    ErrorAddPaymentToCombination = 1203,
    ErrorPaymentNotFound = 1204,
    ErrorPaymentUnableSetMerchant = 1205,
    ErrorUnableToUsePayment = 1206,

    // 2000 Documents
    DocumentNotFound = 2001,
    MaxDocumentsPerType = 2002,
    DocumentSizeErr = 2003,
    DocumentFormatErr = 2004,
    DocumentFileIncorrect = 2005,
    DocumentResolutionTooSmall = 2006,
    CantDeleteDocument = 2007,
    CantDeletePreviewOfDocument = 2008,

    // 3000 Items
    ErrorImageValidation = 3001,
    EntityNotEndProcessed = 3002,
    ErrorLoadImage = 3003,
    ErrorUpdateSla = 3004,

    // 4000 Sellers
    CantDeleteManager = 4001,

    // 5000 HTTP Errors
    InvalidCredentials = 5401,
    NotFound = 5404,
    TechnicalMaintenance = 5503,

    // 6000 Auth
    SessionExpired = 6001,

    // 7000 Kit Errors
    ErrorBlockKit = 7001,
    ErrorKitUpdate = 7002,
    DifferentSlaGoods = 7003,

    // 8000 Advertising Errors
    ErrorBlockCampaign = 8001,
    ErrorCampaignUpdate = 8002,
    ErrorBlockCampaignItem = 8003,
    ErrorCampaignItemUpdate = 8004,
    MarketCampaign = 8005,

    // 9000+ Delivery Errors
    CreateDeliveryCombination = 9001,
    UpdateDeliveryCombination = 9002,
    CreateSla = 10001,
    UpdateSla = 10002,
    CreateDeliveryPickup = 11001,
    UpdateDeliveryPickup = 11002,
    CreateDeliveryCourier = 12001,
    CreateDeliveryService = 13001
}

public class RozErrorResponse
{
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    [JsonPropertyName("errors")]
    public RozApiError Errors { get; set; }
}

public class RozApiError
{
    [JsonPropertyName("message")]
    public string Message { get; set; }

    [JsonPropertyName("code")]
    public RozErrorCode Code { get; set; }

    [JsonPropertyName("details")]
    public object Details { get; set; }
}