using System.Text.Json.Serialization;

namespace irisdropwebservice.Services.Rozetka.Structures;

public class RozAuthRequest
{
    [JsonPropertyName("username")]
    public string Username { get; set; }

    [JsonPropertyName("password")]
    public string Password { get; set; } // Should be base64 encoded
}

public class RozAuthResponse
{
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    [JsonPropertyName("content")]
    public RozAuthContent Content { get; set; } = new();

    [JsonPropertyName("errors")]
    public RozErrorContent Errors { get; set; }
}

public class RozAuthContent
{
    [JsonPropertyName("id")]
    public int Id { get; set; }

    [JsonPropertyName("access_token")]
    public string AccessToken { get; set; }

    [JsonPropertyName("permissions")]
    public string[] Permissions { get; set; } = Array.Empty<string>();

    [JsonPropertyName("roles")]
    public string[] Roles { get; set; } = Array.Empty<string>();

    [JsonPropertyName("seller")]
    public RozSeller Seller { get; set; } = new();

    [JsonPropertyName("market")]
    public RozMarket Market { get; set; } = new();

    [JsonPropertyName("need_interview")]
    public bool NeedInterview { get; set; }

    [JsonPropertyName("lang")]
    public string Lang { get; set; }

    [JsonPropertyName("poll_link")]
    public string PollLink { get; set; }
}

public class RozSeller
{
    [JsonPropertyName("fio")]
    public string Fio { get; set; }

    [JsonPropertyName("email")]
    public string Email { get; set; }

    [JsonPropertyName("first_phone")]
    public RozPhone FirstPhone { get; set; } = new();

    [JsonPropertyName("wizard")]
    public bool Wizard { get; set; }
}

public class RozPhone
{
    [JsonPropertyName("id")]
    public int Id { get; set; }

    [JsonPropertyName("phone_number")]
    public string PhoneNumber { get; set; }

    [JsonPropertyName("confirmed")]
    public bool Confirmed { get; set; }
}

public class RozMarket
{
    [JsonPropertyName("id")]
    public int Id { get; set; }

    [JsonPropertyName("logo")]
    public string Logo { get; set; }

    [JsonPropertyName("business_model")]
    public string BusinessModel { get; set; }

    [JsonPropertyName("title")]
    public string Title { get; set; }

    [JsonPropertyName("title_translit")]
    public string TitleTranslit { get; set; }

    [JsonPropertyName("market_url")]
    public string MarketUrl { get; set; }

    [JsonPropertyName("war_block")]
    public bool WarBlock { get; set; }

    [JsonPropertyName("status")]
    public string Status { get; set; }

    [JsonPropertyName("status_label")]
    public string StatusLabel { get; set; }

    [JsonPropertyName("status_description")]
    public string StatusDescription { get; set; }

    [JsonPropertyName("status_transfer_reason")]
    public RozStatusTransferReason StatusTransferReason { get; set; } = new();

    [JsonPropertyName("fulfillment_available")]
    public bool FulfillmentAvailable { get; set; }
}

public class RozStatusTransferReason
{
    [JsonPropertyName("label")]
    public string Label { get; set; }

    [JsonPropertyName("description")]
    public string Description { get; set; }
}