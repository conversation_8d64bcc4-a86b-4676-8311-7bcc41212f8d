using System.Text.Json.Serialization;

using Refit;

namespace irisdropwebservice.Services.Rozetka.Structures;

public class RozCatalogCategory
{
    [JsonPropertyName("category_id")]
    public int CategoryId { get; set; }

    [JsonPropertyName("parent_id")]
    public int ParentId { get; set; }

    [JsonPropertyName("name")]
    public string Name { get; set; }
}

public class RozMetaResponse
{
    [JsonPropertyName("total_count")]
    public int TotalCount { get; set; }

    [JsonPropertyName("page_count")]
    public int PageCount { get; set; }

    [JsonPropertyName("current_page")]
    public int CurrentPage { get; set; }

    [JsonPropertyName("per_page")]
    public int PerPage { get; set; }
}

public class RozCategoriesResponse
{
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    [JsonPropertyName("content")]
    public RozCategoriesContent Content { get; set; } = new();
}

public class RozCategoriesContent
{
    [JsonPropertyName("market_categories")]
    public IEnumerable<RozCatalogCategory> MarketCategorys { get; set; } = Enumerable.Empty<RozCatalogCategory>();

    [JsonPropertyName("meta")]
    public RozMetaResponse Meta { get; set; } = new();
}

public class CategorySearchParameters
{
    [AliasAs("category_id")]
    public int? CategoryId { get; set; }

    [AliasAs("parent_id")]
    public int? ParentId { get; set; }

    [AliasAs("page")]
    public int? Page { get; set; }

    [AliasAs("page_size_limit")]
    public int? PageSizeLimit { get; set; }
}

public class RozCategoryOptionsRequest
{
    [AliasAs("category_id")]
    public int CategoryId { get; set; }

    [AliasAs("id")]
    public int? Id { get; set; }

    [AliasAs("name")]
    public string Name { get; set; }
}

public class RozCategoryOptionsResponse
{
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    [JsonPropertyName("content")]
    public RozCategoryAttribute[] Content { get; set; } = Array.Empty<RozCategoryAttribute>();

    [JsonPropertyName("errors")]
    public RozApiError Errors { get; set; }
}

public class RozCategoryAttribute
{
    [JsonPropertyName("id")]
    public int Id { get; set; }

    [JsonPropertyName("name")]
    public string Name { get; set; }

    [JsonPropertyName("attr_type")]
    public string AttrType { get; set; }

    [JsonPropertyName("filter_type")]
    public string FilterType { get; set; }

    [JsonPropertyName("unit")]
    public string Unit { get; set; }

    [JsonPropertyName("is_global")]
    public bool IsGlobal { get; set; }

    [JsonPropertyName("value_id")]
    public int? ValueId { get; set; }

    [JsonPropertyName("value_name")]
    public object ValueName { get; set; }  // Can be string or JSON object
}
