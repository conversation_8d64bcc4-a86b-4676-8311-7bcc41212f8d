using System.Diagnostics.CodeAnalysis;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.ExteriorServ;


using irisdropwebservice.Services.Rozetka.Ins;
using irisdropwebservice.Services.Rozetka.Remote;

using JetBrains.Annotations;

using Microsoft.Extensions.DependencyInjection;

namespace irisdropwebservice.Services.Rozetka;

[UsedImplicitly]
public class RozetkaExteriorServiceBuilder: ExteriorServiceBuilderBase<RozetkaExteriorService>
{
    protected override void AddConcealedServices(ServiceCollection concealedServiceCollection)
    {
        concealedServiceCollection.AddWorkerSingleton<RozetkaSyncWorker>();
        concealedServiceCollection.AddWorkerSingleton<RozetkaApiWorker>();
        concealedServiceCollection.AddSingleton<RozetkaPreparation>();
        
        concealedServiceCollection.AddSingleton<RozetkaSyncFlowEvent>();

        concealedServiceCollection.AddSingleton<RozetkaCommon>();
        
        this.RegisterDatabaseTypes(concealedServiceCollection);

        base.AddConcealedServices(concealedServiceCollection);
    }
    
    protected override void ExposeConcealedServices()
    {
        this.ExposeSingleton<RozetkaCommon>();
        
        base.ExposeConcealedServices();
    }
}