using System.Diagnostics;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

using Castle.DynamicProxy;

using Invictus.Nomenklatura.Exceptions;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Workers;
using Invictus.Nomenklatura.Misc;
using Invictus.Nomenklatura.Web;

using irisdropwebservice.Legacy;
using irisdropwebservice.Services.Rozetka.Structures;


using Refit;

using Serilog.Events;

using WebRequestWithRetryResult = irisdropwebservice.Legacy.WebRequestWithRetryResult;

namespace irisdropwebservice.Services.Rozetka.Remote;

[CanFail(LogLevel = LogEventLevel.Verbose)]
public interface IRozetkaApiWorker : IWorker<IRozetkaApiWorker>
{
    Task<RozAuthResponse> LoginAsync(string username, string password);

    Task<RozCategoriesResponse> GetCategories(string token, CategorySearchParameters categorySearchParameters);

    Task<RozCategoryOptionsResponse> GetCategoryOptionsAsync(
        RozCategoryOptionsRequest request,
        string authorization,
        string language = "uk",
        string version = "0.0.1"
    );
}

public class RozetkaApiWorker :
    IWorkerImpl<IRozetkaApiWorker>,
    IRozetkaApiWorker
{
    public WorkerCore Core { get; set; }
    public ILogger Log { get; } = InvLog.Logger<RozetkaApiWorker>();

    public WorkerConfiguration WorkerConfiguration { get; } = new WorkerConfiguration(
        "ROZAPI",
        new WorkerConfiguration.TaskScheduler("RozetkaApi", 1),
        LogEventLevel.Debug,
        AllowDirectCall: true,
        GetCustomInterceptor: _ => new Interceptor()
    );

    public class Interceptor : IInterceptor
    {
        protected ILogger Log { get; } = InvLog.Logger<Interceptor>();

        public void Intercept(IInvocation invocation)
        {
            WebRequestWithRetryOld.WebCallWithRetry(Log, () => this.ApiCallWrapperWebCallWithRetry(invocation));
        }

        private WebRequestWithRetryResult ApiCallWrapperWebCallWithRetry(IInvocation invocation)
        {
            try
            {
                var sw = Stopwatch.StartNew();

                invocation.Proceed();

                sw.Stop();

                return WebRequestWithRetryResult.Success(null);
            }
            catch (Exception exc)
            {
                Log.Information("RozetkaApiWorker Interceptor exception.");

                RetryAdvice advice = WebRequestRetryExceptionBeHandler.GetWebRequestRetryAdvice(Log, exc);

                if (advice == RetryAdvice.WaitALot)
                    advice = RetryAdvice.ThrowFurther;

                return WebRequestWithRetryResult.FromExceptionAdvice(exc, advice);
            }
        }
    }
    private readonly ILowLevelRozetkaSellerApi _api;

    IRozetkaApiWorker IWorkerImpl<IRozetkaApiWorker>.PublicInterface { get; set; }
    
    public RozetkaApiWorker()
    {
        _api = RestService.For<ILowLevelRozetkaSellerApi>("https://api-seller.rozetka.com.ua");
    }
    
    public async Task<RozAuthResponse> LoginAsync(string username, string password)
    {
        var request = new RozAuthRequest
        {
            Username = username,
            Password = Convert.ToBase64String(Encoding.UTF8.GetBytes(password))
        };
        
        return await _api.AuthenticateAsync(request);
    }
    
    // Категорії - 3 Список активних категорій / Пошук
    public async Task<RozCategoriesResponse> GetCategories(string token, CategorySearchParameters categorySearchParameters)
    {
        return await _api.GetActiveCategories(
            categorySearchParameters,
            token,
            contentLanguage: "uk"
        );
    }

    // Категорії - 5 Отримання параметрів категорій в JSON
    public async Task<RozCategoryOptionsResponse> GetCategoryOptionsAsync(
        RozCategoryOptionsRequest request,
        string authorization,
        string language,
        string version
    )
    {
        return await _api.GetCategoryOptionsAsync(request, authorization, language, version);
    }
}