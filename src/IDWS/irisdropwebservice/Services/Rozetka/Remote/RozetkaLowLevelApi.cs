using System.Text;
using System.Threading.Tasks;

using irisdropwebservice.Services.Rozetka.Structures;

using Refit;

namespace irisdropwebservice.Services.Rozetka.Remote;

// IRozetkaSellerApi.cs
public interface ILowLevelRozetkaSellerApi
{
    [Post("/sites")]
    Task<RozAuthResponse> AuthenticateAsync([Body] RozAuthRequest request);
    
    [Get("/market-categories/search")]
    Task<RozCategoriesResponse> GetActiveCategories(
        [Query] CategorySearchParameters parameters,
        [Header("Authorization")] string authorization,
        [Header("Accept-Validate-Exception")] bool? acceptValidateException = null,
        [Header("Content-Language")] string contentLanguage = null);
    
    [Get("/v1/market-categories/category-options")]
    Task<RozCategoryOptionsResponse> GetCategoryOptionsAsync(
        RozCategoryOptionsRequest request,
        [Head<PERSON>("Authorization")] string authorization,
        [Head<PERSON>("Content-Language")] string language = "uk",
        [Header("Version")] string version = "0.0.1");
}