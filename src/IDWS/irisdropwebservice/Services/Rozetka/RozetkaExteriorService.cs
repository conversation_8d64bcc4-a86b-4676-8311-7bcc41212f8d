using System.Threading.Tasks;

using Invictus.Nomenklatura.ExteriorServ;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.AppConfig;
using irisdropwebservice.Services.Chats;
using irisdropwebservice.Services.Rozetka.Ins;

using Telegram.Bot.Types;

namespace irisdropwebservice.Services.Rozetka;

public class RozetkaExteriorService : IExteriorService
{
    private readonly IRozetkaSyncWorker _worker;
    private readonly ILogger _logger = InvLog.Logger<RozetkaExteriorService>();
    private readonly ITelegramSendWorker _telegramSendWorker;

    public RozetkaExteriorService(
        IR<PERSON>etkaSyncWorker worker,
        ITelegramSendWorker telegramSendWorker,
        RozetkaCommon rozetkaCommon,
        RozetkaSyncFlowEvent rozetkaSyncFlowEvent
    )
    {
        _worker = worker;
        _telegramSendWorker = telegramSendWorker;
    }
    
    public Task Run()
    {
        return _worker.Run(w => w.Initialize());
    }
}