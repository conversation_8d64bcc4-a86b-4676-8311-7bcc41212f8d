using System.IO;
using System.Reflection;

using irisdropwebservice.AppConfig;
using Invictus.Nomenklatura.Misc;

namespace irisdropwebservice.Services.Rozetka;

public class RozetkaCommon
{
    // public RozetkaCategoriesRoot CategoriesRoot { get; private set; }
    
    public RozetkaCommon()
    {
        this.CreateCategorySchemas();
    }
    
    private void CreateCategorySchemas()
    {
        /*string filePath = Path.Combine(RRAppConfig.FileSystem.FullResourcesDir, "Rozetka", "all_categories.json");

        using FileStream reader = File.OpenRead(filePath);
        
        CategoriesRoot = new InvJsonSerializer().DeserializeForInternals<RozetkaCategoriesRoot>(reader);*/
    }
}