using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.IO;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Xml.Linq;

using Bukimedia.PrestaSharp.Entities;

using irisdropwebservice.AppConfig;
using irisdropwebservice.AppConfig.ClassConfigurations;
using irisdropwebservice.AppConfig.RemoteResources;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Workers;
using irisdropwebservice.Libs.PrestaSharp;
using Invictus.Nomenklatura.Misc;

using irisdropwebservice.Legacy;
using irisdropwebservice.Services.Chats;
using irisdropwebservice.Services.Rozetka.Remote;
using irisdropwebservice.Services.LinkService;
using irisdropwebservice.Services.LinkService.Ins;
using irisdropwebservice.Services.PrestaSync;
using irisdropwebservice.Services.PrestaSync.Ins;
using irisdropwebservice.Services.PrestaSync.ServiceConductors;
using irisdropwebservice.Services.Rozetka.Structures;
using irisdropwebservice.Services.VkScrap;


using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Primitives;

using Serilog.Events;

namespace irisdropwebservice.Services.Rozetka.Ins;


public interface IRozetkaSyncWorker: IWorker<IRozetkaSyncWorker>
{
    void Initialize();

    [CanFail(50, LogLevel = LogEventLevel.Error)]
    void SyncProducts(ExternalSyncData prestaSyncData);
    
    [CanFail(2, LogLevel = LogEventLevel.Error)]
    void SyncOrders();
}

public class RozetkaSyncWorker : IWorkerImpl<IRozetkaSyncWorker>, IRozetkaSyncWorker
{
    public ILogger Log { get; } = InvLog.Logger<RozetkaSyncWorker>();

    public WorkerCore Core { get; set; }
    public IRozetkaSyncWorker PublicInterface { get; set; }

    public WorkerConfiguration WorkerConfiguration { get; } = new(
        "ROZET",
        new WorkerConfiguration.Thread("Rozetka", ThreadPriority.Normal, IsBackground: false),
        LogEventLevel.Information,
        AllowDirectCall: false
    );
    
    private readonly RozetkaPreparation _preparation;
    private readonly IPrestaProductSyncPublic _prestaProductSyncPublic;
    private readonly GlobalUserErrorsManager _globalUserErrorsManager;
    private readonly InvProductFeatureCentre _invProductFeatureCentre;
    private readonly IVkScrapFileDownload _vkScrapFileDownload;
    private readonly IPrestaWorkerPublic _prestaWorker;
    private readonly ITelegramSendWorker _telegramSendWorker;
    private readonly IHostApplicationLifetime _applicationLifetime;
    private readonly IRozetkaApiWorker _apiWorker;
    private readonly ThisPreExpiredToken _tokenExpiry;

    private ProductAttributesConductorService _productAttributes;
    private readonly IDbAccessFactory<VariousStoredDataDbAccess> _variousDbAccessFactory;
    
    private volatile bool _initializing;

    private string _accessToken;

    public RozetkaSyncWorker(
        RozetkaPreparation preparation,
        IPrestaProductSyncPublic prestaProductSyncPublic,
        GlobalUserErrorsManager globalUserErrorsManager,
        InvProductFeatureCentre invProductFeatureCentre,
        IVkScrapFileDownload vkScrapFileDownload,
        IPrestaWorkerPublic prestaWorker,
        ITelegramSendWorker telegramSendWorker,
        IHostApplicationLifetime applicationLifetime,
        IRozetkaApiWorker RozetkaApiWorker,
        IDbAccessFactory<VariousStoredDataDbAccess> variousDbAccessFactory
    )
    {
        _preparation = preparation;
        _prestaProductSyncPublic = prestaProductSyncPublic;
        _globalUserErrorsManager = globalUserErrorsManager;
        _invProductFeatureCentre = invProductFeatureCentre;
        _vkScrapFileDownload = vkScrapFileDownload;
        _prestaWorker = prestaWorker;
        _telegramSendWorker = telegramSendWorker;
        _applicationLifetime = applicationLifetime;
        _apiWorker = RozetkaApiWorker;
        _variousDbAccessFactory = variousDbAccessFactory;

        _prestaProductSyncPublic.DidPush += this.PrestaProductSyncPublicOnDidPush;
        
        _tokenExpiry = new ThisPreExpiredToken(variousDbAccessFactory, Log, this.Authorize);
    }
    
    private class ThisPreExpiredToken : PreExpiredToken
    {
        public ThisPreExpiredToken(IDbAccessFactory<VariousStoredDataDbAccess> access, ILogger logger, Func<string, string> authorize)
            : base(access, logger, "ROZETKA_API_LO_TOKEN", "ROZETKA_API", 2, authorize, 18)
        {
        }
    }

    private string Authorize(string prevToken)
    {
        RozAuthResponse response = _apiWorker.Do(w => w.LoginAsync(RRAppConfig.Rozetka.Username, RRAppConfig.Rozetka.Password));

        if (!response.Success)
            throw new Exception($"Rozetka authorization fail.{DebugUtil.DumpObject(response)}");

        return _accessToken = response.Content.AccessToken;
    }

    private void PrestaProductSyncPublicOnDidPush(object sender, DidPushEventArgs e)
    {
        PublicInterface.Run(w => w.SyncProducts(e.PrestaSyncData));
    }
    
    [SuppressMessage("ReSharper", "ConvertToLambdaExpression")]
    public void Initialize()
    {
        _initializing = true;
        
        // _tokenExpiry.Authorize();

        // this.ImportAllCategories();
    }

    public void ImportAllCategories()
    {
        RozCategoriesResponse categories = _apiWorker.Do(w => w.GetCategories(_accessToken, new CategorySearchParameters() { }));
        
        GC.KeepAlive(categories);
    }
    
    [CanFail(2, LogLevel = LogEventLevel.Error)]
    public void SyncOrders()
    {
    }
    
    public void SyncProducts(ExternalSyncData prestaSyncData)
    {
        if (!_initializing)
        {
            Log.Debug("Sync postponed 10 sec to wait for initialization.");
            /* return */
            Task.Delay(10 * 1000).ContinueWithShortThread(_ => PublicInterface.Run(w => w.SyncProducts(prestaSyncData))).Unwrap(); // TODO: implement task prolongation

            return;
        }

        _productAttributes = prestaSyncData.ProductAttributesConductor;

        List<ExternalSyncProduct> prestaProducts = prestaSyncData.AllPrestaProducts.ToList();

        // Filter: keep only vk products
        prestaProducts.RemoveAll(p => !LinkedNaming.IsPrestaSiteReferenceVkAuthorityArt(p.PrestaProduct.Reference));

        prestaProducts.RemoveAll(p => {
                InvProductCategory cat = _invProductFeatureCentre.GetMainCategory(p.PrestaProduct.Categories);
                
                return true;
            }
        );

        _globalUserErrorsManager.ClearAllProductErrors(GlobalUserErrorSource.RozetkaValidation);

        foreach (ExternalSyncProduct prestaProduct in prestaProducts.ToArray())
        {
            if (!this.ValidateProduct(prestaSyncData, prestaProduct))
            {
                prestaProducts.Remove(prestaProduct);
            }
        }
        
        
    }

    private bool ValidateProduct(ExternalSyncData prestaSyncData, ExternalSyncProduct product)
    {
        return false;
    }
}