using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;


namespace irisdropwebservice.Services.Rozetka.Ins;

public class RozetkaPreparation
{
    private readonly ILogger _logger = InvLog.Logger<RozetkaPreparation>();
    private readonly InvJsonSerializer _jsonSerializer = new InvJsonSerializer();
    
    public RozetkaPreparation()
    {
    }

    public void ConvertRozAttrsToInternalFormat()
    {
        _logger.Warning("Convert ROZ AttrsToInternalFormat");
        

        _logger.Warning("Convert ROZ AttrsToInternalFormat DONE");
    }
}