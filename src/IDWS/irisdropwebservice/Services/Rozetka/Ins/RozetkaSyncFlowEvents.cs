using System.Threading.Tasks;

using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.Logg;

using irisdropwebservice.Legacy;

using Microsoft.Extensions.Hosting;

namespace irisdropwebservice.Services.Rozetka.Ins;

public class RozetkaSyncFlowEvent : ResoluteEventBase
{
    protected override ILogger Logger => InvLog.Logger<RozetkaSyncFlowEvent>();

    private readonly IRozetkaSyncWorker _syncWorker;

    public RozetkaSyncFlowEvent(IInvAppLifetime applicationLifetime, InvAppConfig appConfig,IRozetkaSyncWorker siteScanSyncWorker)
        : base(
            applicationLifetime, appConfig,
            TimeSpan.FromMinutes(1),
            TimeSpan.FromMinutes(10),
            ResoluteEventInitialState.CanFireNow
        )
    {
        _syncWorker = siteScanSyncWorker;
    }

    protected override object GetCurrentDefaultArg()
    {
        return null;
    }

    protected override Task OnTrigger(object arg)
    {
        return _syncWorker.Run(w => w.SyncOrders());
    }
}