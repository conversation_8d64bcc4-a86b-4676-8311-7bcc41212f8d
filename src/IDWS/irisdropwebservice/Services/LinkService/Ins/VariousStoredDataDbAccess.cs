using System.ComponentModel.DataAnnotations.Schema;

using Invictus.Nomenklatura.App2;

using Microsoft.EntityFrameworkCore;

namespace irisdropwebservice.Services.LinkService.Ins
{
    [Table("VariousStoredData")]
    public class VariousStoredDataDbRecord
    {
        public long Id { get; set; }

        public string PropertyName { get; set; }
        public long? IdProperty { get; set; }
        public string Value { get; set; }
    }


    public class VariousStoredDataDbAccess : DbAccessBase
    {
        private readonly MyDbContext _dbContext;

        public VariousStoredDataDbAccess(DbAccessOptions accessOptions, MyDbContext context) 
            : base(accessOptions, context)
        {
            _dbContext = context;
        }

        public string TryGetValue(string propertyName, long idProperty)
        {
            return this.TryGetRecord(propertyName, idProperty)?.Value;
        }

        public void SetValue(string propertyName, long idProperty, string value)
        {
            VariousStoredDataDbRecord record = this.TryGetRecord(propertyName, idProperty);

            if (record != null)
            {
                record.Value = value;
                _dbContext.SaveChanges();
                return;
            }

            _dbContext.Records.Add(new VariousStoredDataDbRecord
                {
                    PropertyName = propertyName,
                    IdProperty = idProperty,
                    Value = value
                }
            );
            _dbContext.SaveChanges();
        }

        private VariousStoredDataDbRecord TryGetRecord(string propertyName, long idProperty)
        {
            return _dbContext.Records.FirstOrDefault(r => r.PropertyName == propertyName && r.IdProperty == idProperty);
        }

        public class MyDbContext : DbContext
        {
            public virtual DbSet<VariousStoredDataDbRecord> Records { get; set; }

            public MyDbContext(DbContextOptions<MyDbContext> options)
                : base(options)
            {
            }

            protected override void OnModelCreating(ModelBuilder modelBuilder)
            {
                base.OnModelCreating(modelBuilder);
            }
        }
    }
}
