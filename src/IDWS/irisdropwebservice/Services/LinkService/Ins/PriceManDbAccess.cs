using Invictus.Nomenklatura.App2;
using Microsoft.EntityFrameworkCore;

namespace irisdropwebservice.Services.LinkService.Ins
{
    public class PriceManDbAccess : DbAccessBase
    {
        public class MyDbContext : DbContext
        {
            public MyDbContext(DbContextOptions<MyDbContext> options)
                : base(options)
            {
            }

            public DbSet<PriceOverride> PriceOverrides { get; set; }

            protected override void OnModelCreating(ModelBuilder modelBuilder)
            {
                modelBuilder.Entity<PriceOverride>()
                    .HasIndex(p => new { p.ProdRef, p.InvSpace })
                    .IsUnique();
                
                base.OnModelCreating(modelBuilder);
            }
        }
        
        private readonly MyDbContext _dbContext;

        public PriceManDbAccess(DbAccessOptions accessOptions, MyDbContext context) 
            : base(accessOptions, context)
        {
            _dbContext = context;
        }

        public PriceOverride GetPriceOverride(string prodRef, int invSpace)
        {
            return _dbContext.PriceOverrides
                .FirstOrDefault(p => p.ProdRef == prodRef && p.InvSpace == invSpace);
        }

        public void SetPrice(string prodRef, float price, int invSpace)
        {
            PriceOverride priceOverride = this.GetPriceOverride(prodRef, invSpace);

            if (priceOverride != null)
            {
                priceOverride.Price = price;
                _dbContext.SaveChanges();
                return;
            }

            _dbContext.PriceOverrides.Add(new PriceOverride
            {
                ProdRef = prodRef,
                InvSpace = invSpace,
                Price = price,
                PriceModType = 0
            });
            _dbContext.SaveChanges();
        }

        public void DeletePriceOverride(string prodRef, int invSpace)
        {
            PriceOverride priceOverride = this.GetPriceOverride(prodRef, invSpace);

            if (priceOverride != null)
            {
                _dbContext.PriceOverrides.Remove(priceOverride);
                _dbContext.SaveChanges();
            }
        }

        public List<PriceOverride> GetAllPriceOverrides()
        {
            return _dbContext.PriceOverrides.ToList();
        }
    }
}