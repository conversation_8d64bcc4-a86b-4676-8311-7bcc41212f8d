using System.ComponentModel.DataAnnotations.Schema;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Misc;

using Microsoft.EntityFrameworkCore;

namespace irisdropwebservice.Services.LinkService.Ins
{
    [Table("ExposedOnlineResources")]
    public class ExposedResourceDbRecord
    {
        public long Id { get; set; }

        public string ResourceType { get; set; }
        public string ResourceReference { get; set; }
        public string InternalResourceId { get; set; }

        public DateTime ExpiryDateUtc { get; set; }
        public bool Auth { get; set; }
    }

    public class ExposedResourcesDbAccess : DbAccessBase
    {
        private readonly MyDbContext _dbContext;

        public ExposedResourcesDbAccess(DbAccessOptions accessOptions, MyDbContext context)
            : base(accessOptions, context)
        {
            _dbContext = context;
        }

        public void ExecuteDeleteExpiredResources()
        {
            DateTime utcNow = ServerClock.GetCurrentUtcTime().DateTime;
            
            _dbContext.ExposedResources.Where(r => utcNow > r.ExpiryDateUtc).ExecuteDelete();
        }

        public string AddVkComResourceForADay(string fullId, string ext, TimeSpan expireAfter)
        {
            string reference = Guid.NewGuid() + "." + ext;

            _dbContext.ExposedResources.Add(new ExposedResourceDbRecord
                {
                    ResourceType = "vf",
                    ResourceReference = reference,
                    InternalResourceId = fullId,
                    Auth = false,
                    ExpiryDateUtc = DateTime.UtcNow + expireAfter
                }
            );

            return reference;
        }

        public void AddStaticResource(string fullId, string ext)
        {
            string reference = fullId + "." + ext;

            _dbContext.ExposedResources.Add(new ExposedResourceDbRecord
                {
                    ResourceType = "vf",
                    ResourceReference = reference,
                    InternalResourceId = fullId,
                    Auth = false,
                    ExpiryDateUtc = DateTime.UtcNow.AddYears(10)
                }
            );
        }

        public string IsAllowedToDownload_GetInternalResourceId(string resourceType, string resourceId)
        {
            IEnumerable<ExposedResourceDbRecord> r = _dbContext.ExposedResources
                .Where(er => er.ResourceType == resourceType && er.ResourceReference == resourceId)
                .ToArray()
                .Where(er => er.ExpiryDateUtc > DateTime.UtcNow);

            foreach (ExposedResourceDbRecord exposedResourceDbRecord in r)
            {
                return exposedResourceDbRecord.InternalResourceId;
            }

            return null;
        }

        public class MyDbContext : DbContext
        {
            public virtual DbSet<ExposedResourceDbRecord> ExposedResources { get; set; }

            public MyDbContext(DbContextOptions<MyDbContext> options)
                : base(options)
            {
            }

            protected override void OnModelCreating(ModelBuilder modelBuilder)
            {
                base.OnModelCreating(modelBuilder);
            }
        }
    }
}
