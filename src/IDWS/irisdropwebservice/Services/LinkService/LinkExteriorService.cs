using System.Threading.Tasks;

using Invictus.Nomenklatura.ExteriorServ;
using Invictus.Nomenklatura.Misc;

using irisdropwebservice.Services.Chats;
using irisdropwebservice.Services.DropUa;
using irisdropwebservice.Services.Epicentr;
using irisdropwebservice.Services.KastaSync;
using irisdropwebservice.Services.PrestaSync;
using irisdropwebservice.Services.PrestaSync.Ins;
using irisdropwebservice.Services.PromSync;
using irisdropwebservice.Services.Rozetka;
using irisdropwebservice.Services.VkScrap;

using Telegram.Bot.Types;

namespace irisdropwebservice.Services.LinkService
{
    public class LinkExteriorService : ITelegramDialogueHandler, IExteriorService
    {
        private readonly IPrestaWorkerPublic _prestaWorkerPublic;
        private readonly PrestaSyncExteriorService _prestaSyncExteriorService;
        private readonly PromSyncExteriorService _promSyncExteriorService;
        private readonly DropUaExteriorService _dropUaExteriorService;
        private readonly KastaSyncExteriorService _kastaSyncExteriorService;
        private readonly EpicentrExteriorService _epicentrExteriorService;
        private readonly RozetkaExteriorService _rozetkaExteriorService;
        private readonly VkScrapExteriorService _vkScrapExteriorService;

        public LinkExteriorService(
            TelegramChatBotPolling telegramChatBotPolling,
            VkScrapExteriorService vkScrapExteriorService,
            PrestaSyncExteriorService prestaSyncExteriorService,
            IPrestaWorkerPublic prestaWorkerPublic,
            PromSyncExteriorService promSyncExteriorService,
            DropUaExteriorService dropUaExteriorService,
            KastaSyncExteriorService kastaSyncExteriorService,
            EpicentrExteriorService epicentrExteriorService,
            RozetkaExteriorService rozetkaExteriorService
        )
        {
            _vkScrapExteriorService = vkScrapExteriorService;
            _prestaSyncExteriorService = prestaSyncExteriorService;
            _prestaWorkerPublic = prestaWorkerPublic;
            _promSyncExteriorService = promSyncExteriorService;
            _dropUaExteriorService = dropUaExteriorService;
            _kastaSyncExteriorService = kastaSyncExteriorService;
            _epicentrExteriorService = epicentrExteriorService;
            _rozetkaExteriorService = rozetkaExteriorService;

            telegramChatBotPolling.AddDialogueHandler(this);
        }

        public Task Run()
        {
            return Task.CompletedTask;
        }

        public Task Run2()
        {
            _vkScrapExteriorService.OnScrapFetchedEvent += this.VkScrapExteriorService_OnScrapFetchedEvent;

            Task scrapThenPushFlowInitialization = Task.Run(() => {
                    VkScrapResult vkScrapLastResult = _vkScrapExteriorService.GetAnyLastScrapResultFull();

                    Task waitForTasks = Task.WhenAll(new []
                        {
                            _prestaSyncExteriorService.Run2(vkScrapLastResult),
                        }
                    );

                    _vkScrapExteriorService.TryGoFirstScrap();

                    return waitForTasks;
                }
            );

            return scrapThenPushFlowInitialization;
        }

        private void VkScrapExteriorService_OnScrapFetchedEvent(VkScrapResult data)
        {
            _prestaSyncExteriorService.QueueTriggerPushFlow(data);
        }

        TelegramDialogueUpdateHandler ITelegramDialogueHandler.CanHandleConversationInitiation(TelegramBotWho whichBot, Update request)
        {
            if (whichBot != TelegramBotWho.Staff)
                return null;

            if (request.Message == null || request.Message.Text == null)
                return null;

            string text = request.Message.Text.Trim();

            if (!text.StartsWithEither(StringComparison.InvariantCultureIgnoreCase, "арт ", "артикул ", "art "))
                return null;

            return (dialogue, update) =>
            {
                if (update.Message == null || update.Message.Text == null)
                    return;

                this.BeginToRespondToReferenceRequest(update.Message.Text.Trim(), dialogue);
            };
        }

        private void BeginToRespondToReferenceRequest(string commandText, TelegramDialogue dialogue)
        {
            string[] spl = IntStringUtil.SplitCommand(commandText);

            string reference = spl[1];

            Task<string> strTask = _prestaWorkerPublic.Run(w => w.GetVkLinksFromReference(reference));

            dialogue.FinishAfterTask(strTask, new PostToTelegramOptions { DisableWebPagePreview = true, Silent = false });
        }
    }
}