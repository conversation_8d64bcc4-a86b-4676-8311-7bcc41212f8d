namespace irisdropwebservice.Services.LinkService
{
    public class LinkedNaming
    {
        public const string SIZES_RANGE_SPLITTER = "-";

        public static string AddCentimetersToHeightSize(string height)
        {
            if (height == null)
                return null;

            if (height.Contains(SIZES_RANGE_SPLITTER))
            {
                string[] spl = height.Split(SIZES_RANGE_SPLITTER, StringSplitOptions.TrimEntries);

                if (spl.Length != 2)
                    throw new ArgumentException("Incorrect height size", nameof(height));

                return AddCentimetersToHeightSize(spl[0]) + SIZES_RANGE_SPLITTER + AddCentimetersToHeightSize(spl[1]);
            }

            if (!int.TryParse(height, out _))
                throw new ArgumentException("Incorrect height size", nameof(height));

            if (height.EndsWith("см", StringComparison.InvariantCulture))
                return height;

            return height + "см";
        }

        public static string TryRemoveCentimetersFromHeightSize(string height)
        {
            if (height == null)
                return null;

            if (height.Contains(SIZES_RANGE_SPLITTER))
            {
                string[] spl = height.Split(SIZES_RANGE_SPLITTER, StringSplitOptions.TrimEntries);

                if (spl.Length != 2)
                    throw new ArgumentException("Incorrect height size", nameof(height));

                return TryRemoveCentimetersFromHeightSize(spl[0]) + SIZES_RANGE_SPLITTER + TryRemoveCentimetersFromHeightSize(spl[1]);
            }

            if (!height.EndsWith("см"))
                return height;

            return height.Substring(0, height.Length - 2);
        }

        // Presta, VK

        public static long GetVkComComparableArt(string art)
        {
            if (art.Contains("."))
                art = art.Remove(art.IndexOf(".", StringComparison.InvariantCulture));

            if (!long.TryParse(art, out long res))
                return -1;

            return res;
        }

        public static string PrestaSiteReferenceFromVkAuthorityArt(string art)
        {
            ArgumentNullException.ThrowIfNull(art);

            long artLong;

            if (!long.TryParse(art, out artLong))
                throw new Exception("Non-int arts are not implemented yet.");

            return "V" + artLong.ToString("D4");
        }

        public static bool IsPrestaSiteReferenceVkAuthorityArt(string reference)
        {
            ArgumentNullException.ThrowIfNull(reference);

            if (!reference.StartsWith("V"))
                return false;

            string subStr = reference[1..];

            return long.TryParse(subStr, out _);
        }

        public static string GetVkAuthorityArtFromPrestaSiteReference(string reference)
        {
            ArgumentNullException.ThrowIfNull(reference);

            if (!reference.StartsWith("V"))
                throw new Exception("Not a Vk authority art.");

            string subStr = reference[1..];

            long res = long.Parse(subStr);

            return res.ToString();
        }

        // Prom

        public static bool IsExternalSiteReferenceLinkedToPresta(string reference)
        {
            ArgumentNullException.ThrowIfNull(reference);

            if (!reference.StartsWith("IRP"))
                return false;

            string subStr = reference[3..];

            return long.TryParse(subStr, out _);
        }

        public static bool IsExternalSiteReferenceLinkedToPrestaSourcedFromVk(string reference)
        {
            ArgumentNullException.ThrowIfNull(reference);

            if (!reference.StartsWith(ExternalSiteReferenceLinkedToPrestaSourcedFromVkStartsWith()))
                return false;

            string subStr = reference[4..];

            return long.TryParse(subStr, out _);
        }

        public static string ExternalSiteReferenceLinkedToPrestaSourcedFromVkStartsWith()
        {
            return "IRPV";
        }

        public static string ExternalSiteReferenceFromPrestaSourcedVkAuthorityArt(string art)
        {
            ArgumentNullException.ThrowIfNull(art);

            string artNumber = GetVkAuthorityArtFromPrestaSiteReference(art);

            long artLong;

            if (!long.TryParse(artNumber, out artLong))
                throw new Exception("Non-int arts are not implemented yet.");

            return ExternalSiteReferenceLinkedToPrestaSourcedFromVkStartsWith() + artLong.ToString("D4");
        }

        public static string ExternalSiteUniqueSkuIdFromVkAuthorityArt(string art, string size)
        {
            return ExternalSiteReferenceFromPrestaSourcedVkAuthorityArt(art) + " " + size;
        }
    }
}