using System.Text;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Logg;
using irisdropwebservice.Services.Chats;
using irisdropwebservice.Services.LinkService.Ins;
using Telegram.Bot.Types;

namespace irisdropwebservice.Services.LinkService;

public interface IPriceManager
{
    float? GetOverridePrice(string prodRef, int invSpace);
    void SetOverridePrice(string prodRef, float price, int invSpace = 0);
    void ResetOverridePrice(string prodRef, int invSpace = 0);
    List<PriceOverride> GetAllPriceOverrides();
}

public class PriceManager : IPriceManager, ITelegramDialogueHandler
{
    private readonly ILogger _logger = InvLog.Logger<PriceManager>();
    private readonly IDbAccessFactory<PriceManDbAccess> _dbAccessFactory;
    
    public PriceManager(TelegramChatBotPolling telegramChatBotPolling, IDbAccessFactory<PriceManDbAccess> dbAccessFactory)
    {
        _dbAccessFactory = dbAccessFactory;
        
        telegramChatBotPolling.AddDialogueHandler(this);
    }
    
    public float? GetOverridePrice(string prodRef, int invSpace)
    {
        using PriceManDbAccess access = _dbAccessFactory.CreateAccess();
        PriceOverride priceOverride = access.GetPriceOverride(prodRef, invSpace);
        return priceOverride?.Price;
    }
    
    public void SetOverridePrice(string prodRef, float price, int invSpace = 0)
    {
        _logger.Information($"SetPrice {prodRef} {price}");
        
        using PriceManDbAccess access = _dbAccessFactory.CreateAccess();
        access.SetPrice(prodRef, price, invSpace);
    }
    
    public void ResetOverridePrice(string prodRef, int invSpace = 0)
    {
        _logger.Information($"ResetPrice {prodRef}");
        
        using PriceManDbAccess access = _dbAccessFactory.CreateAccess();
        access.DeletePriceOverride(prodRef, invSpace);
    }
    
    public List<PriceOverride> GetAllPriceOverrides()
    {
        _logger.Information($"GetAllPriceOverrides");
        
        using PriceManDbAccess access = _dbAccessFactory.CreateAccess();
        return access.GetAllPriceOverrides();
    }
    
    public TelegramDialogueUpdateHandler CanHandleConversationInitiation(TelegramBotWho who, Update update)
    {
        if (who != TelegramBotWho.Staff)
            return null;

        if (update.Message == null || update.Message.Text == null)
            return null;
        
        if (update.Message.Chat.Id != 5177725569 && update.Message.Chat.Id != 372319067 && update.Message.Chat.Id != 758718283 && update.Message.Chat.Id != 618913402)
            return null;
        
        string messageText = update.Message.Text.Trim();
        
        Action<TelegramDialogue, string[]> action = this.GetCommandAction(messageText);

        if (action == null)
            return null;
        
        return (d, u) => {
            string[] parts = u.Message.Text.Trim().Split(' ', StringSplitOptions.RemoveEmptyEntries);
        
            if (parts.Length == 0)
                return;

            action(d, parts);
        };
    }

    private Action<TelegramDialogue, string[]> GetCommandAction(string messageText)
    {
        if (messageText.StartsWith("/setprice", StringComparison.OrdinalIgnoreCase))
            return this.HandleSetPrice;

        if (messageText.StartsWith("/resetprice", StringComparison.OrdinalIgnoreCase))
            return this.HandleResetPrice;

        if (messageText.StartsWith("/pricelist", StringComparison.OrdinalIgnoreCase))
            return this.HandlePriceList;

        return null;
    }

    private void HandleSetPrice(TelegramDialogue dialogue, string[] parts)
    {
        if (parts.Length < 3)
        {
            dialogue.ReplyEnd("Використання: /setprice <ProdRef> <Price> UAH", new PostToTelegramOptions() { Silent = false });
            return;
        }
        
        string prodRef = this.NormalizeProductRef(parts[1]);
        
        if (!float.TryParse(parts[2], out float price))
        {
            dialogue.ReplyEnd($"Невірний фортам: {parts[2]}", new PostToTelegramOptions() { Silent = false });
            return;
        }

        this.SetOverridePrice(prodRef, price);
        dialogue.ReplyEnd($"Ціна для {prodRef} перевизначена в {price}", new PostToTelegramOptions() { Silent = false });
    }
    
    private void HandleResetPrice(TelegramDialogue dialogue, string[] parts)
    {
        if (parts.Length < 2)
        {
            dialogue.ReplyEnd("Використання: /resetprice <ProdRef>", new PostToTelegramOptions() { Silent = false });
            return;
        }
        
        string prodRef = this.NormalizeProductRef(parts[1]);
        this.ResetOverridePrice(prodRef);
        dialogue.ReplyEnd($"Перевизначення ціни для {prodRef} скинуто", new PostToTelegramOptions() { Silent = false });
    }
    
    private void HandlePriceList(TelegramDialogue dialogue, string[] parts)
    {
        List<PriceOverride> overrides = this.GetAllPriceOverrides();
        
        if (overrides.Count == 0)
        {
            dialogue.ReplyEnd("Перевизначень цін немає", new PostToTelegramOptions() { Silent = false });
            return;
        }
        
        var sb = new StringBuilder();
        sb.AppendLine("Усі перевизначення ціни:");
        
        foreach (PriceOverride priceOverride in overrides)
        {
            sb.AppendLine($"{priceOverride.ProdRef}: {priceOverride.Price})");
        }
        
        dialogue.ReplyEnd(sb.ToString(), new PostToTelegramOptions() { Silent = false });
    }
    
    private string NormalizeProductRef(string prodRef)
    {
        if (LinkedNaming.IsPrestaSiteReferenceVkAuthorityArt(prodRef))
            return prodRef;

        return LinkedNaming.PrestaSiteReferenceFromVkAuthorityArt(prodRef);
    }
}
