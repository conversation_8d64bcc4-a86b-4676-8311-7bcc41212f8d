using System.IO;
using System.Reflection;

using Invictus.Nomenklatura.App2;

using irisdropwebservice.Legacy;
using irisdropwebservice.Services.LinkService.Ins;
using irisdropwebservice.Services.VkScrap;

namespace irisdropwebservice.Services.LinkService
{
    public class ExposedResourcesService
    {
        private readonly IDbAccessFactory<ExposedResourcesDbAccess> _databaseAccessFactory;
        private readonly IVkScrapFileDownload _vkScrapFileDownload;

        public ExposedResourcesService(IDbAccessFactory<ExposedResourcesDbAccess> databaseAccessFactory, IVkScrapFileDownload vkScrapFileDownload, LeBackgroundTasks threadedTasks)
        {
            _databaseAccessFactory = databaseAccessFactory;
            _vkScrapFileDownload = vkScrapFileDownload;
            
            threadedTasks.AddFixedTimeOfDayBackgroundTask(this.CleanupOldTempFileDbRecords, "CleanupOldTempFileDbRecords", TimeSpan.FromHours(2.66));
        }

        private void CleanupOldTempFileDbRecords()
        {
            using ExposedResourcesDbAccess access = _databaseAccessFactory.CreateAccess();
            access.ExecuteDeleteExpiredResources();
        }

        public string RegisterVkComImgFor1Day(string fullVkComPhotoId)
        {
            using ExposedResourcesDbAccess access = _databaseAccessFactory.CreateAccess();
            string reference = access.AddVkComResourceForADay(fullVkComPhotoId, "jpg", TimeSpan.FromDays(1));
            access.SaveChanges();

            return reference;
        }

        public void RegisterStaticResource()
        {
        }

        public byte[] GetFileContents(string resourceType, string resourceId)
        {
            using ExposedResourcesDbAccess access = _databaseAccessFactory.CreateAccess();

            string imgId = access.IsAllowedToDownload_GetInternalResourceId(resourceType, resourceId);

            if (imgId == null)
                return null;

            return _vkScrapFileDownload.DownloadFile(imgId);
        }

        public byte[] GetDefaultFileContents()
        {
            string resourceName = "irisdropwebservice.Views.placeholder_404.jpg";

            using (Stream stream = Assembly.GetExecutingAssembly().GetManifestResourceStream(resourceName))
            using (var reader = new BinaryReader(stream))
            {
                return reader.ReadBytes((int)stream.Length);
            }
        }
    }
}
