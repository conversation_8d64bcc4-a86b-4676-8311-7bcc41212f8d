using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace irisdropwebservice.Services.LinkService;

[Table("ProdPriceOverride")]
public class PriceOverride
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public long Id { get; set; }
        
    [Required]
    [MaxLength(50)]
    public string ProdRef { get; set; }
        
    public int InvSpace { get; set; }
        
    public float Price { get; set; }
        
    public int PriceModType { get; set; }
}