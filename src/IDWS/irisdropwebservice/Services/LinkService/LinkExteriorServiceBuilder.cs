using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.ExteriorServ;

using irisdropwebservice.Services.LinkService.Ins;

using JetBrains.Annotations;

using Microsoft.Extensions.DependencyInjection;

namespace irisdropwebservice.Services.LinkService
{
    [UsedImplicitly]
    public class LinkExteriorServiceBuilder : ExteriorServiceBuilderBase<LinkExteriorService>
    {
        protected override void AddConcealedServices(ServiceCollection concealedServiceCollection)
        {
            concealedServiceCollection.AddSingleton<ExposedResourcesService>();
            concealedServiceCollection.AddSingleton<GlobalUserErrorsManager>();
            
            concealedServiceCollection.AddSingleton<LanguageService>();
            concealedServiceCollection.AddSingleton<PostponedProductUpdateService>();

            concealedServiceCollection.AddSingleton<IPriceManager, PriceManager>();
            
            this.RegisterDatabaseTypes(concealedServiceCollection);

            base.AddConcealedServices(concealedServiceCollection);
        }

        protected override void ExposeConcealedServices()
        {
            this.ExposeSingleton<ExposedResourcesService>();
            this.ExposeSingleton<IDbAccessFactory<VariousStoredDataDbAccess>>();
            this.ExposeSingleton<GlobalUserErrorsManager>();
            
            this.ExposeSingleton<LanguageService>();
            this.ExposeSingleton<PostponedProductUpdateService>();
            this.ExposeSingleton<IPriceManager>();

            base.ExposeConcealedServices();
        }
    }
}