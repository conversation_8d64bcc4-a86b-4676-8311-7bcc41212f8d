using System.Text.Json.Serialization;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Misc;

using irisdropwebservice.Services.LinkService.Ins;

namespace irisdropwebservice.Services.LinkService
{
    public enum GlobalUserErrorSource
    {
        Invalid = 0,
        VkScrap,
        PrestaValidation,
        KastaValidation,
        EpicentrValidation,
        RozetkaValidation,
        PromValidation
    }

    public class GlobalUserErrorsManager
    {
        private readonly IDbAccessFactory<VariousStoredDataDbAccess> _variousStoredDataDbAccessFactory;
        private readonly InvJsonSerializer _jsonSerializer = new ();

        public GlobalUserErrorsManager(IDbAccessFactory<VariousStoredDataDbAccess> variousStoredDataDbAccessFactory)
        {
            _variousStoredDataDbAccessFactory = variousStoredDataDbAccessFactory;
        }

        public GlobalUserErrorsStruct GetErrors()
        {
            using VariousStoredDataDbAccess access = _variousStoredDataDbAccessFactory.CreateAccess();

            GlobalUserErrorsStruct errors = this.GetOrCreateErrorsStruct(access);

            return errors;
        }

        public void FlushErrors()
        {
            using VariousStoredDataDbAccess access = _variousStoredDataDbAccessFactory.CreateAccess();

            GlobalUserErrorsStruct errors = this.GetOrCreateErrorsStruct(access);

            errors.Errors.Clear();
            errors.ProductErrors.Clear();

            this.SaveErrorsStruct(errors, access);
        }

        public void ClearAllProductErrors(GlobalUserErrorSource source)
        {
            using VariousStoredDataDbAccess access = _variousStoredDataDbAccessFactory.CreateAccess();

            GlobalUserErrorsStruct errors = this.GetOrCreateErrorsStruct(access);

            KeyValuePair<GlobalUserProductErrorKey, string>[] keysToClear = errors.ProductErrors.Where(kv => kv.Key.Source == source).ToArray();

            foreach (KeyValuePair<GlobalUserProductErrorKey, string> kv in keysToClear)
            {
                errors.ProductErrors.Remove(kv.Key);
            }

            this.SaveErrorsStruct(errors, access);
        }

        public void SetFlushableSourceError(GlobalUserErrorSource source, string text)
        {
            using VariousStoredDataDbAccess access = _variousStoredDataDbAccessFactory.CreateAccess();

            GlobalUserErrorsStruct errors = this.GetOrCreateErrorsStruct(access);

            if (text == null)
            {
                if (errors.Errors.ContainsKey(source))
                    errors.Errors.Remove(source);
            } else
            {
                errors.Errors[source] = text;
            }

            this.SaveErrorsStruct(errors, access);
        }

        public void SetFlushableProductError(GlobalUserErrorSource source, string prestaArt, string errorText)
        {
            this.SetFlushableProductErrors(source, new List<(string, string)> { (prestaArt, errorText) });
        }

        public void SetFlushableProductErrors(GlobalUserErrorSource source, IList<(string, string)> prestaArtsAndTexts)
        {
            using VariousStoredDataDbAccess access = _variousStoredDataDbAccessFactory.CreateAccess();

            GlobalUserErrorsStruct errors = this.GetOrCreateErrorsStruct(access);

            bool edited = false;

            foreach ((string, string) prestaArtsAndText in prestaArtsAndTexts)
            {
                var key = new GlobalUserProductErrorKey { Source = source, PrestaArt = prestaArtsAndText.Item1 };

                bool containsKey = errors.ProductErrors.ContainsKey(key);

                if (prestaArtsAndText.Item2 == null)
                {
                    if (containsKey)
                    {
                        errors.ProductErrors.Remove(key);
                        edited = true;
                    }
                }
                else
                {
                    if (!containsKey || errors.ProductErrors[key] != prestaArtsAndText.Item2)
                    {
                        errors.ProductErrors[key] = prestaArtsAndText.Item2;
                        edited = true;
                    }
                }
            }

            if (edited)
                this.SaveErrorsStruct(errors, access);
        }

        private void SaveErrorsStruct(GlobalUserErrorsStruct errors, VariousStoredDataDbAccess access)
        {
            string value = _jsonSerializer.SerializeForInternals(errors, typeof(GlobalUserErrorsStruct));

            access.SetValue("GLOBAL_USER_ERRORS", 2, value);
        }

        private GlobalUserErrorsStruct GetOrCreateErrorsStruct(VariousStoredDataDbAccess access)
        {
            string dbRecord = access.TryGetValue("GLOBAL_USER_ERRORS", 2);

            GlobalUserErrorsStruct errorsStruct;

            if (dbRecord == null)
            {
                errorsStruct = new GlobalUserErrorsStruct();
                errorsStruct.Errors = new Dictionary<GlobalUserErrorSource, string>();
                errorsStruct.ProductErrors = new Dictionary<GlobalUserProductErrorKey, string>();
            } else
            {
                errorsStruct = _jsonSerializer.DeserializeForInternals<GlobalUserErrorsStruct>(dbRecord);
            }

            return errorsStruct;
        }
    }

    [Serializable]
    public class GlobalUserErrorsStruct
    {
        [JsonPropertyName("product_errors")]
        [JsonConverter(typeof(DictionaryAsArrayWithComparerConverter<GlobalUserProductErrorKey, string, GlobalUserProductErrorKeyComparer>))]
        public Dictionary<GlobalUserProductErrorKey, string> ProductErrors { get; set; }

        [JsonPropertyName("general_errors")]
        [JsonConverter(typeof(DictionaryAsArrayConverter<GlobalUserErrorSource, string>))]
        public Dictionary<GlobalUserErrorSource, string> Errors { get; set; }
    }

    [Serializable]
    public class GlobalUserProductErrorKey
    {
        [JsonPropertyName("source")]
        [JsonConverter(typeof(StringEnumConverter<GlobalUserErrorSource>))]
        public GlobalUserErrorSource Source { get; set; }

        [JsonPropertyName("presta_art")]
        public string PrestaArt { get; set; }
    }

    public class GlobalUserProductErrorKeyComparer : IEqualityComparer<GlobalUserProductErrorKey>
    {
        public static readonly GlobalUserProductErrorKeyComparer Instance = new();

        public bool Equals(GlobalUserProductErrorKey x, GlobalUserProductErrorKey y)
        {
            if (x == null && y == null) return true;
            if (x == null || y == null) return false;

            // Example comparison logic: Compare by SourceName and ErrorCode
            return x.Source == y.Source && x.PrestaArt == y.PrestaArt;
        }

        public int GetHashCode(GlobalUserProductErrorKey obj)
        {
            // Combine hash codes of properties used in Equals
            int hashSourceName = obj.Source.GetHashCode();
            int hashErrorCode = obj.PrestaArt.GetHashCode();

            return hashSourceName ^ hashErrorCode; // XOR to combine hash codes
        }
    }
}
