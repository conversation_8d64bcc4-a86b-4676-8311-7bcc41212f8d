<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>disable</Nullable>
    <UserSecretsId>aspnet-irisdropwebservice-aa5ddd81-e013-4547-bbc9-caa5fd6c12cf</UserSecretsId>
	<EnableDefaultContentItems>false</EnableDefaultContentItems>
	<Configurations>LocalRelease;LocalDebug;RemoteRelease;RemoteDebug</Configurations>
	<Title>irisdropwebservice</Title>
	<Authors>m3f</Authors>
	<ImplicitUsings>disable</ImplicitUsings>
	<LangVersion>13</LangVersion>
	<Platforms>AnyCPU</Platforms>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='LocalRelease|AnyCPU'">
    <Optimize>True</Optimize>
  </PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='LocalDebug|AnyCPU'">
		<Optimize>False</Optimize>
		<DefineConstants>$(DefineConstants)TRACE;DEBUG</DefineConstants>
	</PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RemoteRelease|AnyCPU'">
    <Optimize>False</Optimize>
  </PropertyGroup>
  
  <ItemGroup>
	  <None Remove="Pages\Shared\_Layout.cshtml" />
    <None Remove="Pages\Shared\_ValidationScriptsPartial.cshtml" />
    <None Remove="Pages\Mgr\Kasta\Index.cshtml" />
    <None Remove="Services\PrestaSync\Resources\cats_and_features.json" />
    <None Remove="Views\placeholder_404.jpg" />
    <None Remove="Views\PrestaShopForChat\Customers\CancelledOrder.cshtml" />
    <None Remove="Views\PrestaShopForChat\Customers\NewOrder.cshtml" />
    <None Remove="Views\PrestaShopForChat\Customers\NewOrderLocal.cshtml" />
    <None Remove="Views\PrestaShopForChat\Customers\OutOfStockOrder.cshtml" />
    <None Remove="Views\PrestaShopForChat\Customers\PaidOrder.cshtml" />
    <None Remove="Views\PrestaShopForChat\Customers\PaymentErrorOrder.cshtml" />
    <None Remove="Views\PrestaShopForChat\Customers\RefundOrder.cshtml" />
    <None Remove="Views\PrestaShopForChat\Customers\ShippedOrder.cshtml" />
    <None Remove="Views\PrestaShopForChat\Partials\OrderAddressesDetails.cshtml" />
    <None Remove="Views\PrestaShopForChat\Partials\OrderCommentsDetails.cshtml" />
    <None Remove="Views\PrestaShopForChat\Partials\OrderSameProductIdsDetails.cshtml" />
    <None Remove="Views\PrestaShopForChat\Staff\NewCustomerMessage.cshtml" />
    <None Remove="Views\PrestaShopForChat\Staff\NewOrder.cshtml" />
    <None Remove="Views\PrestaShopForChat\Staff\NewOrderAttachedFile.cshtml" />
    <None Remove="Views\PrestaShopForChat\Staff\Reservations\NewOrChangedReservation.cshtml" />
    <None Remove="Views\PrestaShopForChat\Staff\SendOrder.cshtml" />
    <None Remove="Views\PrestaShopForChat\Staff\VkReduceInStock.cshtml" />
    <None Remove="Views\Statistics\VkScrapAvaliReport.cshtml" />
    <None Include="Resources\Kasta\kasta_catsДевочкам.json.zip">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>

    <None Include="Resources\Kasta\kasta_catsЖенщинам.json.zip">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>

    <None Update="Resources\Kasta\kasta_catsДетям.json.zip">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
    <None Update="Resources\Kasta\kasta_catsМальчикам.json.zip">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
    <None Update="Resources\Kasta\kasta_catsУнисекс.json.zip">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
    <None Update="Resources\Epicentr\export-attribute-set_1_штани-дитячі.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
    <None Update="Resources\Epicentr\export-attribute-set_2547_лосини-дитячі.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
    <None Update="Resources\Epicentr\export-attribute-set_2599_куртки-дитячі.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
    <None Update="Resources\Epicentr\export-attribute-set_2741_пальта-дитячі.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
    <None Update="Resources\Epicentr\export-attribute-set_2746_піжами-дитячі.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
    <None Update="Resources\Epicentr\export-attribute-set_4104_піжами.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
    <None Update="Resources\Epicentr\all_categories.json.zip">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
    <None Update="Resources\Prom\prom_cat_Костюмы Для Девочек.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
    <None Update="Resources\Prom\prom_cat_ПлатьяСарафаныДевочек.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
    <None Update="Resources\Prom\prom_cat_БрюкиДжинсыДляДевочек.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
    <None Update="Resources\Prom\prom_cat_БлузкиТуникиДляДевочек.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
    <None Update="Resources\Prom\prom_cat_КомбинезоныДляДевочек.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
    <None Update="Resources\Prom\prom_catДетскиеКупальникиИПлавки.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
    <None Update="Resources\Prom\prom_catДетскиеФутболкиИМайки.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
    <None Update="Resources\Prom\prom_catЖилетыДляДевочек.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
    <None Update="Resources\Prom\prom_catКофтыИСвитерыДляДевочек.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
    <None Update="Resources\Prom\prom_cat_ЮбкиДляДевочек.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
	  <None Update="StaticFiles\**\*">
		  <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
	  </None>

	  <Content Include="StaticFiles\css\jstree.style.css" />
    <Content Include="StaticFiles\css\site.css" />
    <Content Include="StaticFiles\img\40px.png" />
    <Content Include="StaticFiles\img\throbber.gif" />
    <Content Include="StaticFiles\js\jstree.min.js" />
    <Content Include="StaticFiles\js\site.js" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-grid.css" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-grid.css.map" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-grid.min.css" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-grid.min.css.map" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-grid.rtl.css" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-grid.rtl.css.map" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css.map" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-reboot.css" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-reboot.css.map" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-reboot.min.css" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-reboot.min.css.map" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css.map" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css.map" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-utilities.css" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-utilities.css.map" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-utilities.min.css" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-utilities.min.css.map" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css.map" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css.map" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap.css" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap.css.map" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap.min.css" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap.min.css.map" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap.rtl.css" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap.rtl.css.map" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap.rtl.min.css" />
    <Content Include="StaticFiles\lib\bootstrap\dist\css\bootstrap.rtl.min.css.map" />
    <Content Include="StaticFiles\lib\bootstrap\dist\js\bootstrap.bundle.js" />
    <Content Include="StaticFiles\lib\bootstrap\dist\js\bootstrap.bundle.js.map" />
    <Content Include="StaticFiles\lib\bootstrap\dist\js\bootstrap.bundle.min.js" />
    <Content Include="StaticFiles\lib\bootstrap\dist\js\bootstrap.bundle.min.js.map" />
    <Content Include="StaticFiles\lib\bootstrap\dist\js\bootstrap.esm.js" />
    <Content Include="StaticFiles\lib\bootstrap\dist\js\bootstrap.esm.js.map" />
    <Content Include="StaticFiles\lib\bootstrap\dist\js\bootstrap.esm.min.js" />
    <Content Include="StaticFiles\lib\bootstrap\dist\js\bootstrap.esm.min.js.map" />
    <Content Include="StaticFiles\lib\bootstrap\dist\js\bootstrap.js" />
    <Content Include="StaticFiles\lib\bootstrap\dist\js\bootstrap.js.map" />
    <Content Include="StaticFiles\lib\bootstrap\dist\js\bootstrap.min.js" />
    <Content Include="StaticFiles\lib\bootstrap\dist\js\bootstrap.min.js.map" />
    <Content Include="StaticFiles\lib\bootstrap\LICENSE" />
    <Content Include="StaticFiles\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.js" />
    <Content Include="StaticFiles\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.min.js" />
    <Content Include="StaticFiles\lib\jquery-validation-unobtrusive\LICENSE.txt" />
    <Content Include="StaticFiles\lib\jquery-validation\dist\additional-methods.js" />
    <Content Include="StaticFiles\lib\jquery-validation\dist\additional-methods.min.js" />
    <Content Include="StaticFiles\lib\jquery-validation\dist\jquery.validate.js" />
    <Content Include="StaticFiles\lib\jquery-validation\dist\jquery.validate.min.js" />
    <Content Include="StaticFiles\lib\jquery-validation\LICENSE.md" />
    <Content Include="StaticFiles\lib\jquery\dist\jquery.js" />
    <Content Include="StaticFiles\lib\jquery\dist\jquery.min.js" />
    <Content Include="StaticFiles\lib\jquery\dist\jquery.min.map" />
    <Content Include="StaticFiles\lib\jquery\LICENSE.txt" />
    <None Remove="AppConfig\idws.cfg.dev.json" />
    <Content Include="AppConfig\idws.cfg.dev.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <CopyToPublishDirectory>Always</CopyToPublishDirectory>
    </Content>
    <None Remove="AppConfig\idws.cfg.integrationtestlocal.json" />
    <Content Include="AppConfig\idws.cfg.integrationtestlocal.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <CopyToPublishDirectory>Never</CopyToPublishDirectory>
    </Content>
    <None Remove="AppConfig\idws.cfg.prodserv.json" />
    <Content Include="AppConfig\idws.cfg.prodserv.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Remove="AppConfig\idws.cfg.shared.json" />
    <Content Include="AppConfig\idws.cfg.shared.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <CopyToPublishDirectory>Always</CopyToPublishDirectory>
    </Content>
    <None Remove="AppConfig\idws.sec.dev.json" />
    <Content Include="AppConfig\idws.sec.dev.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <CopyToPublishDirectory>Always</CopyToPublishDirectory>
    </Content>
    <None Remove="AppConfig\idws.sec.integrationtestlocal.json" />
    <Content Include="AppConfig\idws.sec.integrationtestlocal.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <CopyToPublishDirectory>Never</CopyToPublishDirectory>
    </Content>
    <None Remove="AppConfig\idws.sec.prodserv.json" />
    <Content Include="AppConfig\idws.sec.prodserv.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Remove="AppConfig\idws.sec.shared.json" />
    <Content Include="AppConfig\idws.sec.shared.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <CopyToPublishDirectory>Always</CopyToPublishDirectory>
    </Content>
  </ItemGroup>
	<ItemGroup>
    <Content Include="Pages\Shared\_Layout.cshtml" />
    <Content Include="Pages\Shared\_ValidationScriptsPartial.cshtml" />
    <Content Include="Pages\_ViewImports.cshtml" />
    <Content Include="Pages\_ViewStart.cshtml" />
    <Content Include="Pages\Mgr\Kasta\Index.cshtml" />
    <Content Include="Views\PrestaShopForChat\Customers\CancelledOrder.cshtml" />
    <Content Include="Views\PrestaShopForChat\Customers\NewOrderLocal.cshtml" />
    <Content Include="Views\PrestaShopForChat\Customers\NewOrder.cshtml" />
    <Content Include="Views\PrestaShopForChat\Customers\OutOfStockOrder.cshtml" />
    <Content Include="Views\PrestaShopForChat\Customers\PaidOrder.cshtml" />
    <Content Include="Views\PrestaShopForChat\Customers\PaymentErrorOrder.cshtml" />
    <Content Include="Views\PrestaShopForChat\Customers\RefundOrder.cshtml" />
    <Content Include="Views\PrestaShopForChat\Customers\ShippedOrder.cshtml" />
    <Content Include="Views\PrestaShopForChat\Partials\OrderAddressesDetails.cshtml" />
    <Content Include="Views\PrestaShopForChat\Partials\OrderCommentsDetails.cshtml" />
    <Content Include="Views\PrestaShopForChat\Partials\OrderSameProductIdsDetails.cshtml" />
    <Content Include="Views\PrestaShopForChat\Staff\NewOrderAttachedFile.cshtml" />
    <Content Include="Views\PrestaShopForChat\Staff\NewCustomerMessage.cshtml" />
    <Content Include="Views\PrestaShopForChat\Staff\NewOrder.cshtml" />
    <Content Include="Views\PrestaShopForChat\Staff\SendOrder.cshtml" />
    <Content Include="Views\PrestaShopForChat\Staff\Reservations\NewOrChangedReservation.cshtml" />
    <Content Include="Views\PrestaShopForChat\Staff\VkReduceInStock.cshtml" />
    <Content Include="Views\Statistics\VkScrapAvaliReport.cshtml" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Castle.Core" Version="5.1.1" />
    <PackageReference Include="Castle.Core.AsyncInterceptor" Version="2.1.0" />
    <PackageReference Include="ClosedXML" Version="0.104.2" />
    <PackageReference Include="GitVersion.MsBuild" Version="6.1.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="HtmlAgilityPack" Version="1.11.72" />
    <PackageReference Include="libphonenumber-csharp" Version="8.13.54" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.16" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.16" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.2.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Abstractions" Version="8.0.16" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.16">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.16" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.16" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer.Design" Version="1.1.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.16">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.16" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.16" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="9.0.0" />
    <PackageReference Include="OneOf" Version="3.0.271" />
    <PackageReference Include="PKCsvHelper.Excel.Core" Version="27.2.3" />
    <PackageReference Include="PrestaSharp" Version="1.2.9" />
    <PackageReference Include="Refit" Version="8.0.0" />
    <PackageReference Include="ReflectionMagic" Version="5.0.1" />
    <!-- <PackageReference Include="RestSharp" Version="108.0.4" /> -->
    <PackageReference Include="Serilog" Version="4.2.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
    <PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.6" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="7.2.0" />
    <PackageReference Include="Telegram.Bot" Version="22.3.0" />
    <PackageReference Include="UnidecodeSharpCore" Version="2.0.1" />
    <PackageReference Include="VkNet" Version="1.78.0" />
    <PackageReference Include="VkNet.Extensions.Polling" Version="0.1.3" />
    <PackageReference Include="WTelegramClient" Version="4.2.8" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Libs\Rozetka\" />
    <Folder Include="Resources\Epicentr\" />
    <Folder Include="Resources\Temp\Epicentr\" />
    <Folder Include="Services\SiteScan\Ins\" />
    <Folder Include="Services\DropUa\Remote\" />
    <Folder Include="Views\Shared\" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Serilog" />
    <Using Include="System" />
    <Using Include="System.Collections.Generic" />
    <Using Include="System.Linq" />
    <Using Include="irisdropwebservice.Log" />
  </ItemGroup>

	<ItemGroup>
		<AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
			<_Parameter1>$(MSBuildProjectName).IntegrationTests</_Parameter1>
		</AssemblyAttribute>
	</ItemGroup>

	<ItemGroup>
	  <Content Include="StaticFiles\img\32px.png" />
	</ItemGroup>

	<ItemGroup>

    <EmbeddedResource Include="Services\PrestaSync\Resources\cats_and_features.json" />
    <EmbeddedResource Include="Views\placeholder_404.jpg" />
    <None Remove="Resources\Epicentr\export-template.xlsx" />
    <EmbeddedResource Include="Resources\Epicentr\export-template.xlsx" />
</ItemGroup>

	<ItemGroup>
	  <Compile Remove="Services\SiteScan\Ins\SiteScanWorker.cs" />
	  <Compile Remove="Services\SiteScan\SiteScanExteriorService.cs" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\..\Invictus\Invictus.InvApiClient\Invictus.InvApiClient.csproj" />
	  <ProjectReference Include="..\..\Invictus\Invictus.Jupiter.Shared\Invictus.Jupiter.Shared.csproj" />
	  <ProjectReference Include="..\..\Invictus\Invictus.Nomenklatura\Invictus.Nomenklatura.csproj" />
	</ItemGroup>


	<Choose>
		<When Condition="'$(Configuration)' == 'LocalDebug' Or '$(Configuration)' == 'LocalRelease'" />
		<When Condition="'$(Configuration)' == 'RemoteRelease'">
		</When>
	</Choose>

	
</Project>
