{"profiles": {"irisdropwebservice": {"commandName": "Project", "launchBrowser": true, "launchUrl": "mgr/kasta", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:7276"}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "mgr/kasta", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}, "$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:49509", "sslPort": 44356}}}