using System.IO;

using Invictus.InvApiClient;
using Invictus.Jupiter.Shared;
using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.App.Launchpad;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Misc;
using Invictus.Platform;

using irisdropwebservice.AppConfig;
using irisdropwebservice.Services;
using irisdropwebservice.Services.Auth;
using irisdropwebservice.Services.ThisApp;

using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption;
using Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;

namespace irisdropwebservice;

public class Program
{
    public static void Main(string[] args)
    {
        var appBuilder = new InvictusBasicWebAppBuilder(args);
        
        appBuilder.AddAssembly(typeof(IdwsExteriorService).Assembly);
        appBuilder.AddAssembly(typeof(InvApiClientExteriorService).Assembly);
        appBuilder.AddAssembly(typeof(PlatformExteriorService).Assembly);
        appBuilder.AddAppConfigurationJsonFile("nomenklatura");
        appBuilder.AddAppConfigurationJsonFile("idws");

        appBuilder.CreateBuilder();
        
        CCAppConfig.SetInvAppConfig(appBuilder.PreBuiltServices.GetRequiredService<InvAppConfig>());
        RRAppConfig.SetInvAppConfig(appBuilder.PreBuiltServices.GetRequiredService<InvAppConfig>());
        
        AuthenticationBuilder auth = appBuilder.AddMvc(false);
        auth.AddScheme<AuthenticationSchemeOptions, BasicAuthenticationHandler>(BasicAuthenticationDefaults.AUTHENTICATION_SCHEME, null);
        
        appBuilder.AddRazorPages(null);
        
        appBuilder.Builder.Services
            .AddDataProtection()
            .PersistKeysToFileSystem(new DirectoryInfo(RRAppConfig.FileSystem.SecretsDir))
            .UseCryptographicAlgorithms(
                new AuthenticatedEncryptorConfiguration
                {
                    EncryptionAlgorithm = EncryptionAlgorithm.AES_256_CBC,
                    ValidationAlgorithm = ValidationAlgorithm.HMACSHA512
                }
            );
        
        appBuilder.Builder.Services.AddJupiter(provider => RRAppConfig.Jupiter, (provider, s) => {});
        
        appBuilder.Builder.Services.AddSingleton<RazorTemplateRender>();
        
        // ...
        // Create App.
        // ...
        
        InvBaseApp app = appBuilder.Build();
        
        app.UseMain();
        app.UseMvc();
        app.UseAuth();
        
        app.MsWebApp.MapGroup("/identity").MapIdentityApi<InvIdentityUser>();
        app.MsWebApp.MapRazorPages();
        
        app.MsWebApp.UseStaticFiles(new StaticFileOptions
        {
            FileProvider = new PhysicalFileProvider(Path.Combine(app.MsWebApp.Environment.ContentRootPath, "StaticFiles")),
            RequestPath = "/static"
        });
        
        app.MsWebApp.MapControllers();
        app.MsWebApp.MapHub<IrisOfficeServiceHub>("/office");
        
        app.MsWebApp.UseJupiter();

        app.Run();
    }
}