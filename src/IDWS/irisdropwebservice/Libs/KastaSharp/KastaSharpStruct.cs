using System.Text.Json.Serialization;

using Invictus.Nomenklatura.Misc;

namespace irisdropwebservice.Libs.KastaSharp
{
    #region Kategories

    public readonly record struct KastaCategoryId(long AffiliationId, long KindId)
    {
        public string AffiliationAndKindId => $"{AffiliationId}_{KindId}";

        public override string ToString()
        {
            return AffiliationAndKindId;
        }
    }

    [Serializable]
    public class KastaCategory
    {
        [JsonPropertyName("affiliation_id")]
        public long AffiliationId { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("name_alias")]
        public string NameAlias { get; set; }

        [JsonPropertyName("kinds")]
        public List<KastaCategoryKind> Kinds { get; set; }
    }

    [Serializable]
    public class KastaCategoryKind
    {
        [JsonPropertyName("kind_id")]
        public long KindId { get; set; }

        [JsonPropertyName("affiliation_id")]
        public long AffiliationId { get; set; }

        [JsonPropertyName("name_alias")]
        public string NameAlias { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }
    }

    [Serializable]
    public class KastaCategorySchemaItem
    {
        [JsonPropertyName("help")]
        public string Help { get; set; }

        [JsonPropertyName("key_name")]
        public string KeyName { get; set; }

        [JsonPropertyName("human_name")]
        public string HumanName { get; set; }

        [JsonPropertyName("example")]
        public KastaCategoryExample Example { get; set; }

        [JsonPropertyName("value_ids")]
        public List<KastaCategorySize> ValueIds { get; set; }

        [JsonPropertyName("requirements")]
        public KastaCategoryRequirements Requirements { get; set; }

        [JsonPropertyName("allow_update")]
        public bool AllowUpdate { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }

        [JsonPropertyName("sizecharts")]
        public List<KastaCategorySizeChart> SizeCharts { get; set; }

        public bool IsMandatory => !AllowUpdate || Requirements.IsRequired;
        public bool IsDesirable => Help.Contains("Бажана", StringComparison.InvariantCultureIgnoreCase);
    }

    [Serializable]
    public class KastaCategoryExample
    {
        [JsonPropertyName("ids")]
        public List<long> Ids { get; set; }
    }

    [Serializable]
    public class KastaCategoryRequirements
    {
        [JsonPropertyName("required?")]
        public bool IsRequired { get; set; }

        [JsonPropertyName("multi?")]
        public bool IsMulti { get; set; }

        [JsonPropertyName("translate?")]
        public bool IsTranslate { get; set; }

        [JsonPropertyName("string?")]
        public bool IsString { get; set; }

        [JsonPropertyName("positive-number?")]
        public bool IsPositiveNumber { get; set; }

        [JsonPropertyName("prompt")]
        public List<string> Prompt { get; set; }

        [JsonPropertyName("input-length")]
        public KastaCategoryInputLength InputLength { get; set; }

        [JsonPropertyName("number?")]
        public bool IsNumber { get; set; }

        [JsonPropertyName("whole-int?")]
        public bool IsWholeInt { get; set; }
    }

    [Serializable]
    public class KastaCategoryInputLength
    {
        [JsonPropertyName("min")]
        public long Min { get; set; }

        [JsonPropertyName("max")]
        public long Max { get; set; }
    }

    [Serializable]
    public class KastaCategorySizeChart
    {
        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("sizes")]
        public List<KastaCategorySize> Sizes { get; set; }
    }

    [Serializable]
    public class KastaCategorySize
    {
        [JsonPropertyName("id")]
        public long Id { get; set; }

        [JsonPropertyName("value")]
        public string Value { get; set; }
    }

    #endregion

    #region Products

    [Serializable]
    public enum KastaProductStatus
    {
        None,
        ZeroStock, // - Сток 0
        OnSale, // - У продажу
        Draft, // - Чернетка (товар завантажений, але не відправлений на перевірку менеджеру)
        ContractChangeRequest, //, ContractChangePending - товари переміщуються з одного договору на інший
        ContractChangePending,
        InProduction, // - товари, які на платній основі обробляє Kasta Production - після обробки отримують статус у продажу
        ContentPending, // - товари, відправлені на перевірку менеджеру
        PricePending, // - товари, які очікують підтвердження оновлення цін менеджером
        BuyerDeclinedPrice, // - менеджер відхилив запропоновані ціни
        Rejected, // - відхилені товари; необхідно переглянути причину відхилення, виправити та завантажити знову
    }

    [Serializable]
    public class KastaProductBase
    {
        [JsonPropertyName("color")]
        public string Color { get; set; }

        [JsonPropertyName("barcode")]
        public List<string> Barcode { get; set; }

        [JsonPropertyName("images")]
        public List<string> Images { get; set; }

        [JsonPropertyName("unique_sku_id")]
        public string UniqueSkuId { get; set; }

        [JsonPropertyName("name_uk")]
        public string NameUk { get; set; }

        [JsonPropertyName("name_ru")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string NameRu { get; set; }

        [JsonPropertyName("size")]
        public string Size { get; set; }

        [JsonPropertyName("brand")]
        public string Brand { get; set; }

        [JsonPropertyName("uktzed")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string Uktzed { get; set; }

        [JsonPropertyName("code")]
        public string Code { get; set; }

        [JsonPropertyName("old_price")]
        public double? OldPrice { get; set; }

        [JsonPropertyName("supplier_price")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public double? SupplierPrice { get; set; }

        [JsonPropertyName("model")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string Model { get; set; }

        [JsonPropertyName("new_price")]
        public double NewPrice { get; set; }
    }

    [Serializable]
    public class KastaProductSubmit : KastaProductBase
    {
        [JsonPropertyName("acceptance_date")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonGreatestEverDateTimeConverter("yyyy-MM-dd")]
        public DateTime? AcceptanceDate { get; set; }

        [JsonPropertyName("description_uk")]
        public string DescriptionUk { get; set; }

        [JsonPropertyName("description_ru")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string DescriptionRu { get; set; }

        [JsonPropertyName("composition_ru")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string CompositionRu { get; set; }

        [JsonPropertyName("composition_uk")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string CompositionUk { get; set; }

        [JsonPropertyName("nomenclature")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string Nomenclature { get; set; }

        [JsonPropertyName("docflow_code")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string DocflowCode { get; set; }

        [JsonPropertyName("characteristics")]
        public List<KastaProductCharacteristic> Characteristics { get; set; }

        [JsonPropertyName("stock")]
        public int? Stock { get; set; }

        [JsonPropertyName("promo_supplier_price")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public double? PromoSupplierPrice { get; set; }
    }

    [Serializable]
    public class KastaProduct : KastaProductBase
    {
        [JsonPropertyName("description")]
        public string Description { get; set; }

        [JsonPropertyName("total_stock")]
        public int TotalStock { get; set; }

        [JsonPropertyName("analytics_url")]
        public string AnalyticsUrl { get; set; }

        [JsonPropertyName("analytics_price")]
        public double? AnalyticsPrice { get; set; }

        [JsonPropertyName("extended_characteristics")]
        public List<KastaExtendedCharacteristic> ExtendedCharacteristics { get; set; }

        [JsonPropertyName("last_update_stock")]
        public int LastUpdateStock { get; set; }

        [JsonPropertyName("contract_id")]
        public string ContractId { get; set; }

        [JsonPropertyName("affiliation")]
        public string Affiliation { get; set; }

        [JsonPropertyName("hidden_time")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public DateTime? HiddenTime { get; set; }

        [JsonPropertyName("supplier_name")]
        public string SupplierName { get; set; }

        [JsonPropertyName("brand_origin")]
        public string BrandOrigin { get; set; }

        [JsonPropertyName("site_url")]
        public string SiteUrl { get; set; }

        [JsonPropertyName("season")]
        public string Season { get; set; }

        [JsonPropertyName("hub_sku")]
        public string HubSku { get; set; }

        public string Art => HubSku.Split('|')[1];

        [JsonPropertyName("status")]
        [JsonConverter(typeof(StringEnumNullConverter<KastaProductStatus>))]
        public KastaProductStatus? Status { get; set; }

        [JsonPropertyName("kind")]
        public string Kind { get; set; }

        [JsonPropertyName("supplier_stock")]
        public int SupplierStock { get; set; }

        [JsonPropertyName("depot_stock")]
        public int DepotStock { get; set; }

        [JsonPropertyName("origin")]
        public string Origin { get; set; }

        [JsonPropertyName("composition")]
        public string Composition { get; set; }

        [JsonPropertyName("kasta_size_id")]
        public long KastaSizeId { get; set; }

        [JsonPropertyName("kasta_size_id_max")]
        public long? KastaSizeIdMax { get; set; }
    }

    [Serializable]
    public class KastaExtendedCharacteristic
    {
        [JsonPropertyName("type_id")]
        public long TypeId { get; set; }

        [JsonPropertyName("value_id")]
        public long? ValueId { get; set; }

        [JsonPropertyName("uk")]
        public string Uk { get; set; }

        [JsonPropertyName("ru")]
        public string Ru { get; set; }
    }

    [Serializable]
    public class KastaProductCharacteristic
    {
        [JsonPropertyName("data")]
        public KastaProductCharacteristicData Data { get; set; }

        [JsonPropertyName("key_name")]
        public string KeyName { get; set; }
    }

    [Serializable]
    public class KastaProductCharacteristicData
    {
        [JsonPropertyName("ids")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public List<long> Ids { get; set; }

        [JsonPropertyName("sizes")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public KastaProductCharacteristicDataSize Sizes { get; set; }
    }

    [Serializable]
    public class KastaProductCharacteristicDataSize
    {
        [JsonPropertyName("kasta_size")]
        public long KastaSize { get; set; }
    }

#endregion
}
