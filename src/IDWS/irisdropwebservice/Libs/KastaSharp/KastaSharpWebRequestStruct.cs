using System.Text.Json.Serialization;

namespace irisdropwebservice.Libs.KastaSharp
{
    [Serializable]
    public class KastaSubmitProductRequest
    {
        [JsonPropertyName("kind_id")]
        public long KindId { get; set; }

        [JsonPropertyName("affiliation_id")]
        public long AffiliationId { get; set; }

        [JsonPropertyName("update")]
        public bool Update { get; set; }

        [JsonPropertyName("data")]
        public List<KastaProductSubmit> Data { get; set; }
    }

    [Serializable]
    public class KastaUpdateStockRequest
    {
        [JsonPropertyName("items")]
        public List<KastaUpdateStockRequestItem> Items { get; set; }
    }

    [Serializable]
    public class KastaUpdateStockRequestItem
    {
        [JsonPropertyName("unique_sku_id")]
        public string UniqueSkuId { get; set; }

        [JsonPropertyName("stock")]
        public int Stock { get; set; }
    }

    [Serializable]
    public class KastaUpdatePricesRequest
    {
        [JsonPropertyName("items")]
        public List<KastaUpdatePricesRequestItem> Items { get; set; }
    }

    [Serializable]
    public class KastaUpdatePricesRequestItem
    {
        [JsonPropertyName("unique_sku_id")]
        public string UniqueSkuId { get; set; }

        [JsonPropertyName("old_price")]
        public double? OldPrice { get; set; }

        [JsonPropertyName("new_price")]
        public double? NewPrice { get; set; }

        [JsonPropertyName("promo_new_price")]
        public double? PromoNewPrice { get; set; }
    }

    [Serializable]
    public class KastaGetCustomerOrdersRequest
    {
        [JsonPropertyName("status")]
        public KastaOrderStatus[] Status { get; set; }

        [JsonPropertyName("order_id")]
        public string[] OrderIds { get; set; }

        [JsonPropertyName("cursor")]
        public string Cursor { get; set; }

        [JsonPropertyName("from")]
        public string From { get; set; }

        [JsonPropertyName("limit")]
        public int Limit { get; set; } = 100;
    }
}
