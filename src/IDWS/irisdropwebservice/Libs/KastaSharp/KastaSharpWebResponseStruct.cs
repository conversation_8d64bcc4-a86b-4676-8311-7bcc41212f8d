using System.Text.Json.Serialization;

using Invictus.Nomenklatura.Misc;

namespace irisdropwebservice.Libs.KastaSharp
{
    #region Categories

    [Serializable]
    public class KastaCategoriesListResponse
    {
        [JsonPropertyName("items")]
        public List<KastaCategory> Items { get; set; }
    }

    [Serializable]
    public class KastaCategoryDetailsResponse
    {
        [JsonPropertyName("schema")]
        public List<KastaCategorySchemaItem> Schema { get; set; }
    }

    [Serializable]
    public class KastaMyCategorySchema
    {
        [JsonPropertyName("category_itself")]
        public KastaCategory Category { get; set; }

        [JsonPropertyName("category_kinds")]
        public List<KastaMyCategorySchemaKind> KindsWithSchemas { get; set; }
    }

    [Serializable]
    public class KastaMyCategorySchemaKind
    {
        [JsonPropertyName("schema")]
        public KastaCategoryKind CategoryKindItself { get; set; }

        [JsonPropertyName("schema_items")]
        public List<KastaCategorySchemaItem> SchemaItems { get; set; }
    }

    #endregion

    #region Products

    [Serializable]
    public class KastaProductsListResponse
    {
        [JsonPropertyName("items")]
        public List<KastaProduct> Items { get; set; }

        [JsonPropertyName("cursor")]
        public long? Cursor { get; set; }
    }

    [Serializable]
    public class KastaSubmitProductsResponse
    {
        [JsonPropertyName("upload_id")]
        public long? UploadId { get; set; }

        [JsonPropertyName("update_stats")]
        public KastaSubmitProductsUpdateStats UpdateStats { get; set; }
    }

    [Serializable]
    public class KastaSubmitProductsUpdateStats
    {
        [JsonPropertyName("content_update_stats")]
        public KastaSubmitProductsUpdateStatsContent ContentUpdateStats { get; set; }
    }

    [Serializable]
    public class KastaSubmitProductsUpdateStatsContent
    {
        [JsonPropertyName("new-sku")]
        public int NewSku { get; set; }

        [JsonPropertyName("update-sku")]
        public int UpdateSku { get; set; }

        [JsonPropertyName("skip-not-new")]
        public int SkipNotNew { get; set; }

        [JsonPropertyName("update-forbidden")]
        public int UpdateForbidden { get; set; }

        [JsonPropertyName("update-content-extent-mismatch")]
        public int UpdateContentExtentMismatch { get; set; }
    }

    [Serializable]
    public class KastaSubmitPhotoResponse
    {
        [JsonPropertyName("path")]
        public string Path { get; set; }
    }

    [Serializable]
    public class KastaUpdateProductResponse
    {
        [JsonPropertyName("updated")]
        public List<KastaSkuItem> Updated { get; set; }

        [JsonPropertyName("not-found")]
        public List<KastaSkuItem> NotFound { get; set; }

        [JsonPropertyName("upload_id")]
        public long? UploadId { get; set; }

        [JsonPropertyName("task_id")]
        public string TaskId { get; set; }
    }

    [Serializable]
    public class KastaSkuItem
    {
        [JsonPropertyName("unique_sku_id")]
        public string UniqueSkuId { get; set; }
    }

    #endregion

    #region Orders

    [Serializable]
    public class KastaOrdersListResponse
    {
        [JsonPropertyName("items")]
        public List<KastaOrdersListResponseItem> Items { get; set; }

        [JsonPropertyName("cursor")]
        public string Cursor { get; set; }

        [JsonPropertyName("remains")]
        public int Remains { get; set; }
    }

    [Serializable]
    public class KastaOrderClient
    {
        [JsonPropertyName("first_name")]
        public string FirstName { get; set; }

        [JsonPropertyName("last_name")]
        public string LastName { get; set; }

        [JsonPropertyName("middle_name")]
        public string MiddleName { get; set; }

        [JsonPropertyName("phone")]
        public string Phone { get; set; }
    }

    public struct KastaOrderCancelledItem
    {
        [JsonPropertyName("returned_quantity")]
        public int ReturnedQuantity { get; set; }

        [JsonPropertyName("color")]
        public string Color { get; set; }

        [JsonPropertyName("barcode")]
        public List<string> Barcode { get; set; }

        // string or int
        [JsonPropertyName("campaign_id")]
        public object CampaignId { get; set; }

        [JsonPropertyName("partner")]
        public string Partner { get; set; }

        [JsonPropertyName("original_quantity")]
        public int OriginalQuantity { get; set; }

        [JsonPropertyName("kasta_color")]
        public string KastaColor { get; set; }

        [JsonPropertyName("paid_price")]
        public double PaidPrice { get; set; }

        [JsonPropertyName("new_price")]
        public double NewPrice { get; set; }

        [JsonPropertyName("unique_sku_id")]
        public string UniqueSkuId { get; set; }

        [JsonPropertyName("supplier_code")]
        public string SupplierCode { get; set; }

        [JsonPropertyName("kasta_size")]
        public string KastaSize { get; set; }

        [JsonPropertyName("site_url")]
        public string SiteUrl { get; set; }

        [JsonPropertyName("bonuses")]
        public List<KastaBonus> Bonuses { get; set; }

        [JsonPropertyName("size")]
        public string Size { get; set; }

        [JsonPropertyName("line_id")]
        public int LineId { get; set; }

        [JsonPropertyName("kind")]
        public string Kind { get; set; }

        [JsonPropertyName("quantity")]
        public int Quantity { get; set; }

        [JsonPropertyName("contract_uuid")]
        public string ContractUuid { get; set; }

        [JsonPropertyName("supplier_price")]
        public double SupplierPrice { get; set; }
    }

    public struct KastaDeliveryServiceInfo
    {
        [JsonPropertyName("city_ref_id")]
        public string CityRefId { get; set; }

        [JsonPropertyName("region_ref_id")]
        public string RegionRefId { get; set; }
    }

    public struct KastaWarehouse
    {
        [JsonPropertyName("delivery_service_info")]
        public KastaDeliveryServiceInfo DeliveryServiceInfo { get; set; }

        [JsonPropertyName("number")]
        public int Number { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("ref_id")]
        public string RefId { get; set; }
    }

    public class KastaBnplDocs
    {
        [JsonPropertyName("contract")]
        public string Contract { get; set; }
        
        [JsonPropertyName("register")]
        public string Register { get; set; }
    }

    [Serializable]
    public class KastaOrdersListResponseItem
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("dont_call_me")]
        public bool DontCallMe { get; set; }

        [JsonPropertyName("client")]
        public KastaOrderClient Client { get; set; }

        [JsonPropertyName("shipping_address")]
        public KastaShippingAddress ShippingAddress { get; set; }
        
        [JsonPropertyName("bnpl_docs")]
        public KastaBnplDocs BnplDocs { get; set; }

        [JsonPropertyName("delivery_properties")]
        public KastaDeliveryProperties DeliveryProperties { get; set; }

        [JsonPropertyName("cancelled_items")]
        public KastaOrderCancelledItem[] CancelledItems { get; set; }

        [JsonPropertyName("card_payment_state")]
        public string CardPaymentState { get; set; }

        [JsonPropertyName("kasta_pays_for_shipping")]
        public bool KastaPaysForShipping { get; set; }

        [JsonPropertyName("requested_payment_method")]
        public string RequestedPaymentMethod { get; set; }

        [JsonPropertyName("courier_type")]
        public string CourierType { get; set; }

        [JsonPropertyName("bonus")]
        public double Bonus { get; set; }

        [JsonPropertyName("ordered_items")]
        public List<KastaOrderedItem> OrderedItems { get; set; }

        [JsonPropertyName("returned_items")]
        public List<KastaReturnedItem> ReturnedItems { get; set; }

        [JsonPropertyName("client_returns")]
        public List<KastaClientReturn> ClientReturns { get; set; }

        [JsonPropertyName("comments")]
        public List<string> Comments { get; set; }

        [JsonPropertyName("statuses")]
        public List<KastaStatus> Statuses { get; set; }
    }

    [Serializable]
    public class KastaShippingAddress
    {
        [JsonPropertyName("first_name")]
        public string FirstName { get; set; }

        [JsonPropertyName("middle_name")]
        public string MiddleName { get; set; }

        [JsonPropertyName("last_name")]
        public string LastName { get; set; }

        [JsonPropertyName("building")]
        public string Building { get; set; }

        [JsonPropertyName("phone")]
        public string Phone { get; set; }

        [JsonPropertyName("email")]
        public string Email { get; set; }

        [JsonPropertyName("city")]
        public KastaCity City { get; set; }

        [JsonPropertyName("has_elevator")]
        public bool HasElevator { get; set; }

        [JsonPropertyName("street")]
        public KastaStreet Street { get; set; }

        [JsonPropertyName("region")]
        public KastaRegion Region { get; set; }

        [JsonPropertyName("flat")]
        public string Flat { get; set; }

        [JsonPropertyName("floor")]
        public int Floor { get; set; }

        [JsonPropertyName("warehouse")]
        public KastaWarehouse Warehouse { get; set; }
    }

    [Serializable]
    public class KastaCity
    {
        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("site_id")]
        public int SiteId { get; set; }

        [JsonPropertyName("logistics_id")]
        public int LogisticsId { get; set; }
    }

    [Serializable]
    public class KastaStreet
    {
        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }

        [JsonPropertyName("logistics_id")]
        public int LogisticsId { get; set; }
    }

    [Serializable]
    public class KastaRegion
    {
        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("site_id")]
        public int SiteId { get; set; }

        [JsonPropertyName("logistics_id")]
        public int LogisticsId { get; set; }
    }

    [Serializable]
    public class KastaDeliveryProperties
    {
        [JsonPropertyName("declaration_number")]
        public string DeclarationNumber { get; set; }

        [JsonPropertyName("paid_price")]
        public string PaidPrice { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }

        [JsonPropertyName("rate")]
        public string Rate { get; set; }

        [JsonPropertyName("branch_type")]
        public string BranchType { get; set; }

        [JsonPropertyName("black_used")]
        public bool BlackUsed { get; set; }

        [JsonPropertyName("cost")]
        public double Cost { get; set; }

        [JsonPropertyName("is_partial_release")]
        public bool? IsPartialRelease { get; set; }
    }

    [Serializable]
    public class KastaOrderedItem
    {
        [JsonPropertyName("returned_quantity")]
        public int ReturnedQuantity { get; set; }

        [JsonPropertyName("color")]
        public string Color { get; set; }

        [JsonPropertyName("partner")]
        public string Partner { get; set; }

        [JsonPropertyName("kasta_color")]
        public string KastaColor { get; set; }

        [JsonPropertyName("barcode")]
        public List<string> Barcode { get; set; }

        [JsonPropertyName("campaign_id")]
        public int? CampaignId { get; set; }

        [JsonPropertyName("original_quantity")]
        public int OriginalQuantity { get; set; }

        [JsonPropertyName("paid_price")]
        public double PaidPrice { get; set; }

        [JsonPropertyName("new_price")]
        public double NewPrice { get; set; }

        [JsonPropertyName("unique_sku_id")]
        public string UniqueSkuId { get; set; }

        [JsonPropertyName("supplier_code")]
        public string SupplierCode { get; set; }

        [JsonPropertyName("site_url")]
        public string SiteUrl { get; set; }

        [JsonPropertyName("size")]
        public string Size { get; set; }

        [JsonPropertyName("line_id")]
        public int LineId { get; set; }

        [JsonPropertyName("kasta_size")]
        public string KastaSize { get; set; }

        [JsonPropertyName("kind")]
        public string Kind { get; set; }

        [JsonPropertyName("quantity")]
        public int Quantity { get; set; }

        [JsonPropertyName("contract_uuid")]
        public string ContractUuid { get; set; }

        [JsonPropertyName("supplier_price")]
        public double SupplierPrice { get; set; }

        [JsonPropertyName("bonuses")]
        public List<KastaBonus> Bonuses { get; set; }
    }

    [Serializable]
    public class KastaBonus
    {
        [JsonPropertyName("type")]
        public string Type { get; set; }

        [JsonPropertyName("amount")]
        public double Amount { get; set; }
    }

    [Serializable]
    public class KastaReturnedItem : KastaOrderedItem { }

    [Serializable]
    public class KastaClientReturn
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("declaration_number")]
        public string DeclarationNumber { get; set; }
        
        [JsonPropertyName("return_intent_code")]
        public string ReturnIntentCode { get; set; }

        [JsonPropertyName("ref")]
        public string Ref { get; set; }

        [JsonPropertyName("created_at")]
        public DateTime CreatedAt { get; set; }

        [JsonPropertyName("card_pan")]
        public string CardPan { get; set; }

        [JsonPropertyName("amount")]
        public double? Amount { get; set; }

        [JsonPropertyName("status")]
        public string Status { get; set; }

        [JsonPropertyName("items")]
        public List<KastaReturnItemDetail> Items { get; set; }
    }

    [Serializable]
    public class KastaReturnItemDetail
    {
        [JsonPropertyName("line_id")]
        public int LineId { get; set; }

        [JsonPropertyName("quantity")]
        public int Quantity { get; set; }

        [JsonPropertyName("reason_text")]
        public string ReasonText { get; set; }

        [JsonPropertyName("reason_code")]
        public string ReasonCode { get; set; }
    }

    [Serializable]
    public class KastaStatus
    {
        [JsonPropertyName("created_at")]
        public DateTime CreatedAt { get; set; }

        [JsonPropertyName("type")]
        public KastaOrderStatus Type { get; set; }

        [JsonPropertyName("return_intent_code")]
        public string ReturnIntentCode { get; set; }
        
        [JsonPropertyName("ttn")]
        public string Ttn { get; set; }

        [JsonPropertyName("uuid")]
        public string Uuid { get; set; }
        
        [JsonPropertyName("request_uuid")]
        public string RequestUuid { get; set; }

        [JsonPropertyName("items")]
        public List<KastaStatusItem> Items { get; set; }

        [JsonPropertyName("trans_id")]
        public string TransId { get; set; }

        [JsonPropertyName("amount")]
        public double? Amount { get; set; }

        [JsonPropertyName("partner")]
        public string Partner { get; set; }

        [JsonPropertyName("rrn")]
        public string Rrn { get; set; }

        [JsonPropertyName("payment_type")]
        public string PaymentType { get; set; }

        [JsonPropertyName("card_pan")]
        public string CardPan { get; set; }

        [JsonPropertyName("processing_date")]
        public long? ProcessingDate { get; set; }

        [JsonPropertyName("auth_code")]
        public string AuthCode { get; set; }

        [JsonPropertyName("card_type")]
        public string CardType { get; set; }
        
        [JsonPropertyName("money_return_type")]
        public string MoneyReturnType { get; set; }
        
        [JsonPropertyName("subtype")]
        public string Subtype { get; set; }
        
        [JsonPropertyName("number")]
        public string Number { get; set; }
        
        [JsonPropertyName("name")]
        public string Name { get; set; }
    }

    [Serializable]
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum KastaOrderStatus
    {
        /// <summary>Створено - статус встановлюється Kasta при оформленні замовлення на сайті клієнтом.</summary>
        Created,

        /// <summary>Підтверджено - статус встановлюється Продавцем після успішного завантаження замовлення в свою систему.</summary>
        ConfirmedBySupplier,

        /// <summary>Упаковано - статус встановлюється Продавцем по результатам упаковки замовлення, містить дані які саме товари було упаковано.</summary>
        Packed,

        /// <summary>Зареєстровано у кур'єрській службі - статус встановлюється Продавцем при реєстрації ТТН у КС (кур'єрській службі).</summary>
        AnnouncedForDelivery,

        /// <summary>Відправлено - статус встановлюється Продавцем або Kasta за результатами передачі замовлення КС на доставку.</summary>
        SentToDelivery,

        /// <summary>Прибув у відділення - статус встановлюється при прибутті у відділення.</summary>
        AtLocation,

        /// <summary>Прибув у відділення Kasta Post.</summary>
        ReceivedAtSelfDelivery,

        /// <summary>Доставлено - статус при доставці.</summary>
        Delivered,

        /// <summary>Оплачено - статус при отриманні коштів.</summary>
        PaymentReceived,

        /// <summary>Відмінено - статус при відсутності товару або відмові клієнта.</summary>
        Cancelled,

        /// <summary>Відкориговано - статус при частковій відсутності товару або частковій відмові клієнта.</summary>
        Corrected,

        /// <summary>Прийнято повернення - статус при отриманні продавцем повернення від клієнта.</summary>
        ReturnArrived,

        /// <summary>Повернуто кошти - статус при успішному поверненні коштів клієнту.</summary>
        Refunded,

        /// <summary>Запит на коригування - статус ініціюється Kasta у разі коригування клієнтом замовлення до підтвердження продавцем.</summary>
        CorrectionRequested,

        /// <summary>Запит на відміну - статус ініціюється Kasta у разі відміни клієнтом замовлення до підтвердження продавцем.</summary>
        CancellationRequested,

        /// <summary>Відмова у відміні - статус у разі неможливості відмінити замовлення.</summary>
        CancellationDenied,

        /// <summary>Відмова у коригуванні - статус у разі неможливості відкоригувати замовлення.</summary>
        CorrectionDenied,

        /// <summary>Вдале повернення грошей - статус при успішному поверненні грошей на карту.</summary>
        MoneyRefundSuccess,
        
        /// <summary>
        /// ??? no API docs
        /// </summary>
        MoneyRefund,

        /// <summary>Невдале повернення грошей - статус при невдалому поверненні грошей на карту.</summary>
        MoneyRefundDeclinedStatus,

        /// <summary>Списано - статус вдалого списання по замовленню з HOLD-ом.</summary>
        MoneySettleSuccess,

        /// <summary>Відхилено - статус невдалого списання по замовленню з HOLD-ом.</summary>
        MoneySettleDeclined,

        /// <summary>Відхилено - статус невдалого HOLD по замовленню.</summary>
        MoneyHoldDeclined,

        /// <summary>Повернення створено - клієнт створив повернення через сайт.</summary>
        ReturnCreated,

        /// <summary>Повернення скасовано - клієнт скасував повернення.</summary>
        ReturnCancelled,

        /// <summary>Повернення відправлено - клієнт віддав повернення курʼєрській службі.</summary>
        ReturnSent,

        /// <summary>Повернення у відділенні - повернення прибуло до відділення.</summary>
        ReturnAtLocation,

        /// <summary>Повернення доставлено - постачальник отримав повернення.</summary>
        ReturnDelivered,
        
        DeliveryAddressChanged
    }


    [Serializable]
    public class KastaStatusItem : KastaOrderedItem { }

    #endregion

    [Serializable]
    public class KastaResponse400
    {
        [JsonPropertyName("code")]
        public string Code { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; }

        [JsonPropertyName("args")]
        public KastaResponse400Args Args { get; set; }

        [JsonPropertyName("error")]
        [JsonConverter(typeof(RawJsonConverter))]
        public string Error { get; set; }

        public override string ToString()
        {
            string r = $"{Code}\r\n{Message}\r\n";

            if (Args != null)
                r += $"{Args.Value} {Args.Schema}\r\n";
            else
                r += "(no_args)";

            r += "\r\n";

            if (Error != null)
                r += $"{Error}\r\n";
            else
                r += "(no_error)";

            return r;
        }
    }

    [Serializable]
    public class KastaResponse400Args
    {
        [JsonPropertyName("value")]
        [JsonConverter(typeof(RawJsonConverter))]
        public string Value { get; set; }

        [JsonPropertyName("schema")]
        [JsonConverter(typeof(RawJsonConverter))]
        public string Schema { get; set; }
    }
}
