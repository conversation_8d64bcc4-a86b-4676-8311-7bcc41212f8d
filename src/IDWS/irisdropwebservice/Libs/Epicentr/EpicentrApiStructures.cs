using System.Text.Json.Serialization;

namespace irisdropwebservice.Libs.Epicentr;

public class EpicentrFailResponse
{
    public int? Code { get; set; }
    
    public string Message { get; set; }

    public override string ToString()
    {
        return $"{Code}: {Message}";
    }
}

public class EpicentrApiCategoriesRoot
{
    [JsonPropertyName("page")]
    public int Page { get; set; }

    [JsonPropertyName("pages")]
    public int Pages { get; set; }

    [JsonPropertyName("items")]
    public List<EpicentrApiCategoriesItem> Items { get; set; }
}

public class EpicentrApiCategoriesItem
{
    [JsonPropertyName("code")]
    public string Code { get; set; }

    [JsonPropertyName("parentCode")]
    public string ParentCode { get; set; }

    [JsonPropertyName("hasChild")]
    public bool HasChild { get; set; }

    [JsonPropertyName("attributeSets")]
    public List<EpicentrApiCategoriesAttributeSet> AttributeSets { get; set; }

    [JsonPropertyName("translations")]
    public List<EpicentrApiCategoriesTranslation> Translations { get; set; }
}

public class EpicentrApiCategoriesAttributeSet
{
    [JsonPropertyName("code")]
    public string Code { get; set; }
}

public class EpicentrApiCategoriesTranslation
{
    [JsonPropertyName("languageCode")]
    public string LanguageCode { get; set; }

    [JsonPropertyName("title")]
    public string Title { get; set; }
}

public class EpicentrTotalOrdersResponse
{
    [JsonPropertyName("current")]
    public string Current { get; set; }
    
    [JsonPropertyName("next")]
    public string Next { get; set; }
    
    [JsonPropertyName("prev")]
    public string Prev { get; set; }
    
    [JsonPropertyName("last")]
    public string Last { get; set; }
    
    [JsonPropertyName("total")]
    public int Total { get; set; }
}