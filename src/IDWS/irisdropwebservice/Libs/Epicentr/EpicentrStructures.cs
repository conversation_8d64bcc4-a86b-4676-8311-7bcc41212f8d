using System.Text.Json.Serialization;

namespace irisdropwebservice.Libs.Epicentr;


[Serializable]
public class EpicentrCategoriesRoot
{
    [JsonPropertyName("category_entries")]
    public List<EpicentrCategoryEntry> CategoryEntries { get; set; } = new();
}

[Serializable]
public class EpicentrCategoryEntry
{
    [JsonPropertyName("name")]
    public string Name { get; set; }

    [JsonPropertyName("features")]
    public List<EpicentrFeatureEntry> Features { get; set; } = new();
}

[Serializable]
public class EpicentrFeatureEntry
{
    [JsonPropertyName("feature_id")]
    public int FeatureId { get; set; }
    
    [JsonPropertyName("feature_name")]
    public string FeatureName { get; set; }
        
    /// <summary>
    /// select, multiselect, text, string
    /// </summary>
    [JsonPropertyName("feature_type")]
    public string FeatureType { get; set; }
        
    [JsonPropertyName("attribute_code")]
    public string AttributeCode { get; set; }

    [JsonPropertyName("feature_options")]
    public List<EpicentrFeatureOption> FeatureOptions { get; set; }
}

[Serializable]
public class EpicentrFeatureOption
{
    [JsonPropertyName("option_id")]
    public int? OptionId { get; set; }
    
    [JsonPropertyName("option_name")]
    public string OptionName { get; set; }
    
    [JsonPropertyName("option_code")]
    public string OptionCode { get; set; }
    
    [JsonPropertyName("suffix")]
    public string Suffix { get; set; }
    
    [JsonPropertyName("prefix")]
    public string Prefix { get; set; }
}