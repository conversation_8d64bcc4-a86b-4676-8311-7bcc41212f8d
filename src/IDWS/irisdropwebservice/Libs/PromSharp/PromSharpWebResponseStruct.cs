using System.Text.Json.Serialization;

namespace irisdropwebservice.Libs.PromSharp
{
    [Serializable]
    public class PromProductsListResponse
    {
        [JsonPropertyName("group_id")]
        public int GroupId { get; set; }

        [JsonPropertyName("products")]
        public List<PromProduct> Products { get; set; }
    }

    [Serializable]
    public class PromSingleProductResponse
    {
        [JsonPropertyName("product")]
        public PromProduct Product { get; set; }
    }

    [Serializable]
    public class PromEditProductResponse
    {
        [JsonPropertyName("processed_ids")]
        public List<long> ProcessedIds { get; set; }

        [JsonPropertyName("errors")]
        public Dictionary<string, Dictionary<string, string>> Errors { get; set; }
    }

    [Serializable]
    public class PromImportFileResponse
    {
        [JsonPropertyName("status")]
        public string Status { get; set; }

        [JsonPropertyName("id")]
        public string Id { get; set; }
    }

    [Serializable]
    public class PromImportStatusResponse
    {
        [JsonPropertyName("status")]
        public string Status { get; set; } // Enum: [ SUCCESS, PARTIAL, FATAL ]

        [JsonPropertyName("not_changed")]
        public int NotChanged { get; set; }

        [JsonPropertyName("updated")]
        public int Updated { get; set; }

        [JsonPropertyName("not_in_file")]
        public int NotInFile { get; set; }

        [JsonPropertyName("imported")]
        public int Imported { get; set; }

        [JsonPropertyName("created")]
        public int Created { get; set; }

        [JsonPropertyName("actualized")]
        public int Actualized { get; set; }

        [JsonPropertyName("created_active")]
        public int CreatedActive { get; set; }

        [JsonPropertyName("created_hidden")]
        public int CreatedHidden { get; set; }

        [JsonPropertyName("total")]
        public int Total { get; set; }

        [JsonPropertyName("with_errors_count")]
        public int WithErrorsCount { get; set; }

        [JsonPropertyName("errors")]
        public List<PromImportStatusErrors> Errors { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; }
    }
    
    [Serializable]
    public class PromImportErrorResponse
    {
        [JsonPropertyName("error")]
        public PromImportError Error { get; set; }
    }
    
    [Serializable]
    public class PromImportError
    {
        [JsonPropertyName("status")]
        public int Status { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; }
    }

    [Serializable]
    public class PromImportStatusErrors
    {
        [JsonPropertyName("download")]
        public PromImportStatusErrorDetail Download { get; set; }

        [JsonPropertyName("store_file")]
        public PromImportStatusErrorDetail StoreFile { get; set; }

        [JsonPropertyName("validation")]
        public PromImportStatusErrorDetail Validation { get; set; }

        [JsonPropertyName("import")]
        public PromImportStatusErrorDetail Import { get; set; }

        [JsonPropertyName("download_images")]
        public PromImportStatusErrorDetail DownloadImages { get; set; }
        
        [JsonPropertyName("category")]
        public string Category { get; set; }
        
        [JsonPropertyName("errors")]
        public List<PromImportStatusError2> Errors { get; set; }
    }

    [Serializable]
    public class PromImportStatusError2
    {
        [JsonPropertyName("code")]
        public int Code { get; set; }
        
        [JsonPropertyName("message")]
        public string Message { get; set; }
        
        [JsonPropertyName("positions")]
        public List<string> Positions { get; set; }
        
        [JsonPropertyName("extra")]
        public PromImportStatusError2Extra Extra { get; set; }
        
        [JsonPropertyName("field_code")]
        public string FieldCode { get; set; }
    }

    [Serializable]
    public class PromImportStatusError2Extra
    {
        [JsonPropertyName("status_code")]
        public int StatusCode { get; set; }
        
        [JsonPropertyName("file_checksum")]
        public string FileChecksum { get; set; }
    }

    [Serializable]
    public class PromImportStatusErrorDetail
    {
        [JsonPropertyName("description")]
        public string Description { get; set; }
    }
}
