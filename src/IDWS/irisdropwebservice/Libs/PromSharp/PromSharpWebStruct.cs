using System.Text.Json.Serialization;

namespace irisdropwebservice.Libs.PromSharp
{
    [Serializable]
    public class PromNameMultilang
    {
        [JsonPropertyName("ru")]
        public string Ru { get; set; }

        [JsonPropertyName("uk")]
        public string Uk { get; set; }
    }

    [Serializable]
    public class PromRegion
    {
        [JsonPropertyName("id")]
        public long Id { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("ru")]
        public string ru { get; set; }

        [JsonPropertyName("name_multilang")]
        public PromNameMultilang NameMultilang { get; set; }
    }

    [Serializable]
    public class PromDiscount
    {
        [JsonPropertyName("value")]
        public double Value { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }

        [JsonPropertyName("date_start")]
        public string DateStart { get; set; }

        [JsonPropertyName("date_end")]
        public string DateEnd { get; set; }
    }

    [Serializable]
    public class PromGroup
    {
        [JsonPropertyName("id")]
        public long Id { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("name_multilang")]
        public PromNameMultilang NameMultilang { get; set; }
    }

    [Serializable]
    public class PromCategory
    {
        [JsonPropertyName("id")]
        public long Id { get; set; }

        [JsonPropertyName("caption")]
        public string Caption { get; set; }
    }

    [Serializable]
    public class PromPrice
    {
        [JsonPropertyName("price")]
        public double? PriceValue { get; set; }

        [JsonPropertyName("minimum_order_quantity")]
        public double? MinimumOrderQuantity { get; set; }
    }

    [Serializable]
    public class PromImage
    {
        [JsonPropertyName("url")]
        public string Url { get; set; }

        [JsonPropertyName("thumbnail_url")]
        public string ThumbnailUrl { get; set; }

        [JsonPropertyName("id")]
        public long Id { get; set; }
    }

    [Serializable]
    public class PromProductEdit
    {
        public static PromProductEdit FromFullProduct(PromProduct product, bool includeKeywords)
        {
            return new PromProductEdit
            {
                Sku = product.Sku,
                Status = product.Status,
                ExternalId = product.ExternalId,
                Id = product.Id,
                InStock = product.InStock,
                QuantityInStock = product.QuantityInStock,
                Presence = product.Presence,
                SellingType = product.SellingType,
                Keywords = includeKeywords ? product.Keywords : null,
                Price = product.Price
            };
        }

        [JsonPropertyName("id")]
        public long Id { get; set; }

        [JsonPropertyName("external_id")]
        public string ExternalId { get; set; }

        [JsonPropertyName("sku")]
        public string Sku { get; set; }

        [JsonPropertyName("selling_type")]
        public string SellingType { get; set; }

        [JsonPropertyName("presence")]
        public string Presence { get; set; }
        
        [JsonPropertyName("price")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public double? Price { get; set; }

        // GetProducts() returns null here for some reason
        [JsonPropertyName("in_stock")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public bool? InStock { get; set; }

        [JsonPropertyName("status")]
        public string Status { get; set; }

        [JsonPropertyName("quantity_in_stock")]
        public long? QuantityInStock { get; set; }

        [JsonPropertyName("keywords")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string Keywords { get; set; }
    }

    [Serializable]
    public class PromProduct : PromProductEdit
    {
        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("name_multilang")]
        public PromNameMultilang NameMultilang { get; set; }

        [JsonPropertyName("description")]
        public string Description { get; set; }

        [JsonPropertyName("description_multilang")]
        public PromNameMultilang DescriptionMultilang { get; set; }

        [JsonPropertyName("regions")]
        public List<PromRegion> Regions { get; set; }

        [JsonPropertyName("minimum_order_quantity")]
        public double? MinimumOrderQuantity { get; set; }

        [JsonPropertyName("discount")]
        public PromDiscount Discount { get; set; }

        [JsonPropertyName("currency")]
        public string Currency { get; set; }

        [JsonPropertyName("group")]
        public PromGroup Group { get; set; }

        [JsonPropertyName("category")]
        public PromCategory Category { get; set; }

        [JsonPropertyName("prices")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public List<PromPrice> Prices { get; set; }

        [JsonPropertyName("main_image")]
        public string MainImage { get; set; }

        [JsonPropertyName("images")]
        public List<PromImage> Images { get; set; }

        [JsonPropertyName("measure_unit")]
        public string MeasureUnit { get; set; }

        [JsonPropertyName("is_variation")]
        public bool IsVariation { get; set; }

        [JsonPropertyName("variation_base_id")]
        public long? VariationBaseId { get; set; }

        [JsonPropertyName("variation_group_id")]
        public long? VariationGroupId { get; set; }

        [JsonPropertyName("date_modified")]
        public DateTime? DateModified { get; set; }
    }

    [Serializable]
    public class PromProductCsv : PromProduct
    {
        public string Manufacturer { get; set; }

        public string ManufacturerCountry { get; set; }

        public string ProductCurrentLocation { get; set; }

        public double? WholesalePrice { get; set; }

        public int? WholesaleMinQuantity { get; set; }

        public string ShipmentDate { get; set; }

        public string MethodOfPacking { get; set; }
        public string MethodOfPackingUk { get; set; }

        public string Unused111 { get; set; }
        public string Unused112 { get; set; }
        public string Unused2 { get; set; }
        public string Unused3 { get; set; }
        public string Unused4 { get; set; }
        public string Unused5 { get; set; }
        public string Unused6 { get; set; }
        public string Unused7 { get; set; }
        public string Unused8 { get; set; }
        public string Unused9 { get; set; }
        public string Unused10 { get; set; }
        public string Unused11 { get; set; }
        public string Unused12 { get; set; }
        public string Unused13 { get; set; }
        public string Unused14 { get; set; }
        public string Unused15 { get; set; }

        public string KeywordsUk { get; set; } // TODO
        
        
    }

    public class PromImportProductsOptionsAll
    {
        [JsonPropertyName("force_update")]
        public bool ForceUpdate { get; set; }

        [JsonPropertyName("only_available")]
        public bool OnlyAvailable { get; set; }

        [JsonPropertyName("mark_missing_product_as")]
        public string MarkMissingProductAs { get; set; }
    }

    public class PromImportProductsOptions : PromImportProductsOptionsAll
    {
        [JsonPropertyName("updated_fields")]
        public List<string> UpdatedFields { get; set; }
    }
}
