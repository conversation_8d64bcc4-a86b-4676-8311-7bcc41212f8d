using System.Xml.Serialization;

namespace irisdropwebservice.Libs.PromSharp;

[XmlRoot("categories")]
public class PromCategoryDefinitions
{
    [XmlElement("category")]
    public List<PromCategoryDef> CategoryList { get; set; }
}

public class PromCategoryDef : PromCategoryDefRaw
{
    [XmlAttribute("nameRU")]
    public string NameRU { get; set; }

    [XmlAttribute("nameUK")]
    public string NameUK { get; set; }

    [XmlElement("attribute")]
    public List<PromAttributeDef> Attributes { get; set; }
}

public class PromCategoryDefRaw
{
    public string Cat1 { get; set; }
    
    public string Cat2 { get; set; }
    
    public string Cat3 { get; set; }
    
    public string Cat4 { get; set; }
    
    public string CatLink { get; set; }
    
    [XmlAttribute("id")]
    public int Id { get; set; }
}

public class PromAttributeDef
{
    [XmlAttribute("id")]
    public int Id { get; set; }

    [XmlAttribute("nameRU")]
    public string NameRU { get; set; }

    [XmlAttribute("type")]
    public string Type { get; set; }

    [XmlAttribute("nameUK")]
    public string NameUK { get; set; }

    [XmlElement("attribute_value")]
    public List<PromAttributeValueDef> AttributeValues { get; set; }
}

public class PromBoolArrayCompositeAttributeDef : PromAttributeDef
{
    public PromAttributeDef[] BoolAttributes { get; set; }
}

public class PromAttributeValueDef
{
    [XmlAttribute("id")]
    public int Id { get; set; }

    [XmlAttribute("nameRU")]
    public string NameRU { get; set; }

    [XmlAttribute("nameUK")]
    public string NameUK { get; set; }
}