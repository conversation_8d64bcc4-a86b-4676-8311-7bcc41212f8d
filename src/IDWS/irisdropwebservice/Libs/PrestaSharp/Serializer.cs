using System.Collections;
using System.Reflection;
using System.Xml.Linq;

using RestSharp.Extensions;
using RestSharp.Serializers;

namespace irisdropwebservice.Libs.PrestaSharp;

class PrestaSharpSerializer : XmlSerializer
{
  public PrestaSharpSerializer()
  {
  }

  public PrestaSharpSerializer(string @namespace)
    : base(@namespace)
  {
  }

  public string PrestaSharpSerialize(object obj)
  {
    var xdocument = new XDocument();
    Type type1 = obj.GetType();
    string name1 = type1.Name;
    var attribute1 = type1.GetAttribute<SerializeAsAttribute>();

    if (attribute1 != null)
      name1 = attribute1.TransformName(attribute1.Name ?? name1);

    var xelement1 = new XElement(name1.AsNamespaced(Namespace));

    if (obj is IList)
    {
      string name2 = "";

      foreach (object obj1 in (IEnumerable)obj)
      {
        Type type2 = obj1.GetType();
        var attribute2 = type2.GetAttribute<SerializeAsAttribute>();

        if (attribute2 != null)
          name2 = attribute2.TransformName(attribute2.Name ?? name1);

        if (name2 == "")
          name2 = type2.Name;

        var xelement2 = new XElement((XName)name2);
        this.Map(xelement2, obj1);
        xelement1.Add(xelement2);
      }
    } else
      this.Map(xelement1, obj);

    if (RootElement.HasValue())
    {
      var content = new XElement(RootElement.AsNamespaced(Namespace), xelement1);
      xdocument.Add(content);
    } else
      xdocument.Add(xelement1);

    return xdocument.ToString();
  }

  private void Map(XElement root, object obj)
  {
    Type type1 = obj.GetType();

    IEnumerable<PropertyInfo> propertyInfos = type1.GetProperties().Select(p => new
        {
          p,
          indexAttribute = p.GetAttribute<SerializeAsAttribute>()
        }
      ).Where(_param1 => _param1.p.CanRead && _param1.p.CanWrite).OrderBy(_param1 => _param1.indexAttribute != null ? _param1.indexAttribute.Index : int.MaxValue)
      .Select(_param1 => _param1.p);

    var attribute1 = type1.GetAttribute<SerializeAsAttribute>();

    foreach (PropertyInfo propertyInfo in propertyInfos)
    {
      string name1 = propertyInfo.Name;
      object obj1 = propertyInfo.GetValue(obj, null);

      if (obj.GetType().FullName.Equals("Bukimedia.PrestaSharp.Entities.AuxEntities.language") && root.Name.LocalName.Equals("language") && name1.Equals("id"))
        root.Add(new XAttribute(XName.Get("id"), obj1));
      else if (obj.GetType().FullName.Equals("Bukimedia.PrestaSharp.Entities.AuxEntities.language") && root.Name.LocalName.Equals("language") && name1.Equals("Value"))
      {
        var content = new XText(obj1 == null ? "" : obj1.ToString());
        root.Add(content);
      } else if (obj1 != null)
      {
        string serializedValue = this.GetSerializedValue(obj1);
        Type propertyType = propertyInfo.PropertyType;
        bool flag = false;
        var attribute2 = propertyInfo.GetAttribute<SerializeAsAttribute>();

        if (attribute2 != null)
        {
          name1 = attribute2.Name.HasValue() ? attribute2.Name : name1;
          flag = attribute2.Attribute;
        }

        var attribute3 = propertyInfo.GetAttribute<SerializeAsAttribute>();

        if (attribute3 != null)
          name1 = attribute3.TransformName(name1);
        else if (attribute1 != null)
          name1 = attribute1.TransformName(name1);

        var xelement1 = new XElement(name1.AsNamespaced(Namespace));

        if (propertyType.IsPrimitive || propertyType.IsValueType || propertyType == typeof(string))
        {
          if (flag)
          {
            root.Add(new XAttribute((XName)name1, serializedValue));

            continue;
          }

          xelement1.Value = serializedValue;
        } else if (obj1 is IList)
        {
          string name2 = "";

          foreach (object obj2 in (IEnumerable)obj1)
          {
            if (name2 == "")
            {
              Type type2 = obj2.GetType();
              var attribute4 = type2.GetAttribute<SerializeAsAttribute>();
              name2 = attribute4 == null || !attribute4.Name.HasValue() ? type2.Name : attribute4.Name;
            }

            var xelement2 = new XElement((XName)name2);
            this.Map(xelement2, obj2);
            xelement1.Add(xelement2);
          }
        } else
          this.Map(xelement1, obj1);

        root.Add(xelement1);
      }
    }
  }

  private string GetSerializedValue(object obj)
  {
    object obj1 = obj;

    if (obj is DateTime && DateFormat.HasValue())
    {
      obj1 = ((DateTime)obj).ToString(DateFormat);
    } else
    {
      switch (obj)
      {
        case bool _:    obj1 = obj.ToString().ToLowerInvariant(); break;
        case Decimal _: obj1 = obj.ToString().Replace(",", "."); break;
      }
    }

    return obj1.ToString();
  }
}