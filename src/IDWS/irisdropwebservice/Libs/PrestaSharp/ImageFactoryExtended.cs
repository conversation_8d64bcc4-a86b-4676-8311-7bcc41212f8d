using System.Net;

using Bukimedia.PrestaSharp.Entities.FilterEntities;
using Bukimedia.PrestaSharp.Factories;

using RestSharp;

namespace irisdropwebservice.Libs.PrestaSharp
{
    public class ImageFactoryExtended : ImageFactory
    {
        public ImageFactoryExtended(string baseUrl, string account, string secretKey)
            : base(baseUrl, account, secretKey)
        {
        }
        
        private RestClient CreateRestClient()
        {
            var client = new RestClient
            {
                BaseUrl = new Uri(BaseUrl)
            };

            return client;
        }

        public void AddProductImageNoMatterWhatNoId(long productId, byte[] fileContents)
        {
            this.AddImage("products", productId, fileContents);
        }

        public List<declination> GetProductImagesWithoutException(long productId)
        {
            return this.GetImagesByInstance2("products", productId);
        }

        protected List<declination> GetImagesByInstance2(string resource, long id)
        {
            RestRequest request = this.RequestForFilter("images/" + resource + "/" + id, "full", null, null, null, "image");
            var declinations = this.ExecuteForGetImages2<List<declination>>(request);
            var auxDeclinations = new List<declination>();

            if (declinations == null)
                return new List<declination>();

            foreach (declination declination in declinations)
            {
                if (!auxDeclinations.Contains(declination))
                {
                    auxDeclinations.Add(declination);
                }
            }

            return auxDeclinations;
        }
        
        protected virtual void AddWsKey(RestRequest request)
        {
            request.AddParameter("ws_key", Account, ParameterType.QueryString);
        }

        protected T ExecuteForGetImages2<T>(RestRequest request)
            where T : new()
        {
            RestClient client = this.CreateRestClient();
            this.AddWsKey(request);

            IRestResponse<T> response = client.Execute<T>(request);

            if (response.StatusCode == HttpStatusCode.NotFound)
                return response.Data;

            this.CheckResponse(response, request);

            return response.Data;
        }
    }
}