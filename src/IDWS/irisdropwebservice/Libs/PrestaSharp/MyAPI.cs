using System.Globalization;
using System.IO;
using System.Text;
using System.Xml.Linq;
using System.Xml.Serialization;

using Bukimedia.PrestaSharp;
using Bukimedia.PrestaSharp.Entities;
using Bukimedia.PrestaSharp.Factories;

using Invictus.Nomenklatura.Misc;

using RestSharp;

namespace irisdropwebservice.Libs.PrestaSharp
{
    [XmlType(Namespace = "Bukimedia/PrestaSharp/Entities")]
    public class Foo : PrestaShopEntity, IPrestaShopFactoryEntity
    {
        public long? id { get; set; }
    }

    /******************************************/
    #region Shared

    public class DataForProcessing
    {
        public List<OrdersHistoryEntry> OrdersHistoryEntries { get; set; }
        public List<ReservationHistoryEntry> ProductReservations { get; set; }
        public List<EmailSent> SentEmails { get; set; }
        public List<CustomerMessage> CustomerMessages { get; set; }
        public List<OrderFile> OrderFiles { get; set; }

        public bool Any => (OrdersHistoryEntries.Count + ProductReservations.Count + SentEmails.Count) > 0;
    }

    public record EmailSent(long SentMailId, string Recipient, string Template, string Data);

    public class CustomerExtended
    {
        public customer Customer { get; set; }
        public string PhoneNumber { get; set; }
    }

    public record CustomerMessage(long MessageId, long EmployeeId, long OrderId, long CustomerId, string Message, DateTime DateUpd);

    public record OrderFile(long OrderFileId, string Name, string Author, string FileDesc, DateTime Timestamp);

    #endregion
    /******************************************/

    /******************************************/
    #region Reservations

    public record ReservationHistoryEntry(
        long ReservationHistoryId,
        long ReservationId,
        long CustomerId,
        long GuestId,
        long ProductId,
        long ProductAttributeId,
        int Quantity,
        int QuantityReserved,
        DateTime DateAdd,
        DateTime DateExp,
        bool IsUnprocessed
    );

    public enum ReservationApprovalResult
    {
        None = 0,
        Success,
        ReservationHistoryItemIsOutdated,
        ReservationNotFound,
        ReservationReservedQuantityAlreadyZero
    }

    #endregion
    /******************************************/

    /******************************************/
    #region Orders

    public record OrdersHistoryEntry(long OrderId, long OrderHistoryId, short OrderStateId, bool ProcessForStaff, bool ProcessForCustomers);

    public class OrderDetails
    {
        public long OrderId { get; set; }
        public long CustomerId { get; set; }
        public short CurrentOrderState { get; set; }

        // TODO: remove
        // public string CustomerPhone { get; set; }

        public string OrderReference { get; set; }
        public long CarrierId { get; set; }
        public string AdminOrderNote { get; set; }
        public DateTime OrderCreationDateTimeLocal { get; set; }

        public List<OrderDetailProduct> Products { get; set; }
        public List<OrderDetailMessage> Messages { get; set; }
        public List<OrderDetailsAddress> Addresses { get; set; }
    }

    public class OrderDetailProduct
    {
        public long ProductId { get; set; }
        public long ProductAttributeId { get; set; }
        
        public int Quantity { get; set; }
        public string Reference { get; set; }

        public string SizeDescription { get; set; }
        public string ChosenSizeName { get; set; }
    }

    public class OrderDetailsAddress
    {
        public long CountryId { get; set; }
        public long StateId { get; set; }
        public string City { get; set; }
        public string LastName { get; set; }
        public string FirstName { get; set; }
        public string MiddleName { get; set; }
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string PhoneMobile { get; set; }
    }

    public record OrderDetailMessage(long EmployeeId, string Text, bool IsPrivate);

    #endregion
    /******************************************/

    /******************************************/
    #region All Products

    public record ProductUrls(string ProductUrl, string CategoryUrl);

    public readonly record struct ProductFeatureAndValueId(long FeatureId, long FeatureValueId);

    public record ProductInfo(
        long ProductId,
        decimal Price,
        long ProductVer,
        string Reference,
        string Name,
        string ShortDescriptionHtml,
        string DescriptionHtml,
        long DefaultCategory,
        string PhotoId,
        List<long> Categories,
        List<ProductFeatureAndValueId> Features,
        string[] Tags
    );

    public readonly record struct StockAvailableExtended(
        stock_available StockAvailable,
        int PhysicalQuantity,
        int PrestaReservedQuantity,
        int ReservationModuleReservedQuantity,
        int VkTempQuantityAdj
    )
    {
        // Presta is quirky with its uncomputed quantities.
        public bool QuantityIsUncomputedOrZero => PhysicalQuantity + PrestaReservedQuantity == 0;

        public int RealPhysicalQuantity => QuantityIsUncomputedOrZero
            ? StockAvailable.quantity
            : PhysicalQuantity;

        public int ComputedAvailableQuantity => QuantityIsUncomputedOrZero 
            ? StockAvailable.quantity
            : PhysicalQuantity - PrestaReservedQuantity - ReservationModuleReservedQuantity;

        public int MinBuglessQuantity => Math.Max(0, Math.Min(ComputedAvailableQuantity, StockAvailable.quantity));

        public override string ToString()
        {
            return $"(SQ: {StockAvailable.quantity} P: {PhysicalQuantity} PR: {PrestaReservedQuantity} " +
                   $"RMRQ: {ReservationModuleReservedQuantity} VKA: {VkTempQuantityAdj})" +
                   $"(UNC:{QuantityIsUncomputedOrZero} RPQ: {RealPhysicalQuantity} CAQ: {ComputedAvailableQuantity})";
        }
    }

    #endregion
    /******************************************/

    /******************************************/
    #region Products sourced from vk

    public record SourcedFromVkProductCacheInfo(long ProductId)
    {
        public string VkDescriptionHash { get; set; }
        public string VkFullPhotoId { get; set; }
        public long LastProductVer { get; set; }
    }

    public record HistoricalVkImgId(long Id, long ProductId, string VkFullPhotoId)
    {
        public static string VkFullPhotoId_Deleted => "(deleted)";
    }

    #endregion
    /******************************************/

    /******************************************/
    /******************************************/

    public class MyIrisDropModulePseudoFactory : GenericFactory<Foo>
    {
        protected override string singularEntityName => throw new NotImplementedException();
        protected override string pluralEntityName => throw new NotImplementedException();

        private readonly Lazy<RestClient> _restClient;
        private readonly MemoryStream _memoryStream;
        private readonly StreamWriter _streamWriter;

        public MyIrisDropModulePseudoFactory(string baseUrl, string account, string secretKey)
            : base(baseUrl, account, secretKey)
        {
            _restClient = new Lazy<RestClient>(this.CreateRestClient);
            _memoryStream = new MemoryStream(1024 * 32);
            _streamWriter = new StreamWriter(_memoryStream);
        }

        private RestClient CreateRestClient()
        {
            var client = new RestClient(BaseUrl);

            return client;
        }

        /******************************************/
        #region Shared

        public DataForProcessing Shared_GetAllUpdatesForProcessing(int maxEach)
        {
            XElement[] items = this.ApiCallSelect("IDWS_Shared_Proc_GetAllUpdates", "ProcAllUpdates", new[] { ("maxEach", maxEach.ToString()) });

            XElement orderHistoryEntriesElement = items.Single(e => e.Name == "OrderStatuses");
            XElement reservationsElement = items.Single(e => e.Name == "Reservations");
            XElement mailsElement = items.Single(e => e.Name == "Emails");
            XElement customerMessagesElement = items.Single(e => e.Name == "CustomerMessages");
            XElement orderFilesElement = items.Single(e => e.Name == "OrderFiles");

            var res = new DataForProcessing();

            res.OrdersHistoryEntries = this.Orders_GetNewOrdersHistory(orderHistoryEntriesElement);
            res.ProductReservations = this.Reservations_GetReservationsChanges(reservationsElement);
            res.SentEmails = this.GetEmails(mailsElement);
            res.CustomerMessages = this.GetCustomerMessages(customerMessagesElement);
            res.OrderFiles = this.GetOrderFiles(orderFilesElement);

            return res;
        }

        private List<EmailSent> GetEmails(XElement elementFromProcessing)
        {
            XElement rootElement = elementFromProcessing.Elements().SingleOrDefault(xe => xe.Name.LocalName == "NewEmails");

            if (rootElement == null)
                throw new Exception("Invalid data");

            var res = new List<EmailSent>();

            foreach ((_, XElement[] children) in this.GetElementsExclusiveWithChildren(rootElement.Elements(), "Email"))
            {
                var r = new EmailSent(
                    this.GetElementValue<long>(children, "id_mail"),
                    this.GetElementValue<string>(children, "recipient"),
                    this.GetElementValue<string>(children, "template"),
                    this.GetElementValue<string>(children, "xml_data")
                );

                res.Add(r);
            }

            return res;
        }

        private List<CustomerMessage> GetCustomerMessages(XElement elementFromProcessing)
        {
            XElement rootElement = elementFromProcessing.Elements().SingleOrDefault(xe => xe.Name.LocalName == "NewMessages");

            if (rootElement == null)
                throw new Exception("Invalid data");

            var res = new List<CustomerMessage>();

            foreach ((_, XElement[] children) in this.GetElementsExclusiveWithChildren(rootElement.Elements(), "Message"))
            {
                var r = new CustomerMessage(
                    this.GetElementValue<long>(children, "id_customer_message"),
                    this.GetElementValue<long>(children, "id_employee"),
                    this.GetElementValue<long>(children, "id_order"),
                    this.GetElementValue<long>(children, "id_customer"),
                    this.GetElementValue<string>(children, "message"),
                    this.GetElementValue<DateTime>(children, "date_upd")
                );

                res.Add(r);
            }

            return res;
        }

        private List<OrderFile> GetOrderFiles(XElement elementFromProcessing)
        {
            XElement rootElement = elementFromProcessing.Elements().SingleOrDefault(xe => xe.Name.LocalName == "NewAttachments");

            if (rootElement == null)
                throw new Exception("Invalid data");

            var res = new List<OrderFile>();

            foreach ((_, XElement[] children) in this.GetElementsExclusiveWithChildren(rootElement.Elements(), "Attachment"))
            {
                var r = new OrderFile(
                    this.GetElementValue<long>(children, "id"),
                    this.GetElementValue<string>(children, "name"),
                    this.GetElementValue<string>(children, "author"),
                    this.GetElementValue<string>(children, "filedesc"),
                    this.GetElementValue<DateTime>(children, "cr_timestamp")
                );

                res.Add(r);
            }

            return res;
        }

        public void Mails_SetLastProcessedEmail(long lastProcessedEmailId)
        {
            this.ApiCallSuccess("IDWS_Mails_SetLastProcdMail", new [] { ("lastProcessedEmailId", lastProcessedEmailId.ToString()) });
        }

        public void Misc_SetLastProcessedCustomerMessage(long lastId)
        {
            this.ApiCallSuccess("IDWS_Misc_SetLastProcdCustMsg", new[] { ("lastId", lastId.ToString()) });
        }

        public void Misc_SetLastProcessedOrderFile(long lastId)
        {
            this.ApiCallSuccess("IDWS_Misc_SetLastProcdOrderFile", new[] { ("lastId", lastId.ToString()) });
        }

        public CustomerExtended Customers_GetCustomer(long customerId)
        {
            XElement[] items = this.ApiCallSelect("IDWS_Customers_GetCustomerInfo", "Customers", new[] { ("customerId", customerId.ToString()) });

            foreach ((_, XElement[] children) in this.GetElementsExclusiveWithChildren(items, "Customer"))
            {
                return new CustomerExtended
                {
                    Customer = new customer
                    {
                        id = this.GetElementValue<long>(children, "id_customer"),
                        id_default_group = this.GetElementValue<long>(children, "id_default_group"),
                        id_lang = this.GetElementValue<long>(children, "id_lang"),
                        id_shop = this.GetElementValue<long>(children, "id_shop"),
                        id_shop_group = this.GetElementValue<long>(children, "id_shop_group"),
                        id_risk = this.GetElementValue<long>(children, "id_risk"),
                        id_gender = this.GetElementValue<long>(children, "id_gender"),
                        passwd = this.GetElementValue<string>(children, "passwd"),
                        lastname = this.GetElementValue<string>(children, "lastname"),
                        firstname = this.GetElementValue<string>(children, "firstname"),
                        email = this.GetElementValue<string>(children, "email"),
                        birthday = this.GetElementValue<string>(children, "birthday"),
                        website = this.GetElementValue<string>(children, "website"),
                        company = this.GetElementValue<string>(children, "company"),
                        siret = this.GetElementValue<string>(children, "siret"),
                        ape = this.GetElementValue<string>(children, "ape"),
                        ip_registration_newsletter = this.GetElementValue<string>(children, "ip_registration_newsletter"),
                        secure_key = this.GetElementValue<string>(children, "secure_key"),
                        outstanding_allow_amount = this.GetElementValue<decimal>(children, "outstanding_allow_amount"),
                        max_payment_days = this.GetElementValue<long>(children, "max_payment_days"),
                        note = this.GetElementValue<string>(children, "note"),
                        deleted = this.GetElementValue<int>(children, "deleted"),
                        optin = this.GetElementValue<int>(children, "optin"),
                        newsletter = this.GetElementValue<int>(children, "newsletter"),
                        is_guest = this.GetElementValue<int>(children, "is_guest"),
                        show_public_prices = this.GetElementValue<int>(children, "show_public_prices"),
                        active = this.GetElementValue<int>(children, "active"),
                        newsletter_date_add = this.GetElementValue<string>(children, "newsletter_date_add"),
                        last_passwd_gen = this.GetElementValue<string>(children, "last_passwd_gen"),
                        date_add = this.GetElementValue<string>(children, "date_add"),
                        date_upd = this.GetElementValue<string>(children, "date_upd"),
                    },
                    PhoneNumber = this.GetElementValue<string>(children, "phone")
                };
            }

            return null;
        }

        #endregion
        /******************************************/

        /******************************************/
        #region Reservations

        private List<ReservationHistoryEntry> Reservations_GetReservationsChanges(XElement elementFromProcessing)
        {
            XElement rootElement = elementFromProcessing.Elements().SingleOrDefault(xe => xe.Name.LocalName == "ReservationsChanges");

            if (rootElement == null)
                throw new Exception("Invalid data");

            var res = new List<ReservationHistoryEntry>();

            foreach ((_, XElement[] children) in this.GetElementsExclusiveWithChildren(rootElement.Elements(), "prc"))
            {
                var r = new ReservationHistoryEntry(
                    this.GetElementValue<long>(children, "id"),
                    this.GetElementValue<long>(children, "id_reserved_product"),
                    this.GetElementValue<long>(children, "id_customer"),
                    this.GetElementValue<long>(children, "id_guest"),
                    this.GetElementValue<long>(children, "id_product"),
                    this.GetElementValue<long>(children, "id_product_attribute"),
                    this.GetElementValue<int>(children, "quantity"),
                    this.GetElementValue<int>(children, "quantity_reserved"),
                    this.GetElementValue<DateTime>(children, "date_add"),
                    this.GetElementValue<DateTime>(children, "date_exp"),
                    this.GetElementValue<bool>(children, "is_unprocessed")
                );

                res.Add(r);
            }

            return res;
        }

        // SetLastProcdResv

        public void Reservations_SetLastProcessedHistoryEntry(long reservationHistoryId)
        {
            this.ApiCallSuccess("IDWS_ResvProds_SetLastProcdResv", new [] { ("reservationHistoryId", reservationHistoryId.ToString()) });
        }

        public ReservationApprovalResult Reservations_SetApproval(long reservationHistoryId, long reservedProductId, bool isApproved)
        {
            IRestResponse response = this.ApiCallPost("IDWS_ResvProds_SetApprov",
                new[]
                {
                    ("reservedProductId", reservedProductId.ToString()), 
                    ("reservationHistoryId", reservationHistoryId.ToString()), 
                    ("isApproved", isApproved.ToString())
                }
            );

            if (response.Content.Contains("<result>ReservationIsOutdated</result>"))
                return ReservationApprovalResult.ReservationHistoryItemIsOutdated;

            if (response.Content.Contains("<result>ReservationNotFound</result>"))
                return ReservationApprovalResult.ReservationNotFound;

            if (response.Content.Contains("<result>ReservationAlreadyZero</result>"))
                return ReservationApprovalResult.ReservationReservedQuantityAlreadyZero;

            if (response.Content.Contains("<result>Success</result>"))
                return ReservationApprovalResult.Success;

            throw new Exception($"Presta API call returned ${response.Content} which is not expected.");
        }

        #endregion
        /******************************************/

        /******************************************/
        #region Orders

        private List<OrdersHistoryEntry> Orders_GetNewOrdersHistory(XElement elementFromProcessing)
        {
            XElement rootElement = elementFromProcessing.Elements().SingleOrDefault(xe => xe.Name.LocalName == "mfNextOrderHistory");

            if (rootElement == null)
                throw new Exception("Invalid data");

            var res = new List<OrdersHistoryEntry>();

            foreach ((_, XElement[] children) in this.GetElementsExclusiveWithChildren(rootElement.Elements(), "OrderHistoryEntry"))
            {
                var r = new OrdersHistoryEntry(
                    this.GetElementValue<long>(children, "id_order"),
                    this.GetElementValue<long>(children, "id_order_history"),
                    this.GetElementValue<short>(children, "id_order_state"),
                    this.GetElementValue<bool>(children, "for_staff"),
                    this.GetElementValue<bool>(children, "for_client")
                );

                res.Add(r);
            }
            
            return res;
        }

        public void Orders_SetOrdersHistoryIsProcessed(long lastHistoryEntryId, bool forStaff)
        {
            this.ApiCallSuccess("IDWS_Orders_HistorySetLast",
                new[]
                {
                    ("lastHistoryEntryId", lastHistoryEntryId.ToString()), 
                    ("target", forStaff ? "staff" : "client")
                }
            );
        }

        public OrderDetails Orders_GetOrderDetails(long orderId, long langId)
        {
            XElement[] items = this.ApiCallSelect("IDWS_Orders_GetOrderDetails", "mfOrderDetails", new [] { ("orderId", orderId.ToString()), ("langId", langId.ToString()) });

            var res = new OrderDetails();
            res.OrderId = orderId;
            res.Products = new List<OrderDetailProduct>();
            res.Messages = new List<OrderDetailMessage>();
            res.Addresses = new List<OrderDetailsAddress>();

            XElement orderDetailsElement = items.Single(e => e.Name == "OrderDetails");
            XElement orderMessagesElement = items.Single(e => e.Name == "OrderMessages");
            XElement orderAddressesElement = items.Single(e => e.Name == "OrderAddresses");

            bool hasAnyDetail = false;

            foreach ((_, XElement[] children) in this.GetElementsExclusiveWithChildren(orderDetailsElement.Elements(), "orderDetail"))
            {
                hasAnyDetail = true;

                long orderIdXml = this.GetElementValue<long>(children, "id_order");

                if (orderIdXml != orderId)
                    throw new Exception("MyAPI returned wrong order.");

                res.OrderCreationDateTimeLocal = this.GetElementValue<DateTime>(children, "date_add");
                res.CustomerId = this.GetElementValue<long>(children, "id_customer");
                res.CurrentOrderState = this.GetElementValue<short>(children, "id_order_state");
                // res.CustomerPhone = this.GetElementValue<string>(children, "customer_phone");

                var orderDetailProduct = new OrderDetailProduct();
                orderDetailProduct.ProductId = this.GetElementValue<long>(children, "id_product");
                orderDetailProduct.ProductAttributeId = this.GetElementValue<long>(children, "id_product_attribute");
                orderDetailProduct.Quantity = this.GetElementValue<int>(children, "quantity");
                orderDetailProduct.Reference = this.GetElementValue<string>(children, "product_reference");
                orderDetailProduct.ChosenSizeName = this.GetElementValue<string>(children, "chosen_size_name");
                orderDetailProduct.SizeDescription = this.GetElementValue<string>(children, "size_description");

                res.Products.Add(orderDetailProduct);
            }

            foreach ((_, XElement[] children) in this.GetElementsExclusiveWithChildren(orderMessagesElement.Elements(), "OrderMessage"))
            {
                long orderIdXml = this.GetElementValue<long>(children, "id_order");

                if (orderIdXml != orderId)
                    throw new Exception("MyAPI returned wrong order.");

                string employeeId = this.GetElementValue<string>(children, "id_employee");

                if (string.IsNullOrWhiteSpace(employeeId))
                    continue;

                var orderMessage = new OrderDetailMessage(
                    long.Parse(employeeId),
                    this.GetElementValue<string>(children, "message"),
                    this.GetElementValue<bool>(children, "is_private")
                );

                if (string.IsNullOrWhiteSpace(orderMessage.Text))
                    continue;

                res.Messages.Add(orderMessage);
            }

            foreach ((_, XElement[] children) in this.GetElementsExclusiveWithChildren(orderAddressesElement.Elements(), "OrderAddress"))
            {
                long orderIdXml = this.GetElementValue<long>(children, "id_order");

                if (orderIdXml != orderId)
                    throw new Exception("MyAPI returned wrong order.");

                string orderReferenceXml = this.GetElementValue<string>(children, "order_reference");

                if (res.OrderReference == null)
                    res.OrderReference = orderReferenceXml;
                else if (res.OrderReference != orderReferenceXml)
                    throw new Exception("MyAPI returned wrong order_refence.");

                long carrierIdXml = this.GetElementValue<long>(children, "id_carrier");

                if (res.CarrierId == 0)
                    res.CarrierId = carrierIdXml;
                else if (res.CarrierId != carrierIdXml)
                    throw new Exception("MyAPI returned wrong id_carrier.");

                res.AdminOrderNote = this.GetElementValue<string>(children, "order_note");

                var address = new OrderDetailsAddress();

                address.CountryId = this.GetElementValue<long>(children, "id_country");
                address.StateId = this.GetElementValue<long>(children, "id_state");
                address.City = this.GetElementValue<string>(children, "city");

                address.LastName = this.GetElementValue<string>(children, "lastname");
                address.FirstName = this.GetElementValue<string>(children, "firstname");
                address.MiddleName = this.GetElementValue<string>(children, "middlename");

                address.Address1 = this.GetElementValue<string>(children, "address1");
                address.Address2 = this.GetElementValue<string>(children, "address2");
                address.PhoneMobile = this.GetElementValue<string>(children, "phone_mobile");

                res.Addresses.Add(address);
            }

            if (!hasAnyDetail)
                return null;

            if (string.IsNullOrWhiteSpace(res.OrderReference))
                throw new Exception($"OrderReference is empty, orderId={orderId}, langId={langId}");

            return res;
        }

        #endregion
        /******************************************/

        /******************************************/
        #region All Products

        public ProductUrls Products_GetProductAndCategoryLink(long productId, long categoryId, string categoryLinkRewrite)
        {
            XElement[] items = this.ApiCallSelect("IDWS_Products_GetProductLinks",
                "mfProductLinks",
                new[]
                {
                    ("productId", productId.ToString()),
                    ("categoryId", categoryId.ToString()),
                    ("categoryLinkRewrite", categoryLinkRewrite)
                }
            );

            XElement productLinkElement = items.Single(e => e.Name == "ProductLink");
            XElement categoryLinkElement = items.Single(e => e.Name == "CategoryLink");

            return new ProductUrls(productLinkElement.Value, categoryLinkElement.Value);
        }

        public List<ProductInfo> Products_GetInfo(long[] productIds, short idLang)
        {
            string queryArg =
                productIds.Length == 0
                    ? "ALL"
                    : BuildCommaSeparatedIntegers(productIds);

            XElement[] items = this.ApiCallSelect("IDWS_Products_GetProductsInfo",
                "productsQuickInfo",
                new[]
                {
                    ("productIdsStr", queryArg),
                    ("idLang", idLang.ToString())
                }
            );

            XElement productItemsElement = items.Single(e => e.Name == "products");

            XElement productFeaturesItemsElement = items.Single(e => e.Name == "product_features");
            XElement[] productFeaturesItemsElements = productFeaturesItemsElement.Elements().ToArray();

            XElement productCategoriesItemsElement = items.Single(e => e.Name == "product_categories");
            XElement[] productCategoriesItemsElements = productCategoriesItemsElement.Elements().ToArray();

            XElement productTagsItemsElement = items.Single(e => e.Name == "product_tags");
            XElement[] productTagsItemsElements = productTagsItemsElement.Elements().ToArray();

            var res = new List<ProductInfo>();

            foreach (XElement v in productItemsElement.Elements().ToArray())
            {
                XElement[] vElements = v.Elements().ToArray();

                string productIdStr = vElements.First(e => e.Name.LocalName == "id_product").Value;

                XElement[] featureElements = productFeaturesItemsElements.Where(vv => vv.Elements().First(e => e.Name.LocalName == "id_product").Value == productIdStr).ToArray();

                var features = new List<ProductFeatureAndValueId>();

                foreach (XElement featureElement in featureElements)
                {
                    XElement[] featureElementChildren = featureElement.Elements().ToArray();

                    features.Add(new ProductFeatureAndValueId(
                            long.Parse(featureElementChildren.First(e => e.Name.LocalName == "id_feature").Value),
                            long.Parse(featureElementChildren.First(e => e.Name.LocalName == "id_feature_value").Value)
                        )
                    );
                }

                XElement[] categoriesElements = productCategoriesItemsElements.Where(vv => vv.Elements().First(e => e.Name.LocalName == "id_product").Value == productIdStr).ToArray();

                var categories = new List<long>();

                foreach (XElement categoryElement in categoriesElements)
                {
                    categories.Add(
                        long.Parse(categoryElement.Elements().First(e => e.Name.LocalName == "id_category").Value)
                    );
                }
                
                XElement[] tagsElements = productTagsItemsElements.Where(vv => vv.Elements().First(e => e.Name.LocalName == "id_product").Value == productIdStr).ToArray();

                string[] tags = tagsElements.Select(e => e.Elements().First(ee => ee.Name.LocalName == "name").Value).ToArray();

                var r = new ProductInfo(
                    long.Parse(productIdStr),
                    decimal.Parse(vElements.First(e => e.Name.LocalName == "price").Value),
                    long.Parse(vElements.First(e => e.Name.LocalName == "ver").Value),
                    vElements.First(e => e.Name.LocalName == "ref").Value,
                    vElements.First(e => e.Name.LocalName == "name").Value,
                    vElements.First(e => e.Name.LocalName == "description_short").Value,
                    vElements.First(e => e.Name.LocalName == "description").Value,
                    long.Parse(vElements.First(e => e.Name.LocalName == "id_category_default").Value),
                    vElements.First(e => e.Name.LocalName == "vk_full_photo_id").Value,
                    categories,
                    features,
                    tags
                );

                res.Add(r);
            }

            return res;
        }

        public void Products_SetDefaultReservationSettings(long productId)
        {
            this.ApiCallSuccess("IDWS_Products_SetDefReservation", new [] { ("productId", productId.ToString()) });
        }

        public List<StockAvailableExtended> Products_GetFullStockAvaliInfo(long[] productIds)
        {
            string queryArg = BuildCommaSeparatedIntegers(productIds);

            XElement[] items = this.ApiCallSelect("IDWS_Products_GetFullStockAvaliInfo", "productStockAvailables", new[] { ("productIdsStr", queryArg) });

            var res = new List<StockAvailableExtended>();

            foreach ((_, XElement[] children) in this.GetElementsExclusiveWithChildren(items, "stock_available"))
            {
                long idProductAttribute = this.GetElementValue<long>(children, "id_product_attribute");

                var r = new StockAvailableExtended(
                    new stock_available
                    {
                        id_product = this.GetElementValue<long>(children, "id_product"),
                        id = this.GetElementValue<long>(children, "id_stock_available"),
                        depends_on_stock = this.GetElementValue<int>(children, "depends_on_stock"),
                        id_product_attribute = idProductAttribute,
                        id_shop = this.GetElementValue<long>(children, "id_shop"),
                        id_shop_group = this.GetElementValue<long>(children, "id_shop_group"),
                        location = this.GetElementValue<string>(children, "location"),
                        out_of_stock = this.GetElementValue<int>(children, "out_of_stock"),
                        quantity = this.GetElementValue<int>(children, "quantity"),
                    },
                    this.GetElementValue<int>(children, "physical_quantity"),
                    this.GetElementValue<int>(children, "reserved_quantity"),
                    this.GetElementValue<int?>(children, "wk_reserved_quantity") ?? 0,
                    this.GetElementValue<int?>(children, "vk_quantity_adj") ?? 0
                );
                res.Add(r);
            }

            return res;
        }

        #endregion
        /******************************************/

        /******************************************/
        #region Products sourced from vk

        public List<SourcedFromVkProductCacheInfo> ProductsSourcedFromVk_GetAllActiveProductsCacheInfo()
        {
            XElement[] items = this.ApiCallSelect("IDWS_SFK_GetAllActivePci", "vkAllActiveProductsCacheInfo", Array.Empty<(string, string)>());

            var res = new List<SourcedFromVkProductCacheInfo>();

            foreach (XElement v in items)
            {
                XElement[] vElements = v.Elements().ToArray();

                if (!long.TryParse(vElements.First(e => e.Name.LocalName == "last_product_ver").Value, out long lastProductVer))
                    lastProductVer = -1;

                var r = new SourcedFromVkProductCacheInfo(long.Parse(vElements.First(e => e.Name.LocalName == "id_product").Value))
                {
                    VkDescriptionHash = vElements.First(e => e.Name.LocalName == "vk_description_hash").Value,
                    VkFullPhotoId = vElements.First(e => e.Name.LocalName == "vk_full_photo_id").Value,
                    LastProductVer = lastProductVer
                };

                res.Add(r);
            }

            return res;
        }

        public void ProductsSourcedFromVk_SetProductUpdatedPlusMostRecentProductVer(long productId, string vkDescriptionHash)
        {
            this.ApiCallSuccess("IDWS_SFK_SetPrUpdPlusLastRecVer", new [] { ("productId", productId.ToString()), ("vkDescriptionHash", vkDescriptionHash) });
        }

        public void ProductsSourcedFromVk_SetImageUpdated(long productId, string vkFullPhotoId)
        {
            this.ApiCallSuccess("IDWS_SFK_SetImageUpdated", new[] { ("productId", productId.ToString()), ("vkFullPhotoId", vkFullPhotoId) });
        }

        public void ProductsSourcedFromVk_SetDeactivated(long productId)
        {
            this.ApiCallSuccess("IDWS_SFK_SetDeactivated", new[] { ("productId", productId.ToString()) });
        }

        public void ProductsSourcedFromVk_AddNew(long productId)
        {
            this.ApiCallSuccess("IDWS_SFK_AddNew", new[] { ("productId", productId.ToString()) });
        }

        public List<HistoricalVkImgId> ProductsSourcedFromVk_GetHistoricalVkImgIds(long[] productIds, string reference)
        {
            string queryArg = BuildCommaSeparatedIntegers(productIds);

            XElement[] items = this.ApiCallSelect("IDWS_SFK_GetHistoricalImgIds", "ProductHistoricalImages", new[] { ("productIdsStr", queryArg), ("reference", reference) });

            var res = new List<HistoricalVkImgId>();

            foreach (XElement v in items)
            {
                XElement[] vElements = v.Elements().ToArray();

                var r = new HistoricalVkImgId(
                    long.Parse(vElements.First(e => e.Name.LocalName == "id").Value),
                    long.Parse(vElements.First(e => e.Name.LocalName == "id_product").Value),
                    vElements.First(e => e.Name.LocalName == "vk_full_photo_id").Value
                );

                res.Add(r);
            }

            return res;
        }

        public List<long> ProductsSourcedFromVk_AreTheseProductsSourcedFromVk(long[] productIds)
        {
            string queryArg = BuildCommaSeparatedIntegers(productIds);

            XElement[] items = this.ApiCallSelect("IDWS_SFK_AreSFK", "vkProductsSourcedFromVk", new[] { ("productIdsStr", queryArg) });

            var res = new List<long>();

            foreach ((XElement element, _) in this.GetElementsExclusiveWithChildren(items, "id"))
            {
                res.Add(long.Parse(element.Value));
            }

            return res;
        }

        public void ProductsSourcedFromVk_SetTemporaryQuantityAdjustment(long orderId, long productId, long productAttributeId, int quantity)
        {
            throw new NotSupportedException();

            // this.ApiCallSuccess("IDWS_SFK_SetTempQuantityAdj", orderId, productId, productAttributeId, quantity);
        }

        #endregion
        /******************************************/


        /******************************************/
        #region Other

        public string Other_GetCmsPageContent(long cmsId, long langId, long shopId)
        {
            XElement[] items = this.ApiCallSelect("IDWS_Misc_GetCmsPageContent",
                "Pages",
                new[]
                {
                    ("cmsId", cmsId.ToString()),
                    ("langId", langId.ToString()),
                    ("shopId", shopId.ToString())
                }
            );

            string base64 = items.Single().Value;

            byte[] bytes = Convert.FromBase64String(base64);

            return Encoding.UTF8.GetString(bytes);
        }

        public void Other_SetCmsPageContent(long cmsId, long langId, long shopId, string content)
        {
            byte[] bytes = Encoding.UTF8.GetBytes(content);

            string base64 = Convert.ToBase64String(bytes);

            this.ApiCallSuccess("IDWS_Misc_SetCmsPageContent",
                new[]
                {
                    ("cmsId", cmsId.ToString()),
                    ("langId", langId.ToString()),
                    ("shopId", shopId.ToString()),
                    ("newContent", base64)
                }
            );
        }

        #endregion
        /******************************************/

        private XElement[] ApiCallSelect(string resource, string expectedRootElementName, (string, string)[] xmlBody)
        {
            if (resource == null)
                throw new ArgumentNullException(nameof(resource));

            if (expectedRootElementName == null)
                throw new ArgumentNullException(nameof(expectedRootElementName));

            IRestResponse response = this.ApiCallPost(resource, xmlBody);

            return this.GetApiCallSelectResponseXml(expectedRootElementName, response);
        }

        private XElement[] GetApiCallSelectResponseXml(string expectedRootElementName, IRestResponse response)
        {
            _memoryStream.Position = 0;
            _memoryStream.SetLength(0);

            string fixedContent = response.Content;
            fixedContent = fixedContent.Replace("&nbsp;", " ");
            _streamWriter.WriteLine(fixedContent);
            _streamWriter.Flush();
            _memoryStream.Position = 0;
            XDocument xd = XDocument.Load(_memoryStream);

            XElement rootElement = xd.Root.Elements().SingleOrDefault(xe => xe.Name.LocalName == expectedRootElementName);

            if (rootElement == null)
                throw new PrestaSharpException(response.Content, response.ErrorMessage, response.StatusCode, new Exception("Invalid data"));

            return rootElement.Elements().ToArray();
        }

        private void ApiCallSuccess(string resource, (string, string)[] xmlBody)
        {
            IRestResponse response = this.ApiCallPost(resource, xmlBody);

            if (response.Content.Contains("<result>Success</result>"))
                return;

            throw new Exception($"Presta API call returned ${response.Content} which is not expected.");
        }
        
        protected virtual void AddWsKey(RestRequest request)
        {
            request.AddParameter("ws_key", Account, ParameterType.QueryString);
        }
        
        private void AddBody(RestRequest request, IEnumerable<PrestaShopEntity> entities)
        {
            var ser = new PrestaSharpSerializer();
            request.RequestFormat = (DataFormat) 1;
            string empty = string.Empty;
            foreach (PrestaShopEntity entity in entities)
                empty += ser.PrestaSharpSerialize(entity);
            string str = "<prestashop>\n" + empty + "\n</prestashop>";
            request.AddParameter("application/xml", str, ParameterType.RequestBody);
        }

        protected void AddBody(RestRequest request, (string, string)[] entities)
        {
            request.RequestFormat = (DataFormat) 1;
            string str1 = string.Empty;
            foreach ((string, string) entity in entities)
                str1 = str1 + "<" + entity.Item1 + ">" + entity.Item2 + "</" + entity.Item1 + ">";
            string str2 = "<prestashop>\n" + str1 + "\n</prestashop>";
            request.AddParameter("application/xml", str2, ParameterType.RequestBody);
        }

        private void AddBody(RestRequest request, PrestaShopEntity entity)
        {
            this.AddBody(request, new List<PrestaShopEntity>
            {
                entity
            });
        }

        private IRestResponse ApiCallPost(string resource, (string, string)[] requestBody)
        {
            var bdr = new StringBuilder();
            bdr.Append(resource);

            var request = new RestRequest
            {
                Resource = bdr.ToString(),
                Method = Method.POST
            };

            this.AddWsKey(request);
            this.AddBody(request, requestBody);

            IRestResponse response = _restClient.Value.Execute(request);

            this.CheckResponse(response, request);

            return response;
        }

        private IEnumerable<(XElement element, XElement[] children)> GetElementsExclusiveWithChildren(IEnumerable<XElement> items, string elementName)
        {
            foreach (XElement xElement in items)
            {
                if (xElement.Name.LocalName != elementName)
                    throw new Exception("Unexpected element " + xElement.Name.LocalName);

                yield return (xElement, xElement.Elements().ToArray());
            }
        }

        private T GetElementValue<T>(XElement[] items, string name)
        {
            XElement e = items.SingleOrDefault(e => e.Name.LocalName == name);

            if (e is null)
                throw new Exception("Element with name " + name + " was not found.");

            Type t = typeof(T);

            if (t.IsGenericType && t.GetGenericTypeDefinition() == typeof(Nullable<>))
            {
                if (e.Value == "")
                    return (T)(object)null;
            }

            if (t == typeof(long))
            {
                return (T)(object)long.Parse(e.Value);
            }

            if (t == typeof(int) || t == typeof(int?))
            {
                return (T)(object)int.Parse(e.Value);
            }

            if (t == typeof(short))
            {
                return (T)(object)short.Parse(e.Value);
            }

            if (t == typeof(bool))
            {
                string trimmedValue = e.Value.Trim();

                if (trimmedValue == "1")
                    return (T)(object)true;
                if (trimmedValue == "0")
                    return (T)(object)false;

                return (T)(object)bool.Parse(trimmedValue);
            }

            if (t == typeof(DateTime))
            {
                if (long.TryParse(e.Value, out long unixTimestamp))
                {
                    return (T)(object)Various.UnixTimeStampToDateTime(unixTimestamp);
                }
                return (T)(object)DateTime.ParseExact(e.Value, PrestaSharpConstants.PRODUCT_DATE_TIMES_FORMAT, CultureInfo.InvariantCulture);
            }

            if (t == typeof(decimal))
            {
                return (T)(object)decimal.Parse(e.Value);
            }

            return (T)(object)e.Value;
        }

        private static string BuildCommaSeparatedIntegers(long[] productIds)
        {
            if (productIds.Length == 0)
                return "";

            var bdr = new StringBuilder();

            foreach (long productId in productIds.SkipLast(1))
            {
                bdr.Append(productId.ToString());
                bdr.Append(",");
            }

            bdr.Append(productIds.Last());

            return bdr.ToString();
        }
    }
}