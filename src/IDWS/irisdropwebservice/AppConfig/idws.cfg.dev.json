{
  "ClassesConfiguration": {
    "PrestaShop": {
      "IrisDrop": {
        "StatSourceName": "PrestaShopIrisDropTest",
        "DoWork": true
      }
    },
    "Vk": {
      "IrisDrop": {
        // "IsScrapItemsProducer": true,
        "IsScrapItemsProducer": false,
        "RelativeVkScrapScrappedProductsDir": "ThisAppData\\Vk\\IrisDrop\\Scrap\\Default"
      }
    },
    "Prom": {
      "DoWork": false
    },
    "Telegram": {
      "IrisDrop": {

        "DevChat": {
          "DefinitionName": "Dev"
        },

        "ReservationsChat": {
          "DefinitionName": "DevTestReservations"
        },
        "ToPackAndSendOrdersChat": {
          "DefinitionName": "DevTestStaffSend"
        },
        "ShopAdminAttentionChat": {
          "DefinitionName": "Dev"
        },
        "ReduceInStockChat": {
          "DefinitionName": "Dev" // TODO
        },
        "NewOrdersChat": {
          "DefinitionName": "DevTestNewOrders"
        },
        "TTNSourceChannel": {
          "DefinitionName": "DevTestNewOrders"
        },

        "DuplicateCustomerMessagesChat": {
          "DefinitionName": "DevTestOther"
        },

        "IrisGarbageChat": {
          "DefinitionName": "IrisGarbage"
        }
      }
    },
    "Statistics": {
      "PublishStatistics": false
    }
  } // ClassesConfiguration
}