using irisdropwebservice.Services.Statistics;

namespace irisdropwebservice.AppConfig.ClassConfigurations
{
    public class PrestaShopCCInfo
    {
        public int NonDropShippersPriceIncreaseUAH { get; init; }
        public int AbsoluteMinPriceUAH { get; init; }

        public long DropShippersCustomerGroupId { get; init; }
        public long[] ProcessTheseCustomerIds { get; init; }
        public bool ExcludeTheseCustomerIds { get; init; }

        public StatSource StatSourceName { get; init; }
        public string[] SetAsOnSaleOnlyForEndCategoriesContainingText { get; init; } // Use lower case only here
        public int RootCategoryId1 { get; init; }
        public int RootCategoryId2 { get; init; }
        public int AutoCreateVkSuperCategoryId { get; init; }

        public bool DoWork { get; init; }

        public short NewOrderStatus { get; init; }
        public short PaidOrderStatus { get; init; }
        public short PartiallyPaidOrderStatus { get; init; }
        public short SentOrderStatus { get; init; }
        public short CancelledOrderStatus { get; init; }
        public short[] FinalOrderStatuses { get; init; }
    }
}