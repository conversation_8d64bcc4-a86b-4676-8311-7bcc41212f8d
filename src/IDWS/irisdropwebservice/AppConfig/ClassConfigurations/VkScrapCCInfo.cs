using System.IO;

using irisdropwebservice.Services.Statistics;
using irisdropwebservice.Services.VkScrap;

namespace irisdropwebservice.AppConfig.ClassConfigurations
{
    public class VkScrapCCInfo
    {
        public string[] ExcludeRemoteAlbums { get; init; }

        public long[] ExcludeSpecificPhotoIds { get; init; }
        public long[] ExcludedPhotoArts { get; init; }

        public StatSource StatSourceName { get; init; }

        public bool IsScrapItemsProducer { get; init; }
        public bool IsScrapItemsConsumer { get; init; }

        public string RelativeVkScrapScrappedProductsDir { get; init; }

        public short AddTotalQuantitiesToScrapLogAfter_Days { get; init; }

        private string _scrapImagesDir;

        private string GetVkScrapScrappedProductsDir()
        {
            if (_scrapImagesDir != null)
                return _scrapImagesDir;

            if (!IsScrapItemsProducer)
                throw new Exception("Only ScrapItemsProducer has a reason to access scrapped products dir.");

            string baseDir = RRAppConfig.FileSystem.BaseDirFullPath;

            _scrapImagesDir = Path.Combine(baseDir, RelativeVkScrapScrappedProductsDir);

            if (!Directory.Exists(_scrapImagesDir))
                Directory.CreateDirectory(_scrapImagesDir);

            return _scrapImagesDir;
        }

        public string GetPhotoFilePath(string fullId)
        {
            (_, _, long vkPhotoId) = VkNamesAndUrls.ParseFullId(fullId);

            string vkPhotoIdExceptLast3Digits = new(
                vkPhotoId.ToString().SkipLast(3).ToArray()
            );

            string dir = this.GetVkScrapScrappedProductsDir();
            string subDir = Path.Combine(dir, vkPhotoIdExceptLast3Digits);

            if (!Directory.Exists(subDir))
                Directory.CreateDirectory(subDir);

            return Path.Combine(subDir, fullId + ".jpg");
        }

        public string[] GetAllPhotoFilePathDirs()
        {
            string dir = this.GetVkScrapScrappedProductsDir();

            return Directory.GetDirectories(dir);
        }
    }
}