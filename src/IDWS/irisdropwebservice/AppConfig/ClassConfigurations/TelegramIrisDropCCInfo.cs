namespace irisdropwebservice.AppConfig.ClassConfigurations
{
    public readonly record struct TelegramChatConfiguration
    {
        public string DefinitionName { get; init; }
    }

    public class TelegramIrisDropCCInfo
    {
        public TelegramChatConfiguration DevChat { get; init; }
        public TelegramChatConfiguration ShopAdminAttentionChat { get; init; }
        public TelegramChatConfiguration NewOrdersChat { get; init; }
        public TelegramChatConfiguration ToPackAndSendOrdersChat { get; init; }
        public TelegramChatConfiguration ReservationsChat { get; init; }
        public TelegramChatConfiguration ReduceInStockChat { get; init; }
        public TelegramChatConfiguration TTNSourceChannel { get; init; }
        public TelegramChatConfiguration DuplicateCustomerMessagesChat { get; init; }
        public TelegramChatConfiguration IrisGarbageChat { get; init; }
    }
}
