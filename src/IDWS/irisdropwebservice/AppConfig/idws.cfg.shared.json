{
  "AllowedHosts": "*",
  
  "Nomenklatura": {
    "Log": {
      "TelegramTemplate": "[{Level:u3}] {SourceClassName:l} {Message:lj}{NewLine}{Exception}{ExceptionDetail}"
    },
    "ExtApi": {
      "Issuer": "https://ws.irisdrop.com.ua",
      "Audience": "https://ws.irisdrop.com.ua",
      "LifetimeMinutes": 60,
      "AstraHostUrl": "https://antaresold.irisdrop.com.ua"
    }
  },

  "Serilog": {
    "Using:$merge:$idws": [
      "irisdropwebservice"
    ],
    "WriteTo:$merge:$idws": [
      {
        // Telegram chatbot reporting
        "Name": "TelegramChatBot",
        "Args": {
          "restrictedToMinimumLevel": "Warning",
          "formatter": {
            "type": "Serilog.Formatting.Display.MessageTemplateTextFormatter, Serilog",
            "outputTemplate": "${Nomenklatura:Log:TelegramTemplate}"
          }
        }
      }
    ]
  },

  "ClassesConfiguration": {
    "Vk": {
      "IrisDrop": {
        "IsScrapItemsConsumer": true,
        "ExcludeRemoteAlbums": [
          "ВАШІ ВІДГУКИ",
          "ПРОДАНО 2022",
          "ПРОДАНО 2023",
          "ПРОДАНО 2024",
          "ПРОДАНО 2025",
          "ПРОДАНО",
          "НЕ ПІДІЙШОВ РОЗМІР",
          "ХВАЛУНЬКИ",
          "ПЕРЕДЗАМОВЛЕННЯ",
          "товар"
        ],
        "ExcludeSpecificPhotoIds": [
          // Products, in excluded arts
          457286856,
          457286915,
          457288622,
          457288420,
          457288563,
          457288543,
          457288564,
 
          // Other
          457287085,
          457287760,
          457288727,
          457289056,
          457289661,
          457290884,
          457290970,
          457292146
        ],
        "ExcludedPhotoArts": [ // This must mimic ExcludeSpecificPhotoIds for products
          4279,
          4350,
          2537,
          4793,
          5122,
          5139,
          5123
        ],
        "StatSourceName": "VkComIrisDrop",
        "AddTotalQuantitiesToScrapLogAfter_Days": 7
      }
    },
    "PrestaShop": {
      "IrisDrop": {
        "DropShippersCustomerGroupId": 4,
        "ExcludeTheseCustomerIds": true,
        "ProcessTheseCustomerIds": [ 3 ], // Autotest
        "NonDropShippersPriceIncreaseUAH": 250,
        "AbsoluteMinPriceUAH": 100,
        "SetAsOnSaleOnlyForEndCategoriesContainingText": [ // Use lower case only here
          "розпродаж",
          "знижки"
        ],
        "RootCategoryId1": 1,
        "RootCategoryId2": 2,
        "AutoCreateVkSuperCategoryId": 34,
        "NewOrderStatus": 10,
        "PaidOrderStatus": 2,
        "PartiallyPaidOrderStatus": 16,
        "SentOrderStatus": 4,
        "CancelledOrderStatus": 6,
        "FinalOrderStatuses": [ 4, 5, 6, 7, 9, 12 ] // Also used in module, for removeVkOrderedProductsTempQuantityAdj
      }
    },
    "Statistics": {
      "KeepStatisticsFor_Days": 30,
      "PublishStatisticsTimesUTC": [
        "6:00:00",
        "14:00:00",
        "19:00:00"
      ],
      "PublishStatistics2TimesUTC": [
        "18:00:00"
      ],
      "PublicDevErrorsTimesUTC": [
        "17:00:00"
      ],
      "PublishStatisticsDevTimesUTC": [
        "17:00:00"
      ]
    }
  } // ClassesConfiguration
}