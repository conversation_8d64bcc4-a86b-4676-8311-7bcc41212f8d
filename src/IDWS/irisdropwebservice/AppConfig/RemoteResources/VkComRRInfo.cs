namespace irisdropwebservice.AppConfig.RemoteResources
{
    public class VkProxyConfiguration
    {
        public string Url { get; init; }
        public int Port { get; init; }
        public string Username { get; init; }
        public string Password { get; init; }
    }

    public class VkComSecretRRInfo
    {
        public string Login { get; init; }
        public string Password { get; init; }
        public string PhoneNumber { get; init; }

        public long AppId { get; init; }

        public string ApiKey { get; init; }

        public long GroupId { get; init; }

        public VkProxyConfiguration Proxy { get; init; }
    }
}