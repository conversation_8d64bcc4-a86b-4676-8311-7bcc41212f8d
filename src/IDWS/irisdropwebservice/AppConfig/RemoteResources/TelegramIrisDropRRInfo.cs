using System.Reflection;

using JetBrains.Annotations;

namespace irisdropwebservice.AppConfig.RemoteResources
{
    public class TelegramIrisDropRRInfo
    {
        public string CustomersChatBotClientToken { get; init; }
        public string StaffChatBotClientToken { get; init; }

        public long GetChatId(string chatDefinitionName)
        {
            return ChatDefinitions.GetChatIdByDefinitionName(chatDefinitionName);
        }

        public string GetChatDefinitionNameByChatId(long chatId)
        {
            return ChatDefinitions.GetChatDefinitionNameByChatId(chatId);
        }

        public TelegramChatDefinitions ChatDefinitions { get; init; }
    }

    public class TelegramChatDefinitions
    {
        private static readonly PropertyInfo[] _Props;

        static TelegramChatDefinitions()
        {
            _Props = typeof(TelegramChatDefinitions).GetProperties(BindingFlags.Public | BindingFlags.Instance);
        }

        [UsedImplicitly]
        public long DevChatId { get; init; }

        [UsedImplicitly]
        public long ReservationsChatId { get; init; }
        [UsedImplicitly]
        public long DevTestReservationsChatId { get; init; }

        [UsedImplicitly]
        public long NewOrdersChatId { get; init; }
        [UsedImplicitly]
        public long DevTestNewOrdersChatId { get; init; }

        [UsedImplicitly]
        public long StaffSendChatId { get; init; }
        [UsedImplicitly]
        public long DevTestStaffSendChatId { get; init; }

        [UsedImplicitly]
        public long ShopAdminAttentionChatId { get; init; }
        [UsedImplicitly]
        public long DevTestShopAdminAttentionChatId { get; init; }

        [UsedImplicitly]
        public long StaffAvaliChatId { get; init; }
        [UsedImplicitly]
        public long IrisTTNChatId { get; init; }

        [UsedImplicitly]
        public long OtherChatId { get; init; }
        [UsedImplicitly]
        public long DevTestOtherChatId { get; init; }

        public long IrisOperatorUserChatId { get; init; }
        [UsedImplicitly]
        public long DeveloperUserChatId { get; init; }
        [UsedImplicitly]
        public long IrisGarbageChatId { get; init; }

        public long GetChatIdByDefinitionName(string name)
        {
            name += "ChatId";
            return (long)_Props.Where(p => p.PropertyType == typeof(long)).Single(p => p.Name == name).GetValue(this);
        }

        public string GetChatDefinitionNameByChatId(long chatId)
        {
            string propName = _Props.SingleOrDefault(p => (long)p.GetValue(this) == chatId)?.Name;

            return propName?.Remove(propName.Length - "ChatId".Length);
        }
    }
}