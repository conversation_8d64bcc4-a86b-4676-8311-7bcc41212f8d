namespace irisdropwebservice.AppConfig.RemoteResources
{
    public class PromRRInfo
    {
        public string ApiUrl { get; init; }

        public string[] CustomersOn { get; init; }
    }

    public class PromRRCustomerInfo
    {
        public string Name { get; init; }
        public string Token { get; init; }
        
        public int PriceIncrease { get; init; }
        public float PricePercentTop { get; init; }
        public long ThisCustomerTelegramId { get; init; }
        public int CustomerId { get; init; }
    }
}
