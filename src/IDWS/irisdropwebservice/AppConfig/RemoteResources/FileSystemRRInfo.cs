using System.IO;

namespace irisdropwebservice.AppConfig.RemoteResources
{
    public class FileSystemRRInfo
    {
        public string BaseDir { get; init; }
        public string BaseDirFullPath => Path.GetFullPath(BaseDir);
        public string RelativeLogsDir { get; init; }
        public string RelativeSecretsDir { get; init; }
        
        public string ResourcesDir { get; init; }

        public string LogsDir => Path.Combine(BaseDirFullPath,    RelativeLogsDir);
        public string SecretsDir => Path.Combine(BaseDirFullPath, RelativeSecretsDir);

        public static string ExeResourcesDir => Path.Combine(Environment.CurrentDirectory, "Resources");

        public string FullResourcesDir
        {
            get {
                if (ResourcesDir == "{CURRENTDIRRESOURCES}")
                    return Path.Combine(Environment.CurrentDirectory, "Resources");

                return ResourcesDir;
            }
        }
    }
}