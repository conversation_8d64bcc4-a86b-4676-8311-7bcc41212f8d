using Invictus.Nomenklatura.App;

using irisdropwebservice.AppConfig.ClassConfigurations;

namespace irisdropwebservice.AppConfig
{
    public class CCAppConfig
    {
        private static InvAppConfig _InvAppConfig;
        
        public static void SetInvAppConfig(InvAppConfig invAppConfig)
        {
            _InvAppConfig = invAppConfig;
        }
        
        private static T GetCompositeCCInfo<T>(params string[] path)
            where T : class
        {
            return _InvAppConfig.GetSection<T>(new [] { "ClassesConfiguration" }.Concat(path).ToArray());
        }

        public static PrestaShopCCInfo PrestaIrisDrop => GetCompositeCCInfo<PrestaShopCCInfo>("PrestaShop", "IrisDrop");
        public static VkScrapCCInfo VkScrapIrisDrop => GetCompositeCCInfo<VkScrapCCInfo>("Vk", "IrisDrop");
        public static PromCCInfo Prom => GetCompositeCCInfo<PromCCInfo>("Prom");
        public static StatisticsCCInfo Statistics => GetCompositeCCInfo<StatisticsCCInfo>("Statistics");

        public static TelegramIrisDropCCInfo TelegramIrisDrop => GetCompositeCCInfo<TelegramIrisDropCCInfo>("Telegram", "IrisDrop");
    }
}