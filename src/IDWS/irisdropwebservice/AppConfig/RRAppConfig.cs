using Invictus.Jupiter.Shared;
using Invictus.Nomenklatura.App;

using irisdropwebservice.AppConfig.RemoteResources;

namespace irisdropwebservice.AppConfig
{
    public class RRAppConfig
    {
        private static InvAppConfig _InvAppConfig;
        
        public static void SetInvAppConfig(InvAppConfig invAppConfig)
        {
            _InvAppConfig = invAppConfig;
        }
        
        private static T GetCompositeRRInfo<T>(params string[] path)
            where T : class
        {
            return _InvAppConfig.GetSection<T>(new [] { "RemoteResources" }.Concat(path).ToArray());
        }
        
        public static FileSystemRRInfo FileSystem => GetCompositeRRInfo<FileSystemRRInfo>("FileSystem");

        public static VkComSecretRRInfo SecretVkIrisDrop => GetCompositeRRInfo<VkComSecretRRInfo>("Vk", "IrisDrop");

        // ReSharper disable once UnusedMember.Global
        public static VkComSecretRRInfo SecretVkIrisDropMirror => GetCompositeRRInfo<VkComSecretRRInfo>("Vk", "IrisDropMirror");

        public static PromRRInfo Prom => GetCompositeRRInfo<PromRRInfo>("Prom");
        public static PromRRCustomerInfo PromCustomerTest => GetCompositeRRInfo<PromRRCustomerInfo>("PromCustomers", "Test");
        public static PromRRCustomerInfo PromCustomerBestBaby => GetCompositeRRInfo<PromRRCustomerInfo>("PromCustomers", "BestBaby");

        public static KastaRRInfo Kasta => GetCompositeRRInfo<KastaRRInfo>("Kasta");
        public static RozetkaRRInfo Rozetka => GetCompositeRRInfo<RozetkaRRInfo>("Rozetka");
        
        public static EpicentrRRCustomerInfo EpicentrIrisDrop => GetCompositeRRInfo<EpicentrRRCustomerInfo>("Epicentr", "IrisDrop");

        public static TelegramIrisDropRRInfo TelegramIrisDrop => GetCompositeRRInfo<TelegramIrisDropRRInfo>("Telegram", "IrisDrop");

        public static PrestaShopRRInfo PrestaShopIrisDrop => GetCompositeRRInfo<PrestaShopRRInfo>("PrestaShop", "IrisDrop");
        public static ApplicationRRInfo Application => GetCompositeRRInfo<ApplicationRRInfo>("Application");

        public static JupiterConfiguration Jupiter => GetCompositeRRInfo<JupiterConfiguration>("Jupiter");
    }
}