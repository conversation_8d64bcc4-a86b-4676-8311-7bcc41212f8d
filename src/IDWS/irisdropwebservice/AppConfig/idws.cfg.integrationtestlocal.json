{
  "ClassesConfiguration": {
    "PrestaShop": {
      "IrisDrop": {
        "StatSourceName": "Invalid",
        "DoWork": true,
        "ExcludeTheseCustomerIds": false
      }
    },
    "Vk": {
      "IrisDrop": {
        "IsScrapItemsProducer": true,
        "IsScrapItemsConsumer": false,
        "StatSourceName": "Invalid"
      }
    },
    "Prom": {
      "DoWork": false
    },
    "Statistics": null // Explicitly fail reading this.
  } // ClassesConfiguration
}