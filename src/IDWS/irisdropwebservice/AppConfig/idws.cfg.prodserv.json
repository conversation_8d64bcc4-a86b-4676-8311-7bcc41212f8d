{
  "ClassesConfiguration": {
    "PrestaShop": {
      "IrisDrop": {
        "StatSourceName": "PrestaShopIrisDrop",
        "DoWork": true // def: true
      }
    },
    "Vk": {
      "IrisDrop": {
        "IsScrapItemsProducer": true,
        "RelativeVkScrapScrappedProductsDir": "Vk\\IrisDrop\\Scrap\\Default"
      }
    },
    "Prom": {
      "DoWork": true
    },
    "Telegram": {
      "IrisDrop": {

        "DevChat": {
          "DefinitionName": "Dev"
        },

        "ReservationsChat": {
          "DefinitionName": "Reservations"
        },
        "ToPackAndSendOrdersChat": {
          "DefinitionName": "StaffSend"
        },
        "ShopAdminAttentionChat": {
          "DefinitionName": "ShopAdminAttention"
        },
        "ReduceInStockChat": {
          "DefinitionName": "StaffAvali"
        },
        "NewOrdersChat": {
          "DefinitionName": "NewOrders"
        },
        "TTNSourceChannel": {
          "DefinitionName": "IrisTTN"
        },

        "DuplicateCustomerMessagesChat": {
          "DefinitionName": "Other"
        },

        "IrisGarbageChat": {
          "DefinitionName": "IrisGarbage"
        }
      }
    },
    "Statistics": {
      "PublishStatistics": true // def: true
    }
  } // ClassesConfiguration
}