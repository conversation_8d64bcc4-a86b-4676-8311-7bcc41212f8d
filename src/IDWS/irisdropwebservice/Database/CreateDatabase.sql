USE [idws_id]
GO
/****** Object:  Table [dbo].[<PERSON>pi<PERSON><PERSON><PERSON>]    Script Date: 29.09.2024 14:48:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ApiKeys](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[ApiK<PERSON>] [nchar](32) NOT NULL,
 CONSTRAINT [PK_ApiKeys] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[CustomerInfo]    Script Date: 29.09.2024 14:48:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CustomerInfo](
	[Id] [bigint] IDENTITY(1,1) NOT NULL,
	[TelegramUserHandle] [nvarchar](128) NULL,
	[TelegramFirstName] [nvarchar](128) NULL,
	[TelegramLastName] [nvarchar](128) NULL,
	[PhoneNumber] [nchar](32) NULL,
	[TelegramUserId] [bigint] NULL,
	[TelegramLastChatId] [bigint] NULL,
	[SubToTelegramFromSiteUpdates] [bit] NOT NULL,
 CONSTRAINT [PK_ClientInfo] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [AK_TelegramUserId] UNIQUE NONCLUSTERED 
(
	[TelegramUserId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ExposedOnlineResources]    Script Date: 29.09.2024 14:48:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ExposedOnlineResources](
	[Id] [bigint] IDENTITY(1,1) NOT NULL,
	[ResourceType] [nvarchar](64) NULL,
	[ResourceReference] [nvarchar](128) NULL,
	[ExpiryDateUtc] [datetime] NULL,
	[Auth] [bit] NULL,
	[InternalResourceId] [nvarchar](128) NULL,
 CONSTRAINT [PK_ExposedOnlineResources] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PersistentTask]    Script Date: 29.09.2024 14:48:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PersistentTask](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[TaskName] [varchar](64) NOT NULL,
	[TaskXmlBlock] [varbinary](1500) NOT NULL,
	[Created] [datetime] NOT NULL,
 CONSTRAINT [PK_PersistentTasks] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[StatisticsLog]    Script Date: 29.09.2024 14:48:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[StatisticsLog](
	[Id] [bigint] IDENTITY(1,1) NOT NULL,
	[DateTime] [datetime] NOT NULL,
	[OperationType] [smallint] NOT NULL,
	[StatSource] [smallint] NOT NULL,
	[Parameter1] [nvarchar](max) NULL,
	[Parameter2] [nvarchar](max) NULL,
	[Parameter3] [nvarchar](max) NULL,
 CONSTRAINT [PK_StatisticsLog] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[TelegramChatMessage]    Script Date: 29.09.2024 14:48:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TelegramChatMessage](
	[Id] [bigint] IDENTITY(1,1) NOT NULL,
	[ChatId] [varchar](64) NOT NULL,
	[ChatType] [tinyint] NOT NULL,
	[MessageId] [int] NOT NULL,
	[SenderId] [bigint] NOT NULL,
	[MessageText] [nvarchar](max) NOT NULL,
	[DateTimeUtc] [datetime] NOT NULL,
 CONSTRAINT [PK_TelegramChatMessage] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[TelegramChatMessageLinkedInfo]    Script Date: 29.09.2024 14:48:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TelegramChatMessageLinkedInfo](
	[Id] [bigint] IDENTITY(1,1) NOT NULL,
	[ChatMessageId] [bigint] NOT NULL,
	[Data] [varchar](255) NOT NULL,
	[InfoType] [int] NOT NULL,
 CONSTRAINT [PK_TelegramOrderMessageInfo] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[VariousStoredData]    Script Date: 29.09.2024 14:48:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[VariousStoredData](
	[Id] [bigint] IDENTITY(1,1) NOT NULL,
	[PropertyName] [nvarchar](64) NULL,
	[IdProperty] [bigint] NULL,
	[Value] [nvarchar](max) NOT NULL,
 CONSTRAINT [PK_VariousStoredProperties] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [IX_VariousStoredData] UNIQUE NONCLUSTERED 
(
	[IdProperty] ASC,
	[PropertyName] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[VkScrapArtLog]    Script Date: 29.09.2024 14:48:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[VkScrapArtLog](
	[Art] [nvarchar](64) NOT NULL,
	[DataXmlBlock] [varbinary](6000) NOT NULL,
	[IsArtless] [bit] NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[VkScrapGeneration]    Script Date: 29.09.2024 14:48:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[VkScrapGeneration](
	[Id] [bigint] IDENTITY(1,1) NOT NULL,
	[ScrapStartDateTime] [datetime] NOT NULL,
	[LastFetch] [datetime] NULL,
	[IsComplete] [bit] NOT NULL,
	[ScrapInfoFetchingDurationMs] [int] NULL,
 CONSTRAINT [PK_VkScrapGeneration] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[VkScrapItem]    Script Date: 29.09.2024 14:48:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[VkScrapItem](
	[Id] [bigint] IDENTITY(1,1) NOT NULL,
	[ScrapGeneration] [bigint] NOT NULL,
	[QtysXmlBlock] [varbinary](1500) NULL,
	[MaxPhotoSizeUri] [nvarchar](512) NOT NULL,
	[Text] [ntext] NOT NULL,
	[VkGroupId] [bigint] NOT NULL,
	[VkAlbumId] [bigint] NOT NULL,
	[VkPhotoId] [bigint] NOT NULL,
	[VkPhotoCreationDate] [datetime] NOT NULL,
	[OrderInAlbum] [int] NOT NULL,
	[ParsingResult] [tinyint] NOT NULL,
	[AlbumTitle] [nvarchar](128) NULL,
	[PhotoTitle] [nvarchar](128) NULL,
	[Art] [nvarchar](16) NULL,
	[Description] [ntext] NULL,
	[PriceNew] [int] NULL,
	[PriceOld] [int] NULL,
	[FullTextHash] [nvarchar](50) NULL,
	[ParsingError] [text] NULL,
 CONSTRAINT [PK_VkScrapItem] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
ALTER TABLE [dbo].[VkScrapItem]  WITH CHECK ADD  CONSTRAINT [FK_VkScrapItem_VkScrapGeneration] FOREIGN KEY([ScrapGeneration])
REFERENCES [dbo].[VkScrapGeneration] ([Id])
GO
ALTER TABLE [dbo].[VkScrapItem] CHECK CONSTRAINT [FK_VkScrapItem_VkScrapGeneration]
GO
