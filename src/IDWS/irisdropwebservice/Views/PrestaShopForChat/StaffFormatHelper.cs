using Bukimedia.PrestaSharp.Entities;

using irisdropwebservice.Libs.PrestaSharp;

namespace irisdropwebservice.Views.PrestaShopForChat
{
    public class StaffFormatHelper
    {
        public static string CustomerToString(CustomerExtended customer, string telegramHandleLink)
        {
            return $"{customer.Customer.firstname} {customer.Customer.lastname} {customer.PhoneNumber} {telegramHandleLink ?? ""} {customer.Customer.email}";
        }

        public static string CustomerToString(guest guest)
        {
            return $"Гість з ID# {guest.id}";
        }
    }
}
