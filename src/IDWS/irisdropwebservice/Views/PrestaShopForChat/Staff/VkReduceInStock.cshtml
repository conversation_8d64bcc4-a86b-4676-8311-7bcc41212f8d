@page
@using Invictus.Nomenklatura.Misc
@using irisdropwebservice.Views.PrestaShopForChat.Partials
@using irisdropwebservice.Views.PrestaShopForChat.Staff
@model irisdropwebservice.Views.PrestaShopForChat.Staff.VkReduceInStockModel
@{
    var my = (VkReduceInStockModel)ViewContext.ViewData.Model;
    PaidOrderModel m = my.M;
}
@if (m.IsTestSite)
{
    <p>🚨 ЗНІМІТЬ З НАЯВНОСТІ або зв’яжіться з замовником (ОПЛАЧЕНЕ ЗАМОВЛЕННЯ) (test.irisdrop) </p>
}
else
{
    <p>🚨 ЗНІМІТЬ З НАЯВНОСТІ або зв’яжіться з замовником (ОПЛАЧЕНЕ ЗАМОВЛЕННЯ) </p>
}
<p>Після зняття або незняття з наявності - дайте відвопідь + або - на це повідомлення. </p>
<p>@m.OrderReference</p>
<p>@m.OrderCreationDateTimeUser</p>
<p>
    <s>Замовник: </s>
    <s>@my.CustomerInfoString</s>
</p>
<p>
    <s>Доставка: @m.CarrierName </s>

    @if (m.IsPaidPartially)
    {
        <s>(наложка).</s>
    }
    else
    {
        <s>(повна оплата).</s>
    }
</p>
<p>
    @if (!string.IsNullOrWhiteSpace(m.AdminOrderNote))
    {
        <s>Примітка відсутня.</s>
    }
    else
    {
        <s>Примітка: @m.AdminOrderNote</s>
    }
</p>
<br />

@foreach (OrderSameProductIdsDetailsModel sameIdProducts in m.Products)
{
    @await RazorTemplateRender.RenderPartial(Html, @"PrestaShopForChat/Partials/OrderSameProductIdsDetails", sameIdProducts)
}

<br />
<p>☵☵☵☵☵☵☵☵☵☵☵☵☵☵☵</p>
<br />

@await RazorTemplateRender.RenderPartial(Html, @"PrestaShopForChat/Partials/OrderCommentsDetails", new OrderCommentsDetailsModel() { Messages = m.Messages })