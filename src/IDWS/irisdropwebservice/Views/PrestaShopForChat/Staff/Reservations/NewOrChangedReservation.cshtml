@page
@using irisdropwebservice.Views.PrestaShopForChat.Staff.Reservations
@model irisdropwebservice.Views.PrestaShopForChat.Staff.Reservations.NewOrChangedReservationModel
@{
    var m = (NewOrChangedReservationModel)ViewContext.ViewData.Model;
}
@if (m.IsTestSite)
{
    <!-- <p>(test.irisdrop) </p> -->
    <br/>
}
else
{
    <!-- <p>Забровьована к-ть товару була змінена</p> -->
    <p>🚨</p>
}

<p>
    <s>Замовник: </s>
    <s>@m.CustomerInfoString</s>
</p>
<br />
<p>
    <s>Товар: @m.ProductReference </s>
    <s>@m.ProductSizeDescription: @m.ProductChosenSizeName</s>
</p>
<p>
    <s>Кількість: </s>
    @if (m.PrevReservedQuantity == null && m.NewReservedQuantity != 0)
    {
        <s>@m.NewReservedQuantity шт. (нова бронь)</s>
    }
    else if (m.NewReservedQuantity == 0)
    {
        @if (m.UnreservedIsInCart)
        {
            <s>@m.PrevReservedQuantity ➡ 0 шт. (замовник переніс товар у кошик).</s>
        } else
        {
            <s>@m.PrevReservedQuantity ➡ 0 шт. (бронь протухла або була видалена).</s>
        }

    } else
    {
        <s>@m.PrevReservedQuantity ➡ @m.NewReservedQuantity шт. (кількість змінена).</s>
    }
</p>
<br />
<p>
    <s>Бронь: ВІД @m.ReservationDateUser ДО @m.ReservationDateExpiryUser</s>
</p>
<br />
<br />
@if (m.VkUrls.Length == 0)
{
    <p>⚠️ Нажаль програма не змогла знайти посилання на фото.</p>
} else
{
    @if (m.VkUrls[0] == null)
    {
        <p>💔 видалено або перенесено у ПРОДАНО.</p>
    }
    else
    {
        <p>○○○ @m.VkUrls[0]</p>
    }

    <br />

    @foreach (string vkUrl in m.VkUrls.Skip(1))
    {
        @if (vkUrl != null)
        {
            <p>○ @vkUrl</p>
        }
    }
    <p>‣ @m.PrestaProductLink</p>
}
<br/>