using Microsoft.AspNetCore.Mvc.RazorPages;

namespace irisdropwebservice.Views.PrestaShopForChat.Staff.Reservations
{
    public class NewOrChangedReservationModel
    {
        public bool IsTestSite { get; set; }
        public string ReservationDateUser { get; set; }
        public string ReservationDateExpiryUser { get; set; }
        public string CustomerInfoString { get; set; }

        public string ProductReference { get; set; }
        public string ProductSizeDescription { get; set; }
        public string ProductChosenSizeName { get; set; }
        public int? PrevReservedQuantity { get; set; }
        public int NewReservedQuantity { get; set; }
        public bool UnreservedIsInCart { get; set; }
        public string PrestaProductLink { get; set; }
        public string[] VkUrls { get; set; }
    }
}
