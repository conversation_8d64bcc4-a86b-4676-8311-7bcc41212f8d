@page
@using Invictus.Nomenklatura.Misc
@using irisdropwebservice.Views.PrestaShopForChat.Partials
@using irisdropwebservice.Views.PrestaShopForChat.Staff
@model irisdropwebservice.Views.PrestaShopForChat.Staff.NewOrderModel
@{
    var m = (NewOrderModel)ViewContext.ViewData.Model;
}
@if (m.IsTestSite)
{
    <p>Нове замовлення на ТЕСТОВОМУ сайті</p>
}
else
{
    <p>👉 Нове замовлення на сайті 👈</p>
}
<p>№@m.OrderReference @m.OrderCreationDateTimeUser</p>
<p>Доставка: @m.CarrierName </p>

<br/>

@foreach (OrderSameProductIdsDetailsModel sameIdProducts in m.Products)
{
    @await RazorTemplateRender.RenderPartial(Html, @"PrestaShopForChat/Partials/OrderSameProductIdsDetails", sameIdProducts)
}

<br/>
<p>☵☵☵☵☵☵☵☵☵☵☵☵☵☵☵</p>
<br/>

@await RazorTemplateRender.RenderPartial(Html, @"PrestaShopForChat/Partials/OrderCommentsDetails", new OrderCommentsDetailsModel() { Messages = m.Messages })
