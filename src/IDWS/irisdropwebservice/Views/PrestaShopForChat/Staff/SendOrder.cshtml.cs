using irisdropwebservice.Libs.PrestaSharp;
using irisdropwebservice.Views.PrestaShopForChat.Partials;

using Microsoft.AspNetCore.Mvc.RazorPages;

namespace irisdropwebservice.Views.PrestaShopForChat.Staff
{
    public class PaidOrderModel : PageModel
    {
        public bool IsTestSite { get; set; }
        public bool IsPaidPartially { get; set; }

        public string OrderReference { get; set; }
        public string CarrierName { get; set; }
        public string OrderCreationDateTimeUser { get; init; }
        public string AdminOrderNote { get; set; }

        public List<OrderSameProductIdsDetailsModel> Products { get; set; }
        public List<OrderDetailMessage> Messages { get; set; }
        public List<OrderDetailsAddressModel> Addresses { get; set; }
    }
}
