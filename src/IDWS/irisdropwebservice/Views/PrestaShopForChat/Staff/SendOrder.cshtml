@page
@using Invictus.Nomenklatura.Misc
@using irisdropwebservice.Views.PrestaShopForChat.Partials
@using irisdropwebservice.Views.PrestaShopForChat.Staff
@model irisdropwebservice.Views.PrestaShopForChat.Staff.PaidOrderModel
@{
    var m = (PaidOrderModel)ViewContext.ViewData.Model;
}
<p>
    @if (m.IsTestSite)
    {
        <s>Замовлення (test.irisdrop) </s>
    }
    else
    {
        <s>Замовлення </s>
    }
    <s>@m.OrderReference</s>
</p>
<p>Від @m.OrderCreationDateTimeUser</p>
<p>
    <s>Доставка: @m.CarrierName </s>

    @if (m.IsPaidPartially)
    {
        <s>(наложка).</s>
    }
    else
    {
        <s>(повна оплата).</s>
    }
</p>
<p>
    @if (!string.IsNullOrWhiteSpace(m.AdminOrderNote))
    {
        <s>Примітка відсутня.</s>
    }
    else
    {
        <s>Примітка: @m.AdminOrderNote</s>
    }
</p>
<br/>

@foreach (OrderSameProductIdsDetailsModel sameIdProducts in m.Products)
{
    @await RazorTemplateRender.RenderPartial(Html, @"PrestaShopForChat/Partials/OrderSameProductIdsDetails", sameIdProducts);
}

<br />
<p>☵☵☵☵☵☵☵☵☵☵☵☵☵☵☵</p>
<br />

@await  RazorTemplateRender.RenderPartial(Html, @"PrestaShopForChat/Partials/OrderAddressesDetails", new OrderAddressesDetailsModel() { Addresses = m.Addresses })

<br />
<p>☵☵☵☵☵☵☵☵☵☵☵☵☵☵☵</p>
<br />

@await RazorTemplateRender.RenderPartial(Html, @"PrestaShopForChat/Partials/OrderCommentsDetails", new OrderCommentsDetailsModel() { Messages = m.Messages })