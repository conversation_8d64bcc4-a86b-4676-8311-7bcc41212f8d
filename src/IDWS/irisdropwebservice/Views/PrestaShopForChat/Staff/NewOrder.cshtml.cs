using irisdropwebservice.Libs.PrestaSharp;
using irisdropwebservice.Views.PrestaShopForChat.Partials;

using Microsoft.AspNetCore.Mvc.RazorPages;

namespace irisdropwebservice.Views.PrestaShopForChat.Staff
{
    public class NewOrderModel : PageModel
    {
        public bool IsTestSite { get; init; }

        public string OrderReference { get; init; }
        public string CarrierName { get; init; }
        public string OrderCreationDateTimeUser { get; init; }

        public List<OrderSameProductIdsDetailsModel> Products { get; init; }
        public List<OrderDetailMessage> Messages { get; init; }
    }
}
