@page
@using irisdropwebservice.Views.PrestaShopForChat.Customers
@model irisdropwebservice.Views.PrestaShopForChat.Customers.CustomersGenericOrderModel
@{
    var m = (CustomersGenericOrderModel)ViewContext.ViewData.Model;
}
<p>Під час обробки вашого платежу за замовлення з номером @m.OrderReference в магазині @m.ShopName сталася помилка. Будь ласка, зв’яжіться з нами якомога швидше.</p>
<p>Ви можете очікувати на доставку, як тільки ми отримаємо ваш платіж. </p>