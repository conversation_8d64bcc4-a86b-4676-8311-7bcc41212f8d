@page
@using irisdropwebservice.Views.PrestaShopForChat.Customers
@model irisdropwebservice.Views.PrestaShopForChat.Customers.CustomersGenericOrderModel
@{
    var m = (CustomersGenericOrderModel)ViewContext.ViewData.Model;
}
<p>Дякуємо за покупку в магазині @m.ShopName</p>
<p>Ваше замовлення з номером @m.OrderReference успішно розміщено. Ви можете очікувати на доставку, як тільки ми отримаємо ваш платіж.</p>