using Microsoft.AspNetCore.Mvc.RazorPages;

namespace irisdropwebservice.Views.PrestaShopForChat.Partials
{
    public class OrderDetailsAddressModel
    {
        public string CountryName { get; init; }
        public string OblastName { get; init; }

        public string City { get; init; }
        public string LastName { get; init; }
        public string FirstName { get; init; }
        public string MiddleName { get; init; }
        public string Address1 { get; init; }
        public string Address2 { get; init; }
        public string PhoneMobile { get; init; }
    }

    public class OrderAddressesDetailsModel : PageModel
    {
        public List<OrderDetailsAddressModel> Addresses { get; set; }
    }
}
