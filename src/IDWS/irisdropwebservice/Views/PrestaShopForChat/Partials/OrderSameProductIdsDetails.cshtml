@page
@using Invictus.Nomenklatura.Misc
@using irisdropwebservice.Libs.PrestaSharp
@model irisdropwebservice.Views.PrestaShopForChat.Partials.OrderSameProductIdsDetailsModel
@{
    var m = (irisdropwebservice.Views.PrestaShopForChat.Partials.OrderSameProductIdsDetailsModel)ViewContext.ViewData.Model;

    int counter = m.ProductSizeNumberInOrder;
}
<p>🔵 @m.ProductReference</p>
@foreach (OrderDetailProduct productDetail in m.OrderProductsDetails)
{
    <p>
        <s>➱ @IntStringUtil.ReplaceIntWithUnicodeFancyChars(counter): </s>

        @{ counter++; } 

        <s>@productDetail.SizeDescription: @productDetail.ChosenSizeName, </s> 
        
        @if (productDetail.Quantity > 1)
        {
            <s>⚠️ </s>
        }

        <s>@productDetail.Quantity шт.</s> 
    </p>
}

@if (m.VkUrls.Length == 0) 
{
    <p>⚠️ Нажаль програма не змогла знайти посилання на фото.</p>
} else 
{
    @if (m.VkUrls[0] == null)
    {
        <p>💔 видалено або перенесено у ПРОДАНО.</p>
    } else
    {
        <p>○○○ @m.VkUrls[0] </p>
    }

    <br/>

    @foreach (string vkUrl in m.VkUrls.Skip(1)) 
    {
        @if (vkUrl != null) 
        {
            <p>○ @vkUrl</p>
        }
    }
    <p>‣ @m.PrestaProductLink</p>
}