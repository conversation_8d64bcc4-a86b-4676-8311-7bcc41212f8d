@page
@using irisdropwebservice.Views.PrestaShopForChat.Partials
@model irisdropwebservice.Views.PrestaShopForChat.Partials.OrderAddressesDetailsModel
@{
    var m = (OrderAddressesDetailsModel)ViewContext.ViewData.Model;
    OrderDetailsAddressModel addr = m.Addresses.First();
}
@if (m.Addresses.Count == 0)
{
    <p>⚠️⚠️⚠️ Помилка на сайті: немає адреси.</p>
}
else if (m.Addresses.Count > 1)
{
    <p>⚠️⚠️⚠️ Помилка на сайті: адрес чомусь кілька.</p>
}
else
{

    <p>@if (addr.OblastName == null)
       {
           <s>Область не вказана</s>
       } else
       {
           <s>@addr.OblastName</s>
       },
       @addr.City, @addr.CountryName</p>

    <p>@addr.Address1</p>

    @if (!string.IsNullOrWhiteSpace(addr.Address2))
    {
        <p>@addr.Address2</p>
    }

    @if (!string.IsNullOrWhiteSpace(addr.MiddleName))
    {
        <p>@addr.LastName @addr.FirstName @addr.MiddleName</p>
    }
    else
    {
        <p>@addr.FirstName @addr.LastName</p>
    }

    <p>@addr.PhoneMobile</p>
}