using irisdropwebservice.Services.VkScrap;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace irisdropwebservice.Views.Statistics
{
    public class VkScrapHistoryItem
    {
        public string VkLink { get; set; }
        public VkScrapArtLogItem LogItem { get; set; }
        public string HistoryString { get; set; }
    }

    public class VkScrapAvaliReportModel : PageModel
    {
        public DateTime FromLocal { get; set; }
        public DateTime ToLocal { get; set; }

        public VkScrapHistoryItem[] LogItems { get; set; }
    }
}

