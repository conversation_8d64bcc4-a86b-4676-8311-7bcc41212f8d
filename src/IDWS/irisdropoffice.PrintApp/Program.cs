using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Printing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;

using Microsoft.AspNetCore.SignalR.Client;

namespace irisdropoffice.PrintApp
{
    static class Program
    {
        private static NotifyIcon _NotifyIcon;
        private static SynchronizationContext _MainThread;

        private static Mutex _AliveUntilAppFinishes;

        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        public static void Main()
        {
            bool r;
            _AliveUntilAppFinishes = new Mutex(true, "IrisDropOfficeApp_Mutex1", out r);
            if (!r)
                return;

            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            _NotifyIcon = new NotifyIcon();

            _NotifyIcon.Visible = true;
            _NotifyIcon.Icon = new Icon("Resources/favicon.ico");
            _NotifyIcon.DoubleClick += IconOnDoubleClick;

            Task.Delay(3000).ContinueWith(PollCycles);

            _MainThread = SynchronizationContext.Current ?? new SynchronizationContext();

            Application.Run();
        }

        private static void IconOnDoubleClick(object sender, EventArgs e)
        {
            MessageBox.Show("?", "???", MessageBoxButtons.OK);
        }

        private static void PrintDocUnparsed(string text, string guid, HubConnection connection)
        {
            try
            {
                var bdr = new StringBuilder();
                var ints = new List<int>();

                string[] spl = text.Split(new string[] { " " }, StringSplitOptions.RemoveEmptyEntries);
                bool isText = true;

                foreach (string s in spl)
                {
                    int i;

                    if (isText)
                    {
                        if (int.TryParse(s, out i))
                        {
                            isText = false;
                            ints.Add(i);
                        } else
                        {
                            bdr.Append(s);
                            bdr.Append(' ');
                        }
                    } else
                    {
                        if (int.TryParse(s, out i))
                        {
                            ints.Add(i);
                        } else
                        {
                            ints.Add(-1);
                        }
                    }
                }

                string title = bdr.ToString();

                int[] intsArray = ints.ToArray();
                Array.Sort(intsArray);

                int rows = (int)Math.Ceiling(intsArray.Length / 3.0);
                int rowsLow = (int)Math.Floor(intsArray.Length / 3.0);
                int highCols = intsArray.Length % 3;

                var intsList = new List<int>();

                for (int row = 0; row < rows; row++)
                {
                    for (int col = 0; col < 3; col++)
                    {
                        if (intsList.Count == intsArray.Length)
                            break;

                        if (rows != rowsLow && row == rows - 1)
                        {
                            GC.KeepAlive(0);
                        }

                        int highCol = Math.Min(col, highCols);
                        int lowCol = col - highCol;
                        int index = (highCol * rows) + (lowCol * rowsLow);
                        
                        intsList.Add(intsArray[index + row]);
                    }
                }

                string[] intArray = intsList
                    .Distinct()
                    .Select(i => i.ToString(CultureInfo.CurrentCulture).PadLeft(4, '0'))
                    /*.GroupByIncrement((i1, i2, i) => (i % 3 == 2) ? false : Math.Abs(i2 - i1) < 2) // This doesn't work with vertical sorting
                    .Select(i => {
                        int[] arr = i.ToArray();

                        if (arr.Length == 1)
                            return arr[0].ToString(CultureInfo.CurrentCulture).PadLeft(4, ' ');

                        if (arr.Length == 0)
                            return "?";

                        return arr.Min().ToString(CultureInfo.CurrentCulture) + "-" + arr.Max().ToString(CultureInfo.CurrentCulture);
                    })*/
                    .ToArray();
                
                string textt = string.Join(" ", intArray);

                PrintDoc(title, textt, guid, connection);
            }
            catch (Exception)
            {
                Task.Run(() => connection.InvokeAsync("PrintResult", "fail", guid));
            }
        }

        public static void PrintDoc(string title, string textt, string guid, HubConnection connection)
        {
            byte[] byteArray = File.ReadAllBytes("Resources/template.docx");

            using (var stream = new MemoryStream())
            {
                stream.Write(byteArray, 0, (int)byteArray.Length);

                using (WordprocessingDocument doc = WordprocessingDocument.Open(stream, true))
                {
                    Body body = doc.MainDocumentPart.Document.Body;
                    IEnumerable<Paragraph> paras = body.Elements<Paragraph>();

                    foreach (Paragraph para in paras)
                    {
                        foreach (Run run in para.Elements<Run>())
                        {
                            foreach (Text text in run.Elements<Text>())
                            {
                                if (text.Text.Contains("TEXT1"))
                                {
                                    text.Text = text.Text.Replace("TEXT1", title);
                                }

                                if (text.Text.Contains("TEXT2"))
                                {
                                    text.Text = text.Text.Replace("TEXT2", textt);
                                }
                            }
                        }
                    }
                }

                File.WriteAllBytes("Resources/current_template.docx", stream.ToArray());
            }

            var printDlg = new PrintDialog();
            var printDoc = new PrintDocument();
            var prnSetting = new PrinterSettings();

            prnSetting.PrintFileName = Path.GetFullPath("Resources/current_template.docx");
            prnSetting.PrintRange = PrintRange.AllPages;

            var pge = new PageSettings(prnSetting);
            pge.Landscape = false;

            printDoc.DocumentName = "test";
            printDoc.PrinterSettings = prnSetting;

            printDoc.PrintController = new StandardPrintController();

            printDlg.Document = printDoc;
            printDlg.AllowSelection = true;
            printDlg.AllowSomePages = true;

            Task.Run(() => connection.InvokeAsync("PrintResult", "success", guid));

            var f = new Form();
            f.ShowInTaskbar = true;
            f.Size = new Size(100, 20);

            try
            {
                f.Show();

                Application.DoEvents();
                Application.DoEvents();

                f.TopMost = true;
                f.Focus();
                f.BringToFront();

                Application.DoEvents();
                Application.DoEvents();

                if (printDlg.ShowDialog(f) == DialogResult.OK)
                {
                    printDlg.Dispose();
                    printDoc.Dispose();

                    // printDoc.Print();

                    var info = new ProcessStartInfo(Path.GetFullPath("Resources/current_template.docx"));
                    info.Verb = "PrintTo";
                    info.Arguments = printDoc.PrinterSettings.PrinterName;
                    info.CreateNoWindow = true;
                    info.WindowStyle = ProcessWindowStyle.Hidden;
                    Process.Start(info);
                } else
                {
                    printDlg.Dispose();
                    printDoc.Dispose();
                }
            }
            finally
            {
                Application.DoEvents();
                Application.DoEvents();

                f.Close();
                f.Dispose();

                Application.DoEvents();
                Application.DoEvents();
            }
           
        }

        private static async Task OnePollCycle()
        {
            // string hubUrl = "https://localhost:7276/office";
            string hubUrl = "https://ws.irisdrop.com.ua/office";

            // Create the SignalR connection
            HubConnection connection = new HubConnectionBuilder()
                .WithUrl(hubUrl, options => {
                    options.Transports = Microsoft.AspNetCore.Http.Connections.HttpTransportType.LongPolling;
                })
                .Build();

            // Handle received messages
            connection.On<string, string, string>("PrintTo", (user, message, guid) =>
            {
                _MainThread.Post(state => {
                    PrintDocUnparsed(message, guid, connection);
                }, null);
            });

            try
            {
                // Start the connection
                await connection.StartAsync();
                Console.WriteLine("Connected to the server.");

                // Keep the connection open
                while (true)
                {
                    if (connection.State != HubConnectionState.Connected)
                        break;

                    // Send a message to the hub
                    await Task.Delay(2500);
                }
            }
            catch (Exception ex)
            {
                Thread.Sleep(5000);

                Console.WriteLine($"Error: {ex.Message}");
            }
            finally
            {
                await connection.DisposeAsync();
            }
        }

        private static async Task PollCycles()
        {
            while (true)
            {
                try
                {
                    await OnePollCycle();
                }
                catch (Exception)
                {
                    Thread.Sleep(5000);

                    // ignored
                }
            }
        }

        private static async Task PollCycles(Task obj)
        {
            await PollCycles();
        }
    }

    public static class Enumerable
    {
        public static IEnumerable<IEnumerable<TElement>> GroupByIncrement<TElement>(
            this IEnumerable<TElement> source,
            Func<TElement, TElement, int, bool> isIncrement
        )
        {
            return GroupBySequenceIncrementIterator(source, isIncrement);
        }

        private static IEnumerable<IEnumerable<TElement>> GroupBySequenceIncrementIterator<TElement>(
            IEnumerable<TElement> source,
            Func<TElement, TElement, int, bool> isIncrementFunc)
        {
            using (IEnumerator<TElement> enumerator = source.GetEnumerator())
            {
                if (!enumerator.MoveNext())
                {
                    yield break;
                }

                int i = 0;
                TElement prev = enumerator.Current;
                var foundItems = new List<TElement>();

                do
                {
                    bool isIncrement = isIncrementFunc(prev, enumerator.Current, i);

                    if (!isIncrement)
                    {
                        yield return foundItems;
                        foundItems = new List<TElement>();
                    }

                    prev = enumerator.Current;

                    foundItems.Add(enumerator.Current);
                    i++;
                } while (enumerator.MoveNext());

                if (foundItems.Count > 0)
                {
                    yield return foundItems;
                }
            }
        }
    }
}
