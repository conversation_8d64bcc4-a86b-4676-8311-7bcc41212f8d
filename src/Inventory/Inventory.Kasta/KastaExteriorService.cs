using Inventory.Kasta.Api;

using Invictus.Nomenklatura.ExteriorServ;
using Invictus.Nomenklatura.Logg;

using JetBrains.Annotations;

namespace Inventory.Kasta;

[UsedImplicitly]
public class KastaExteriorService : IExteriorService
{
    private readonly ILogger _logger = InvLog.Logger<KastaExteriorService>();
    
    public KastaExteriorService(IKastaInitWorker kastaInitWorker, IKastaWorker kastaWorker, IKastaApiWorker kastaApiWorker)
    {
    }

    public Task Run()
    {
        return Task.CompletedTask;
    }
}