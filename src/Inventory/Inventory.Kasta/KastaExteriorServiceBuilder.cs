using Inventory.Kasta.Api;

using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.ExteriorServ;

using JetBrains.Annotations;

using Microsoft.Extensions.DependencyInjection;

namespace Inventory.Kasta;

[UsedImplicitly]
public class KastaExteriorServiceBuilder : ExteriorServiceBuilderBase<KastaExteriorService>
{
    protected override void AddConcealedServices(ServiceCollection concealedServiceCollection)
    {
        concealedServiceCollection.AddSingleton<KastaApiFactory>();
        concealedServiceCollection.AddWorkerSingleton<KastaApiWorker>();
        concealedServiceCollection.AddWorkerSingleton<KastaWorker>();
        
        // concealedServiceCollection.AddRefitClient<IKastaApi>();

        this.RegisterDatabaseTypes(concealedServiceCollection);

        base.AddConcealedServices(concealedServiceCollection);
    }

    protected override void ExposeConcealedServices()
    {
        this.ExposeSingleton<IKastaInitWorker>();
        this.ExposeSingleton<IKastaWorker>();
        
        base.ExposeConcealedServices();
    }
}