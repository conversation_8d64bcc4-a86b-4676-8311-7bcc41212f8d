using Inventory.Kasta.Structures;

using Invictus.Nomenklatura.Web;

using Refit;

namespace Inventory.Kasta.Api;

public interface IKastaRefitApi
{
    [Get("/supplier-content/category/all")]
    Task<WebResponse<KastaCategoriesListResponse, KastaResponse400>> GetAllCategoriesAsync(CancellationToken ct);

    [Get("/supplier-content/category/details")]
    Task<WebResponse<KastaCategoryDetailsResponse, KastaResponse400>> GetCategoryDetailsAsync(
        [Query] long kind_id, 
        [Query] long affiliation_id, 
        CancellationToken ct
    );

    [Get("/products/list")]
    Task<WebResponse<KastaProductsListResponse, KastaResponse400>> GetProductsAsync(
        [Query] long? cursor,
        [Query] bool include_extended_characteristics = true,
        CancellationToken ct = default
    );

    [Post("/supplier-content/submit/image")]
    Task<WebResponse<KastaSubmitPhotoResponse, KastaResponse400>> SubmitPhotoAsync(
        [Body] string url, 
        CancellationToken ct
    );

    [Post("/supplier-content/submit/products")]
    Task<WebResponse<KastaSubmitProductsResponse, KastaResponse400>> SubmitProductsAsync(
        [Body] KastaSubmitProductRequest request, 
        CancellationToken ct
    );

    [Post("/products/update-stock/id")]
    Task<WebResponse<KastaUpdateProductResponse, KastaResponse400>> UpdateStockAsync(
        [Body] KastaUpdateStockRequest request, 
        CancellationToken ct
    );

    [Post("/products/update-price/id")]
    Task<WebResponse<KastaUpdateProductResponse, KastaResponse400>> UpdatePricesAsync(
        [Body] KastaUpdatePricesRequest request, 
        CancellationToken ct
    );

    [Get("/orders/list")]
    Task<WebResponse<KastaOrdersListResponse, KastaResponse400>> GetOrdersAsync(
        [Query] string from, 
        CancellationToken ct
    );
}