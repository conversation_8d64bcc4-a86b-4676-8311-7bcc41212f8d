using Inventory.Kasta.Structures;

using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Web;
using Invictus.Nomenklatura.Workers;
using Invictus.Platform;

namespace Inventory.Kasta.Api;

[CanFail(LogLevel = LogEventLevel.Verbose)]
public interface IKastaApiWorker : IWorker<IKastaApiWorker>
{
    Task<WebResponse<KastaCategoriesListResponse, KastaResponse400>> GetAllCategories(CancellationToken ct);
    Task<WebResponse<KastaCategoryDetailsResponse, KastaResponse400>> GetCategoryDetails(long kindId, long affiliationId, CancellationToken ct);

    /*
    WebResponse<List<KastaProduct>, KastaResponse400> GetAllProducts(CancellationToken ct);
    WebResponse<KastaProductsListResponse, KastaResponse400> GetProducts(long? cursor, CancellationToken ct);
    
    WebResponse<KastaSubmitPhotoResponse, KastaResponse400> SubmitPhoto(string url, CancellationToken ct);
    WebResponse<KastaSubmitProductsResponse, KastaResponse400> SubmitProducts(List<KastaProductSubmit> products, KastaCategoryId kastaCategoryId, bool isUpdate, CancellationToken ct);

    WebResponse<KastaUpdateProductResponse, KastaResponse400> UpdateStock(List<KastaUpdateStockRequestItem> products, CancellationToken ct);
    WebResponse<KastaUpdateProductResponse, KastaResponse400> UpdatePrices(List<KastaUpdatePricesRequestItem> products, CancellationToken ct);

    WebResponse<KastaOrdersListResponse, KastaResponse400> GetOrders(DateTime from, CancellationToken ct);
    */
}

public class KastaApiWorker :
    IWorkerImpl<IKastaApiWorker>, IKastaApiWorker
{
    public WorkerCore Core { get; set; }
    public ILogger Log { get; } = InvLog.Logger<KastaApiWorker>();

    public WorkerConfiguration WorkerConfiguration { get; } = new WorkerConfiguration(
        "KASAPI",
        new WorkerConfiguration.TaskScheduler("KastaWebApiCalls", 1),
        LogEventLevel.Debug,
        AllowDirectCall: true
        // GetCustomInterceptor: w => new Interceptor((KastaSiteWebApiCallsWorker)w)
    );
    
    IKastaApiWorker IWorkerImpl<IKastaApiWorker>.PublicInterface { get; set; }

    private readonly CustomerLocal<IKastaRefitApi> _refitApiFactory;

    public KastaApiWorker(KastaApiFactory kastaApiFactory)
    {
        _refitApiFactory = new CustomerLocal<IKastaRefitApi>(kastaApiFactory);
    }

    public async Task<WebResponse<KastaCategoriesListResponse, KastaResponse400>> GetAllCategories(CancellationToken ct)
    {
        WebResponse<KastaCategoriesListResponse, KastaResponse400> response = await this.GetApi().GetAllCategoriesAsync(ct);
        return response;
    }

    public async Task<WebResponse<KastaCategoryDetailsResponse, KastaResponse400>> GetCategoryDetails(long kindId, long affiliationId, CancellationToken ct)
    {
        WebResponse<KastaCategoryDetailsResponse, KastaResponse400> response = await this.GetApi().GetCategoryDetailsAsync(kindId, affiliationId, ct);
        return response;
    }

    private IKastaRefitApi GetApi()
    {
        return _refitApiFactory.GetOrCreate(1);
    }
}