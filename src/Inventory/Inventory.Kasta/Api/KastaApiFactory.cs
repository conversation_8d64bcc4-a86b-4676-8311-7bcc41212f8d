using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Web;
using Invictus.Platform;

namespace Inventory.Kasta.Api;

public class KastaApiFactory : RefitApiFactoryBase<IKastaRefitApi>
{
    private readonly CustomerSubconfig<KastaCustomerConfiguration>[] _customersConfiguration;

    public KastaApiFactory(InvTasks invTasks, InvAppConfig invAppConfig)
        : base (invTasks, invAppConfig.GetSection<KastaConfiguration>("Kasta").ApiUrl)
    {
        _customersConfiguration = invAppConfig.GetCustomersSections<KastaCustomerConfiguration>("Kasta");
    }

    protected override void ModifyHttpClient(HttpClient httpClient)
    {
        string customerName = CustomerContext.GetCustomerName(1);
        
        if (customerName == null)
            throw new Exception("Customer env (1) is not set.");
        
        CustomerSubconfig<KastaCustomerConfiguration> customerConfiguration = _customersConfiguration.Single(c => c.CustName == customerName);
        
        httpClient.DefaultRequestHeaders.Add("Authorization", customerConfiguration.Configuration.ApiKey);
    }
}