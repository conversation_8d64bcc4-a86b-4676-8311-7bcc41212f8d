using Inventory.Kasta.Api;
using Inventory.Kasta.Structures;

using Invictus.Emerald.Shared;
using Invictus.Inventory;
using Invictus.Nomenklatura.App;
using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.AppConfig;
using Invictus.Nomenklatura.Logg;
using Invictus.Nomenklatura.Misc;
using Invictus.Nomenklatura.Web;
using Invictus.Nomenklatura.Workers;
using Invictus.Platform;

namespace Inventory.Kasta;

[EmeraldFeature("KastaInit")]
public interface IKastaInitWorker : IWorker<IKastaInitWorker>
{
    [EmeraldOperation("FetchSupercategories")]
    void InitFetchSupercategories(CancellationToken ct);

    [EmeraldOperation("MergeKastaCatsToInventory")]
    void InitMergeKastaCatsToInventory();
}

[EmeraldFeature("Kasta")]
public interface IKastaWorker : IWorker<IKastaWorker>
{
}

public class KastaWorker : 
    IWorkerImpl<IKastaInitWorker>, IKastaInitWorker,
    IWorkerImpl<IKastaWorker>, IKastaWorker
{
    public ILogger Log { get; } = InvLog.Logger<KastaWorker>();

    public WorkerCore Core { get; set; }
    IKastaInitWorker IWorkerImpl<IKastaInitWorker>.PublicInterface { get; set; }
    IKastaWorker IWorkerImpl<IKastaWorker>.PublicInterface { get; set; }

    public WorkerConfiguration WorkerConfiguration { get; } = new(
        "KASTA0",
        new WorkerConfiguration.Thread("KastaSync", ThreadPriority.Normal, IsBackground: false),
        LogEventLevel.Information,
        AllowDirectCall: false
    );
    
    private readonly InvJsonSerializer _jsonSerializer = new();
    private readonly IKastaApiWorker _webApi;
    private readonly IDbAccessFactory<InventoryDbAccess> _inventoryDbAccess;
    private readonly PlatformService _platformService;
    private readonly InvAppConfig _appConfig;
    private readonly InvEnv _invEnv;

    private string ResourcesDir => Path.Combine(_appConfig.BasicConfiguration.Dirs.ExeResourcesDir, "Kasta");

    public KastaWorker(IKastaApiWorker apiWorker, IDbAccessFactory<InventoryDbAccess> inventoryDbAccess, PlatformService platformService, InvAppConfig appConfig, InvEnv invEnv)
    {
        _webApi = apiWorker;
        _inventoryDbAccess = inventoryDbAccess;
        _platformService = platformService;
        _appConfig = appConfig;
        _invEnv = invEnv;
    }
    
    public void InitFetchSupercategories(CancellationToken ct)
    {
        if (_invEnv.EnvEnum != InvEnvEnum.Dev)
            throw new Exception("This operation is allowed only in dev env.");
        
        using IDisposable custCtx = CustomerContext.WithContext(1, "IRIS");
        
        string[] relevantTopCategories = new[]
        {
            "Дітям",
            "Унісекс",
            "Хлопчикам",
            "Дівчаткам",
            "Чоловікам",
        };
        
        WebResponse<KastaCategoriesListResponse, KastaResponse400> categories = _webApi.Do(w => w.GetAllCategories(ct));

        if (categories.IsFail)
            throw new Exception("");

        List<KastaCategory> kastaCategories = categories.Success.Items;

        foreach (KastaCategory kastaCategory in kastaCategories)
        {
            if (!relevantTopCategories.Contains(kastaCategory.Name))
                continue;

            var kastaCategorySchemas = new List<KastaMyCategorySchemaKind>();

            foreach (KastaCategoryKind kastaCategoryKind in kastaCategory.Kinds)
            {
                WebResponse<KastaCategoryDetailsResponse, KastaResponse400> schemaResponse =
                    _webApi.Do(w => w.GetCategoryDetails(kastaCategoryKind.KindId, kastaCategoryKind.AffiliationId, ct));

                if (schemaResponse.IsFail)
                    throw new Exception("");

                kastaCategorySchemas.Add(
                    new KastaMyCategorySchemaKind { CategoryKindItself = kastaCategoryKind, SchemaItems = schemaResponse.Success.Schema }
                );
            }

            var schema = new KastaMyCategorySchema
            {
                Category = kastaCategory,
                KindsWithSchemas = kastaCategorySchemas
            };

            string json = _jsonSerializer.SerializeForInternals(schema, typeof(KastaMyCategorySchema));

            using (var sw = new StreamWriter(File.Open(Path.Combine(ResourcesDir, $"kasta_cats{schema.Category.NameAlias}.json"), FileMode.Create)))
            {
                sw.Write(json);
            }
        }

        GC.KeepAlive(0);
    }

    public void InitMergeKastaCatsToInventory()
    {
        // using IDisposable custCtx = CustomerContext.WithContext(1, "IRIS");
        
        InvPla platform = _platformService.GetPlatform(PlatformEnum.Kasta);

        var newPlatformCategories = new List<InvPlaCat>();

        string[] kastaCatsFiles = Directory.GetFiles(ResourcesDir, "kasta_cats*.json");

        var schemas = new KastaMyCategorySchema[kastaCatsFiles.Length];

        for (int i = 0; i < kastaCatsFiles.Length; i++)
        {
            using FileStream sr = File.OpenRead(kastaCatsFiles[i]);
            
            schemas[i] = _jsonSerializer.DeserializeForInternals<KastaMyCategorySchema>(sr);
        }

        foreach (KastaMyCategorySchema kastaMyCategorySchema in schemas)
        {
            foreach (KastaMyCategorySchemaKind kind in kastaMyCategorySchema.KindsWithSchemas)
            {
                var plaCat = new InvPlaCat
                {
                    Name = kind.CategoryKindItself.Name,
                    PlaId = platform.Id
                };
                
                // kind.CategoryKindItself.
            }
        }
        
        using InventoryDbAccess access = _inventoryDbAccess.CreateAccess();



        access.IntegratePlatformFeatures(platform.Id, newPlatformCategories.ToArray());
    }
}