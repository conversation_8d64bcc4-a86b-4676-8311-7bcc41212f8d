using Invictus.Nomenklatura.App2;
using Invictus.Nomenklatura.Logg;
using Invictus.Platform;

using Microsoft.EntityFrameworkCore;

namespace Invictus.Inventory;

public class InventoryDbAccess : DbAccessBase
{
    private readonly ILogger _logger = InvLog.Logger<InventoryDbAccess>();
    
    public InventoryDbContext DbContext { get; }

    public InventoryDbAccess(DbAccessOptions dbAccessOptions, InventoryDbContext dbContext)
        : base(dbAccessOptions, dbContext)
    {
        DbContext = dbContext;
    }

    public void IntegratePlatformFeatures(int platformId, InvPlaCat[] categories)
    {
        
    }

    public void DeduplicatePlatformFeatures(int platformId)
    {
        
    }
}

public class InventoryDbContext : PlatformDbContext
{
    public InventoryDbContext(DbContextOptions<InventoryDbContext> options)
        : base(options)
    {
    }

    public DbSet<InvPlaCat> PlatformCategories { get; set; }
    public DbSet<InvPlaFea> PlatformFeatures { get; set; }
    public DbSet<InvPlaFeaCat> PlatformFeatureCategories { get; set; }
    public DbSet<InvPlaFeaSelVal> PlatformFeatureSelectValues { get; set; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        
        this.ConfigureInventoryEntities(modelBuilder);
    }

    private void ConfigureInventoryEntities(ModelBuilder modelBuilder)
    {
        this.ConfigurePlatformFeatureCategories(modelBuilder);
        this.ConfigureFeatureSelectValues(modelBuilder);
        this.ConfigureTranslationRelationships(modelBuilder);
    }

    private void ConfigurePlatformFeatureCategories(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<InvPlaFeaCat>(entity =>
        {
            entity.HasKey(fc => new { fc.PlaFeaId, fc.PlaCatId });

            entity.HasOne(fc => fc.Feature)
                .WithMany(f => f.Categories)
                .HasForeignKey(fc => fc.PlaFeaId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(fc => fc.Category)
                .WithMany(c => c.FeatureCategories)
                .HasForeignKey(fc => fc.PlaCatId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        modelBuilder.Entity<InvPlaFea>()
            .ToTable(t => t.HasCheckConstraint("CK_InvPlaFea_IsMandatory", "[IsMandatory] IN (0, 1, 2) OR [IsMandatory] IS NULL"));
    }

    private void ConfigureFeatureSelectValues(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<InvPlaFeaSelVal>()
            .HasOne(sv => sv.Feature)
            .WithMany(f => f.SelectValues)
            .HasForeignKey(sv => sv.PlaFeaId)
            .OnDelete(DeleteBehavior.Cascade);
    }

    private void ConfigureTranslationRelationships(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<InvPlaCat>()
            .HasOne(pc => pc.Platform)
            .WithMany()
            .HasForeignKey(pc => pc.PlaId)
            .OnDelete(DeleteBehavior.NoAction);

        modelBuilder.Entity<InvPlaFea>()
            .HasOne(pf => pf.Translation)
            .WithMany()
            .HasPrincipalKey(t => t.Key)
            .HasForeignKey(pf => pf.TransKey)
            .OnDelete(DeleteBehavior.NoAction);

        modelBuilder.Entity<InvPlaFeaSelVal>()
            .HasOne(sv => sv.Translation)
            .WithMany()
            .HasPrincipalKey(t => t.Key)
            .HasForeignKey(sv => sv.TransKey)
            .OnDelete(DeleteBehavior.NoAction);
    }
}