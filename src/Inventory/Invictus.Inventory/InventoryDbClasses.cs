using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using Invictus.Platform;

namespace Invictus.Inventory;

[Table("InvPlaCat")]
public class InvPlaCat
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public long Id { get; set; }

    [Required]
    public int PlaId { get; set; }

    [Required]
    [StringLength(255)]
    public string Name { get; set; }

    public virtual ICollection<InvPlaFeaCat> FeatureCategories { get; set; }
    
    [ForeignKey(nameof(PlaId))]
    public virtual InvPla Platform { get; set; }
}

[Table("InvPlaFea")]
public class InvPlaFea
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public long Id { get; set; }

    [Required]
    [StringLength(255)]
    public string Name { get; set; }

    [Required]
    [StringLength(255)]
    public string TransKey { get; set; }

    [Required]
    public int Type { get; set; }

    [Column(TypeName = "binary(1000)")]
    public byte[] PlaData { get; set; }

    public byte? IsMandatory { get; set; }

    public virtual ICollection<InvPlaFeaCat> Categories { get; set; }
    public virtual ICollection<InvPlaFeaSelVal> SelectValues { get; set; }
    
    public virtual InvTra Translation { get; set; }
}

[Table("InvPlaFeaSelVal")]
public class InvPlaFeaSelVal
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public long Id { get; set; }

    [Required]
    public long PlaFeaId { get; set; }

    [Required]
    [StringLength(255)]
    public string TransKey { get; set; }

    [Column(TypeName = "binary(1000)")]
    public byte[] PlaData { get; set; }

    [ForeignKey(nameof(PlaFeaId))]
    public virtual InvPlaFea Feature { get; set; }
    
    public virtual InvTra Translation { get; set; }
}

[Table("InvPlaFeaCat")]
public class InvPlaFeaCat
{
    [Required]
    public long PlaFeaId { get; set; }

    [Required]
    public long PlaCatId { get; set; }

    [ForeignKey(nameof(PlaFeaId))]
    public virtual InvPlaFea Feature { get; set; }

    [ForeignKey(nameof(PlaCatId))]
    public virtual InvPlaCat Category { get; set; }
}