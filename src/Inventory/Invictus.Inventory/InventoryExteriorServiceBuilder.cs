using Invictus.Nomenklatura.ExteriorServ;

using JetBrains.Annotations;

using Microsoft.Extensions.DependencyInjection;

namespace Invictus.Inventory;

[UsedImplicitly]
public class InventoryExteriorServiceBuilder : ExteriorServiceBuilderBase<InventoryExteriorService>
{
    protected override void AddConcealedServices(ServiceCollection concealedServiceCollection)
    {
        // concealedServiceCollection.AddSingleton<CustomerRelations>();

        this.RegisterDatabaseTypes(concealedServiceCollection);

        base.AddConcealedServices(concealedServiceCollection);
    }

    protected override void ExposeConcealedServices()
    {
        this.ExposeDbAccess<InventoryDbAccess>();
        // this.ExposeSingleton<CustomerRelations>();
        
        base.ExposeConcealedServices();
    }
}