global using System.Globalization;
global using System.Text;
global using System.Collections.Generic;
global using System.Linq;
global using System.Threading.Tasks;

global using Serilog;
global using Serilog.Events;
global using ILogger = Serilog.ILogger;

// TODO: use invalid substitute
global using Path = System.IO.Path;
global using StringReader = System.IO.StringReader;
global using StringWriter = System.IO.StringWriter;
global using TextWriter = System.IO.TextWriter;
global using MemoryStream = System.IO.MemoryStream;
global using StreamReader = System.IO.StreamReader;
global using StreamWriter = System.IO.StreamWriter;
global using BinaryReader = System.IO.BinaryReader;
global using BinaryWriter = System.IO.BinaryWriter;
global using IOException = System.IO.IOException;